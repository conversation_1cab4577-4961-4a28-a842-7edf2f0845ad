{"workbench.colorCustomizations": {"activityBar.background": "#483D8B", "titleBar.activeBackground": "#483D8B", "titleBar.activeForeground": "#FFFCFB"}, "java.configuration.maven.userSettings": "${workspaceFolder}/.mvn/settings.xml", "java.import.maven.embedded": null, "maven.localRepository": "/Volumes/workspace/SDK/repository_hncrc", "java.debug.settings.onBuildFailureProceed": true, "java.configuration.updateBuildConfiguration": "automatic", "java.compile.nullAnalysis.mode": "automatic"}