{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "java",
      "name": "SpringBootApplication",
      "request": "launch",
      "mainClass": "com.hainancrc.module.creditcxbs.CreditCxbsApplication",
      "projectName": "hncrc-module-credit-cxbs-biz",
      "cwd": "${workspaceFolder}/hncrc-module-credit-cxbs/hncrc-module-credit-cxbs-biz",
      "vmArgs": ["-Dspring.profiles.active=dev"],
      "env": {
        "SPRING_PROFILES_ACTIVE": "dev"
      }
    },
    {
      "type": "chrome",
      "request": "launch",
      "name": "Start FE Mobile",
      "url": "http://localhost:9102",
      "webRoot": "${workspaceFolder}/frontend-mobile",
      "sourceMaps": true,
      "sourceMapPathOverrides": {
        "webpack:///src/*": "${webRoot}/src/*",
        "webpack:///./*": "${webRoot}/*",
        "webpack:///./~/*": "${webRoot}/node_modules/*",
        "webpack://?:*/*": "${webRoot}/*"
      },
      "skipFiles": ["node_modules/**", "<node_internals>/**"],
      "pathMapping": {
        "/": "${workspaceFolder}/frontend-mobile/src"
      },
      "preLaunchTask": "Pre-launch FE Mobile"
    },
    {
      "type": "chrome",
      "request": "launch",
      "name": "Start FE PC",
      "url": "http://localhost:9599",
      "webRoot": "${workspaceFolder}/hncrc-cxbs-web",
      "sourceMaps": true,
      "sourceMapPathOverrides": {
        "webpack:///src/*": "${webRoot}/src/*",
        "webpack:///./*": "${webRoot}/*",
        "webpack:///./~/*": "${webRoot}/node_modules/*",
        "webpack://?:*/*": "${webRoot}/*"
      },
      "skipFiles": ["node_modules/**", "<node_internals>/**"],
      "pathMapping": {
        "/": "${workspaceFolder}/hncrc-cxbs-web/src"
      },
      "preLaunchTask": "Pre-launch FE PC"
    }
  ],
  "compounds": [
    {
      "name": "Server & Mobile & Pc Fe",
      "configurations": [
        "SpringBootApplication",
        "Start FE Mobile",
        "Start FE PC"
      ],
      "stopAll": true
    },
    {
      "name": "Server & FE PC",
      "configurations": ["SpringBootApplication", "Start FE PC"],
      "stopAll": true
    },
    {
      "name": "Server & FE Mobile",
      "configurations": ["SpringBootApplication", "Start FE Mobile"],
      "stopAll": true
    }
  ]
}
