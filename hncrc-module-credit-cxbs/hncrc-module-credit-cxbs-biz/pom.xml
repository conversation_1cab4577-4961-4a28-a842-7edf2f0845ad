<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>hainancrc</groupId>
        <artifactId>hncrc-module-credit-cxbs</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>hncrc-module-credit-cxbs-biz</artifactId>
    <packaging>jar</packaging>

    <name>hncrc-module-credit-cxbs-biz</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>


    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>3.8.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-spring-boot-starter-redis</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-spring-boot-starter-security</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-module-credit-cxbs-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-spring-boot-starter-web</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!-- DB 相关 -->
        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-spring-boot-starter-mybatis</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>hainancrc</groupId>-->
        <!--            <artifactId>hncrc-baseuser-service</artifactId>-->
        <!--            <version>1.0-SNAPSHOT</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-module-log-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-spring-boot-starter-rpc</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>


        <!-- 日志 -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>

        <!-- 文件上传 -->
        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-module-upload-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.hncrc.openapi</groupId>
            <artifactId>hncrc-openapi-api</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-module-creditreports-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-module-codeengine-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.5</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
