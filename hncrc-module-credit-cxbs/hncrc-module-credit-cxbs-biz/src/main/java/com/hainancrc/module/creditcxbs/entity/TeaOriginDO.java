package com.hainancrc.module.creditcxbs.entity;

import java.math.BigDecimal;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;


/**
 * 茶叶产地信息 DO
 */
@TableName("cxbs_tea_origin")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeaOriginDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 产地名称
     */
    private String originName;

    /**
     * 茶叶类型
     */
    private String teaType;

    /**
     * 茶园名称
     */
    private String teaEstate;

    /**
     * 茶园面积(亩)
     */
    private BigDecimal estateArea;

    /**
     * 茶青产量(吨)
     */
    private BigDecimal teaYouthYield;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 产地地址
     */
    private String originAddress;

    /**
     * 产地简介
     */
    private String originIntroduction;

    /**
     * 产地图片
     */
    private String originPic;

    /**
     * 产地状态
     */
    private String originStatus;

}
