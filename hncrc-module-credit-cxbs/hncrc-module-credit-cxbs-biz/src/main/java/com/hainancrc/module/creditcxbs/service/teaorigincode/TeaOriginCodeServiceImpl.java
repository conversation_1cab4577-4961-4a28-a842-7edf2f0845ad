package com.hainancrc.module.creditcxbs.service.teaorigincode;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.enums.TeaOriginCodeStatus;
import com.hainancrc.module.creditcxbs.api.teaorigincode.dto.*;
import com.hainancrc.module.creditcxbs.api.teaorigincode.vo.TeaOriginCodePageRespVO;
import com.hainancrc.module.creditcxbs.api.teaorigincode.vo.TeaOriginCodeSubjectRespVO;
import com.hainancrc.module.creditcxbs.convert.teaorigincode.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.teaorigin.TeaOriginMapper;
import com.hainancrc.module.creditcxbs.mapper.teaorigincode.*;

import com.hainancrc.module.creditcxbs.mapper.teaprogincoderecord.TeaOriginCodeRecordMapper;
import com.hainancrc.module.creditcxbs.mapper.teasubject.TeaSubjectMapper;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;
/**
*  Service 实现类
*
*/
@Service
@Validated
public class TeaOriginCodeServiceImpl implements TeaOriginCodeService {

    @Resource
    private TeaOriginMapper teaOriginMapper;

    @Resource
    private TeaSubjectMapper teaSubjectMapper;
    
    @Resource
    private TeaOriginCodeMapper teaOriginCodeMapper;

    @Resource
    private TeaOriginCodeRecordMapper teaOriginCodeRecordMapper;
    
    
    
    private void validateExists(Long id) {
        if (teaOriginCodeMapper.selectById(id) == null) {
            throw exception(TEA_ORIGIN_CODE_NOT_EXISTS);
        }
    }
    
    
    @Override
    public Long create(TeaOriginCodeCreateDTO createDTO) {
        
        
        // 插入
        TeaOriginCodeDO teaOriginCode = TeaOriginCodeConvert.INSTANCE.convert(createDTO);
        teaOriginCodeMapper.insert(teaOriginCode);
        // 返回
        return teaOriginCode.getId();
    }
    
    @Override
    public void update(TeaOriginCodeUpdateDTO updateDTO) {
        

        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        TeaOriginCodeDO updateObj = TeaOriginCodeConvert.INSTANCE.convert(updateDTO);
        updateObj.setCode(StringEscapeUtils.unescapeHtml(updateDTO.getCode()));
        teaOriginCodeMapper.updateById(updateObj);
    }
    
    
    @Override
    public PageResult<TeaOriginCodePageRespVO> getPage(TeaOriginCodePageDTO pageDTO) {
        Page<TeaOriginCodePageRespVO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        IPage<TeaOriginCodePageRespVO> pageResult = teaOriginCodeMapper.selectOriginCodePage(page, pageDTO);

        List<TeaOriginCodePageRespVO> records = pageResult.getRecords();
        for (TeaOriginCodePageRespVO code : records) {
            code.setCode(StringEscapeUtils.unescapeHtml(code.getCode()));
        }

        List<Long> collect = records.stream().map(TeaOriginCodePageRespVO::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            List<Map<String, Object>> maps = teaOriginCodeRecordMapper.selectMaps(collect);
            for (Map<String, Object> map : maps) {
                for (TeaOriginCodePageRespVO code : records) {
                    if (map.get("origin_code_id").toString().equals(code.getId().toString())) {
                        code.setUseCount(Integer.valueOf(map.get("use_count").toString()));
                        code.setTotalTeaWeight(new BigDecimal(map.get("total_tea_weight").toString()));
                    }
                }
            }
        }


        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }
    
    @Override
    public List<TeaOriginCodeDO> getList() {
        return teaOriginCodeMapper.selectList();
    }
    
    
    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        teaOriginCodeMapper.deleteById(id);
    }
    
    
    @Override
    public TeaOriginCodeDO get(Long id) {
        return teaOriginCodeMapper.selectById(id);
    }

    @Override
    public TeaOriginCodeSubjectRespVO getByCode(String code) {
        TeaOriginCodeSubjectRespVO respVo = new TeaOriginCodeSubjectRespVO();
        TeaOriginCodeDO teaOriginCodeDO = teaOriginCodeMapper.selectOne(new LambdaQueryWrapper<TeaOriginCodeDO>()
                .eq(TeaOriginCodeDO::getCodeNum, code));
        if (ObjectUtil.isNotEmpty(teaOriginCodeDO)) {

            if (TeaOriginCodeStatus.OFFLINE.name().equals(teaOriginCodeDO.getCodeStatus())) {
                throw new ServiceException(-1, "产地码已停用");
            }

            respVo.setTeaOriginCodeId(teaOriginCodeDO.getId());
            TeaOriginDO teaOriginDO = teaOriginMapper.selectById(teaOriginCodeDO.getTeaOriginId());

            TeaOriginCodeRecordDO teaOriginCodeRecordDO = teaOriginCodeRecordMapper.selectOne(new LambdaQueryWrapper<TeaOriginCodeRecordDO>()
                    .eq(TeaOriginCodeRecordDO::getOriginId, teaOriginCodeDO.getTeaOriginId())
                    .eq(TeaOriginCodeRecordDO::getOriginCodeId, teaOriginCodeDO.getId())
                    .orderByDesc(TeaOriginCodeRecordDO::getUpdateTime)
                    .last("limit 1"));

            if (ObjectUtil.isNotEmpty(teaOriginDO)) {
                respVo.setTeaOrigin(teaOriginDO.getOriginName());
                respVo.setTeaEstate(teaOriginDO.getTeaEstate());
                respVo.setTeaType(teaOriginCodeRecordDO != null ? teaOriginCodeRecordDO.getTeaType() : teaOriginDO.getTeaType());
                respVo.setOriginIntroduction(teaOriginDO.getOriginIntroduction());
                JSONObject originPic = JSON.parseObject(teaOriginDO.getOriginPic());
                respVo.setOriginPic(StringEscapeUtils.unescapeHtml(originPic.getString("url")));
            }

            TeaSubjectDO teaSubjectDO = teaSubjectMapper.selectById(teaOriginCodeDO.getTeaSubjectId());
            if (ObjectUtil.isNotEmpty(teaSubjectDO)) {
                respVo.setSubjectName(teaSubjectDO.getSubjectName());
            }

            if (teaOriginCodeDO.getViewCount() == null || teaOriginCodeDO.getViewCount() == 0) {
                teaOriginCodeDO.setViewCount(1);
            }else {
                teaOriginCodeDO.setViewCount(teaOriginCodeDO.getViewCount() + 1);
            }

            teaOriginCodeMapper.updateById(teaOriginCodeDO);
        }
        return respVo;
    }


}

