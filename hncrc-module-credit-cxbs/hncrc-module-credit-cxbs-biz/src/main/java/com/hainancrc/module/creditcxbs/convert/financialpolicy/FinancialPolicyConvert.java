package com.hainancrc.module.creditcxbs.convert.financialpolicy;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.financialpolicy.dto.*;
import com.hainancrc.module.creditcxbs.api.financialpolicy.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface FinancialPolicyConvert {

    FinancialPolicyConvert INSTANCE = Mappers.getMapper(FinancialPolicyConvert.class);


    FinancialPolicyDO convert(FinancialPolicyCreateDTO bean);


    FinancialPolicyDO convert(FinancialPolicyUpdateDTO bean);


   FinancialPolicyRespVO convert(FinancialPolicyDO bean);

    List<FinancialPolicyRespVO> convertList(List<FinancialPolicyDO> list);

    PageResult<FinancialPolicyRespVO> convertPage(PageResult<FinancialPolicyDO> page);



}
