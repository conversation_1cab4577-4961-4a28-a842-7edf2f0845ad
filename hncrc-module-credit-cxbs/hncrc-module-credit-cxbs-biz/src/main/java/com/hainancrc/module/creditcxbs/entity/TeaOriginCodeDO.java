package com.hainancrc.module.creditcxbs.entity;

import java.math.BigDecimal;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;


/**
 * 产地码 DO
 */
@TableName("cxbs_tea_origin_code")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeaOriginCodeDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 产地ID(列表:关联tea_origin表id,新增:必填)
     */
    private Long teaOriginId;

    /**
     * 产地主体ID
     */
    private Long teaSubjectId;

    /**
     * 码内容(新增:必填)
     */
    private String code;

    /**
     * 码编号(新增)
     */
    private String codeNum;

    /**
     * 查看次数(列表,新增)
     */
    private Integer viewCount;

    /**
     * 用码累计数(列表,新增)
     */
    private Integer useCount;

    /**
     * 累计茶青总数（斤）(列表,新增)
     */
    private BigDecimal totalTeaWeight;

    /**
     * 承诺书文件key(新增)
     */
    private String promiseFileKey;

    /**
     * 承诺书文件url(新增)
     */
    private String promiseFileUrl;

    /**
     * 产地码有效期
     */
    private Date expireTime;

    /**
     * 状态
     */
    private String codeStatus;

}
