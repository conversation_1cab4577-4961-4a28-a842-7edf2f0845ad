package com.hainancrc.module.creditcxbs.entity;

import java.math.BigDecimal;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaPolicyStatus;

/**
 * 油茶政策信息 DO
 */
@TableName("cxbs_oil_tea_policy")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OilTeaPolicyDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 政策名称(列表,新增,编辑)
     */
    private String policyName;

    /**
     * 政策内容描述(新增,编辑)
     */
    private String policyContent;

    /**
     * 政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)
     */
    private OilTeaPolicyStatus policyStatus;

    /**
     * 政策附件(新增,编辑)
     */
    private String policyFileListJson;

}
