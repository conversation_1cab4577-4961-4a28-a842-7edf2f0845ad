package com.hainancrc.module.creditcxbs.mapper.financialpolicy;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.enums.FinancialPolicyPolicyStatus;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.financialpolicy.dto.*;

import org.apache.ibatis.annotations.*;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Mapper
public interface FinancialPolicyMapper extends BaseMapperX<FinancialPolicyDO> {
    
    
    // PageResult<FinancialPolicyDO> selectPage(@Param("dto") FinancialPolicyPageDTO reqDTO);
    default PageResult<FinancialPolicyDO> selectPage(FinancialPolicyPageDTO pageDTO) {
        return selectPage(pageDTO, new LambdaQueryWrapperX<FinancialPolicyDO>()
                .likeIfPresent(FinancialPolicyDO::getPolicyName, pageDTO.getPolicyName())
                .eqIfPresent(FinancialPolicyDO::getPolicyStatus, pageDTO.getPolicyStatus())
                .orderByDesc(FinancialPolicyDO::getCreateTime));
    }

    default Long selectOne(String policyName) {
        return selectCount(new LambdaQueryWrapperX<FinancialPolicyDO>().eq(FinancialPolicyDO::getPolicyName, policyName));
    }

    default List<FinancialPolicyDO> getFinancialPolicyList() {
        return selectList(new LambdaQueryWrapperX<FinancialPolicyDO>()
                .eq(FinancialPolicyDO::getPolicyStatus, FinancialPolicyPolicyStatus.ONLINE)
                .orderByDesc(FinancialPolicyDO::getUpdateTime));
    }
}

