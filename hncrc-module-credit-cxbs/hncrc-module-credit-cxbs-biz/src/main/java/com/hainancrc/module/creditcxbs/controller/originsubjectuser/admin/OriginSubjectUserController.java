package com.hainancrc.module.creditcxbs.controller.originsubjectuser.admin;

import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.module.creditcxbs.api.originsubjectuser.dto.*;
import com.hainancrc.module.creditcxbs.api.originsubjectuser.vo.*;
import com.hainancrc.module.creditcxbs.convert.originsubjectuser.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.originsubjectuser.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "产地主体对应用户表")
@RestController
@RequestMapping("/originsubjectuser")
@Validated
public class OriginSubjectUserController {
    
    @Resource
    private OriginSubjectUserService originSubjectUserService;


    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Long> create(@Valid @RequestBody OriginSubjectUserCreateDTO createDTO) {
        return success(originSubjectUserService.create(createDTO));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody OriginSubjectUserUpdateDTO updateDTO) {
        originSubjectUserService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<OriginSubjectUserRespVO>> getList() {
        List<OriginSubjectUserDO> list = originSubjectUserService.getList();
        return success(OriginSubjectUserConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<OriginSubjectUserRespVO>> getPage(@Valid OriginSubjectUserPageDTO pageDTO) {
        PageResult<OriginSubjectUserDO> pageResult = originSubjectUserService.getPage(pageDTO);
        return success(OriginSubjectUserConvert.INSTANCE.convertPage(pageResult));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        originSubjectUserService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<OriginSubjectUserRespVO> get(@RequestParam("id") Long id) {
        OriginSubjectUserDO originSubjectUser = originSubjectUserService.get(id);
        return success(OriginSubjectUserConvert.INSTANCE.convert(originSubjectUser));
    }


}
