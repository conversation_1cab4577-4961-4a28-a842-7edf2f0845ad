package com.hainancrc.module.creditcxbs.convert.seedlingcategory;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.dto.*;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface SeedlingCategoryConvert {

    SeedlingCategoryConvert INSTANCE = Mappers.getMapper(SeedlingCategoryConvert.class);


    SeedlingCategoryDO convert(SeedlingCategoryCreateDTO bean);


    SeedlingCategoryDO convert(SeedlingCategoryUpdateDTO bean);


   SeedlingCategoryRespVO convert(SeedlingCategoryDO bean);

    List<SeedlingCategoryRespVO> convertList(List<SeedlingCategoryDO> list);

    List<SeedlingCategorySelectVO> convertSelectList(List<SeedlingCategoryDO> list);

    PageResult<SeedlingCategoryRespVO> convertPage(PageResult<SeedlingCategoryDO> page);



}
