package com.hainancrc.module.creditcxbs.convert.businessfunding;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.businessfunding.dto.*;
import com.hainancrc.module.creditcxbs.api.businessfunding.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface BusinessFundingConvert {

    BusinessFundingConvert INSTANCE = Mappers.getMapper(BusinessFundingConvert.class);


    BusinessFundingDO convert(BusinessFundingCreateDTO bean);


    BusinessFundingDO convert(BusinessFundingUpdateDTO bean);


   BusinessFundingRespVO convert(BusinessFundingDO bean);

    List<BusinessFundingRespVO> convertList(List<BusinessFundingDO> list);

    PageResult<BusinessFundingRespVO> convertPage(PageResult<BusinessFundingDO> page);

    List<BusinessFundingDO> convertFile(List<BusinessFundingFileRespVO> list);



}
