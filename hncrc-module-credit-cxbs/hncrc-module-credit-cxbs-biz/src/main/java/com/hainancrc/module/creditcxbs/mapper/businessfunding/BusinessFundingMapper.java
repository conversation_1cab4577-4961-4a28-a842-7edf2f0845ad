package com.hainancrc.module.creditcxbs.mapper.businessfunding;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.businessfunding.dto.*;

import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;

@Mapper
public interface BusinessFundingMapper extends BaseMapperX<BusinessFundingDO> {
    
    
    PageResult<BusinessFundingDO> selectPage(@Param("dto") BusinessFundingPageDTO reqDTO);
    
    Long selectApplySubjectCount();

    Long selectApplySuccessSubjectCount();

    BigDecimal selectTotalAmount();
}

