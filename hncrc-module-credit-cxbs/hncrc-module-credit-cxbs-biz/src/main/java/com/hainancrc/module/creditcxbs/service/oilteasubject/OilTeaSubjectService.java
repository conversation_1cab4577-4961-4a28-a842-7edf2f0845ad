package com.hainancrc.module.creditcxbs.service.oilteasubject;

import java.io.IOException;
import java.util.*;
import javax.validation.*;

import com.hainancrc.module.creditcxbs.api.oiltea.vo.OilTeaOverviewVO;
import com.hainancrc.module.creditcxbs.api.oilteasubject.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteasubject.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;
import org.springframework.web.multipart.MultipartFile;

/**
*  Service 接口
*
*/
public interface OilTeaSubjectService {
    
    
    /**
    * 创建
    *
    * @param createDTO 创建信息
    * @return 编号
    */
    Long create(@Valid OilTeaSubjectCreateDTO createDTO);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid OilTeaSubjectUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    List<OilTeaSubjectDO> getList();
    
    /**
    * 获得分页
    *
    * @param pageDTO 分页查询
    * @return 分页
    */
    PageResult<OilTeaSubjectDO> getPage(OilTeaSubjectPageDTO pageDTO);
    
    
    /**
    * 删除
    *
    * @param id 主键
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id 主键
    * @return
    */
    OilTeaSubjectDO get(Long id);

    /**
     * 导入油茶经营主体
     * @param file
     * @return
     */
    String importFile(MultipartFile file) throws IOException;

    /**
     * 油茶主体数据总览分析
     * @return
     */
    OilTeaOverviewVO getStatistics();

    /**
     * 获取油茶主体分类统计数据
     * @return 统计结果
     */
    OilTeaSubjectTypeStatVO getSubjectTypeStat();

}

