package com.hainancrc.module.creditcxbs.service.financialproductfunding;

import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.businessfunding.vo.BusinessFundingFileRespVO;
import com.hainancrc.module.creditcxbs.api.enums.FinancialProductFundingFileStatus;
import com.hainancrc.module.creditcxbs.api.financialproductfunding.dto.*;
import com.hainancrc.module.creditcxbs.convert.businessfunding.BusinessFundingConvert;
import com.hainancrc.module.creditcxbs.convert.financialproductfunding.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.businessfunding.BusinessFundingMapper;
import com.hainancrc.module.creditcxbs.mapper.financialproductfunding.*;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;
import static com.hainancrc.module.creditcxbs.constant.Constants.SYSTEM_CREATOR;

/**
*  Service 实现类
*
*/
@Slf4j
@Service
@Validated
public class FinancialProductFundingServiceImpl implements FinancialProductFundingService {
    
    @Resource
    private FinancialProductFundingMapper financialProductFundingMapper;

    @Resource
    private BusinessFundingMapper businessFundingMapper;
    
    
    
    private void validateExists(Long id) {
        if (financialProductFundingMapper.selectById(id) == null) {
            throw exception(FINANCIAL_PRODUCT_FUNDING_NOT_EXISTS);
        }
    }
    
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean create(MultipartFile file) {
        //验证名称重复
        if (financialProductFundingMapper.selectCount(new LambdaQueryWrapperX<FinancialProductFundingDO>()
                .eq(FinancialProductFundingDO::getFileName, file.getOriginalFilename())) > 0) {
            throw exception(FINANCIAL_PRODUCT_FUNDING_EXISTS);
        }

        if (ObjectUtil.isNull(file) || file.isEmpty()) {
            throw new ServiceException(-1, "请上传文件");
        }
        //文件不能大于20MB，
        if (file.getSize() > 20 * 1024 * 1024) {
            throw new ServiceException(-1, "上传文件不能大于20MB");
        }
        // 或者文件名称
        FinancialProductFundingDO financialProductFunding = new FinancialProductFundingDO();
        financialProductFunding.setFileName(file.getOriginalFilename());
        financialProductFunding.setFileContent(file.getOriginalFilename());
        financialProductFunding.setCreator(SYSTEM_CREATOR);
        financialProductFunding.setFileStatus(FinancialProductFundingFileStatus.ONLINE);
        financialProductFundingMapper.insert(financialProductFunding);

        // 解析xlsx文件数据，插入到数据库中
        List<BusinessFundingFileRespVO> dataList;
        try (InputStream inputStream = file.getInputStream()) {
            dataList = EasyExcel.read(inputStream).head(BusinessFundingFileRespVO.class).sheet().doReadSync();
        } catch (ExcelAnalysisException e) {
            if (e.getCause() instanceof ExcelDataConvertException) {
                throw new ServiceException(-1, "日期格式错误");
            } else {
                throw new ServiceException(-1, "读取Excel文件时出错");
            }
        } catch (IOException e) {
            log.error("读取Excel文件时出错", e);
            return false;
        }
        try {
            dataList.forEach(item -> item.setFinancialProductFundingId(financialProductFunding.getId()));
            dataList.forEach(item -> item.setCreator(SYSTEM_CREATOR));
            businessFundingMapper.insertBatch(BusinessFundingConvert.INSTANCE.convertFile(dataList));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return true;

    }
    
    @Override
    public void update(FinancialProductFundingUpdateDTO updateDTO) {
        
        
        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        FinancialProductFundingDO updateObj = FinancialProductFundingConvert.INSTANCE.convert(updateDTO);
        financialProductFundingMapper.updateById(updateObj);
    }
    
    
    @Override
    public PageResult<FinancialProductFundingDO> getPage(FinancialProductFundingPageDTO pageDTO) {
        PageResult<FinancialProductFundingDO> pageResult = financialProductFundingMapper.selectPage(pageDTO, new LambdaQueryWrapperX<FinancialProductFundingDO>()
                .likeIfPresent(FinancialProductFundingDO::getFileName, pageDTO.getFileName())
                .eqIfPresent(FinancialProductFundingDO::getDeleted, 0)
                .orderByDesc(FinancialProductFundingDO::getCreateTime));

        // 去除文件名称后缀
        if (!CollectionUtils.isEmpty(pageResult.getList())) {
            pageResult.getList().forEach(item -> {
                item.setFileName(FileNameUtil.getPrefix(item.getFileName()));
            });
        }

        return pageResult;
    }
    
    @Override
    public List<FinancialProductFundingDO> getList() {
        return financialProductFundingMapper.selectList();
    }
    
    
    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        financialProductFundingMapper.deleteById(id);
        businessFundingMapper.delete(new LambdaQueryWrapperX<BusinessFundingDO>()
                .eq(BusinessFundingDO::getFinancialProductFundingId, id));
    }
    
    
    @Override
    public FinancialProductFundingDO get(Long id) {
        return financialProductFundingMapper.selectById(id);
    }



}

