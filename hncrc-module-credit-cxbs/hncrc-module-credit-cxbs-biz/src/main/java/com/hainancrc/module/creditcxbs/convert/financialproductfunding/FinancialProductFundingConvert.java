package com.hainancrc.module.creditcxbs.convert.financialproductfunding;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.financialproductfunding.dto.*;
import com.hainancrc.module.creditcxbs.api.financialproductfunding.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface FinancialProductFundingConvert {

    FinancialProductFundingConvert INSTANCE = Mappers.getMapper(FinancialProductFundingConvert.class);


    FinancialProductFundingDO convert(FinancialProductFundingCreateDTO bean);


    FinancialProductFundingDO convert(FinancialProductFundingUpdateDTO bean);


   FinancialProductFundingRespVO convert(FinancialProductFundingDO bean);

    List<FinancialProductFundingRespVO> convertList(List<FinancialProductFundingDO> list);

    PageResult<FinancialProductFundingRespVO> convertPage(PageResult<FinancialProductFundingDO> page);



}
