package com.hainancrc.module.creditcxbs.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-19
 */
@Slf4j
public class ExcelKits {

    /**
     * 获取指定路径下的Excel模板文件
     * @param path 模板文件路径
     * @param response
     * @return
     */
    public static void getTemplate(String path, HttpServletResponse response, String fileName) {
        InputStream inputStream = null;
        ServletOutputStream servletOutputStream = null;
        try {
            Resource resource = new DefaultResourceLoader().getResource("classpath:" + path);
            response.setContentType("application/force-download");
            // 使用URLEncoder对文件名进行编码
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            response.setHeader("Content-Disposition", "attachment;fileName=" + encodedFileName
                                                              + ".xlsx");
            inputStream = resource.getInputStream();
            servletOutputStream = response.getOutputStream();
            IOUtils.copy(inputStream, servletOutputStream);
            response.flushBuffer();
        } catch (Exception e) {
            log.error("下载模板文件错误", e);
        } finally {
            try {
                if (servletOutputStream != null) {
                    servletOutputStream.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("下载模板文件错误", e);
            }
        }
    }
}