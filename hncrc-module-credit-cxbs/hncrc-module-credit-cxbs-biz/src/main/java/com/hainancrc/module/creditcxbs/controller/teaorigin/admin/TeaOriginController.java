package com.hainancrc.module.creditcxbs.controller.teaorigin.admin;

import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.module.creditcxbs.api.teaorigin.dto.*;
import com.hainancrc.module.creditcxbs.api.teaorigin.vo.*;
import com.hainancrc.module.creditcxbs.convert.teaorigin.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.teaorigin.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "茶叶产地信息")
@RestController
@RequestMapping("/teaOrigin")
@Validated
public class TeaOriginController {
    
    @Resource
    private TeaOriginService teaOriginService;


    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Long> create(@Valid @RequestBody TeaOriginCreateDTO createDTO) {
        return success(teaOriginService.create(createDTO));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody TeaOriginUpdateDTO updateDTO) {
        teaOriginService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<TeaOriginRespVO>> getList() {
        List<TeaOriginDO> list = teaOriginService.getList();
        return success(TeaOriginConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/originList")
    @ApiOperation("获得茶叶产地下拉列表")
    public CommonResult<List<TeaOriginListVO>> getOriginList() {
        return success(teaOriginService.getOriginList());
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<TeaOriginPageRespVO>> getPage(@Valid TeaOriginPageDTO pageDTO) {
        return success(teaOriginService.getPage(pageDTO));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        teaOriginService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<TeaOriginRespVO> get(@RequestParam("id") Long id) {
        TeaOriginDO teaOrigin = teaOriginService.get(id);
        return success(TeaOriginConvert.INSTANCE.convert(teaOrigin));
    }

    @GetMapping("/getApplyPage")
    @ApiOperation("获得申领主体分页")
    public CommonResult<PageResult<ApplySubjectPageRespVO>> getApplyPage(@Valid TeaOriginPageDTO pageDTO) {
        return success(teaOriginService.getApplyPage(pageDTO));
    }


}
