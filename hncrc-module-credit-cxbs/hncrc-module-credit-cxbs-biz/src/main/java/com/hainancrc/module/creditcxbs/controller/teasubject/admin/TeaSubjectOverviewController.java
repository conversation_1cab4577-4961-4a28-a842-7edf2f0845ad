package com.hainancrc.module.creditcxbs.controller.teasubject.admin;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.creditcxbs.api.teasubject.dto.DataOverViewSelectDTO;
import com.hainancrc.module.creditcxbs.api.teasubject.vo.TeaStatisticsRespVO;
import com.hainancrc.module.creditcxbs.api.teasubject.vo.TeaSubjectAnalysisVO;
import com.hainancrc.module.creditcxbs.api.teasubject.vo.TeaSubjectDataVO;
import com.hainancrc.module.creditcxbs.service.teasubject.TeaSubjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "茶叶经营主体数据统计信息")
@RestController
@RequestMapping("/teaSubject/overview")
@Validated
public class TeaSubjectOverviewController {
    
    @Resource
    private TeaSubjectService teaSubjectService;

    @GetMapping("/statistics")
    @ApiOperation("茶叶产地主体统计信息")
    public CommonResult<TeaStatisticsRespVO> getStatistics() {
        return success(teaSubjectService.getStatistics());
    }

    @GetMapping("/listByTeaSubjectData")
    @ApiOperation("茶叶产地主体申领数据分析")
    public CommonResult<List<TeaSubjectDataVO>> listByTeaSubjectData(DataOverViewSelectDTO dto) {
        return CommonResult.success(teaSubjectService.listByTeaSubjectData(dto));
    }

    @GetMapping("/analysis")
    @ApiOperation("茶叶入驻经营主体分析")
    public CommonResult<TeaSubjectAnalysisVO> getAnalysis() {
        return success(teaSubjectService.getAnalysis());
    }
}
