package com.hainancrc.module.creditcxbs.controller.seedlingcategory.admin;

import cn.hutool.core.util.ObjectUtil;
import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.dto.*;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.vo.*;
import com.hainancrc.module.creditcxbs.convert.seedlingcategory.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.seedlingcategory.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "苗木信息")
@RestController
@RequestMapping("/seedlingCategory")
@Validated
public class SeedlingCategoryController {
    
    @Resource
    private SeedlingCategoryService seedlingCategoryService;


    @PostMapping("/create")
    @ApiOperation("新增苗木数据")
    public CommonResult<Long> create(@Valid @RequestBody SeedlingCategoryCreateDTO createDTO) {
        return success(seedlingCategoryService.create(createDTO));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody SeedlingCategoryUpdateDTO updateDTO) {
        seedlingCategoryService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("苗木介绍列表")
    public CommonResult<List<SeedlingCategoryRespVO>> getList() {
        List<SeedlingCategoryDO> list = seedlingCategoryService.getList();
        return success(SeedlingCategoryConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/selectList")
    @ApiOperation("苗木下拉列表")
    public CommonResult<List<SeedlingCategorySelectVO>> getSelectList() {
        List<SeedlingCategoryDO> list = seedlingCategoryService.getSelectList();
        return success(SeedlingCategoryConvert.INSTANCE.convertSelectList(list));
    }

    @GetMapping("/page")
    @ApiOperation("后台特色苗木列表分页")
    public CommonResult<PageResult<SeedlingCategoryRespVO>> getPage(@Valid SeedlingCategoryPageDTO pageDTO) {
        PageResult<SeedlingCategoryDO> pageResult = seedlingCategoryService.getPage(pageDTO);
        return success(SeedlingCategoryConvert.INSTANCE.convertPage(pageResult));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        seedlingCategoryService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得苗木信息")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<SeedlingCategoryRespVO> get(@RequestParam("id") Long id) {
// !!REVIEW
// TODO 可能的问题: 在get方法中，调用`seedlingCategoryService.get(id)`可能会返回null，如果后续转换为`SeedlingCategoryRespVO`会导致NullPointerException。
// 修改建议：在调用`SeedlingCategoryConvert.INSTANCE.convert(seedlingCategory)`之前，增加对`seedlingCategory`是否为null的检查，并返回适当的错误结果。
        SeedlingCategoryDO seedlingCategory = seedlingCategoryService.get(id);
        return success(SeedlingCategoryConvert.INSTANCE.convert(seedlingCategory));
    }


    @GetMapping("/getBySubjectId")
    @ApiOperation("根据主体id获取苗木种类")
    public CommonResult<SeedlingCategoryRespVO> getBySubjectId(@RequestParam("subjectId") Long subjectId) {
        SeedlingCategoryDO seedlingCategory = seedlingCategoryService.getBySubjectId(subjectId);
        if (ObjectUtil.isNull(seedlingCategory)){
            return CommonResult.error(-1, "没有找到该主体对应的苗木种类");
        }
        return success(SeedlingCategoryConvert.INSTANCE.convert(seedlingCategory));
    }

    @GetMapping("/getDistinctCategoryAndVariety")
    @ApiOperation("获取苗木种类列表和品种列表")
    public CommonResult<SeedlingCategoryDistinctRespVO> getDistinctCategoryAndVariety(){
        return success(seedlingCategoryService.getSeedlingTypesAndVarieties());
    }


    @GetMapping("/subjectSeedlingPage")
    @ApiOperation("获取经营主体下苗木信息分页")
    public CommonResult<PageResult<SeedlingCategoryRespVO>> getSubjectSeedlingPage(@Valid SeedlingCategoryPageDTO pageDTO) {
        PageResult<SeedlingCategoryDO> pageResult = seedlingCategoryService.getSubjectSeedlingPage(pageDTO);
        return success(SeedlingCategoryConvert.INSTANCE.convertPage(pageResult));
    }


}
