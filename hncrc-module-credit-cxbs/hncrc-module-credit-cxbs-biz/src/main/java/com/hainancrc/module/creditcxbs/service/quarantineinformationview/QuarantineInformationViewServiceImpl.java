package com.hainancrc.module.creditcxbs.service.quarantineinformationview;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.quarantineinformationview.dto.QuarantineInformationViewCreateDTO;
import com.hainancrc.module.creditcxbs.api.quarantineinformationview.dto.QuarantineInformationViewPageDTO;
import com.hainancrc.module.creditcxbs.api.quarantineinformationview.dto.QuarantineInformationViewUpdateDTO;
import com.hainancrc.module.creditcxbs.convert.quarantineinformationview.QuarantineInformationViewConvert;
import com.hainancrc.module.creditcxbs.entity.OilTeaSubjectDO;
import com.hainancrc.module.creditcxbs.entity.QuarantineInformationViewDO;
import com.hainancrc.module.creditcxbs.entity.TeaOriginCodeDO;
import com.hainancrc.module.creditcxbs.entity.TeaSubjectDO;
import com.hainancrc.module.creditcxbs.mapper.oilteasubject.OilTeaSubjectMapper;
import com.hainancrc.module.creditcxbs.mapper.quarantineinformationview.QuarantineInformationViewMapper;
import com.hainancrc.module.creditcxbs.mapper.teaorigincode.TeaOriginCodeMapper;
import com.hainancrc.module.creditcxbs.mapper.teasubject.TeaSubjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.COMPANY_NOT_EXISTS;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.OIL_TEA_SUBJECT_EXISTS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-06
 */

@Service
public class QuarantineInformationViewServiceImpl implements QuarantineInformationViewService {

    @Resource
    private QuarantineInformationViewMapper quarantineInformationViewMapper;

    @Resource
    private OilTeaSubjectMapper oilTeaSubjectMapper;

    @Resource
    private TeaOriginCodeMapper teaOriginCodeMapper;
    @Resource
    private TeaSubjectMapper teaSubjectMapper;

    @Override
    @Transactional
    public Long create(QuarantineInformationViewCreateDTO createDTO) {
        //参数校验
        TeaOriginCodeDO originCodeDO = teaOriginCodeMapper.selectById(createDTO.getId());
        if (originCodeDO == null) {
            throw exception(OIL_TEA_SUBJECT_EXISTS);
        }
        //查询当天是否有查看记录
        Date date = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String viewDate = formatter.format(date);
        QuarantineInformationViewDO quotaInformationViewDO =  quarantineInformationViewMapper.selectReportView(originCodeDO.getId(), viewDate);
        if (quotaInformationViewDO == null) {
            QuarantineInformationViewDO quarantineInformationViewDO = new QuarantineInformationViewDO();
            quarantineInformationViewDO.setViewCount(1L);
            quarantineInformationViewDO.setViewDate(new Date());
            quarantineInformationViewDO.setTeaOriginCodeId(originCodeDO.getId());
            quarantineInformationViewDO.setDeleted(false);
            quarantineInformationViewMapper.insert(quarantineInformationViewDO);
            return quarantineInformationViewDO.getId();
        }

        recordViewCount(originCodeDO.getId(), quotaInformationViewDO);

        return quotaInformationViewDO.getId();
    }

    private void recordViewCount(Long teaOriginCodeId, QuarantineInformationViewDO reportViewDO){
        Long viewCount = reportViewDO.getViewCount();
        reportViewDO.setViewCount(viewCount + 1);
        reportViewDO.setTeaOriginCodeId(teaOriginCodeId);
        reportViewDO.setViewDate(new Date());
        quarantineInformationViewMapper.updateById(reportViewDO);
    }
    @Override
    public void update(QuarantineInformationViewUpdateDTO updateDTO) {

        // 更新
        QuarantineInformationViewDO updateObj = QuarantineInformationViewConvert.INSTANCE.convert(updateDTO);
        quarantineInformationViewMapper.updateById(updateObj);
    }

    @Override
    public List<QuarantineInformationViewDO> getList() {
        return quarantineInformationViewMapper.selectList();
    }

    @Override
    public PageResult<QuarantineInformationViewDO> getPage(QuarantineInformationViewPageDTO pageDTO) {
        return quarantineInformationViewMapper.selectPage(pageDTO);
    }

    @Override
    public void delete(Long id) {

        // 删除
        quarantineInformationViewMapper.deleteById(id);
    }

    @Override
    public QuarantineInformationViewDO get(Long id) {
        return quarantineInformationViewMapper.selectById(id);
    }
}
