package com.hainancrc.module.creditcxbs.service.seedlingsubject;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.codeengine.api.publicapi.PublicApi;
import com.hainancrc.module.codeengine.api.publicapi.dto.ApplyCreateDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.GenerateCreateDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.GenerateStatusDTO;
import com.hainancrc.module.codeengine.api.publicapi.vo.ApplyCreateRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.GenerateCreateRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.GenerateStatusRespVO;
import com.hainancrc.module.codeengine.enums.ApplyCategory;
import com.hainancrc.module.codeengine.enums.CodeLogStatus;
import com.hainancrc.module.creditcxbs.api.enums.SeedlingSubjectSubjectType;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.vo.SeedlingCategorySelectVO;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.dto.*;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.vo.*;
import com.hainancrc.module.creditcxbs.config.GskConfig;
import com.hainancrc.module.creditcxbs.convert.seedlingsubject.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.code.CodeCa11Mapper;
import com.hainancrc.module.creditcxbs.mapper.code.CodeCa16Mapper;
import com.hainancrc.module.creditcxbs.mapper.code.CodeEx02Mapper;
import com.hainancrc.module.creditcxbs.mapper.originviewdown.OriginViewDownMapper;
import com.hainancrc.module.creditcxbs.mapper.seedlingcategory.SeedlingCategoryMapper;
import com.hainancrc.module.creditcxbs.mapper.seedlingsubject.*;

import com.hainancrc.module.creditcxbs.mapper.seedlingsubjectcategory.SeedlingSubjectCategoryMapper;
import com.hainancrc.module.creditcxbs.utils.HmacAuthUtil;
import com.hainancrc.module.creditcxbs.utils.Okhttp3Utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import javax.annotation.Resource;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;
import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;
import static com.hainancrc.module.creditcxbs.constant.Constants.CreditLevelA;
import static com.hainancrc.module.creditcxbs.constant.Constants.IMAGE_URL_STRING;
import java.util.List;
import java.util.stream.Stream;

/**
*  Service 实现类
*
*/
@Service
@Validated
@Slf4j
public class SeedlingSubjectServiceImpl implements SeedlingSubjectService {

    @Resource
    private SeedlingSubjectMapper seedlingSubjectMapper;

    @Resource
    private SeedlingCategoryMapper seedlingCategoryMapper;

    @Resource
    private SeedlingSubjectCategoryMapper seedlingSubjectCategoryMapper;

    @Resource
    private OriginViewDownMapper originViewDownMapper;

    @Resource
    private GskConfig gskConfig;

    @Resource
    private CodeCa11Mapper codeCa11Mapper;

    @Resource
    private CodeEx02Mapper codeEx02Mapper;

    @Resource
    private PublicApi publicApi;

    @Value("${codeengine.application}")
    private String codeengineApplication;

    @Value("${codeengine.channel}")
    private String codeengineChannel;

    @Value("${codeengine.credit-code-scene}")
    private String codeengineCreditCodeScene;


    private void validateExists(Long id) {
        if (seedlingSubjectMapper.selectById(id) == null) {
            throw exception(SEEDLING_SUBJECT_NOT_EXISTS);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(SeedlingSubjectCreateDTO createDTO) {

        boolean exists = seedlingSubjectMapper.exists(new LambdaQueryWrapper<SeedlingSubjectDO>()
                .eq(SeedlingSubjectDO::getSubjectName, createDTO.getSubjectName())
                .or()
                .eq(SeedlingSubjectDO::getUniscid, createDTO.getUniscid()));
        if (exists) {
            throw exception(SEEDLING_SUBJECT_EXISTS);
        }

        SeedlingSubjectDO seedlingSubject = SeedlingSubjectConvert.INSTANCE.convert(createDTO);

        // 申码
        // 拿出苗木主体对应的信息
        CommonResult<ApplyCreateRespVO> applyCreateRespVO = getApplyCreateRespVOCommonResult(seedlingSubject);

        // 亮码请求
        GenerateCreateDTO generateCreateDTO = getGenerateCreateDTO(applyCreateRespVO);
        log.info("调用码引擎亮码请求:{}", generateCreateDTO);

        // 调用码引擎亮码
        CommonResult<GenerateCreateRespVO> generateCreateRespVO = getGenerateCreateRespVOCommonResult(generateCreateDTO);

        // 亮码结果
        CommonResult<GenerateStatusRespVO> generateStatusRespVO = getGenerateStatusRespVOCommonResult(generateCreateRespVO);

        if (CodeLogStatus.SUCCESS.equals(generateStatusRespVO.getData().getGenerateStatus())){
            seedlingSubject.setOneCode(generateStatusRespVO.getData().getCodeUrl());
            seedlingSubject.setOneSeedCode(generateStatusRespVO.getData().getCodeUrl());

            String codeUrl = generateStatusRespVO.getData().getCodeUrl();
            // 截取code后面的值
            String param = "code=";
            int start = codeUrl.indexOf(param) + param.length();
            int end = codeUrl.indexOf("&", start);
            seedlingSubject.setCodeNum(codeUrl.substring(start, end));
        }

        // 插入
        seedlingSubjectMapper.insert(seedlingSubject);

        // 插入苗木类型
        if (!CollectionUtils.isEmpty(createDTO.getSeedlingVarietyIds())) {
            for (Long seedlingVarietyId : createDTO.getSeedlingVarietyIds()) {
                SeedlingSubjectCategoryDO seedlingSubjectCategoryDO = new SeedlingSubjectCategoryDO();
                seedlingSubjectCategoryDO.setSubjectId(seedlingSubject.getId());
                seedlingSubjectCategoryDO.setCategoryId(seedlingVarietyId);
                seedlingSubjectCategoryMapper.insert(seedlingSubjectCategoryDO);
            }
        }

        // 返回
        return seedlingSubject.getId();
    }

    @NotNull
    private CommonResult<ApplyCreateRespVO> getApplyCreateRespVOCommonResult(SeedlingSubjectDO seedlingSubjectDO) {
        ApplyCreateDTO applyCreateDTO = new ApplyCreateDTO();
        applyCreateDTO.setApplication(codeengineApplication);
        applyCreateDTO.setChannel(codeengineChannel);
        applyCreateDTO.setScene(codeengineCreditCodeScene);
        applyCreateDTO.setEntityId(seedlingSubjectDO.getUniscid());
        applyCreateDTO.setEntity(seedlingSubjectDO.getSubjectName());
        applyCreateDTO.setCategory(ApplyCategory.STATIC);
        applyCreateDTO.setDescription("一户一苗码申请");
        CommonResult<ApplyCreateRespVO> applyCreateRespVO = publicApi.createApply(applyCreateDTO);
        log.info("调用码引擎申领结果:{}", applyCreateRespVO);
        if(applyCreateRespVO.getCode() != 0 || applyCreateRespVO.getData().getApplyStatus() != CodeLogStatus.SUCCESS){
            throw new IllegalArgumentException("调用码引擎申领失败:" + applyCreateRespVO.getMsg());
        }
        return applyCreateRespVO;
    }
    private GenerateCreateDTO getGenerateCreateDTO(CommonResult<ApplyCreateRespVO> applyCreateRespVO) {
        GenerateCreateDTO generateCreateDTO = new GenerateCreateDTO();
        generateCreateDTO.setApplication(codeengineApplication);
        generateCreateDTO.setApplyOrderId(applyCreateRespVO.getData().getApplyOrderId());
        generateCreateDTO.setChannel(codeengineChannel);
        generateCreateDTO.setCategory(ApplyCategory.STATIC);
        return generateCreateDTO;
    }

    @NotNull
    private CommonResult<GenerateCreateRespVO> getGenerateCreateRespVOCommonResult(GenerateCreateDTO generateCreateDTO) {
        CommonResult<GenerateCreateRespVO> generateCreateRespVO = publicApi.createGenerate(generateCreateDTO);
        if(generateCreateRespVO.getCode() != 0 || generateCreateRespVO.getData().getGenerateStatus() != CodeLogStatus.SUCCESS){
            throw new IllegalArgumentException("调用码引擎亮码失败:" + generateCreateRespVO.getMsg());
        }
        return generateCreateRespVO;
    }

    private CommonResult<GenerateStatusRespVO> getGenerateStatusRespVOCommonResult(CommonResult<GenerateCreateRespVO> generateCreateRespVO) {
        GenerateStatusDTO generateStatusDTO = new GenerateStatusDTO();
        generateStatusDTO.setApplication(codeengineApplication);
        generateStatusDTO.setGenerateOrderId(generateCreateRespVO.getData().getGenerateOrderId());
        generateStatusDTO.setCategory(ApplyCategory.STATIC);

        log.info("调用码引擎亮码:{}", generateStatusDTO);
        CommonResult<GenerateStatusRespVO> generateStatusRespVO = publicApi.generateStatus(generateStatusDTO);
        log.info("调用码引擎亮码结果:{}", generateStatusRespVO);

        if(generateStatusRespVO.getCode() != 0 || generateStatusRespVO.getData().getGenerateStatus() != CodeLogStatus.SUCCESS){
            throw new IllegalArgumentException("调用码引擎亮码结果失败:" + generateStatusRespVO.getMsg());
        }
        return generateStatusRespVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SeedlingSubjectUpdateDTO updateDTO) {

        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        SeedlingSubjectDO updateObj = SeedlingSubjectConvert.INSTANCE.convert(updateDTO);

        // 方案1：使用UpdateWrapper明确指定要更新的字段
        LambdaUpdateWrapper<SeedlingSubjectDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SeedlingSubjectDO::getId, updateObj.getId())
                .set(SeedlingSubjectDO::getQrCodeUrl, updateObj.getQrCodeUrl())
                .set(SeedlingSubjectDO::getSubjectPictureListJson, updateObj.getSubjectPictureListJson());  // 明确设置字段，即使是null值也会更新
        seedlingSubjectMapper.update(updateObj, updateWrapper);


        // 删除苗木类型与经营主体关系
        seedlingSubjectCategoryMapper.delete(new LambdaQueryWrapper<SeedlingSubjectCategoryDO>()
                .eq(SeedlingSubjectCategoryDO::getSubjectId, updateObj.getId()));

        if (ObjectUtil.isNotEmpty(updateDTO.getSeedlingVarietyIds())) {

            // 插入苗木类型
            for (Long seedlingVarietyId : updateDTO.getSeedlingVarietyIds()) {
                SeedlingSubjectCategoryDO seedlingSubjectCategoryDO = new SeedlingSubjectCategoryDO();
                seedlingSubjectCategoryDO.setSubjectId(updateObj.getId());
                seedlingSubjectCategoryDO.setCategoryId(seedlingVarietyId);
                seedlingSubjectCategoryMapper.insert(seedlingSubjectCategoryDO);
            }
        }

    }


    @Override
    public PageResult<SeedlingSubjectRespVO> getPage(SeedlingSubjectPageDTO pageDTO) {
        PageResult<SeedlingSubjectDO> pageResult = seedlingSubjectMapper.selectPage(pageDTO);
        pageResult.getList().stream().forEach(seedlingSubjectDO -> {
            if (StringUtils.isBlank(seedlingSubjectDO.getLegalName()) || StringUtils.isBlank(seedlingSubjectDO.getSubjectAddress())) {
                try {
                    JSONObject qyzbInfo = getGskResult(String.valueOf(System.currentTimeMillis() / 1000), gskConfig.getQyzb(), "uniscid", seedlingSubjectDO.getUniscid());
                    if (qyzbInfo != null && StringUtils.isBlank(seedlingSubjectDO.getLegalName())) {
                        seedlingSubjectDO.setLegalName(qyzbInfo.getString("NAME"));
                    }
                    if (qyzbInfo != null && StringUtils.isBlank(seedlingSubjectDO.getSubjectAddress())) {
                        seedlingSubjectDO.setSubjectAddress(qyzbInfo.getString("DOM"));
                    }
                    seedlingSubjectMapper.updateById(seedlingSubjectDO);
                } catch (Exception ex) {
                    log.error("获取工商库信息异常", ex);
                }
            }
        });

        PageResult<SeedlingSubjectRespVO> seedlingSubjectRespVOPageResult = SeedlingSubjectConvert.INSTANCE.convertPage(pageResult);
        seedlingSubjectRespVOPageResult.getList().forEach(seedlingSubjectRespVO -> {
            // 收集categoryId
            List<SeedlingCategorySelectVO> categoryList = seedlingCategoryMapper.getSeedlingCategoryIds(seedlingSubjectRespVO.getId());
            List<Long> categoryIds = categoryList.stream()
                    .map(SeedlingCategorySelectVO::getId)
                    .collect(Collectors.toList());
            String categoryLabel = categoryList.stream().map(SeedlingCategorySelectVO::getSeedlingCategory).collect(Collectors.joining("、"));
            seedlingSubjectRespVO.setSeedlingVarietyIds(categoryIds);
            seedlingSubjectRespVO.setSeedlingVariety(categoryLabel);

            if (StringUtils.isNotBlank(seedlingSubjectRespVO.getEvaluationScore())) {
                List<Integer> list = JSON.parseArray(seedlingSubjectRespVO.getEvaluationScore(), Integer.class);
                seedlingSubjectRespVO.setEvaluationScore(String.valueOf(list.stream().filter(Objects::nonNull).mapToInt(Integer::intValue).sum()));
            }
        });
        return seedlingSubjectRespVOPageResult;
    }

    @Override
    public PageResult<SeedlingSubjectMerchantProfileVO> getList(SeedlingSubjectSelectDTO selectDTO) {
        PageResult<SeedlingSubjectDO> pageResult = seedlingSubjectMapper.selectPage(selectDTO,
                new LambdaQueryWrapperX<SeedlingSubjectDO>()
                        .likeIfPresent(SeedlingSubjectDO::getSubjectName, selectDTO.getSubjectName())
                        .eqIfPresent(SeedlingSubjectDO::getCreditLevel, selectDTO.getCreditLevel())
                        .eqIfPresent(SeedlingSubjectDO::getDeleted,0)
                        .apply(selectDTO.getCreditLevel() != null && selectDTO.getCreditLevel().equals("A"),
                                "subject_type <> {0}", SeedlingSubjectSubjectType.Farmer.getCode())
                        .orderByDesc(SeedlingSubjectDO::getId)
        );
        // 判空返回
        if (ObjectUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }
        // 拿到集合seedlingSubjectDOS里边的所有id组成新的集合
        List<Long> subjectIds = pageResult.getList().stream()
                .map(SeedlingSubjectDO::getId)
                .collect(Collectors.toList());
        // 根据中间表seedlingSubjectCategoryMapper 根据SubjectIds 查询关联表 拿到category_id 集合
        List<Long> categoryIds = seedlingSubjectCategoryMapper.selectList(new LambdaQueryWrapperX<SeedlingSubjectCategoryDO>()
                        .in(SeedlingSubjectCategoryDO::getSubjectId, subjectIds))
                .stream().map(SeedlingSubjectCategoryDO::getCategoryId).collect(Collectors.toList());
        // 查询seedlingCategoryMapper
        Map<Long, SeedlingCategoryDO> categoryMap = null;
        if (!CollectionUtils.isEmpty(categoryIds)) {
            List<SeedlingCategoryDO> seedlingCategoryDOS = seedlingCategoryMapper.selectList(new LambdaQueryWrapperX<SeedlingCategoryDO>()
                    .in(SeedlingCategoryDO::getId, categoryIds));
            // 将 seedlingCategoryDOS 转换为 Map 以便快速查找
            categoryMap = seedlingCategoryDOS.stream()
                    .collect(Collectors.toMap(SeedlingCategoryDO::getId, category -> category));
        }

        // 组合seedlingSubjectDOS 和 seedlingCategoryDOS数据
        List<SeedlingSubjectMerchantProfileVO> result = new ArrayList<>();
        for (SeedlingSubjectDO subject : pageResult.getList()) {
            SeedlingSubjectMerchantProfileVO vo = new SeedlingSubjectMerchantProfileVO();
            vo.setId(subject.getId());
            vo.setSubjectName(subject.getSubjectName());
            vo.setSubjectType(subject.getSubjectType());
            vo.setUniscid(subject.getUniscid());
            vo.setLegalName(subject.getLegalName());
            vo.setSubjectAddress(subject.getSubjectAddress());
            vo.setCreditLevel(subject.getCreditLevel());
            vo.setSubjectIntroduction(subject.getSubjectIntroduction());
            vo.setEvaluationScore(subject.getEvaluationScore());
            vo.setEvaluationDate(subject.getEvaluationDate());
            vo.setContactPhone(subject.getContactPhone());
            vo.setSaleChannel(subject.getSaleChannel());
            vo.setQrCodeUrl(subject.getQrCodeUrl());
            // 处理json串 判断非空用hutools
            if (StrUtil.isBlank(subject.getSubjectPictureListJson())) {
                vo.setSubjectPictureListJson("");
            }
            if (StrUtil.isNotBlank(subject.getSubjectPictureListJson()) && JSONUtil.isTypeJSON(subject.getSubjectPictureListJson())) {
                try {
                    JSONArray jsonArray = JSON.parseArray(subject.getSubjectPictureListJson());
                    if (jsonArray != null && !jsonArray.isEmpty()) {
                        // 如果是数组格式且不为空，直接返回原始JSON字符串
                        vo.setSubjectPictureListJson(subject.getSubjectPictureListJson());
                    } else {
                        // 如果是空数组，返回"[]"
                        vo.setSubjectPictureListJson("");
                    }
                } catch (Exception e) {
                    // 如果解析数组失败，尝试解析对象格式
                    try {
                        JSONObject jsonObject = JSON.parseObject(subject.getSubjectPictureListJson());
                        if (jsonObject != null && jsonObject.containsKey(IMAGE_URL_STRING)) {
                            vo.setSubjectPictureListJson(jsonObject.getString(IMAGE_URL_STRING));
                        } else {
                            vo.setSubjectPictureListJson("");
                        }
                    } catch (Exception ex) {
                        // 如果所有解析都失败，返回空数组
                        vo.setSubjectPictureListJson("");
                    }
                }
            } else {
                // 如果输入为空或不是JSON格式，返回空数组
                vo.setSubjectPictureListJson("");
            }
            // 获取关联的 SeedlingCategoryDO
            List<SeedlingSubjectCategoryDO> subjectCategories = seedlingSubjectCategoryMapper
                    .selectList(new LambdaQueryWrapperX<SeedlingSubjectCategoryDO>()
                            .eq(SeedlingSubjectCategoryDO::getSubjectId, subject.getId()));
            if (!subjectCategories.isEmpty()) {
                SeedlingSubjectCategoryDO firstCategory = subjectCategories.get(0);
                if (categoryMap != null) {
                    SeedlingCategoryDO category = categoryMap.get(firstCategory.getCategoryId());
                    if (category!= null) {
                        vo.setSeedlingCategory(category.getSeedlingCategory());
                        vo.setSeedlingVariety(category.getSeedlingVariety());
                        vo.setPriceRange(category.getPieceRange());
                        vo.setSeedlingSpecs(category.getSeedlingSpecs());
                        vo.setSeedlingAdvantages(category.getSeedlingAdvantages());
                    }
                }
            }

            result.add(vo);
        }

        //  处理完数据返回结果
        return new PageResult<>(result, pageResult.getTotal());

    }


    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        seedlingSubjectMapper.deleteById(id);
    }


    @Override
    public SeedlingSubjectRespVO get(Long id) {
        SeedlingSubjectDO seedlingSubjectDO = seedlingSubjectMapper.selectById(id);
        if (seedlingSubjectDO == null) {
            throw exception(SEEDLING_SUBJECT_NOT_EXISTS);
        }

        SeedlingSubjectRespVO seedlingSubjectRespVO = SeedlingSubjectConvert.INSTANCE.convert(seedlingSubjectDO);

        if(!seedlingSubjectRespVO.getSubjectType().equals(SeedlingSubjectSubjectType.Farmer) && StrUtil.isBlank(seedlingSubjectRespVO.getUniscid())){
            throw new ServiceException(-1, "经营主体信用代码为空，查询失败");
        }

        // 收集categoryId - 提前获取本地数据
        List<SeedlingCategorySelectVO> categoryList = seedlingCategoryMapper.getSeedlingCategoryIds(id);
        List<Long> categoryIds = categoryList.stream()
                .map(SeedlingCategorySelectVO::getId)
                .collect(Collectors.toList());
        seedlingSubjectRespVO.setSeedlingVarietyIds(categoryIds);

        // 使用CompletableFuture并行获取外部API数据
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String uniscid = seedlingSubjectRespVO.getUniscid();
        String subjectName = seedlingSubjectRespVO.getSubjectName();

        // 创建并行任务
        CompletableFuture<Void> basicInfoFuture = CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                JSONObject qyzbInfo = getGskResult(timestamp, gskConfig.getQyzb(), "uniscid", uniscid);
                setBasicInfo(seedlingSubjectRespVO, qyzbInfo);
                log.info("获取工商信息成功，耗时：{} ms", System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                log.error("获取工商信息失败，耗时：{} ms，错误：{}", System.currentTimeMillis() - startTime, e.getMessage());
                // 不抛出异常，允许部分数据获取失败
            }
        });

        CompletableFuture<Void> licenseInfoFuture = CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                JSONObject sdsjgxptInfo = getGskResult(timestamp, gskConfig.getSdsjgxpt(), "czzt", subjectName);
                setLicenseInfo(seedlingSubjectRespVO, sdsjgxptInfo, seedlingSubjectDO);
                log.info("获取林木种子生产经营许可证信息成功，耗时：{} ms", System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                log.error("获取林木种子生产经营许可证信息失败，耗时：{} ms，错误：{}", System.currentTimeMillis() - startTime, e.getMessage());
            }
        });

        CompletableFuture<Void> patentInfoFuture = CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                seedlingSubjectRespVO.setPatentInfoVoList(
                        getInfoList(timestamp, gskConfig.getZljbxx(), "uniscid", uniscid, this::convertToPatentInfo, "sqrq"));
                log.info("获取专利信息成功，耗时：{} ms", System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                log.error("获取专利信息失败，耗时：{} ms，错误：{}", System.currentTimeMillis() - startTime, e.getMessage());
                seedlingSubjectRespVO.setPatentInfoVoList(Collections.emptyList());
            }
        });

        CompletableFuture<Void> xzcfInfoFuture = CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                seedlingSubjectRespVO.setXzcfInfoVoList(
                        getInfoList(timestamp, gskConfig.getXzcf(), "uniscid", uniscid, this::convertToXzcfInfo));
                log.info("获取行政处罚信息成功，耗时：{} ms", System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                log.error("获取行政处罚信息失败，耗时：{} ms，错误：{}", System.currentTimeMillis() - startTime, e.getMessage());
                seedlingSubjectRespVO.setXzcfInfoVoList(Collections.emptyList());
            }
        });

        CompletableFuture<Void> yzwfInfoFuture = CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                seedlingSubjectRespVO.setYzwfInfoVoList(
                        getInfoList(timestamp, gskConfig.getYzwfsx(), "uniscid", uniscid, this::convertToYzwfInfo, "indate"));
                log.info("获取严重违法信息成功，耗时：{} ms", System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                log.error("获取严重违法信息失败，耗时：{} ms，错误：{}", System.currentTimeMillis() - startTime, e.getMessage());
                seedlingSubjectRespVO.setYzwfInfoVoList(Collections.emptyList());
            }
        });

        CompletableFuture<Void> sxbzxrInfoFuture = CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                seedlingSubjectRespVO.setSxbzxrInfoVoList(
                        getInfoList(timestamp, gskConfig.getSxbzxr(), "uniscid", uniscid, this::convertToSxbzxrInfo, "FSX_FBDATE"));
                log.info("获取失信被执行人信息成功，耗时：{} ms", System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                log.error("获取失信被执行人信息失败，耗时：{} ms，错误：{}", System.currentTimeMillis() - startTime, e.getMessage());
                seedlingSubjectRespVO.setSxbzxrInfoVoList(Collections.emptyList());
            }
        });

        try {
            // 等待所有任务完成，设置超时时间为5秒
            CompletableFuture.allOf(
                    basicInfoFuture, licenseInfoFuture, patentInfoFuture,
                    xzcfInfoFuture, yzwfInfoFuture, sxbzxrInfoFuture
            ).get();
        } catch (Exception e) {
            log.error("获取外部数据超时或异常", e);
            // 继续返回已获取的数据，不抛出异常中断整个请求
        }

        return seedlingSubjectRespVO;
    }

    private <T> List<T> getInfoList(String timestamp, String url, String param, String requestParam, 
            Function<JSONObject, T> converter, String... sortFields) throws Exception {

        log.info("获取工商库信息-url： {}", url);

        String info = getHttpRequest(timestamp, url, param, requestParam);

        log.info("获取工商库信息-结果： {}", info);

        JSONArray dataArray = Optional.ofNullable(JSON.parseObject(info))
                .map(json -> json.getJSONObject("data"))
                .map(data -> data.getJSONArray("list"))
                .orElse(null);

        if (ObjectUtil.isEmpty(dataArray)) {
            return Collections.emptyList();
        }

        Stream<JSONObject> stream = dataArray.stream()
                .map(item -> (JSONObject) item)
                .filter(ObjectUtil::isNotNull);

        // 如果提供了排序字段，则进行排序
        if (sortFields != null && sortFields.length > 0) {
            stream = stream.sorted((o1, o2) -> {
                // 遍历所有排序字段，按优先级排序
                for (String field : sortFields) {
                    String value1 = o1.getString(field);
                    String value2 = o2.getString(field);

                    if (value1 == null && value2 == null) continue;
                    if (value1 == null) return 1;
                    if (value2 == null) return -1;

                    int result = value2.compareTo(value1); // 倒序排序
                    if (result != 0) return result;
                }
                return 0;
            });
        }

        return stream.map(converter)
                .collect(Collectors.toList());
    }

    private void setBasicInfo(SeedlingSubjectRespVO vo, JSONObject info) {
        if (info != null) {
            vo.setLegalName(info.getString("NAME"));
            vo.setSubjectAddress(info.getString("DOM"));
            String entstatus = info.getString("ENTSTATUS");
            if (StringUtils.isNotBlank(entstatus)) {
                CodeEx02DO codeEx02DO = codeEx02Mapper.selectOne(new LambdaQueryWrapperX<CodeEx02DO>().eq(CodeEx02DO::getCode, entstatus));
                vo.setEntStatus(codeEx02DO != null ? codeEx02DO.getName() : info.getString("ENTSTATUS"));
            }
            if ("个体工商户".equals(vo.getEntStatus())) {
                vo.setRegCap(null);
            }else {
                vo.setRegCap(info.getDouble("REGCAP"));
            }
            vo.setEsDate(info.getString("ESDATE"));
            if (StringUtils.isNotBlank(info.getString("REGORG"))) {
                CodeCa11DO ca11DO = codeCa11Mapper.selectOne(new LambdaQueryWrapperX<CodeCa11DO>().eq(CodeCa11DO::getCode, info.getString("REGORG")));
                vo.setRegOrg(ca11DO != null ? ca11DO.getName() : info.getString("REGORG"));
            }
            vo.setOpScope(info.getString("OPSCOPE"));
        }
    }

    private void setLicenseInfo(SeedlingSubjectRespVO vo, JSONObject info, SeedlingSubjectDO seedlingSubjectDO) {
        if (info != null && StringUtils.isBlank(vo.getXkzh())) {
            vo.setXkzh(info.getString("xkzh"));
        }
        if (info != null && StringUtils.isBlank(vo.getScjyzl())) {
            vo.setScjyzl(info.getString("scjyzl"));
        }
        if (info != null && (ObjectUtil.isEmpty(seedlingSubjectDO.getZzyxqqsrq()) || ObjectUtil.isEmpty(seedlingSubjectDO.getZzyxqqsrq()))) {
            vo.setZzyxqjzrq(info.getString("zzyxqjzrq"));
            vo.setZzyxqqsrq(info.getString("zzyxqqsrq"));
        }else if (ObjectUtil.isNotEmpty(seedlingSubjectDO.getZzyxqqsrq()) && ObjectUtil.isNotEmpty(seedlingSubjectDO.getZzyxqqsrq())) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            dateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));

            // 格式化日期时补全年份为4位数
            Calendar cal = Calendar.getInstance();

            // 处理截止日期
            cal.setTime(seedlingSubjectDO.getZzyxqjzrq());
            if (cal.get(Calendar.YEAR) < 100) {
                cal.set(Calendar.YEAR, cal.get(Calendar.YEAR) + 2000);
            }
            vo.setZzyxqjzrq(dateFormat.format(cal.getTime()));

            // 处理起始日期
            cal.setTime(seedlingSubjectDO.getZzyxqqsrq());
            if (cal.get(Calendar.YEAR) < 100) {
                cal.set(Calendar.YEAR, cal.get(Calendar.YEAR) + 2000);
            }
            vo.setZzyxqqsrq(dateFormat.format(cal.getTime()));
        }else {
            vo.setZzyxqjzrq(null);
            vo.setZzyxqqsrq(null);
        }
    }

    private SeedlingSubjectRespVO.PatentInfoVo convertToPatentInfo(JSONObject info) {
        SeedlingSubjectRespVO.PatentInfoVo vo = new SeedlingSubjectRespVO.PatentInfoVo();
        vo.setPatName(info.getString("patname"));
        vo.setSqrq(info.getString("sqrq"));
        return vo;
    }

    private SeedlingSubjectRespVO.XzcfInfoVo convertToXzcfInfo(JSONObject info) {
        SeedlingSubjectRespVO.XzcfInfoVo vo = new SeedlingSubjectRespVO.XzcfInfoVo();
        vo.setIllegFact(info.getString("ILLEGFACT"));
        vo.setPenType(info.getString("PENTYPE"));
        vo.setPenResult(info.getString("PENRESULT"));
        vo.setPenBasis(info.getString("PENBASIS"));
        vo.setPenDecissDate(info.getString("PENDECISSDATE"));
        vo.setPenAuthName(info.getString("PENAUTHNAME"));
        return vo;
    }

    private SeedlingSubjectRespVO.YzwfInfoVo convertToYzwfInfo(JSONObject info) {
        SeedlingSubjectRespVO.YzwfInfoVo vo = new SeedlingSubjectRespVO.YzwfInfoVo();
        vo.setInDate(info.getString("indate"));
        vo.setInReason(info.getString("inreason"));
        vo.setInOrg(info.getString("inorg"));
        vo.setOutDate(info.getString("outdate"));
        vo.setOutReason(info.getString("outreason"));
        vo.setOutOrg(info.getString("outorg"));
        return vo;
    }

    private SeedlingSubjectRespVO.SxbzxrInfoVo convertToSxbzxrInfo(JSONObject info) {
        SeedlingSubjectRespVO.SxbzxrInfoVo vo = new SeedlingSubjectRespVO.SxbzxrInfoVo();
        vo.setFsxName(info.getString("FSX_NAME"));
        vo.setUniscid(info.getString("UNISCID"));
        vo.setFsxZxfyName(info.getString("FSX_ZXFYNAME"));
        vo.setFsxZxyj(info.getString("FSX_ZXYJ"));
        vo.setFsxLasj(info.getString("FSX_LASJ"));
        vo.setFsxAh(info.getString("FSX_AH"));
        vo.setFsxZczxdw(info.getString("FSX_ZCZXDW"));
        vo.setFsxLxqk(info.getString("FSX_LXQK"));
        vo.setFsxSxjtqx(info.getString("FSX_SXJTQX"));
        vo.setFsxFbDate(info.getString("FSX_FBDATE"));
        return vo;
    }

    @Nullable
    private JSONObject getGskResult(String timestamp, String url, String param, String requestParam) throws NoSuchAlgorithmException, InvalidKeyException, IOException {
        try {
            log.info("获取工商库信息 - URL: {}, 参数: {}", url, requestParam);

            String result = getHttpRequest(timestamp, url, param, requestParam);

            log.info("获取工商库信息 - url: {}, 结果: {}", url, result);

            return Optional.ofNullable(result)
                    .filter(StrUtil::isNotBlank)
                    .map(JSON::parseObject)
                    .map(json -> json.getJSONObject("data"))
                    .map(data -> data.getJSONArray("list"))
                    .filter(ObjectUtil::isNotEmpty)
                    .map(array -> array.getJSONObject(0))
                    .orElse(null);
                    
        } catch (Exception e) {
            log.error("获取工商库信息失败 - URL: {}, 参数: {}, 错误: {}", url, requestParam, e.getMessage());
            throw e;
        }
    }

    @Nullable
    private String getHttpRequest(String timestamp, String url, String param, String requestParam) throws NoSuchAlgorithmException, InvalidKeyException, IOException {
        String signStr = HmacAuthUtil.generateSignStr("POST", url, gskConfig.getAk(), timestamp);
        String signature = HmacAuthUtil.generateHmacSignature(gskConfig.getSk(), signStr);
        Map<String, String> headers = new HashMap<>();
        headers.put("x-hnzx-access-key", gskConfig.getAk());
        headers.put("x-hnzx-timestamp", timestamp);
        headers.put("x-hnzx-signature", signature);
        JSONObject params = new JSONObject();
        params.put(param, requestParam);
        params.put("currentPage", "1");
        params.put("pageSize", "1000");
        return Okhttp3Utils.httpRequest("POST", url, headers, params.toString());
    }

    @Override
    public SeedlingSubjectDO getHome(Long id) {
        SeedlingSubjectDO subjectDO = seedlingSubjectMapper.selectById(id);
        if (StrUtil.isBlank(subjectDO.getSubjectPictureListJson())) {
            subjectDO.setSubjectPictureListJson("");
        }
        if(StrUtil.isNotBlank(subjectDO.getSubjectPictureListJson())) {
            subjectDO.setSubjectPictureListJson(JSON.parseObject(subjectDO.getSubjectPictureListJson()).getString(IMAGE_URL_STRING));
        }

        return subjectDO;
    }

    @Override
    public Long totalCount() {
        return seedlingSubjectMapper.selectCount();
    }
    
    @Override
    public SeedlingCreditStatsVO getCreditLevelStats() {
        SeedlingCreditStatsVO statsVO = new SeedlingCreditStatsVO();
        List<Map<String, Object>> stats = seedlingSubjectMapper.getCreditLevelStats();

        // 设置总数
        Long totalCount = seedlingSubjectMapper.selectCount(new LambdaQueryWrapper<SeedlingSubjectDO>()
                .ne(SeedlingSubjectDO::getSubjectType, SeedlingSubjectSubjectType.Farmer.name()));
        statsVO.setTotalCount(totalCount);
        
        for (Map<String, Object> stat : stats) {
            log.info("stat:{}",stat);
            String level = (String) stat.get("creditLevel");
            if (StringUtils.isEmpty(level)) {
                // 空处理
                level = "未知";
            }
            Long count = (Long) stat.get("count");
            String percentage = (String) stat.get("percentage");
            
            switch (level) {
                case "A":
                    statsVO.setLevelACount(count);
                    statsVO.setLevelAPercent(percentage);
                    break;
                case "B":
                    statsVO.setLevelBCount(count);
                    statsVO.setLevelBPercent(percentage);
                    break;
                case "C":
                    statsVO.setLevelCCount(count);
                    statsVO.setLevelCPercent(percentage);
                    break;
                case "D":
                    statsVO.setLevelDCount(count);
                    statsVO.setLevelDPercent(percentage);
                    break;
            }
        }
        
        return statsVO;
    }

    @Override
    public SeedlingSubjectTypeStatsVO getSubjectTypeStats() {
        SeedlingSubjectTypeStatsVO statsVO = new SeedlingSubjectTypeStatsVO();
        List<Map<String, Object>> stats = seedlingSubjectMapper.getSubjectTypeStats();
        
        // 设置总数
        statsVO.setTotalCount(totalCount());
        
        for (Map<String, Object> stat : stats) {
            String type = (String) stat.get("subjectType");
            if (StringUtils.isEmpty(type)) {
                // 空处理
                type = "Others";
            }
            Long count = (Long) stat.get("count");
            String percentage = (String) stat.get("percentage");
            switch (type) {
                case "Cooperative":
                    statsVO.setCooperativeCount(count);
                    statsVO.setCooperativePercentage(percentage);
                    break;
                case "Farmer":
                    statsVO.setFarmerCount(count);
                    statsVO.setFarmerPercentage(percentage);
                    break;
                case "Enterprise":
                    statsVO.setEnterpriseCount(count);
                    statsVO.setEnterprisePercentage(percentage);
                    break;
                case "IndvBusiness":
                    statsVO.setPlanterCount(count);
                    statsVO.setPlanterPercentage(percentage);
                    break;
            }
        }
        
        return statsVO;
    }

    /**
     * 根据苗木id获取经营主体信息列表
     *
     * @param categoryId 苗木id
     * @return 经营主体信息列表
     */
    @Override
    public PageResult<SeedlingSubjectDO> getSubjectListBySeedlingId(SeedlingSubjectPageDTO pageDTO,Long categoryId) {
        List<Long> subjectIdList = seedlingSubjectCategoryMapper.selectList(new LambdaQueryWrapperX<SeedlingSubjectCategoryDO>()
                        .eq(SeedlingSubjectCategoryDO::getCategoryId, categoryId)
                        .select(SeedlingSubjectCategoryDO::getSubjectId))
                .stream().map(SeedlingSubjectCategoryDO::getSubjectId).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(subjectIdList)) {
            return PageResult.empty();
        }
        // 根据subjectIdList 获取经营主体信息列表
        return seedlingSubjectMapper.selectPage(pageDTO,new LambdaQueryWrapper<SeedlingSubjectDO>()
                .in(SeedlingSubjectDO::getId, subjectIdList)
                .orderByDesc(SeedlingSubjectDO::getEvaluationDate));
    }

    @Override
    public List<SeedlingSubjectDO> getASeedlingSubjectList() {
        return seedlingSubjectMapper.getASeedlingSubjectList();
    }

    @Override
    public List<SeedlingSubjectVO> getSeedlingSubjectInfo() {
        return seedlingSubjectMapper.getSeedlingSubjectInfo();
    }

    @Override
    public SubjectStatisticsVO getSubjectStatistics() {
        return seedlingSubjectMapper.countSubjectStatistics();
    }

    @Override
    public ALevelSubjectStatisticsVO getALevelSubjectStatistics() {
        return seedlingSubjectMapper.getALevelSubjectStatistics();
    }

    @Override
    public ALevelSubjectScanStatisticsVO getALevelSubjectScanStatistics() {

        ALevelSubjectScanStatisticsVO aLevelSubjectScanStatisticsVO = new ALevelSubjectScanStatisticsVO();
        // 查询A级经营主体总量
        LambdaQueryWrapperX<SeedlingSubjectDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(SeedlingSubjectDO::getCreditLevel, CreditLevelA);
        aLevelSubjectScanStatisticsVO.setTotalCount(Math.toIntExact(seedlingSubjectMapper.selectCount(queryWrapper)));
        aLevelSubjectScanStatisticsVO.setFamilyCodeDownload(Math.toIntExact(originViewDownMapper.selectFamilyCodeDownload()));
        aLevelSubjectScanStatisticsVO.setFamilyCodeView(Math.toIntExact(originViewDownMapper.selectFamilyCodeViewCount()));
        aLevelSubjectScanStatisticsVO.setSeedlingCodeDownload(Math.toIntExact(originViewDownMapper.selectSeedlingCodeDownload()));
        aLevelSubjectScanStatisticsVO.setSeedlingCodeView(Math.toIntExact(originViewDownMapper.selectSeedlingCodeViewCount()));
        return aLevelSubjectScanStatisticsVO;
    }

    @Override
    public OriginViewDownDO getViewDownloadCount() {
        return originViewDownMapper.getViewDownloadCount();
    }

    @Override
    public SeedlingSubjectRespVO getByCode(String code) {
        if (StringUtils.isNotBlank(code)){
            LambdaQueryWrapperX<SeedlingSubjectDO> queryWrapperX = new LambdaQueryWrapperX<>();
            queryWrapperX.eq(SeedlingSubjectDO::getCodeNum,code);
            queryWrapperX.last("limit 1");
            log.info("code:{}", code);
            SeedlingSubjectDO seedlingSubjectDO = seedlingSubjectMapper.selectOne(queryWrapperX);
            if (seedlingSubjectDO != null){

                SeedlingSubjectRespVO seedlingSubjectRespVO = SeedlingSubjectConvert.INSTANCE.convert(seedlingSubjectDO);

                if(StrUtil.isBlank(seedlingSubjectRespVO.getUniscid())){
                    throw new ServiceException(-1, "经营主体信用代码为空，查询失败");
                }

                String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
                String uniscid = seedlingSubjectRespVO.getUniscid();
                String subjectName = seedlingSubjectRespVO.getSubjectName();

                try {
                    JSONObject qyzbInfo = getGskResult(timestamp, gskConfig.getQyzb(), "uniscid", uniscid);
                    setBasicInfo(seedlingSubjectRespVO, qyzbInfo);
                } catch (Exception e) {
                    log.error("获取工商信息失败，错误：{}", e.getMessage());
                    // 不抛出异常，允许部分数据获取失败
                }

                try {
                    JSONObject sdsjgxptInfo = getGskResult(timestamp, gskConfig.getSdsjgxpt(), "czzt", subjectName);
                    setLicenseInfo(seedlingSubjectRespVO, sdsjgxptInfo, seedlingSubjectDO);
                } catch (Exception e) {
                    log.error("获取林木种子生产经营许可证信息失败，错误：{}", e.getMessage());
                }

                return seedlingSubjectRespVO;
            }
        }
        return null;
    }

    @Override
    public List<SeedlingCategoryVO> getSeedlingCategory() {

        List<SeedlingCategoryVO> seedlingCategoryVOS = new ArrayList<>();
        List<SeedlingCategoryDTO> seedlingCategoryDTOS = seedlingCategoryMapper.getSeedlingCategory();
        //收集seedlingCategory并去重
        seedlingCategoryDTOS = seedlingCategoryDTOS.stream().distinct().collect(Collectors.toList());
        seedlingCategoryDTOS.forEach(seedlingCategoryDTO -> {
            SeedlingCategoryVO categoryVO = new SeedlingCategoryVO();
            categoryVO.setId(seedlingCategoryDTO.getId());
            categoryVO.setSeedlingCategory(seedlingCategoryDTO.getSeedlingCategory());
            categoryVO.setSeedlingVariety(new ArrayList<>());
            seedlingCategoryVOS.add(categoryVO);
        });
        for (SeedlingCategoryDTO seedlingCategoryDTO : seedlingCategoryDTOS) {
            seedlingCategoryVOS.forEach(seedlingCategoryVO -> {
                if (seedlingCategoryVO.getSeedlingCategory().equals(seedlingCategoryDTO.getSeedlingCategory())){
                    if (StrUtil.isNotBlank(seedlingCategoryDTO.getSeedlingVariety())){
                        SeedlingVarietyVO varietyVO = new SeedlingVarietyVO();
                        varietyVO.setId(seedlingCategoryDTO.getId());
                        varietyVO.setSeedlingVariety(seedlingCategoryDTO.getSeedlingVariety());
                        seedlingCategoryVO.getSeedlingVariety().add(varietyVO);
                    }
                }
            });
        }
        return seedlingCategoryVOS;
    }

    @Override
    public SeedlingScoreLevelVO getScoreAndLevel(Long id, List<Integer> scoreList) {
        SeedlingSubjectDO seedlingSubjectDO = seedlingSubjectMapper.selectById(id);
        if (seedlingSubjectDO == null){
            throw new ServiceException(-1, "经营主体不存在");
        }

        if (CollectionUtils.isEmpty(scoreList)){
            throw new ServiceException(-1, "评分不能为空");
        }

        // 计算总分
        Integer totalScore = scoreList.stream()
                            .filter(Objects::nonNull)  // 过滤掉null值
                            .mapToInt(Integer::intValue)
                            .sum();

        // 计算等级 90分以上得A，80-90得B，60-79得C，60以下得D
        String creditLevel = null;
        if (totalScore >= 90){
            creditLevel = "A";
        }else if (totalScore >= 80){
            creditLevel = "B";
        }else if (totalScore >= 60){
            creditLevel = "C";
        }else if (totalScore < 60){
            creditLevel = "D";
        }

        seedlingSubjectDO.setEvaluationScore(JSON.toJSONString(scoreList));
        seedlingSubjectDO.setCreditLevel(creditLevel);
        seedlingSubjectDO.setEvaluationDate(new Date());

        seedlingSubjectMapper.updateById(seedlingSubjectDO);

        SeedlingScoreLevelVO result = new SeedlingScoreLevelVO();
        result.setScore(totalScore);
        result.setLevel(creditLevel);

        return result;
    }

}

