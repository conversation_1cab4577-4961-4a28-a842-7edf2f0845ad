package com.hainancrc.module.creditcxbs.config;

import com.hainancrc.module.codeengine.api.publicapi.PublicApi;
import com.hainancrc.module.creditreports.creditreportrevision.GenerateCreditReportApi;
import com.hainancrc.module.log.api.operatelog.OperateLogApi;
import com.hainancrc.module.upload.api.upload.UploadFileApi;
// import com.hncrc.openapi.api.CreditBaiShaApi;
import com.hncrc.openapi.api.CreditHklhApi;
import com.hncrc.openapi.api.CreditWuzhishanApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * EnableFeignClients 中引入UploadFileApi.class 允许远程调用
 */
@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = { OperateLogApi.class, UploadFileApi.class, CreditWuzhishanApi.class, CreditHklhApi.class,
        GenerateCreditReportApi.class, PublicApi.class })
public class RpcConfig {
}
