package com.hainancrc.module.creditcxbs.controller.oilteasubsidypublicnote.admin;

import cn.hutool.core.io.file.FileNameUtil;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.framework.excel.core.utils.ExcelUtils;
import com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants;
import com.hainancrc.module.creditcxbs.api.oilteasubsidypublicnote.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidypublicnote.vo.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.dto.OilTeaSubsidySubjectExcelDTO;
import com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.vo.OilTeaSubsidySubjectRespVO;
import com.hainancrc.module.creditcxbs.convert.oilteasubsidypublicnote.*;
import com.hainancrc.module.creditcxbs.convert.oilteasubsidysubject.OilTeaSubsidySubjectConvert;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.oilteasubsidypublicnote.*;
import com.hainancrc.module.creditcxbs.service.oilteasubsidysubject.OilTeaSubsidySubjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Slf4j
@Api(tags = "油茶补贴公示信息")
@RestController
@RequestMapping("/oilteasubsidypublicnote")
@Validated
public class OilTeaSubsidyPublicNoteController {
    
    @Resource
    private OilTeaSubsidyPublicNoteService oilTeaSubsidyPublicNoteService;

    @Resource
    private OilTeaSubsidySubjectService oilTeaSubsidySubjectService;


    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Long> create(@Valid @RequestParam("file") MultipartFile file) {
        OilTeaSubsidyPublicNoteCreateDTO createDTO = new OilTeaSubsidyPublicNoteCreateDTO();
        createDTO.setPublicFileName(FileNameUtil.getPrefix(file.getOriginalFilename()));
        List<OilTeaSubsidySubjectExcelDTO> excelDTOS = null;
        try {
            excelDTOS = ExcelUtils.read(file, OilTeaSubsidySubjectExcelDTO.class);
        } catch (Exception e) {
            log.info("文件读取失败:" + e);
            throw new ServiceException(ErrorCodeConstants.READ_FILE_FAIL);
        }
        return success(oilTeaSubsidyPublicNoteService.create(createDTO, excelDTOS));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody OilTeaSubsidyPublicNoteUpdateDTO updateDTO) {
        oilTeaSubsidyPublicNoteService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<OilTeaSubsidyPublicNoteRespVO>> getList() {
        List<OilTeaSubsidyPublicNoteDO> list = oilTeaSubsidyPublicNoteService.getList();
        return success(OilTeaSubsidyPublicNoteConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<OilTeaSubsidyPublicNoteRespVO>> getPage(@Valid OilTeaSubsidyPublicNotePageDTO pageDTO) {
        PageResult<OilTeaSubsidyPublicNoteDO> pageResult = oilTeaSubsidyPublicNoteService.getPage(pageDTO);
        return success(OilTeaSubsidyPublicNoteConvert.INSTANCE.convertPage(pageResult));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        oilTeaSubsidyPublicNoteService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<OilTeaSubsidyPublicNoteRespVO> get(@RequestParam("id") Long id) {
        OilTeaSubsidyPublicNoteDO oilTeaSubsidyPublicNote = oilTeaSubsidyPublicNoteService.get(id);
        return success(OilTeaSubsidyPublicNoteConvert.INSTANCE.convert(oilTeaSubsidyPublicNote));
    }

    @GetMapping("/template")
    @ApiOperation("获取模板")
    public void getTemplate(HttpServletResponse response) {
        try {
            String fileName = "油茶补贴公示信息模板.xls";
            ExcelUtils.write(response, fileName, "sheet 1", OilTeaSubsidySubjectExcelDTO.class,null);
        } catch (Exception e) {
            throw new ServiceException(ErrorCodeConstants.TEMPLATE_DOWNLOAD_FAIL);
        }
    }

    @GetMapping("/getSubjectList")
    @ApiOperation("获取补贴主体名单")
    public CommonResult<List<OilTeaSubsidySubjectRespVO>> getSubjectList(Long noteId) {
        List<OilTeaSubsidySubjectDO> list = oilTeaSubsidySubjectService.getSubsidySubjectList(noteId);
        return success(OilTeaSubsidySubjectConvert.INSTANCE.convertList(list));
    }



}
