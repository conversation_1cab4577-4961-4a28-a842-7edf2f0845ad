package com.hainancrc.module.creditcxbs.service.teasubject;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.excel.core.utils.ExcelUtils;
import com.hainancrc.module.creditcxbs.api.enums.TeaOriginStatus;
import com.hainancrc.module.creditcxbs.api.enums.TeaSubjectType;
import com.hainancrc.module.creditcxbs.api.teaorigin.vo.TeaOriginRespVO;
import com.hainancrc.module.creditcxbs.api.teasubject.dto.*;
import com.hainancrc.module.creditcxbs.api.teasubject.vo.TeaSubjectDataVO;
import com.hainancrc.module.creditcxbs.api.teasubject.vo.TeaSubjectRespVO;
import com.hainancrc.module.creditcxbs.convert.teaorigin.TeaOriginConvert;
import com.hainancrc.module.creditcxbs.convert.teasubject.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.teaorigincode.TeaOriginCodeMapper;
import com.hainancrc.module.creditcxbs.mapper.teaprogincoderecord.TeaOriginCodeRecordMapper;
import com.hainancrc.module.creditcxbs.mapper.teasubject.*;
import com.hainancrc.module.creditcxbs.api.teasubject.vo.TeaStatisticsRespVO;
import com.hainancrc.module.creditcxbs.mapper.teaorigin.TeaOriginMapper;
import com.hainancrc.module.creditcxbs.api.teasubject.vo.TeaSubjectAnalysisVO;

import com.hainancrc.module.creditcxbs.utils.excel.TeaSubjectExcelListener;
import com.hncrc.common.exception.BusinessException;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.math.BigDecimal;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;

/**
 * Service 实现类
 */
@Service
@Validated
@Slf4j
public class TeaSubjectServiceImpl implements TeaSubjectService {

    @Resource
    private TeaSubjectMapper teaSubjectMapper;

    @Resource
    private TeaOriginMapper teaOriginMapper;

    @Resource
    private TeaOriginCodeMapper teaOriginCodeMapper;

    @Resource
    private TeaOriginCodeRecordMapper teaOriginCodeRecordMapper;

    private void validateExists(Long id) {
        if (teaSubjectMapper.selectById(id) == null) {
            throw exception(TEA_SUBJECT_NOT_EXISTS);
        }
    }

    @Override
    public Long create(TeaSubjectCreateDTO createDTO) {

        // 插入
        TeaSubjectDO teaSubject = TeaSubjectConvert.INSTANCE.convert(createDTO);
        teaSubjectMapper.insert(teaSubject);
        // 返回
        return teaSubject.getId();
    }

    @Override
    public void update(TeaSubjectUpdateDTO updateDTO) {

        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        TeaSubjectDO updateObj = TeaSubjectConvert.INSTANCE.convert(updateDTO);
        teaSubjectMapper.updateById(updateObj);
    }

    @Override
    public PageResult<TeaSubjectDO> getPage(TeaSubjectPageDTO pageDTO) {
        return teaSubjectMapper.selectPage(pageDTO);
    }

    @Override
    public List<TeaSubjectDO> getList() {
        return teaSubjectMapper.selectList();
    }

    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        teaSubjectMapper.deleteById(id);
    }

    @Override
    public TeaSubjectDO get(Long id) {
        return teaSubjectMapper.selectById(id);
    }

    @Override
    public TeaStatisticsRespVO getStatistics() {
        TeaStatisticsRespVO statistics = new TeaStatisticsRespVO();

        // 茶叶经营主体入驻数量
        statistics.setSubjectCount(teaSubjectMapper.selectCount());

        // 茶叶产地个数
        statistics.setOriginCount(teaOriginMapper.selectCount(new LambdaQueryWrapper<TeaOriginDO>()
                .eq(TeaOriginDO::getOriginStatus, TeaOriginStatus.ONLINE)));

        // 产地码申请主体数
        statistics.setOriginCodeApplyCount(teaOriginCodeMapper.selectApplySubjectCount());

        // 申领茶青数量
        statistics.setTeaLeafAmount(teaOriginCodeMapper.selectTeaLeafAmount());

        // 产地码申领数
        statistics.setOriginCodeApplyNum(teaOriginCodeMapper.selectOriginCodeApplyCount());

        // 产地码查看次数
        statistics.setOriginCodeViewCount(teaOriginCodeMapper.selectOriginCodeViewCount());

        return statistics;
    }

    @Override
    public TeaSubjectAnalysisVO getAnalysis() {
        TeaSubjectAnalysisVO analysisVO = new TeaSubjectAnalysisVO();

        // 查询各类型主体数量及占比
        List<Map<String, Object>> typeStats = teaSubjectMapper.selectSubjectTypeCount();

        // 设置各类型数量和占比
        for (Map<String, Object> stat : typeStats) {
            String type = stat.get("subjectType").toString();
            if (StringUtils.isEmpty(type)) {
                // 空处理
                type = "Others";
            }
            Long count = Long.parseLong(stat.get("count").toString());
            String percentage = stat.get("percentage") + "%";

            switch (type) {
                case "Cooperative":
                    analysisVO.setCooperativeCount(count);
                    analysisVO.setCooperativePercentage(percentage);
                    break;
                case "Farmer":
                    analysisVO.setFarmerCount(count);
                    analysisVO.setFarmerPercentage(percentage);
                    break;
                case "Enterprise":
                    analysisVO.setEnterpriseCount(count);
                    analysisVO.setEnterprisePercentage(percentage);
                    break;
                case "IndvBusiness":
                    analysisVO.setPlanterCount(count);
                    analysisVO.setPlanterPercentage(percentage);
                    break;
            }
        }

        return analysisVO;
    }

    @Override
    public List<TeaSubjectDataVO> listByTeaSubjectData(DataOverViewSelectDTO dto) {
        List<TeaSubjectDO> list = teaSubjectMapper.selectList();

        List<TeaSubjectDataVO> resultList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(list)) {
            for (TeaSubjectDO teaSubject : list) {
                TeaSubjectDataVO dataVO = new TeaSubjectDataVO();
                dataVO.setSubjectName(teaSubject.getSubjectName());
                dataVO.setTotalTeaCount(0D);
                dataVO.setTotalViewCount(0L);
                dataVO.setTotalUseCodeCount(0L);
                resultList.add(dataVO);
            }
        }

        List<TeaSubjectDataVO> teaSubjectDataList = teaSubjectMapper.listByTeaSubjectData(dto);
        if (CollectionUtils.isNotEmpty(teaSubjectDataList)) {
            resultList = resultList.stream()
                    .map(teaSubjectDataVO -> {
                        // 在companyDataVOList中查找与当前元素subjectName相同的对象
                        for (TeaSubjectDataVO teaSubjectData : teaSubjectDataList) {
                            if (teaSubjectData.getSubjectName().equals(teaSubjectDataVO.getSubjectName())) {
                                // 更新totalTeaCount、totalViewCount、totalUseCodeCount
                                teaSubjectDataVO.setTotalTeaCount(teaSubjectData.getTotalTeaCount());
                                teaSubjectDataVO.setTotalViewCount(teaSubjectData.getTotalViewCount());
                                teaSubjectDataVO.setTotalUseCodeCount(teaSubjectData.getTotalUseCodeCount());
                                break; // 找到匹配的就跳出循环
                            }
                        }
                        return teaSubjectDataVO; // 返回可能已更新的对象
                    })
                    // 排序：根据totalTeaCount降序
                    .sorted(Comparator.comparing(TeaSubjectDataVO::getTotalTeaCount).reversed())
                    .distinct()
                    .collect(Collectors.toList()); // 收集到一个新的List中
        }

        return resultList;
    }

    @Override
    public List<TeaSubjectDataVO> listByTeaSubjectData2(DataOverViewSelectDTO dto) {
        List<TeaSubjectDataVO> vos = teaSubjectMapper.listByTeaSubjectData2(dto);
        if (CollectionUtils.isEmpty(vos)) {
            return Collections.emptyList();
        }
        return vos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importFile(MultipartFile file) throws IOException {
        if (ObjectUtils.isEmpty(file)) {
            throw new ServiceException(-1, "上传文件为空");
        }


        try {
            // 创建验证器
            Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

            List<TeaSubjectExportListDTO> read = EasyExcel
                    .read(file.getInputStream(), TeaSubjectExportListDTO.class, new TeaSubjectExcelListener())
                    .autoCloseStream(false)
                    .doReadAllSync();

            // 添加对空数据的检查
            if (read == null || read.isEmpty()) {
                throw new ServiceException(-1, "导入的Excel文件没有数据或格式不正确，请确保使用正确的模板并填写数据");
            }

            // 遍历所有数据并进行验证
            for (int i = 0; i < read.size(); i++) {
                TeaSubjectExportListDTO item = read.get(i);
                // 使用验证器验证注解
                Set<ConstraintViolation<TeaSubjectExportListDTO>> violations = validator.validate(item);

                if (!violations.isEmpty()) {
                    // 构建错误信息
                    String errorMsg = violations.stream()
                            .map(ConstraintViolation::getMessage)
                            .collect(Collectors.joining("; "));
                    throw new ServiceException(-1, "第" + (i + 2) + "行数据校验失败: " + errorMsg);
                }

                // 特殊校验：统一社会信用代码必须为18位（对于非农户）
                if (StringUtils.isNotBlank(item.getUniscid()) && !"农户".equals(item.getSubjectType()) && item.getUniscid().length() != 18) {
                    throw new ServiceException(-1, "第" + (i + 2) + "行: 统一社会信用代码必须为18位");
                }

                // 特殊校验：非农户类型的统一社会信用代码不能为空
                if (StringUtils.isBlank(item.getUniscid()) && !"农户".equals(item.getSubjectType())) {
                    throw new ServiceException(-1, "第" + (i + 2) + "行: 主体类型非农户，统一社会信用代码不能为空");
                }
            }

            List<TeaSubjectDO> list = TeaSubjectConvert.INSTANCE.convertImportList(read);
            List<TeaSubjectDO> filterList = list.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator
                                    .comparing(TeaSubjectDO::getSubjectName,
                                            Comparator.nullsLast(Comparator.naturalOrder()))
                                    .thenComparing(TeaSubjectDO::getSubjectType,
                                            Comparator.nullsLast(Comparator.naturalOrder()))
                                    .thenComparing(TeaSubjectDO::getUniscid,
                                            Comparator.nullsLast(Comparator.naturalOrder())))),
                            ArrayList::new));
            saveOrUpdate(filterList);
            return "导入成功";
        } catch (Exception e) {
            if (e instanceof ServiceException) {
                // 直接重新抛出ServiceException，不做额外包装
                throw new ServiceException(-1, e.getMessage());
            } else {
                // 对其他类型的异常进行包装
                throw new ServiceException(-1, "导入失败，模板内容不正确！");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(List<TeaSubjectDO> entityList) {
        if (ObjectUtil.isEmpty(entityList)) {
            return;
        }

        int insertCount = 0;
        int updateCount = 0;
        try {
            for (TeaSubjectDO entity : entityList) {
                TeaSubjectDO validateObj = teaSubjectMapper.selectOne(new LambdaQueryWrapper<TeaSubjectDO>()
                        .eq(TeaSubjectDO::getSubjectName, entity.getSubjectName())
                        .eq(StringUtils.isNotBlank(entity.getUniscid()), TeaSubjectDO::getUniscid, entity.getUniscid()));
                if (ObjectUtil.isNull(validateObj)) {
                    teaSubjectMapper.insert(entity);
                    insertCount++;
                } else {
                    entity.setId(validateObj.getId());
                    teaSubjectMapper.updateById(entity);
                    updateCount++;
                }

            }
        } catch (Exception e) {
            log.error("批量导入茶叶经营主体信息失败 >> ", e);
        }
        log.info("批量导入茶叶经营主体信息成功，新增{}条，更新{}条", insertCount, updateCount);

    }

    @Override
    public List<TeaSubjectRespVO> getTeaSubjectList() {
        // 1. 查询所有非农户类型的主体
        List<TeaSubjectDO> teaSubjects = teaSubjectMapper.selectList(
                new LambdaQueryWrapper<TeaSubjectDO>()
                        .orderByDesc(TeaSubjectDO::getUpdateTime));

        if (CollectionUtils.isEmpty(teaSubjects)) {
            return Collections.emptyList();
        }

        // 2. 获取所有主体ID
        List<Long> subjectIds = teaSubjects.stream()
                .map(TeaSubjectDO::getId)
                .collect(Collectors.toList());

        // 3. 批量查询产地码信息
        List<TeaOriginCodeDO> allOriginCodes = teaOriginCodeMapper.selectList(
                new LambdaQueryWrapper<TeaOriginCodeDO>()
                        .in(TeaOriginCodeDO::getTeaSubjectId, subjectIds));

        // 4. 提取所有产地ID
        Set<Long> originIds = allOriginCodes.stream()
                .map(TeaOriginCodeDO::getTeaOriginId)
                .collect(Collectors.toSet());

        // 5. 批量查询产地信息
        Map<Long, TeaOriginDO> originMap = new HashMap<>();
        if (!originIds.isEmpty()) {
            List<TeaOriginDO> origins = teaOriginMapper.selectList(
                    new LambdaQueryWrapper<TeaOriginDO>()
                            .in(TeaOriginDO::getId, originIds));
            originMap = origins.stream().collect(
                    Collectors.toMap(TeaOriginDO::getId, origin -> origin));
        }

        // 6. 按主体ID组织产地码信息
        Map<Long, List<TeaOriginCodeDO>> subjectOriginCodesMap = allOriginCodes.stream()
                .collect(Collectors.groupingBy(TeaOriginCodeDO::getTeaSubjectId));

        // 7. 转换并设置产地信息
        List<TeaSubjectRespVO> result = new ArrayList<>(teaSubjects.size());
        Map<Long, TeaOriginDO> finalOriginMap = originMap;

        for (TeaSubjectDO subject : teaSubjects) {
            TeaSubjectRespVO vo = TeaSubjectConvert.INSTANCE.convert(subject);
            List<TeaOriginCodeDO> originCodes = subjectOriginCodesMap.get(subject.getId());

            if (CollectionUtils.isNotEmpty(originCodes)) {
                List<String> originNames = originCodes.stream()
                        .map(code -> {
                            TeaOriginDO origin = finalOriginMap.get(code.getTeaOriginId());
                            return origin != null ? origin.getTeaEstate() : "";
                        })
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toList());

                vo.setTeaOrigin(String.join(",", originNames));
            }

            result.add(vo);
        }

        return result;
    }

    @Override
    public TeaOriginRespVO getTeaInfo(String originName, String id) {
        // 通过茶园名称去查
        TeaOriginDO teaOrigin = teaOriginMapper.selectOne(new LambdaQueryWrapper<TeaOriginDO>()
                .eq(TeaOriginDO::getTeaEstate, originName));

        if (ObjectUtil.isNull(teaOrigin)) {
            return null;
        }

        TeaOriginCodeDO teaOriginCodeDO = teaOriginCodeMapper.selectOne(new LambdaQueryWrapper<TeaOriginCodeDO>()
                .eq(TeaOriginCodeDO::getTeaOriginId, teaOrigin.getId())
                .eq(TeaOriginCodeDO::getTeaSubjectId, id)
                .orderByDesc(TeaOriginCodeDO::getUpdateTime)
                .last("limit 1"));

        if (ObjectUtil.isNull(teaOriginCodeDO)) {
            return null;
        }

        TeaOriginDO teaOriginDO = teaOriginMapper.selectById(teaOriginCodeDO.getTeaOriginId());
        if (ObjectUtil.isNull(teaOriginDO)) {
            return null;
        }

        TeaOriginRespVO convert = TeaOriginConvert.INSTANCE.convert(teaOriginDO);

        TeaOriginCodeRecordDO teaOriginCodeRecordDO = teaOriginCodeRecordMapper
                .selectOne(new LambdaQueryWrapper<TeaOriginCodeRecordDO>()
                        .eq(TeaOriginCodeRecordDO::getOriginCodeId, teaOriginCodeDO.getId())
                        .eq(TeaOriginCodeRecordDO::getOriginId, teaOriginCodeDO.getTeaOriginId())
                        .orderByDesc(TeaOriginCodeRecordDO::getUpdateTime)
                        .last("limit 1"));

        if (ObjectUtil.isNotNull(teaOriginCodeRecordDO)) {
            convert.setTeaType(teaOriginCodeRecordDO.getTeaType());
        }

        return convert;
    }

}
