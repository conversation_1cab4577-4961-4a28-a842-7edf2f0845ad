package com.hainancrc.module.creditcxbs.controller.oilteasubject.admin;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.creditcxbs.api.oiltea.vo.OilTeaOverviewVO;
import com.hainancrc.module.creditcxbs.api.oilteasubject.vo.OilTeaSubjectTypeStatVO;
import com.hainancrc.module.creditcxbs.service.oilteasubject.OilTeaSubjectService;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import static com.hainancrc.framework.common.pojo.CommonResult.success;

import javax.annotation.Resource;

@Api(tags = "油茶数据总览")
@RestController
@RequestMapping("oilTea/overview")
@Validated
public class OilTeaOverviewController {

    @Resource
    private OilTeaSubjectService oilTeaSubjectService;

    @GetMapping("/statistics")
    @Operation(summary = "获取油茶数据总览统计")
    public CommonResult<OilTeaOverviewVO> getStatistics() {
        return CommonResult.success(oilTeaSubjectService.getStatistics());
    }

    @GetMapping("/subject-type-stat")
    @Operation(summary = "获取油茶主体分类统计数据")
    public CommonResult<OilTeaSubjectTypeStatVO> getSubjectTypeStat() {
        return success(oilTeaSubjectService.getSubjectTypeStat());
    }

}