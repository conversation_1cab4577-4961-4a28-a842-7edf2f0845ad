package com.hainancrc.module.creditcxbs.service.seedlingsubjectcategory;

import java.util.*;
import javax.validation.*;
import com.hainancrc.module.creditcxbs.api.seedlingsubjectcategory.dto.*;
import com.hainancrc.module.creditcxbs.api.seedlingsubjectcategory.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
*  Service 接口
*
*/
public interface SeedlingSubjectCategoryService {
    
    
    /**
    * 创建
    *
    * @param createDTO 创建信息
    * @return 编号
    */
    Long create(@Valid SeedlingSubjectCategoryCreateDTO createDTO);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid SeedlingSubjectCategoryUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    List<SeedlingSubjectCategoryDO> getList();
    
    /**
    * 获得分页
    *
    * @param pageDTO 分页查询
    * @return 分页
    */
    PageResult<SeedlingSubjectCategoryDO> getPage(SeedlingSubjectCategoryPageDTO pageDTO);
    
    
    /**
    * 删除
    *
    * @param id 主键
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id 主键
    * @return
    */
    SeedlingSubjectCategoryDO get(Long id);
    
    
}

