package com.hainancrc.module.creditcxbs.service.teaorigin;

import java.util.*;
import javax.validation.*;
import com.hainancrc.module.creditcxbs.api.teaorigin.dto.*;
import com.hainancrc.module.creditcxbs.api.teaorigin.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
*  Service 接口
*
*/
public interface TeaOriginService {
    
    
    /**
    * 创建
    *
    * @param createDTO 创建信息
    * @return 编号
    */
    Long create(@Valid TeaOriginCreateDTO createDTO);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid TeaOriginUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    List<TeaOriginDO> getList();
    
    /**
     * 获得分页
     *
     * @param pageDTO 分页查询
     * @return 分页
     */
    PageResult<TeaOriginPageRespVO> getPage(TeaOriginPageDTO pageDTO);
    
    
    /**
    * 删除
    *
    * @param id 主键
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id 主键
    * @return
    */
    TeaOriginDO get(Long id);


    /**
     * 获得申领主体分页
     * @param pageDTO 分页参数
     * @return pageResult
     */
    PageResult<ApplySubjectPageRespVO> getApplyPage(TeaOriginPageDTO pageDTO);

    /**
     * 获得茶叶产地下拉列表
     *
     * @return list
     */
    List<TeaOriginListVO> getOriginList();
}

