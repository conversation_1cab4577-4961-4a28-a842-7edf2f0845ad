package com.hainancrc.module.creditcxbs.entity;

import java.math.BigDecimal;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaEstateEstateStatus;

/**
 * 油茶茶园信息 DO
 */
@TableName("cxbs_oil_tea_estate")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OilTeaEstateDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 茶园名称(列表,新增,编辑)
     */
    private String estateName;

    /**
     * 种植品种(列表,新增,编辑)
     */
    private String plantType;

    /**
     * 油茶园面积(亩)(列表,新增,编辑)
     */
    private BigDecimal estateArea;

    /**
     * 联系电话(列表,新增,编辑)
     */
    private String contactPhone;

    /**
     * 油茶园地址(列表,新增,编辑)
     */
    private String estateAddress;

    /**
     * 油茶茶园简介
     */
    private String estateIntroduction;

    /**
     * 状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)ONLINE=使用中,OFFLINE=已下线
     */
    private OilTeaEstateEstateStatus estateStatus;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 茶园图片(列表,新增,编辑)
     */
    private String estatePictureListJson;

}
