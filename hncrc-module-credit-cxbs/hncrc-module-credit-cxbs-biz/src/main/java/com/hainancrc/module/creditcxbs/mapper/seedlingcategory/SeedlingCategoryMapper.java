package com.hainancrc.module.creditcxbs.mapper.seedlingcategory;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.vo.SeedlingCategorySelectVO;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.dto.SeedlingCategoryDTO;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.dto.*;

import org.apache.ibatis.annotations.*;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

import static com.hainancrc.module.creditcxbs.api.enums.OilTeaPolicyStatus.ONLINE;

@Mapper
public interface SeedlingCategoryMapper extends BaseMapperX<SeedlingCategoryDO> {

    // PageResult<SeedlingCategoryDO> selectPage(@Param("dto")
    // SeedlingCategoryPageDTO reqDTO);

    default PageResult<SeedlingCategoryDO> selectPage(SeedlingCategoryPageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<SeedlingCategoryDO>()
                .likeIfPresent(SeedlingCategoryDO::getSeedlingVariety, reqDTO.getSeedlingVariety())
                .likeIfPresent(SeedlingCategoryDO::getSeedlingCategory, reqDTO.getSeedlingCategory())
                .inIfPresent(SeedlingCategoryDO::getId, reqDTO.getCategoryIds())
                .eq(SeedlingCategoryDO::getDeleted, 0)
                .orderByDesc(SeedlingCategoryDO::getCreateTime));
    }

    default PageResult<SeedlingCategoryDO> selectPageHome(SeedlingCategoryPageDTO reqDTO) {

        if (!Objects.isNull(reqDTO.getSubjectId()) && CollectionUtils.isEmpty(reqDTO.getCategoryIds())) {
            return PageResult.empty();
        }

        return selectPage(reqDTO, new LambdaQueryWrapperX<SeedlingCategoryDO>()
                .likeIfPresent(SeedlingCategoryDO::getSeedlingVariety, reqDTO.getSeedlingVariety())
                .likeIfPresent(SeedlingCategoryDO::getSeedlingCategory, reqDTO.getSeedlingCategory())
                .inIfPresent(SeedlingCategoryDO::getId, reqDTO.getCategoryIds())
                .eq(SeedlingCategoryDO::getSeedlingStatus, ONLINE)
                .eq(SeedlingCategoryDO::getDeleted, 0)
                // 优先使用sortOrder 升序排序（没有值的默认排在最后）
                .orderByAsc(SeedlingCategoryDO::getSortOrder)
                .orderByDesc(SeedlingCategoryDO::getCreateTime));
    }

    List<String> selectVarietyList();

    List<String> selectCategoryList();

    List<SeedlingCategoryDTO> getSeedlingCategory();

    List<SeedlingCategorySelectVO> getSeedlingCategoryIds(@Param("subjectId") Long subjectId);
}
