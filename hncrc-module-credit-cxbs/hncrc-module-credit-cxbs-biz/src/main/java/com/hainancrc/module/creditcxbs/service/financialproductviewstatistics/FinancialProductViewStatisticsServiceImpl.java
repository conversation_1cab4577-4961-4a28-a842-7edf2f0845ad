package com.hainancrc.module.creditcxbs.service.financialproductviewstatistics;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.framework.redis.service.RedisService;
import com.hainancrc.framework.security.SystemLoginUser;
import com.hainancrc.framework.security.core.util.TokenUtils;
import com.hainancrc.module.creditcxbs.api.enums.FinancialStatisticsType;
import com.hainancrc.module.creditcxbs.entity.FinancialProductViewStatisticsDO;
import com.hainancrc.module.creditcxbs.mapper.financialproductviewstatistics.FinancialProductViewStatisticsMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class FinancialProductViewStatisticsServiceImpl implements FinancialProductViewStatisticsService {

    @Resource
    private RedisService redisService;

    @Resource
    private FinancialProductViewStatisticsMapper financialProductViewStatisticsMapper;

    @Override
    public void updateStatistics() {
        financialProductViewStatisticsMapper.update(null, new LambdaUpdateWrapper<FinancialProductViewStatisticsDO>()
                .eq(FinancialProductViewStatisticsDO::getStatisticsType, FinancialStatisticsType.Views.name())
                .setSql("statistics_count = statistics_count + 1"));

        SystemLoginUser sysLoginUser = TokenUtils.getSysLoginUser(redisService);
        if (ObjectUtil.isNotEmpty(sysLoginUser) && ObjectUtil.isNotEmpty(sysLoginUser.getUserAccount())) {
            financialProductViewStatisticsMapper.update(null, new LambdaUpdateWrapper<FinancialProductViewStatisticsDO>()
                    .eq(FinancialProductViewStatisticsDO::getStatisticsType, FinancialStatisticsType.ViewSubject.name())
                    .setSql("statistics_count = statistics_count + 1"));
        }

    }
}
