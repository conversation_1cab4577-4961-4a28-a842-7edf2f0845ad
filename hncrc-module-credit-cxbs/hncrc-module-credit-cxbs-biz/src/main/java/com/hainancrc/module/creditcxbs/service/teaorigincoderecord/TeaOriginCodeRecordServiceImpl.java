package com.hainancrc.module.creditcxbs.service.teaorigincoderecord;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.framework.redis.service.RedisService;
import com.hainancrc.framework.security.SystemLoginUser;
import com.hainancrc.framework.security.core.util.TokenUtils;
import com.hainancrc.module.codeengine.api.publicapi.PublicApi;
import com.hainancrc.module.codeengine.api.publicapi.dto.ApplyCreateDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.GenerateCreateDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.GenerateStatusDTO;
import com.hainancrc.module.codeengine.api.publicapi.vo.ApplyCreateRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.GenerateCreateRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.GenerateStatusRespVO;
import com.hainancrc.module.codeengine.enums.ApplyCategory;
import com.hainancrc.module.codeengine.enums.CodeLogStatus;
import com.hainancrc.module.creditcxbs.api.enums.TeaOriginCodeStatus;
import com.hainancrc.module.creditcxbs.api.teaorigincodehistory.vo.TeaOriginCodeHistoryRespVO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.dto.TeaOriginCodeRecordCreateDTO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.dto.TeaOriginCodeRecordPageDTO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.dto.TeaOriginCodeRecordUpdateDTO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordPageRespVO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordDetailRespVO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordRespVO;
import com.hainancrc.module.creditcxbs.convert.teaorigincodehistory.TeaOriginCodeHistoryConvert;
import com.hainancrc.module.creditcxbs.convert.teaorigincoderecord.TeaOriginCodeRecordConvert;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.originsubjectuser.OriginSubjectUserMapper;
import com.hainancrc.module.creditcxbs.mapper.teaorigincode.TeaOriginCodeMapper;
import com.hainancrc.module.creditcxbs.mapper.teaprogincoderecord.TeaOriginCodeRecordMapper;
import com.hainancrc.module.creditcxbs.mapper.teasubject.TeaSubjectMapper;
import com.hainancrc.module.creditcxbs.service.teaorigincodehistory.TeaOriginCodeHistoryService;
import com.hainancrc.module.creditcxbs.service.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.TEA_ORIGIN_CODE_HISTORY_NOT_EXISTS;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.TEA_ORIGIN_CODE_NOT_EXISTS;

/**
 *  Service 实现类
 *
 */
@Slf4j
@Service
@Validated
public class TeaOriginCodeRecordServiceImpl implements TeaOriginCodeRecordService {

    @Resource
    private PublicApi publicApi;

    @Value("${codeengine.application}")
    private String codeengineApplication;

    @Value("${codeengine.channel}")
    private String codeengineChannel;

    @Value("${codeengine.origin-code-scene}")
    private String codeengineOriginCodeScene;

    @Resource
    private UserService userService;

    @Resource
    private RedisService redisService;

    @Resource
    private TeaSubjectMapper teaSubjectMapper;

    @Resource
    private TeaOriginCodeMapper teaOriginCodeMapper;

    @Resource
    private TeaOriginCodeRecordMapper teaOriginCodeRecordMapper;

    @Resource
    private TeaOriginCodeHistoryService teaOriginCodeHistoryService;

    @Resource
    private OriginSubjectUserMapper originSubjectUserMapper;


    private void validateExists(Long id) {
        if (teaOriginCodeRecordMapper.selectById(id) == null) {
            throw exception(TEA_ORIGIN_CODE_HISTORY_NOT_EXISTS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TeaOriginCodeRecordCreateDTO createDTO) {

        SystemLoginUser sysLoginUser = getTokenSysLoginUser();
        if(ObjectUtil.isNull(sysLoginUser)){
            throw exception(-1, "系统用户不存在");
        }

        TeaSubjectDO teaSubjectDO = getOriginSubject(sysLoginUser.getId());
        if (Objects.isNull(teaSubjectDO)) {
            throw new ServiceException(-1, "当前用户未绑定产地主体");
        }

        boolean exists = teaOriginCodeRecordMapper.exists(new LambdaQueryWrapperX<TeaOriginCodeRecordDO>()
                .eq(TeaOriginCodeRecordDO::getOriginId, createDTO.getOriginId())
                .eq(TeaOriginCodeRecordDO::getTeaType, createDTO.getTeaType()));
        if (exists) {
            throw new ServiceException(-1, "已存在该产地码申领，您可选择继续申报");
        }

        // 插入
        TeaOriginCodeRecordDO teaOriginCodeRecord = TeaOriginCodeRecordConvert.INSTANCE.convert(createDTO);

        TeaOriginCodeDO teaOriginCodeDO = newOriginCodeDO(createDTO, teaSubjectDO);
        if (ObjectUtil.isNotEmpty(teaOriginCodeDO)) {
            teaOriginCodeRecord.setOriginCodeId(teaOriginCodeDO.getId());
        }
        if (createDTO.getPromiseFileKey() != null) {
            JSONObject promiseFile = JSON.parseObject(createDTO.getPromiseFileKey());
            teaOriginCodeRecord.setPromiseFileKey(promiseFile.getString("key"));
            teaOriginCodeRecord.setPromiseFileUrl(promiseFile.getString("url"));
        }
        teaOriginCodeRecordMapper.insert(teaOriginCodeRecord);

        // 产品规格说明
        teaOriginCodeHistoryService.saveBatch(teaOriginCodeRecord.getId(), createDTO.getTeaOriginCodeHistoryList());

        return teaOriginCodeRecord.getId();
    }

    private TeaSubjectDO getOriginSubject(Long id) {
        Long teaSubjectId = userService.getTeaSubjectId(String.valueOf(id));
        return teaSubjectMapper.selectById(teaSubjectId);
    }

    @Override
    public Long apply(TeaOriginCodeRecordCreateDTO createDTO) {

        SystemLoginUser sysLoginUser = getTokenSysLoginUser();
        if(ObjectUtil.isNull(sysLoginUser)){
            throw exception(-1, "系统用户不存在");
        }

        TeaOriginCodeDO teaOriginCodeDO = teaOriginCodeMapper.selectById(createDTO.getOriginCodeId());
        if (ObjectUtil.isEmpty(teaOriginCodeDO)) {
            throw exception(TEA_ORIGIN_CODE_NOT_EXISTS);
        }

        if (teaOriginCodeDO.getCodeStatus().equals(TeaOriginCodeStatus.OFFLINE.name())) {
            throw new ServiceException(-1, "该产地码下架");
        }

        //产地码承诺书修改
        if (createDTO.getPromiseFileKey() != null) {
            JSONObject promiseFile = JSON.parseObject(createDTO.getPromiseFileKey());
            teaOriginCodeDO.setPromiseFileKey(promiseFile.getString("key"));
            teaOriginCodeDO.setPromiseFileUrl(promiseFile.getString("url"));
        }
        teaOriginCodeDO.setUpdateTime(new Date());
        teaOriginCodeMapper.updateById(teaOriginCodeDO);

        // 插入
        TeaOriginCodeRecordDO teaOriginCodeRecord = TeaOriginCodeRecordConvert.INSTANCE.convert(createDTO);
        teaOriginCodeRecord.setOriginId(teaOriginCodeDO.getTeaOriginId());
        if (ObjectUtil.isNotEmpty(createDTO.getPromiseFileKey())) {
            JSONObject promiseFile = JSON.parseObject(createDTO.getPromiseFileKey());
            teaOriginCodeRecord.setPromiseFileKey(promiseFile.getString("key"));
            teaOriginCodeRecord.setPromiseFileUrl(promiseFile.getString("url"));
        }
        teaOriginCodeRecordMapper.insert(teaOriginCodeRecord);

        // 产品规格说明
        teaOriginCodeHistoryService.saveBatch(teaOriginCodeRecord.getId(), createDTO.getTeaOriginCodeHistoryList());

        return teaOriginCodeRecord.getId();
    }

    @Override
    public void update(TeaOriginCodeRecordUpdateDTO updateDTO) {
        // 校验存在
        this.validateExists(updateDTO.getId());

        TeaOriginCodeRecordDO updateObj = TeaOriginCodeRecordConvert.INSTANCE.convert(updateDTO);
        teaOriginCodeRecordMapper.updateById(updateObj);
    }

    @Override
    public List<TeaOriginCodeRecordDO> getList() {
        return teaOriginCodeRecordMapper.selectList();
    }

    @Override
    public PageResult<TeaOriginCodeRecordPageRespVO> getPage(TeaOriginCodeRecordPageDTO pageDTO) {
        SystemLoginUser sysLoginUser = TokenUtils.getSysLoginUser(redisService);
        if(ObjectUtil.isNull(sysLoginUser)){
            throw new ServiceException(-1, "系统用户不存在");
        }

        if (!"admin".equals(sysLoginUser.getUserAccount())) {
            OriginSubjectUserDO originSubjectUserDO = originSubjectUserMapper.selectOne(new LambdaQueryWrapperX<OriginSubjectUserDO>()
                    .eq(OriginSubjectUserDO::getUserId, sysLoginUser.getId()));
            if (ObjectUtil.isNull(originSubjectUserDO)) {
                throw new ServiceException(-1, "当前用户未绑定产地主体");
            }

            TeaSubjectDO teaSubjectDO = teaSubjectMapper.selectById(originSubjectUserDO.getTeaSubjectId());
            if (Objects.isNull(teaSubjectDO)) {
                throw new ServiceException(-1, "产地主体不存在");
            }

            pageDTO.setTeaSubjectId(teaSubjectDO.getId());
        }


        Page<TeaOriginCodeRecordPageRespVO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        IPage<TeaOriginCodeRecordPageRespVO> pageResult = teaOriginCodeRecordMapper.selectRecordPage(page, pageDTO);
        for (TeaOriginCodeRecordPageRespVO record : pageResult.getRecords()) {
            TeaOriginCodeDO teaOriginCodeDO = teaOriginCodeMapper.selectById(record.getCodeId());
            if (teaOriginCodeDO != null) {
                record.setViewCount(teaOriginCodeDO.getViewCount() == null ? 0 : Long.valueOf(teaOriginCodeDO.getViewCount()));
            }
        }
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public PageResult<TeaOriginCodeRecordDetailRespVO> getRecordPage(TeaOriginCodeRecordPageDTO pageDTO) {
        Page<TeaOriginCodeRecordDetailRespVO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        IPage<TeaOriginCodeRecordDetailRespVO> pageResult = teaOriginCodeRecordMapper.selectApplyRecordPage(page, pageDTO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        teaOriginCodeRecordMapper.deleteById(id);
    }

    @Override
    public TeaOriginCodeRecordRespVO get(Long id) {
        TeaOriginCodeRecordRespVO respVO = teaOriginCodeRecordMapper.selectByRecordId(id);

        if (ObjectUtil.isNotEmpty(respVO)) {
            List<TeaOriginCodeHistoryDO> historyList = teaOriginCodeHistoryService.getListByRecordId(respVO.getId());
            List<TeaOriginCodeHistoryRespVO> historyRespList = TeaOriginCodeHistoryConvert.INSTANCE.convertList(historyList);

            respVO.setTeaOriginCodeHistoryList(historyRespList);
        }

        return respVO;
    }


    private TeaOriginCodeDO newOriginCodeDO(TeaOriginCodeRecordCreateDTO createDTO, TeaSubjectDO teaSubjectDO) {

        // 申码
        // 拿出产地主体对应的信息
        CommonResult<ApplyCreateRespVO> applyCreateRespVO = getApplyCreateRespVOCommonResult(teaSubjectDO);

        // 亮码请求
        GenerateCreateDTO generateCreateDTO = getGenerateCreateDTO(applyCreateRespVO);
        log.info("调用码引擎亮码请求:{}", generateCreateDTO);

        // 调用码引擎亮码
        CommonResult<GenerateCreateRespVO> generateCreateRespVO = getGenerateCreateRespVOCommonResult(generateCreateDTO);

        // 亮码结果
        CommonResult<GenerateStatusRespVO> generateStatusRespVO = getGenerateStatusRespVOCommonResult(generateCreateRespVO);

        // 新增产地码
        return getTeaOriginCodeDO(createDTO, teaSubjectDO, generateStatusRespVO);
    }

    private CommonResult<ApplyCreateRespVO> getApplyCreateRespVOCommonResult(TeaSubjectDO teaSubjectDO) {
        ApplyCreateDTO applyCreateDTO = new ApplyCreateDTO();
        applyCreateDTO.setApplication(codeengineApplication);
        applyCreateDTO.setChannel(codeengineChannel);
        applyCreateDTO.setScene(codeengineOriginCodeScene);
        applyCreateDTO.setEntityId(teaSubjectDO.getUniscid());
        applyCreateDTO.setEntity(teaSubjectDO.getSubjectName());
        applyCreateDTO.setCategory(ApplyCategory.STATIC);
        applyCreateDTO.setDescription("产地码申请");
        CommonResult<ApplyCreateRespVO> applyCreateRespVO = publicApi.createApply(applyCreateDTO);
        log.info("调用码引擎申领结果:{}", applyCreateRespVO);
        if(applyCreateRespVO.getCode() != 0 || applyCreateRespVO.getData().getApplyStatus() != CodeLogStatus.SUCCESS){
            throw new IllegalArgumentException("调用码引擎申领失败:" + applyCreateRespVO.getMsg());
        }
        return applyCreateRespVO;
    }

    private GenerateCreateDTO getGenerateCreateDTO(CommonResult<ApplyCreateRespVO> applyCreateRespVO) {
        GenerateCreateDTO generateCreateDTO = new GenerateCreateDTO();
        generateCreateDTO.setApplication(codeengineApplication);
        generateCreateDTO.setApplyOrderId(applyCreateRespVO.getData().getApplyOrderId());
        generateCreateDTO.setChannel(codeengineChannel);
        generateCreateDTO.setCategory(ApplyCategory.STATIC);
        return generateCreateDTO;
    }

    private CommonResult<GenerateCreateRespVO> getGenerateCreateRespVOCommonResult(GenerateCreateDTO generateCreateDTO) {
        CommonResult<GenerateCreateRespVO> generateCreateRespVO = publicApi.createGenerate(generateCreateDTO);
        if(generateCreateRespVO.getCode() != 0 || generateCreateRespVO.getData().getGenerateStatus() != CodeLogStatus.SUCCESS){
            throw new IllegalArgumentException("调用码引擎亮码失败:" + generateCreateRespVO.getMsg());
        }
        return generateCreateRespVO;
    }

    private CommonResult<GenerateStatusRespVO> getGenerateStatusRespVOCommonResult(CommonResult<GenerateCreateRespVO> generateCreateRespVO) {
        GenerateStatusDTO generateStatusDTO = new GenerateStatusDTO();
        generateStatusDTO.setApplication(codeengineApplication);
        generateStatusDTO.setGenerateOrderId(generateCreateRespVO.getData().getGenerateOrderId());
        generateStatusDTO.setCategory(ApplyCategory.STATIC);

        log.info("调用码引擎亮码:{}", generateStatusDTO);
        CommonResult<GenerateStatusRespVO> generateStatusRespVO = publicApi.generateStatus(generateStatusDTO);
        log.info("调用码引擎亮码结果:{}", generateStatusRespVO);

        if(generateStatusRespVO.getCode() != 0 || generateStatusRespVO.getData().getGenerateStatus() != CodeLogStatus.SUCCESS){
            throw new IllegalArgumentException("调用码引擎亮码结果失败:" + generateStatusRespVO.getMsg());
        }
        return generateStatusRespVO;
    }

    private TeaOriginCodeDO getTeaOriginCodeDO(TeaOriginCodeRecordCreateDTO createDTO, TeaSubjectDO teaSubjectDO, CommonResult<GenerateStatusRespVO> generateStatusRespVO) {
        // 新增产地码
        TeaOriginCodeDO teaOriginCode = new TeaOriginCodeDO();
        teaOriginCode.setTeaOriginId(createDTO.getOriginId());
        String codeUrl = generateStatusRespVO.getData().getCodeUrl();
        teaOriginCode.setCode(StringEscapeUtils.unescapeHtml(codeUrl));
        // 截取code=后面的值提供给前端查询
        String param = "code=";
        int start = codeUrl.indexOf(param) + param.length();
        int end = codeUrl.indexOf("&", start);
        String result = codeUrl.substring(start, end);
        teaOriginCode.setCodeNum(result);
        teaOriginCode.setTeaSubjectId(teaSubjectDO.getId());

        //新增产地码申领承诺书
        if (createDTO.getPromiseFileKey() != null) {
            JSONObject promiseFile = JSON.parseObject(createDTO.getPromiseFileKey());
            teaOriginCode.setPromiseFileKey(promiseFile.getString("key"));
            teaOriginCode.setPromiseFileUrl(promiseFile.getString("url"));
        }
        teaOriginCode.setDeleted(false);
        teaOriginCode.setCodeStatus(TeaOriginCodeStatus.ONLINE.name());
        teaOriginCode.setExpireTime(DateUtil.offset(new Date(), DateField.YEAR, 2));

        teaOriginCodeMapper.insert(teaOriginCode);
        return teaOriginCode;
    }

    private SystemLoginUser getTokenSysLoginUser() {
        return TokenUtils.getSysLoginUser(redisService);
    }
}
