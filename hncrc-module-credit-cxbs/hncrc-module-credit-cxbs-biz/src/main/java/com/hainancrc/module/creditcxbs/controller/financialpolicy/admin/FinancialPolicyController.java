package com.hainancrc.module.creditcxbs.controller.financialpolicy.admin;

import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.module.creditcxbs.api.financialpolicy.dto.*;
import com.hainancrc.module.creditcxbs.api.financialpolicy.vo.*;
import com.hainancrc.module.creditcxbs.convert.financialpolicy.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.financialpolicy.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "金融政策信息")
@RestController
@RequestMapping("/financialPolicy")
@Validated
public class FinancialPolicyController {
    
    @Resource
    private FinancialPolicyService financialPolicyService;


    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Long> create(@Valid @RequestBody FinancialPolicyCreateDTO createDTO) {
        return success(financialPolicyService.create(createDTO));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody FinancialPolicyUpdateDTO updateDTO) {
        financialPolicyService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<FinancialPolicyRespVO>> getList() {
        List<FinancialPolicyDO> list = financialPolicyService.getList();
        return success(FinancialPolicyConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<FinancialPolicyRespVO>> getPage(@Valid FinancialPolicyPageDTO pageDTO) {
        PageResult<FinancialPolicyDO> pageResult = financialPolicyService.getPage(pageDTO);
        return success(FinancialPolicyConvert.INSTANCE.convertPage(pageResult));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        financialPolicyService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<FinancialPolicyRespVO> get(@RequestParam("id") Long id) {
        FinancialPolicyDO financialPolicy = financialPolicyService.get(id);
        return success(FinancialPolicyConvert.INSTANCE.convert(financialPolicy));
    }

    @GetMapping("/getListByPolicyName")
    @ApiOperation("根据政策名称获取列表")
    public CommonResult<List<FinancialPolicyRespVO>> getListByPolicyName(@RequestParam("policyName") String policyName) {
        List<FinancialPolicyDO> list = financialPolicyService.getListByPolicyName(policyName);
        return success(FinancialPolicyConvert.INSTANCE.convertList(list));
    }


}
