package com.hainancrc.module.creditcxbs.controller.oilteasubject.admin;

import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.framework.excel.core.utils.ExcelUtils;
import com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants;
import com.hainancrc.module.creditcxbs.api.oilteasubject.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteasubject.vo.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.dto.OilTeaSubsidySubjectExcelDTO;
import com.hainancrc.module.creditcxbs.convert.oilteasubject.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.oilteasubject.*;
import com.hainancrc.module.creditcxbs.utils.ExcelKits;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "油茶经营主体信息")
@RestController
@RequestMapping("/oilTeaSubject")
@Validated
@Slf4j
public class OilTeaSubjectController {
    
    @javax.annotation.Resource
    private OilTeaSubjectService oilTeaSubjectService;


    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Long> create(@Valid @RequestBody OilTeaSubjectCreateDTO createDTO) {
        return success(oilTeaSubjectService.create(createDTO));
    }

    @PostMapping("/importFile")
    @ApiOperation("导入油茶经营主体")
    public CommonResult<String> importFile(@ApiParam(value = "文件流", required = true) @RequestPart("file") MultipartFile file) throws IOException {
        return success(oilTeaSubjectService.importFile(file));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody OilTeaSubjectUpdateDTO updateDTO) {
        oilTeaSubjectService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<OilTeaSubjectRespVO>> getList() {
        List<OilTeaSubjectDO> list = oilTeaSubjectService.getList();
        return success(OilTeaSubjectConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<OilTeaSubjectRespVO>> getPage(@Valid OilTeaSubjectPageDTO pageDTO) {
        PageResult<OilTeaSubjectDO> pageResult = oilTeaSubjectService.getPage(pageDTO);
        return success(OilTeaSubjectConvert.INSTANCE.convertPage(pageResult));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        oilTeaSubjectService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<OilTeaSubjectRespVO> get(@RequestParam("id") Long id) {
        OilTeaSubjectDO oilTeaSubject = oilTeaSubjectService.get(id);
        return success(OilTeaSubjectConvert.INSTANCE.convert(oilTeaSubject));
    }

    @GetMapping("/template")
    @ApiOperation("获取模板")
    public void getTemplate(HttpServletResponse response) {
        ExcelKits.getTemplate("templates/oilTea.xlsx",response, "导入油茶经营主体模板");
    }
}
