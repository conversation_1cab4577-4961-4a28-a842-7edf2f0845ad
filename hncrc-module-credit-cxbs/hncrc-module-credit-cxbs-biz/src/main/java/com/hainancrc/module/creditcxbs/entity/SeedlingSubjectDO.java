package com.hainancrc.module.creditcxbs.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import com.hainancrc.module.creditcxbs.api.enums.SeedlingSubjectSubjectType;

/**
 * 苗木经营主体信息 DO
 */
@TableName("cxbs_seedling_subject")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SeedlingSubjectDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 商户名称(列表)
     */
    private String subjectName;

    /**
     * 主体类型(列表)(ENUMS: Cooperative-合作社, Grower-种植户, Enterprise-企业,
     * Others-其它)Cooperative=合作社,Grower=种植户,Enterprise=企业
     */
    private SeedlingSubjectSubjectType subjectType;

    /**
     * 统一信用代码(列表)
     */
    private String uniscid;

    /**
     * 法定代表人(列表)
     */
    private String legalName;

    /**
     * 地址(列表)
     */
    private String subjectAddress;

    /**
     * 联系电话(列表)
     */
    private String contactPhone;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 是否按规定建立经营档案
     */
    private Integer hasStandardArchive;

    /**
     * 档案内容是否完整及时
     */
    private Integer hasArchiveComplete;

    /**
     * 种苗是否已检疫
     */
    private Integer hasSeedQuarantine;

    /**
     * 种苗是否已检验
     */
    private Integer hasSeedInspection;

    /**
     * 包装是否有标签或使用说明
     */
    private Integer hasPackageStandard;

    /**
     * 包装标签或使用说明是否规范完整
     */
    private Integer hasPackageComplete;

    /**
     * 是否超范围营业（种子生产经营许可）
     */
    private Integer hasBeyondScope;

    /**
     * 经营主体照片
     */
    private String subjectPictureListJson;

    /**
     * 经营主体简介
     */
    private String subjectIntroduction;

    /**
     * 信用等级(列表)
     */
    private String creditLevel;

    /**
     * 评价得分(列表)
     */
    private String evaluationScore;

    /**
     * 评价日期(列表)
     */
    private Date evaluationDate;

    /**
     * 信用查看次数(列表)
     */
    private Integer viewCount;

    /**
     * 一户一码(列表)
     */
    private String oneCode;

    /**
     * 一苗一码(列表)
     */
    private String oneSeedCode;

    /**
     * 附加信息JSON
     */
    private String extraInfo;

    /**
     * codeNum
     */
    private String codeNum;

    /**
     * 销售渠道
     */
    private String saleChannel;

    /**
     * 二维码链接
     */
    private String qrCodeUrl;

    /**
     * 种子生产经营许可证号
     */
    private String xkzh;

    /**
     * 种子生产经营许可证生产经营范围
     */
    private String scjyzl;

    /**
     * 种子生产经营许可证起始时间
     */
    private Date zzyxqqsrq;

    /**
     * 种子生产经营许可证结束时间
     */
    private Date zzyxqjzrq;
}
