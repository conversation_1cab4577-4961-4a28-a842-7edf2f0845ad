package com.hainancrc.module.creditcxbs.service.businessfundingview;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.businessfundingview.dto.BusinessFundingViewCreateDTO;
import com.hainancrc.module.creditcxbs.api.businessfundingview.dto.BusinessFundingViewPageDTO;
import com.hainancrc.module.creditcxbs.api.businessfundingview.dto.BusinessFundingViewUpdateDTO;
import com.hainancrc.module.creditcxbs.entity.BusinessFundingViewDO;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-06
 */

public interface BusinessFundingViewService {

    /**
     * 创建
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    Long create(@Valid BusinessFundingViewCreateDTO createDTO);


    /**
     * 修改
     *
     * @param updateDTO 更新信息
     */
    void update(@Valid BusinessFundingViewUpdateDTO updateDTO);


    /**
     * 获得列表
     *
     * @return 列表
     */
    List<BusinessFundingViewDO> getList();

    /**
     * 获得分页
     *
     * @param pageDTO 分页查询
     * @return 分页
     */
    PageResult<BusinessFundingViewDO> getPage(BusinessFundingViewPageDTO pageDTO);


    /**
     * 删除
     *
     * @param id id
     */
    void delete(Long id);



    /**
     * 获得
     *
     * @param id id
     * @return
     */
    BusinessFundingViewDO get(Long id);

}
