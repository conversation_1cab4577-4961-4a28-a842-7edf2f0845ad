package com.hainancrc.module.creditcxbs.service.financialpolicy;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.springframework.stereotype.Service;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.enums.FinancialPolicyPolicyStatus;
import com.hainancrc.module.creditcxbs.api.financialpolicy.dto.*;
import com.hainancrc.module.creditcxbs.convert.financialpolicy.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.financialpolicy.*;

import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;
import static com.hainancrc.module.creditcxbs.constant.Constants.IMAGE_URL_STRING;

/**
*  Service 实现类
*
*/
@Service
@Validated
public class FinancialPolicyServiceImpl implements FinancialPolicyService {
    
    @Resource
    private FinancialPolicyMapper financialPolicyMapper;
    
    
    
    private void validateExists(Long id) {
        if (financialPolicyMapper.selectById(id) == null) {
            throw exception(FINANCIAL_POLICY_NOT_EXISTS);
        }
    }
    
    
    @Override
    public Long create(FinancialPolicyCreateDTO createDTO) {
        Long count = financialPolicyMapper.selectOne(createDTO.getPolicyName());
        if (count > 0) {
            throw  new ServiceException(-1 ,"政策名字已存在");
        }

//        if (!CollectionUtils.isEmpty(createDTO.getFiles())) {
//            for (FinancialPolicyFileDTO dto : createDTO.getFiles()) {
//                dto.setUrl(StringEscapeUtils.unescapeHtml(dto.getUrl()));
//            }
//        }

        // 插入
        FinancialPolicyDO financialPolicy = FinancialPolicyConvert.INSTANCE.convert(createDTO);
//        financialPolicy.setPolicyFileUrl(JSONObject.toJSONString(createDTO.getFiles()));
        financialPolicy.setPolicyStatus(FinancialPolicyPolicyStatus.ONLINE);
        financialPolicyMapper.insert(financialPolicy);
        // 返回
        return financialPolicy.getId();
    }
    
    @Override
    public void update(FinancialPolicyUpdateDTO updateDTO) {
        
        
        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        FinancialPolicyDO updateObj = FinancialPolicyConvert.INSTANCE.convert(updateDTO);
        financialPolicyMapper.updateById(updateObj);
    }


    @Override
    public PageResult<FinancialPolicyDO> getPage(FinancialPolicyPageDTO pageDTO) {
        PageResult<FinancialPolicyDO> result = financialPolicyMapper.selectPage(pageDTO);

        result.getList().forEach(financialPolicyDO -> {
            String policyFileJson = financialPolicyDO.getPolicyFileJson();
            if (policyFileJson!= null) {
                try {
                    JSONArray jsonArray = JSON.parseArray(policyFileJson);
                    List<String> urls = (List<String>) jsonArray.stream()
                            .map(jsonObject -> ((JSONObject) jsonObject).getString(IMAGE_URL_STRING))
                            .collect(Collectors.toList());
                    financialPolicyDO.setPolicyFileJson(JSON.toJSONString(urls));
                } catch (Exception e) {
                    // 这里可以根据实际情况进行更详细的日志记录或错误处理
                    e.printStackTrace();
                }
            }
        });
        return result;
    }

    @Override
    public List<FinancialPolicyDO> getList() {
        return financialPolicyMapper.selectList();
    }
    
    
    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        financialPolicyMapper.deleteById(id);
    }
    
    
    @Override
    public FinancialPolicyDO get(Long id) {
        return financialPolicyMapper.selectById(id);
    }

    @Override
    public List<FinancialPolicyDO> getFinancialPolicyList() {
        return financialPolicyMapper.getFinancialPolicyList();
    }

    @Override
    public List<FinancialPolicyDO> getListByPolicyName(String policyName) {
        // 根据政策名称查询列表
        LambdaQueryWrapperX<FinancialPolicyDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.likeIfPresent(FinancialPolicyDO::getPolicyName, policyName);
        return financialPolicyMapper.selectList(queryWrapper);
    }


}

