package com.hainancrc.module.creditcxbs.utils.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellData;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.module.creditcxbs.api.oilteasubject.dto.OilTeaSubjectExportListDTOS;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@Slf4j
public class OilTeaSubjectExcelListener extends AnalysisEventListener<OilTeaSubjectExportListDTOS> {
    @Override
    public void invoke(OilTeaSubjectExportListDTOS data, AnalysisContext context) {
        String dateString = data.getSubsidyTime();
        try {
            if (dateString != null && !dateString.trim().isEmpty()) {
                String normalizedDate = Pattern.compile("\\D").matcher(dateString).replaceAll("-");
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate.parse(normalizedDate, formatter);
            }
        } catch (DateTimeParseException e) {
            // 抛出自定义异常或以其他方式处理错误
            throw new ServiceException(-1, "错误的日期格式: " + dateString + "；(正确的日期格式：2020/12/24)");
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    @Override
    public void invokeHead(Map<Integer, CellData> headMap, AnalysisContext context) {
        // 导入数据Excel表头
        List<String> importHeader = getImportExcelHeader(headMap);
        // 模板表头
        List<String> templateHeader = getTemplateHeader();

        if (!importHeader.equals(templateHeader)) {
            throw new ServiceException(-1, "导入模板错误！");
        }
    }


    private List<String> getImportExcelHeader(Map<Integer, CellData> headMap) {
        List<String> importHeader = new ArrayList<>();
        for (Map.Entry<Integer, CellData> entry : headMap.entrySet()) {
            CellData cellData = entry.getValue();
            if (cellData.getType() == CellDataTypeEnum.STRING) {
                importHeader.add(cellData.getStringValue());
            } else {
                importHeader.add(String.valueOf(cellData.getNumberValue()));
            }
        }

        return importHeader;
    }


    private List<String> getTemplateHeader() {
        List<String> header = new ArrayList<>();
        Class<OilTeaSubjectExportListDTOS> clazz = OilTeaSubjectExportListDTOS.class;
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(ExcelProperty.class)) {
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                header.add(excelProperty.value()[0]);
            }
        }
        return header;
    }
}
