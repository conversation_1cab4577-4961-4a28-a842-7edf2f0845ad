package com.hainancrc.module.creditcxbs.entity;

import java.math.BigDecimal;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import com.hainancrc.module.creditcxbs.api.enums.SeedlingCategorySeedlingStatus;

/**
 * 苗木信息 DO
 */
@TableName("cxbs_seedling_category")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SeedlingCategoryDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 苗木种类(列表,新增,编辑)
     */
    private String seedlingCategory;

    /**
     * 苗木品种(列表,新增,编辑)
     */
    private String seedlingVariety;

    /**
     * 价格范围(列表,新增,编辑)
     */
    private String pieceRange;

    /**
     * 规格情况(列表,新增,编辑)
     */
    private String seedlingSpecs;

    /**
     * 特点优势(列表,新增,编辑)
     */
    private String seedlingAdvantages;

    /**
     * 苗木状态(列表)(ENUMS:ONLINE-使用中,OFFLINE-已下架)ONLINE=使用中,OFFLINE=已下架
     */
    private SeedlingCategorySeedlingStatus seedlingStatus;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 相关政策(条)(列表,新增,编辑)
     */
    private Integer relatedPolicy;

    /**
     * 苗木图片列表
     */
    private String seedlingPictureJson;

    /**
     * 相关政策列表
     */
    private String relatedPolicyListJson;

    /**
     * 附加信息JSON
     */
    private String extraInfo;

}
