package com.hainancrc.module.creditcxbs.convert.teaorigincodehistory;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.teaorigincodehistory.dto.*;
import com.hainancrc.module.creditcxbs.api.teaorigincodehistory.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface TeaOriginCodeHistoryConvert {

    TeaOriginCodeHistoryConvert INSTANCE = Mappers.getMapper(TeaOriginCodeHistoryConvert.class);


    TeaOriginCodeHistoryDO convert(TeaOriginCodeHistoryCreateDTO bean);


    TeaOriginCodeHistoryDO convert(TeaOriginCodeHistoryUpdateDTO bean);


   TeaOriginCodeHistoryRespVO convert(TeaOriginCodeHistoryDO bean);

    List<TeaOriginCodeHistoryRespVO> convertList(List<TeaOriginCodeHistoryDO> list);

    List<TeaOriginCodeHistoryDO> convertToList(List<TeaOriginCodeHistoryCreateDTO> list);

    PageResult<TeaOriginCodeHistoryRespVO> convertPage(PageResult<TeaOriginCodeHistoryDO> page);



}
