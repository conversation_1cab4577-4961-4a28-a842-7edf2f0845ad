package com.hainancrc.module.creditcxbs.mapper.quarantineinformationview;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.quarantineinformationview.dto.QuarantineInformationViewPageDTO;
import com.hainancrc.module.creditcxbs.entity.QuarantineInformationViewDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface QuarantineInformationViewMapper extends BaseMapperX<QuarantineInformationViewDO> {

    PageResult<QuarantineInformationViewDO> selectPage(@Param("dto") QuarantineInformationViewPageDTO reqDTO);

    default QuarantineInformationViewDO selectReportView(Long teaOriginCodeId, String viewDate){
        return selectOne(new LambdaQueryWrapperX<QuarantineInformationViewDO>()
                                 .eq(QuarantineInformationViewDO::getTeaOriginCodeId, teaOriginCodeId)
                                 .between(QuarantineInformationViewDO::getViewDate, viewDate + " 00:00:00", viewDate + " 23:59:59"));
    }


}

