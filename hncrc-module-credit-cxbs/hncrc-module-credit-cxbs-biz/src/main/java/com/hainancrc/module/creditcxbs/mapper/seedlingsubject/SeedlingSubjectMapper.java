package com.hainancrc.module.creditcxbs.mapper.seedlingsubject;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.dto.*;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.vo.ALevelSubjectStatisticsVO;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.vo.SeedlingSubjectVO;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.vo.SubjectStatisticsVO;

import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

@Mapper
public interface SeedlingSubjectMapper extends BaseMapperX<SeedlingSubjectDO> {
    default PageResult<SeedlingSubjectDO> selectPage(SeedlingSubjectPageDTO pageDTO) {
        return selectPage(pageDTO, new LambdaQueryWrapperX<SeedlingSubjectDO>()
                .likeIfPresent(SeedlingSubjectDO::getSubjectName, pageDTO.getSubjectName())
                .eqIfPresent(SeedlingSubjectDO::getSubjectType, pageDTO.getSubjectType())
                .eqIfPresent(SeedlingSubjectDO::getCreditLevel, pageDTO.getCreditLevel())
                .orderByDesc(SeedlingSubjectDO::getCreateTime));
    }

    default SeedlingSubjectDO selectByCreditCode(String creditCode) {
        return selectOne(new LambdaQueryWrapperX<SeedlingSubjectDO>()
                                 .eq(SeedlingSubjectDO::getUniscid, creditCode));
    }

    /**
     * 获取信用等级统计数据
     */
    List<Map<String, Object>> getCreditLevelStats();

    /**
     * 获取主体类型统计数据
     */
    List<Map<String, Object>> getSubjectTypeStats();

    default List<SeedlingSubjectDO> getASeedlingSubjectList() {
        return selectList(new LambdaQueryWrapperX<SeedlingSubjectDO>()
        .eq(SeedlingSubjectDO::getCreditLevel, "A")
        .orderByDesc(SeedlingSubjectDO::getEvaluationDate)
        .last("limit 6"));
    }

    List<SeedlingSubjectVO> getSeedlingSubjectInfo();

    /**
     * 统计各类型主体数量
     */
    SubjectStatisticsVO countSubjectStatistics();

    /**
     * 获取A级主体数量统计信息
     */
    ALevelSubjectStatisticsVO getALevelSubjectStatistics();
}


