package com.hainancrc.module.creditcxbs.controller.pipeline.admin;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.creditcxbs.api.code.vo.CodeRespVO;
import com.hainancrc.module.creditcxbs.service.pipeline.PipelineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

/**
 * 手动调用处理数据管道
 */
@Api(tags = "手动调用处理数据管道")
@RestController
@RequestMapping("/pipeline")
@Validated
@Slf4j
public class PipelineController {

    @Resource
    private PipelineService pipelineService;

    /**
     * 获取所有企业信息
     *
     * @return 返回所有企业信息的数量
     */
    @GetMapping("/getCompanies")
    @ApiOperation("获取所有企业信息")
    public CommonResult<Long> getCompanies() {

        log.info("获取所有企业信息");
        return success(pipelineService.getCompanies());

    }

    /**
     * 获取单个企业信息
     *
     * @param creditCode 企业信用代码
     * @return 是否成功获取单个企业信息
     */
    @GetMapping("/getCompany")
    @ApiOperation("获取单个企业信息")
    public CommonResult<Boolean> getCompany(@RequestParam("creditCode") String creditCode) {

        log.info("获取单个企业信息");
        return success(pipelineService.getCompany(creditCode));

    }

    @GetMapping("/getCodeType")
    @ApiOperation("获取码类型")
    public CommonResult<CodeRespVO> getCodeType(@RequestParam("codeUrl") String codeUrl) {
        return success(pipelineService.getCodeType(codeUrl));
    }
}
