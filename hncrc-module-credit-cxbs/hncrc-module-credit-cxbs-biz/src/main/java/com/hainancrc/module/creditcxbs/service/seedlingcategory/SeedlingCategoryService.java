package com.hainancrc.module.creditcxbs.service.seedlingcategory;

import java.util.*;
import javax.validation.*;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.dto.*;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
*  Service 接口
*
*/
public interface SeedlingCategoryService {
    
    
    /**
    * 创建
    *
    * @param createDTO 创建信息
    * @return 编号
    */
    Long create(@Valid SeedlingCategoryCreateDTO createDTO);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid SeedlingCategoryUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    List<SeedlingCategoryDO> getList();
    
    /**
    * 获得分页
    *
    * @param pageDTO 分页查询
    * @return 分页
    */
    PageResult<SeedlingCategoryDO> getPage(SeedlingCategoryPageDTO pageDTO);


    PageResult<SeedlingCategoryDO> getPageHome(SeedlingCategoryPageDTO pageDTO);

    
    /**
    * 删除
    *
    * @param id 主键
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id 主键
    * @return
    */
    SeedlingCategoryDO get(Long id);
    
    
    /**
     * 统计不同苗木种类数量
     */
    Long countDistinctCategory();

    /**
     * 统计不同苗木品种数量
     */
    Long countDistinctVariety();

    /**
     * 根据苗木经营主体id获取苗木种类
     */
    SeedlingCategoryDO getBySubjectId(Long subjectId);

    SeedlingCategoryDistinctRespVO getSeedlingTypesAndVarieties();

    PageResult<SeedlingCategoryDO> getSubjectSeedlingPage(SeedlingCategoryPageDTO pageDTO);

    List<SeedlingCategoryDO> getSelectList();
}

