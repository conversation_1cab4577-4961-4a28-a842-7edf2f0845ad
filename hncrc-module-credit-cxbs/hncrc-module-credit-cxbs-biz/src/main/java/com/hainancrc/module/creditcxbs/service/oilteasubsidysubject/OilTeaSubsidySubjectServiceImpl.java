package com.hainancrc.module.creditcxbs.service.oilteasubsidysubject;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.dto.*;
import com.hainancrc.module.creditcxbs.convert.oilteasubsidysubject.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.oilteasubsidypublicnote.OilTeaSubsidyPublicNoteMapper;
import com.hainancrc.module.creditcxbs.mapper.oilteasubsidysubject.*;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;
/**
*  Service 实现类
*
*/
@Slf4j
@Service
@Validated
public class OilTeaSubsidySubjectServiceImpl implements OilTeaSubsidySubjectService {
    
    @Resource
    private OilTeaSubsidySubjectMapper oilTeaSubsidySubjectMapper;

    
    
    private void validateExists(Long id) {
        if (oilTeaSubsidySubjectMapper.selectById(id) == null) {
            throw exception(OIL_TEA_SUBSIDY_SUBJECT_NOT_EXISTS);
        }
    }
    
    
    @Override
    public Long create(OilTeaSubsidySubjectCreateDTO createDTO) {
        
        
        // 插入
        OilTeaSubsidySubjectDO oilTeaSubsidySubject = OilTeaSubsidySubjectConvert.INSTANCE.convert(createDTO);
        oilTeaSubsidySubjectMapper.insert(oilTeaSubsidySubject);
        // 返回
        return oilTeaSubsidySubject.getId();
    }
    
    @Override
    public void update(OilTeaSubsidySubjectUpdateDTO updateDTO) {
        
        
        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        OilTeaSubsidySubjectDO updateObj = OilTeaSubsidySubjectConvert.INSTANCE.convert(updateDTO);
        oilTeaSubsidySubjectMapper.updateById(updateObj);
    }
    
    
    @Override
    public PageResult<OilTeaSubsidySubjectDO> getPage(OilTeaSubsidySubjectPageDTO pageDTO) {
        return oilTeaSubsidySubjectMapper.selectPage(pageDTO);
    }
    
    @Override
    public List<OilTeaSubsidySubjectDO> getList() {
        return oilTeaSubsidySubjectMapper.selectList();
    }
    
    
    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        oilTeaSubsidySubjectMapper.deleteById(id);
    }
    
    
    @Override
    public OilTeaSubsidySubjectDO get(Long id) {
        return oilTeaSubsidySubjectMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(Long noteId, List<OilTeaSubsidySubjectExcelDTO> excelDTOS) {
        List<OilTeaSubsidySubjectDO> convert = OilTeaSubsidySubjectConvert.INSTANCE.convert(excelDTOS);
        for (OilTeaSubsidySubjectDO oilTeaSubsidySubjectDO : convert) {
            oilTeaSubsidySubjectDO.setNoteId(noteId);
        }
        try {
            oilTeaSubsidySubjectMapper.insertBatch(convert);
        } catch (Exception e) {
            log.error("批量插入补贴主体数据失败", e);
        }

    }

    @Override
    public List<OilTeaSubsidySubjectDO> getSubsidySubjectList(Long noteId) {
        return oilTeaSubsidySubjectMapper.selectSubsidySubjectByNoteId(noteId);
    }

    @Override
    public List<OilTeaSubsidySubjectDO> SubsidySubjectList() {

        return oilTeaSubsidySubjectMapper.selectSubsidySubjectList();
    }


}

