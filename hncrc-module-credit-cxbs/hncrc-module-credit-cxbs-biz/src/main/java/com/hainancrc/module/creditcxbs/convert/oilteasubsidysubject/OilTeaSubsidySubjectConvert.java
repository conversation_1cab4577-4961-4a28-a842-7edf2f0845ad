package com.hainancrc.module.creditcxbs.convert.oilteasubsidysubject;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface OilTeaSubsidySubjectConvert {

    OilTeaSubsidySubjectConvert INSTANCE = Mappers.getMapper(OilTeaSubsidySubjectConvert.class);


    OilTeaSubsidySubjectDO convert(OilTeaSubsidySubjectCreateDTO bean);


    OilTeaSubsidySubjectDO convert(OilTeaSubsidySubjectUpdateDTO bean);


   OilTeaSubsidySubjectRespVO convert(OilTeaSubsidySubjectDO bean);

    List<OilTeaSubsidySubjectRespVO> convertList(List<OilTeaSubsidySubjectDO> list);

    PageResult<OilTeaSubsidySubjectRespVO> convertPage(PageResult<OilTeaSubsidySubjectDO> page);

    List<OilTeaSubsidySubjectDO> convert(List<OilTeaSubsidySubjectExcelDTO> list);

}
