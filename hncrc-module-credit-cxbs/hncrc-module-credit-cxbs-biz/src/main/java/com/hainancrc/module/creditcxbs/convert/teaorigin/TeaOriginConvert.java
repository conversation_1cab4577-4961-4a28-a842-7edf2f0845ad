package com.hainancrc.module.creditcxbs.convert.teaorigin;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.teaorigin.dto.*;
import com.hainancrc.module.creditcxbs.api.teaorigin.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface TeaOriginConvert {

    TeaOriginConvert INSTANCE = Mappers.getMapper(TeaOriginConvert.class);


    TeaOriginDO convert(TeaOriginCreateDTO bean);


    TeaOriginDO convert(TeaOriginUpdateDTO bean);


   TeaOriginRespVO convert(TeaOriginDO bean);

    List<TeaOriginRespVO> convertList(List<TeaOriginDO> list);

    PageResult<TeaOriginRespVO> convertPage(PageResult<TeaOriginDO> page);



}
