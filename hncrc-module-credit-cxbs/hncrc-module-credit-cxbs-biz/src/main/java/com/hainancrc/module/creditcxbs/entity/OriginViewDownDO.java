package com.hainancrc.module.creditcxbs.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-20
 */

@TableName("cxbs_origin_view_down")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OriginViewDownDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 商户id
     */
    private Long companyId;

    /**
     * 一户一码下载
     */
    private Long familyCodeDownload;

    /**
     * 一户一码查看
     */
    private Long familyCodeView;

    /**
     * 一苗一码下载
     */
    private Long seedlingCodeDownload;

    /**
     * 一苗一码查看
     */
    private Long seedlingCodeView;

    /**
     * 查看日期
     */
    private Date viewDate;

}
