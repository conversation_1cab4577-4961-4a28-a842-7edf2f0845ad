package com.hainancrc.module.creditcxbs.controller.oilteaestate.admin;

import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.module.creditcxbs.api.oilteaestate.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteaestate.vo.*;
import com.hainancrc.module.creditcxbs.convert.oilteaestate.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.oilteaestate.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "油茶茶园信息")
@RestController
@RequestMapping("/oilteaestate")
@Validated
public class OilTeaEstateController {
    
    @Resource
    private OilTeaEstateService oilTeaEstateService;


    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Long> create(@Valid @RequestBody OilTeaEstateCreateDTO createDTO) {
        return success(oilTeaEstateService.create(createDTO));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody OilTeaEstateUpdateDTO updateDTO) {
        oilTeaEstateService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<OilTeaEstateRespVO>> getList() {
        List<OilTeaEstateDO> list = oilTeaEstateService.getList();
        return success(OilTeaEstateConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/statisticsList")
    @ApiOperation("数据总览-获得列表")
    public CommonResult<List<OilTeaEstateRespVO>> getStatisticsList() {
        List<OilTeaEstateDO> list = oilTeaEstateService.getStatisticsList();
        return success(OilTeaEstateConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<OilTeaEstateRespVO>> getPage(@Valid OilTeaEstatePageDTO pageDTO) {
        PageResult<OilTeaEstateDO> pageResult = oilTeaEstateService.getPage(pageDTO);
        return success(OilTeaEstateConvert.INSTANCE.convertPage(pageResult));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        oilTeaEstateService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<OilTeaEstateRespVO> get(@RequestParam("id") Long id) {
        OilTeaEstateDO oilTeaEstate = oilTeaEstateService.get(id);
        return success(OilTeaEstateConvert.INSTANCE.convert(oilTeaEstate));
    }


}
