package com.hainancrc.module.creditcxbs.api.businessfunding.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel("金融产品统计信息 Response VO")
@Data
public class BusinessfundingStatisticsRespVO {
    
    @ApiModelProperty(value = "金融产品个数")
    private Long financialProductCount;

    @ApiModelProperty(value = "金融产品浏览数")
    private Long financialProductViewCount;

    @ApiModelProperty(value = "浏览经营主体数")
    private Long subjectViewCount;

    @ApiModelProperty(value = "申请经营主体数")
    private Long applySubjectCount;

    @ApiModelProperty(value = "申请经成功营主体数")
    private Long applySuccessSubjectCount;

    @ApiModelProperty(value = "融资总金额")
    private BigDecimal totalAmount;
}