package com.hainancrc.module.creditcxbs.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@TableName("cxbs_financial_product_view_statistics")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinancialProductViewStatisticsDO extends BaseDO {

    /**
     * id
     */
    private Long id;

    /**
     * 统计类型
     */
    private String statisticsType;

    /**
     * 统计数量
     */
    private Long statisticsCount;
}
