package com.hainancrc.module.creditcxbs.convert.originviewdown;


import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.originviewdown.dto.OriginViewDownCreateDTO;
import com.hainancrc.module.creditcxbs.api.originviewdown.dto.OriginViewDownUpdateDTO;
import com.hainancrc.module.creditcxbs.api.originviewdown.vo.OriginViewDownRespVO;
import com.hainancrc.module.creditcxbs.entity.OriginViewDownDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-06
 */

@Mapper
public interface OriginViewDownConvert {

    OriginViewDownConvert INSTANCE = Mappers.getMapper(OriginViewDownConvert.class);

    OriginViewDownDO convert(OriginViewDownCreateDTO bean);

    OriginViewDownDO convert(OriginViewDownUpdateDTO bean);

    OriginViewDownRespVO convert(OriginViewDownDO bean);

    List<OriginViewDownRespVO> convertList(List<OriginViewDownDO> list);

    PageResult<OriginViewDownRespVO> convertPage(PageResult<OriginViewDownDO> page);
}
