package com.hainancrc.module.creditcxbs.service.teaorigincode;

import java.util.*;
import javax.validation.*;
import com.hainancrc.module.creditcxbs.api.teaorigincode.dto.*;
import com.hainancrc.module.creditcxbs.api.teaorigincode.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
*  Service 接口
*
*/
public interface TeaOriginCodeService {
    
    
    /**
    * 创建
    *
    * @param createDTO 创建信息
    * @return 编号
    */
    Long create(@Valid TeaOriginCodeCreateDTO createDTO);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid TeaOriginCodeUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    List<TeaOriginCodeDO> getList();
    
    /**
     * 获得分页
     *
     * @param pageDTO 分页查询
     * @return 分页
     */
    PageResult<TeaOriginCodePageRespVO> getPage(TeaOriginCodePageDTO pageDTO);
    
    
    /**
    * 删除
    *
    * @param id id
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id id
    * @return
    */
    TeaOriginCodeDO get(Long id);

    /**
     * 根据code查询
     *
     * @param code code
     * @return vo
     */
    TeaOriginCodeSubjectRespVO getByCode(String code);
}

