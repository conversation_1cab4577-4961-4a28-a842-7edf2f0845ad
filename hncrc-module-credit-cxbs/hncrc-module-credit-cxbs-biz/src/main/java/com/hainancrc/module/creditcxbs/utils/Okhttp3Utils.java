package com.hainancrc.module.creditcxbs.utils;

/**
 * @Classname Okhttp3Utils
 * @Description TODO
 * @Date 2022/2/27 17:31
 * @Created by os.yangd
 */

import com.alibaba.fastjson2.JSONObject;
import okhttp3.*;
import okio.BufferedSink;
import okio.Okio;
import okio.Source;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Objects;

import static org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE;

public class Okhttp3Utils {
    // 设置 MediaType，用于指定请求的内容类型
    public static final MediaType FORM_DATA_TYPE = MediaType.parse("multipart/form-data;charset=utf-8");
    public static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");

    public static final String ContentType="Content-Type";
    public static final String CONTENT_TYPE_JSON = "application/json;charset=UTF-8";
    public static final String CONTENT_TYPE_FORMDATA = "multipart/form-data";

    public static final String DEFAULT_FILE_NAME = "file";

    // OkHttpClient 是一个线程安全的 HTTP 客户端
    public static final OkHttpClient httpClient = new OkHttpClient();

    // GET 请求方法
    public static String httpGet(HttpUrl url) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .build();
        Response response = httpClient.newCall(request).execute();
        return response.body().string();
    }

    // POST 请求方法，用 JSON 作为请求体
    public static String httpPost(String url, String content) throws IOException {
        RequestBody requestBody = RequestBody.create(JSON_TYPE, content);
        Request request = new Request.Builder()
                .addHeader(ContentType, CONTENT_TYPE_JSON)
                .url(url)
                .post(requestBody)
                .build();
        Response response = httpClient.newCall(request).execute();
        return response.body().string();
    }


    /**
     * form-data方法
     * @param url
     * @param content
     * @return
     * @throws IOException
     */
    public static String httpPostFormData(HttpUrl url, String content) throws IOException {

        FormBody.Builder formBodyBulider = new FormBody.Builder();

        JSONObject params = JSONObject.parseObject(content);

        // 遍历 JSONObject
        for (Map.Entry<String, Object> entry : params.entrySet())
        {
            formBodyBulider.add(entry.getKey(), String.valueOf(entry.getValue()));
        }

        Request request = new Request.Builder()
                .addHeader(ContentType, CONTENT_TYPE_FORMDATA)
                .post(formBodyBulider.build())
                .url(url)
                .build();
        Response response = httpClient.newCall(request).execute();
        return response.body().string();
    }



    // POST 请求方法，用 JSON 作为请求体，并可以传入额外的头部信息
    public static String httpPost(String url, Map<String, String> headers, String content) throws IOException {
        RequestBody requestBody = RequestBody.create(JSON_TYPE, content);

        // 创建一个 Request.Builder 对象
        Request.Builder requestBuilder = new Request.Builder()
                .url(url);

        // 添加头部信息
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }

        // 设置请求体
        requestBuilder.post(requestBody);

        // 构建 Request 对象
        Request request = requestBuilder.build();

        // 发送请求并获取响应
        Response response = httpClient.newCall(request).execute();
        return response.body().string();
    }

    // 发送 GET 请求，可以传入额外的头部信息和查询参数
    public static String httpGet(String url, Map<String, String> headers, String content) throws IOException {
        if (content != null) {
            // 解析 JSON 字符串为 JsonObject
            JSONObject json = JSONObject.parseObject(content);

            // 构建查询参数字符串
            StringBuilder queryParams = new StringBuilder();
            for (Map.Entry<String, Object> entry : json.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue().toString();
                queryParams.append(key).append("=").append(value).append("&");
            }

            // 移除末尾的 "&" 字符
            String queryString = queryParams.substring(0, queryParams.length() - 1);

            // 拼接查询参数到 URL
            url = url + "?" + queryString;
        }

        // 创建一个 Request.Builder 对象
        Request.Builder requestBuilder = new Request.Builder()
                .url(url);

        // 添加头部信息
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }

        // 构建 Request 对象
        Request request = requestBuilder.build();

        // 发送请求并获取响应
        Response response = httpClient.newCall(request).execute();
        return response.body().string();
    }

    // 根据请求方法发送 HTTP 请求
    public static String httpRequest(String method, String url, Map<String, String> headers, String content) throws IOException {
        if ("GET".equals(method)) {
            return httpGet(url, headers, content);
        }
        if ("POST".equals(method)) {
            return httpPost(url, headers, content);
        }
        return null;
    }


    /**
     * 上传文件使用POST方法。
     *
     * @param url     要上传文件的URL。
     * @param file    要上传的MultipartFile。
     * @param headers 要包含在请求中的额外头部。
     * @return 响应体的字符串表示形式。
     * @throws IOException 如果发生I/O错误。
     */
    public static String httpUploadFile(String url, MultipartFile file, Map<String, String> headers) throws IOException {
        return httpUploadFile(url,file,DEFAULT_FILE_NAME,headers);
    }



    /**
     * 上传文件使用POST方法。
     *
     * @param url     要上传文件的URL。
     * @param file    要上传的MultipartFile。
     * @param headers 要包含在请求中的额外头部。
     * @return 响应体的字符串表示形式。
     * @throws IOException 如果发生I/O错误。
     */
    public static String httpUploadFile(String url, MultipartFile file,String fileParamName, Map<String, String> headers) throws IOException {
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart(fileParamName, file.getOriginalFilename(), RequestBody.create(MediaType.parse("application/octet-stream"), file.getBytes()))
                .build();

        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(requestBody);

        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        Request request = requestBuilder.build();

        Response response = httpClient.newCall(request).execute();
        return response.body().string();
    }


    public static String httpPostFormData(JSONObject params, String url, Map<String, String> headerMap){
        Response response = null;
        try {
            MultipartBody.Builder multipartBodyBuilder = builderFileDataParams(params);
            RequestBody requestBody = multipartBodyBuilder.build();

            Request.Builder builder = new Request.Builder();
            builder.url(url);

            for(Map.Entry<String, String> map: headerMap.entrySet()){
                builder.addHeader(map.getKey(),map.getValue());
            }


            Request request = builder.post(requestBody).build();
            Call call = httpClient.newCall(request);
            response = call.execute();
            ResponseBody body = response.body();
            String result = body.string();
            if (body != null) {
                System.out.println("httpPostFormData-ResponseBody: " + result);
                return result;
            }
            return null;
        }catch (Exception e){
            throw new RuntimeException(e);
        }finally {
            if (response != null){
                response.close();
            }
        }
    }

    static MultipartBody.Builder builderFileDataParams(JSONObject params) {
        MultipartBody.Builder builder = new MultipartBody.Builder();
        params.forEach((k, v) ->{
            if (Objects.nonNull(v)){
                if (v instanceof File){
                    File f = (File) v;
                    builder.addFormDataPart(k, f.getName(),
                            RequestBody.create(MediaType.parse(APPLICATION_OCTET_STREAM_VALUE), f));
                } else if (v instanceof InputStream){
                    fileTypeIsInputStream(params, k, (InputStream) v, builder);
                } else {
                    builder.addFormDataPart(k, v.toString());
                }
            }
        });
        return builder;
    }

    static void fileTypeIsInputStream(JSONObject params, String k, InputStream v, MultipartBody.Builder builder) {
        InputStream inputStream = v;
        builder.addFormDataPart(k, params.getString("fileName"),
                new RequestBody() {
                    @Override
                    public MediaType contentType() {
                        return MediaType.parse(APPLICATION_OCTET_STREAM_VALUE);
                    }
                    @Override
                    public void writeTo(BufferedSink sink) throws IOException {
                        try (Source source = Okio.source(inputStream)) {
                            sink.writeAll(source);
                        }
                    }
                });
    }

}

