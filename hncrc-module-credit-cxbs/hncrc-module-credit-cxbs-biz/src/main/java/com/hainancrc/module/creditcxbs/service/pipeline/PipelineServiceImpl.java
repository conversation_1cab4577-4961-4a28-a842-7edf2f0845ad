package com.hainancrc.module.creditcxbs.service.pipeline;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.codeengine.api.publicapi.PublicApi;
import com.hainancrc.module.codeengine.api.publicapi.dto.*;
import com.hainancrc.module.codeengine.api.publicapi.vo.*;
import com.hainancrc.module.codeengine.enums.ApplyCategory;
import com.hainancrc.module.codeengine.enums.CodeLogStatus;
import com.hainancrc.module.creditcxbs.api.code.vo.CodeRespVO;
import com.hainancrc.module.creditcxbs.entity.SeedlingSubjectDO;
import com.hainancrc.module.creditcxbs.mapper.seedlingsubject.SeedlingSubjectMapper;
import com.hainancrc.module.creditcxbs.utils.HttpClientUtil;
// import com.hncrc.openapi.api.CreditBaiShaApi;
import com.hncrc.openapi.api.CreditWuzhishanApi;
// import com.hncrc.openapi.api.req.CreditBaiShaEnterpriseArchiveReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;

@Service
@Validated
@Slf4j
public class PipelineServiceImpl implements PipelineService {

    @Resource
    private SeedlingSubjectMapper seedlingSubjectMapper;

    @Resource
    private PublicApi publicApi;

    @Value("${codeengine.application}")
    private String codeengineApplication;

    @Value("${codeengine.channel}")
    private String codeengineChannel;

    @Value("${codeengine.credit-code-scene}")
    private String codeengineCreditCodeScene;

    @Value("${openapi.queryAccount}")
    private String openApiQueryAccount;

    // @Resource
    // private CreditBaiShaApi creditBaiShaApi;

    /**
     *
     *
     * @param seedlingSubjectDO 公司信息实体
     */
    public void getCompanyByDO(SeedlingSubjectDO seedlingSubjectDO) {

        // Map<String, String> params = new HashMap<>();
        // params.put("uniscid", seedlingSubjectDO.getUniscid());
        // params.put("entName", seedlingSubjectDO.getSubjectName());
        // log.info("archiveReq: {}", params);
        // String archiveReqBody;
        // JSONObject carchiveReqObject = null;

        // CreditBaiShaEnterpriseArchiveReq req = new
        // CreditBaiShaEnterpriseArchiveReq();
        // req.setUniscid(seedlingSubjectDO.getUniscid());
        // req.setEntName(seedlingSubjectDO.getSubjectName());
        // req.setQueryAccount(openApiQueryAccount);
        // String baisha = creditBaiShaApi.queryEnterpriseArchive(req);
        // log.info("baisha: {}", baisha);
        //
        // // 判断 archiveReqBody 是否为合法的 Base64 编码字符串
        // String archiveReqBody = null;
        //
        // byte[] buff = Base64.getDecoder().decode(baisha);
        // try {
        // archiveReqBody = new String(buff, "UTF-8");
        // } catch (UnsupportedEncodingException e) {
        // archiveReqBody = null;
        // }
        // log.info("archiveReqBody: {}", archiveReqBody);
        //
        // if (StringUtils.isBlank(archiveReqBody)) {
        // throw exception(ARCHIVE_REQ_BODY_PARSE_ERROR);
        // }
        //
        // // 判断 archiveReqBody 是不是一个合法的 JSON 字符串
        // com.alibaba.fastjson2.JSONObject companyJsonObject = null;
        // try {
        // companyJsonObject =
        // com.alibaba.fastjson2.JSONObject.parseObject(archiveReqBody);
        // } catch (Exception e) {
        // throw exception(COMPANY_JSON_OBJECT_PARSE_ERROR);
        // }
        //
        //
        // JSONObject extraJson = new JSONObject();
        // extraJson.put("archive", companyJsonObject);

        // 评价日期当前时间
        // seedlingSubjectDO.setEvaluationDate(new Date());
        // seedlingSubjectDO.setExtraInfo(extraJson.toString());
        seedlingSubjectDO.setUpdateTime(new Date());

        // 申码
        // 拿出苗木主体对应的信息
        CommonResult<ApplyCreateRespVO> applyCreateRespVO = getApplyCreateRespVOCommonResult(seedlingSubjectDO);

        // 亮码请求
        GenerateCreateDTO generateCreateDTO = getGenerateCreateDTO(applyCreateRespVO);
        log.info("调用码引擎亮码请求:{}", generateCreateDTO);

        // 调用码引擎亮码
        CommonResult<GenerateCreateRespVO> generateCreateRespVO = getGenerateCreateRespVOCommonResult(
                generateCreateDTO);

        // 亮码结果
        CommonResult<GenerateStatusRespVO> generateStatusRespVO = getGenerateStatusRespVOCommonResult(
                generateCreateRespVO);

        if (CodeLogStatus.SUCCESS.equals(generateStatusRespVO.getData().getGenerateStatus())) {
            seedlingSubjectDO.setOneCode(generateStatusRespVO.getData().getCodeUrl());
            seedlingSubjectDO.setOneSeedCode(generateStatusRespVO.getData().getCodeUrl());

            String codeUrl = generateStatusRespVO.getData().getCodeUrl();
            // 截取code后面的值
            String param = "code=";
            int start = codeUrl.indexOf(param) + param.length();
            int end = codeUrl.indexOf("&", start);
            seedlingSubjectDO.setCodeNum(codeUrl.substring(start, end));
        }

        seedlingSubjectMapper.updateById(seedlingSubjectDO);
    }

    @Override
    public Long getCompanies() {

        List<SeedlingSubjectDO> companies = seedlingSubjectMapper.selectList(
                new LambdaQueryWrapper<SeedlingSubjectDO>()
                        .eq(SeedlingSubjectDO::getDeleted, 0)
                        .orderBy(true, true, SeedlingSubjectDO::getId));

        Long successCount = 0L;

        for (SeedlingSubjectDO companyDO : companies) {

            try {
                getCompanyByDO(companyDO);
                successCount++;
            } catch (Exception e) {
                log.error("companyDO: {} Exception: {} Extra: {}", companyDO.getSubjectName(), e, companyDO);
            }
        }

        return successCount;
    }

    @Override
    public Boolean getCompany(String creditCode) {

        log.info("creditCode: {}", creditCode);

        SeedlingSubjectDO companyDO = seedlingSubjectMapper.selectByCreditCode(creditCode);

        if (companyDO == null) {
            throw exception(SEEDLING_SUBJECT_NOT_EXISTS);
        }

        getCompanyByDO(companyDO);

        return true;

    }

    @Override
    public String getByCode(String code) {
        log.info("code: {}", code);

        // 解码流程
        CommonResult<DecodeCreateRespVO> decodeCreateRespVOCommonResult = getDecodeCreateRespVOCommonResult(code);
        log.info("decodeCreateRespVOCommonResult: {}", decodeCreateRespVOCommonResult);
        if (decodeCreateRespVOCommonResult.isSuccess()) {
            if (CodeLogStatus.SUCCESS.equals(decodeCreateRespVOCommonResult.getData().getDecodeStatus())) {
                // 解码状态查询
                DecodeStatusDTO statusDTO = new DecodeStatusDTO();
                statusDTO.setDecodeOrderId(decodeCreateRespVOCommonResult.getData().getDecodeOrderId());
                statusDTO.setApplication(codeengineApplication);
                CommonResult<DecodeStatusRespVO> decodeStatusRespVOCommonResult = publicApi.decodeStatus(statusDTO);
                log.info("decodeStatusRespVOCommonResult: {}", decodeStatusRespVOCommonResult);
                if (decodeStatusRespVOCommonResult.isSuccess()) {
                    if (CodeLogStatus.SUCCESS.equals(decodeStatusRespVOCommonResult.getData().getDecodeStatus())) {
                        SeedlingSubjectDO seedlingSubjectDO = seedlingSubjectMapper
                                .selectById(decodeStatusRespVOCommonResult.getData().getEntityId());
                        log.info("seedlingSubjectDO: {}", seedlingSubjectDO);
                        if (seedlingSubjectDO != null && StringUtils.isNotEmpty(seedlingSubjectDO.getExtraInfo())) {
                            return seedlingSubjectDO.getExtraInfo();
                        }
                    }
                }
            }
        }
        return null;
    }

    @Override
    public CodeRespVO getCodeType(String codeUrl) {

        if (ObjectUtil.isEmpty(codeUrl)) {
            return null;
        }

        CodeRespVO codeRespVO = new CodeRespVO();
        codeUrl = codeUrl.replace("%23", "#")
                .replace("%26", "&");

        // 解码转义
        codeUrl = StringEscapeUtils.unescapeHtml(codeUrl);

        log.info("codeUrl: {}", codeUrl);

        // 解码流程
        CommonResult<DecodeCreateRespVO> decodeCreateRespVOCommonResult = getDecodeCreateRespVOCommonResult(codeUrl);
        log.info("decodeCreateRespVOCommonResult: {}", decodeCreateRespVOCommonResult);
        if (decodeCreateRespVOCommonResult.isSuccess()) {
            if (CodeLogStatus.SUCCESS.equals(decodeCreateRespVOCommonResult.getData().getDecodeStatus())) {
                // 解码状态查询
                DecodeStatusDTO statusDTO = new DecodeStatusDTO();
                statusDTO.setDecodeOrderId(decodeCreateRespVOCommonResult.getData().getDecodeOrderId());
                statusDTO.setApplication(codeengineApplication);
                CommonResult<DecodeStatusRespVO> decodeStatusRespVOCommonResult = publicApi.decodeStatus(statusDTO);
                log.info("decodeStatusRespVOCommonResult: {}", decodeStatusRespVOCommonResult);
                if (decodeStatusRespVOCommonResult.isSuccess()) {
                    if (CodeLogStatus.SUCCESS.equals(decodeStatusRespVOCommonResult.getData().getDecodeStatus())) {
                        String extraParameters = Optional.of(decodeStatusRespVOCommonResult)
                                .map(CommonResult::getData)
                                .map(DecodeStatusRespVO::getExtraParameters)
                                .orElse(null);

                        try {
                            JSONObject extraParas = JSON.parseObject(extraParameters);
                            codeRespVO.setCodeType(extraParas.getString("type"));
                        } catch (Exception e) {
                            log.error("Failed to parse extraParameters: {}. Exception: {}", extraParameters,
                                    e.getMessage(), e);
                            codeRespVO.setCodeType(null);
                        }

                    }
                }
            }
        }

        // 截取code的值
        String param = "code=";
        int start = codeUrl.indexOf(param) + param.length();
        int end = codeUrl.indexOf("&", start);
        codeRespVO.setCodeNum(codeUrl.substring(start, end));
        return codeRespVO;
    }

    private CommonResult<DecodeCreateRespVO> getDecodeCreateRespVOCommonResult(String code) {
        DecodeCreateDTO createDTO = new DecodeCreateDTO();
        createDTO.setApplication(codeengineApplication);
        createDTO.setChannel(codeengineChannel);
        createDTO.setCodeUrl(code);
        return publicApi.createDecode(createDTO);
    }

    @NotNull
    private CommonResult<ApplyCreateRespVO> getApplyCreateRespVOCommonResult(SeedlingSubjectDO seedlingSubjectDO) {
        ApplyCreateDTO applyCreateDTO = new ApplyCreateDTO();
        applyCreateDTO.setApplication(codeengineApplication);
        applyCreateDTO.setChannel(codeengineChannel);
        applyCreateDTO.setScene(codeengineCreditCodeScene);
        applyCreateDTO.setEntityId(seedlingSubjectDO.getUniscid());
        applyCreateDTO.setEntity(seedlingSubjectDO.getSubjectName());
        applyCreateDTO.setCategory(ApplyCategory.STATIC);
        applyCreateDTO.setDescription("一户一苗码申请");
        CommonResult<ApplyCreateRespVO> applyCreateRespVO = publicApi.createApply(applyCreateDTO);
        log.info("调用码引擎申领结果:{}", applyCreateRespVO);
        if (applyCreateRespVO.getCode() != 0 || applyCreateRespVO.getData().getApplyStatus() != CodeLogStatus.SUCCESS) {
            throw new IllegalArgumentException("调用码引擎申领失败:" + applyCreateRespVO.getMsg());
        }
        return applyCreateRespVO;
    }

    private GenerateCreateDTO getGenerateCreateDTO(CommonResult<ApplyCreateRespVO> applyCreateRespVO) {
        GenerateCreateDTO generateCreateDTO = new GenerateCreateDTO();
        generateCreateDTO.setApplication(codeengineApplication);
        generateCreateDTO.setApplyOrderId(applyCreateRespVO.getData().getApplyOrderId());
        generateCreateDTO.setChannel(codeengineChannel);
        generateCreateDTO.setCategory(ApplyCategory.STATIC);
        return generateCreateDTO;
    }

    @NotNull
    private CommonResult<GenerateCreateRespVO> getGenerateCreateRespVOCommonResult(
            GenerateCreateDTO generateCreateDTO) {
        CommonResult<GenerateCreateRespVO> generateCreateRespVO = publicApi.createGenerate(generateCreateDTO);
        if (generateCreateRespVO.getCode() != 0
                || generateCreateRespVO.getData().getGenerateStatus() != CodeLogStatus.SUCCESS) {
            throw new IllegalArgumentException("调用码引擎亮码失败:" + generateCreateRespVO.getMsg());
        }
        return generateCreateRespVO;
    }

    private CommonResult<GenerateStatusRespVO> getGenerateStatusRespVOCommonResult(
            CommonResult<GenerateCreateRespVO> generateCreateRespVO) {
        GenerateStatusDTO generateStatusDTO = new GenerateStatusDTO();
        generateStatusDTO.setApplication(codeengineApplication);
        generateStatusDTO.setGenerateOrderId(generateCreateRespVO.getData().getGenerateOrderId());
        generateStatusDTO.setCategory(ApplyCategory.STATIC);

        log.info("调用码引擎亮码:{}", generateStatusDTO);
        CommonResult<GenerateStatusRespVO> generateStatusRespVO = publicApi.generateStatus(generateStatusDTO);
        log.info("调用码引擎亮码结果:{}", generateStatusRespVO);

        if (generateStatusRespVO.getCode() != 0
                || generateStatusRespVO.getData().getGenerateStatus() != CodeLogStatus.SUCCESS) {
            throw new IllegalArgumentException("调用码引擎亮码结果失败:" + generateStatusRespVO.getMsg());
        }
        return generateStatusRespVO;
    }

    public static void main(String[] args) {
        String codeUrl = "https://sit-app.hainancrc.com/multi/#/cxbsPackage/pages/loading/index?code=99059076-85c4-4d22-b9a1-af299cc671e0&amp;e=undefined&amp;l=undefined";
        String s = StringEscapeUtils.unescapeHtml(codeUrl);
        System.out.println(s);
    }
}
