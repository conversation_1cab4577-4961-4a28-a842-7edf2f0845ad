package com.hainancrc.module.creditcxbs.convert.oilteaestate;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.oilteaestate.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteaestate.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface OilTeaEstateConvert {

    OilTeaEstateConvert INSTANCE = Mappers.getMapper(OilTeaEstateConvert.class);


    OilTeaEstateDO convert(OilTeaEstateCreateDTO bean);


    OilTeaEstateDO convert(OilTeaEstateUpdateDTO bean);


   OilTeaEstateRespVO convert(OilTeaEstateDO bean);

    List<OilTeaEstateRespVO> convertList(List<OilTeaEstateDO> list);

    PageResult<OilTeaEstateRespVO> convertPage(PageResult<OilTeaEstateDO> page);



}
