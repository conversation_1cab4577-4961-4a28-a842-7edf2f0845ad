package com.hainancrc.module.creditcxbs.convert.seedlingsubject;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.dto.*;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface SeedlingSubjectConvert {

    SeedlingSubjectConvert INSTANCE = Mappers.getMapper(SeedlingSubjectConvert.class);


    SeedlingSubjectDO convert(SeedlingSubjectCreateDTO bean);


    SeedlingSubjectDO convert(SeedlingSubjectUpdateDTO bean);


   SeedlingSubjectRespVO convert(SeedlingSubjectDO bean);

    List<SeedlingSubjectRespVO> convertList(List<SeedlingSubjectDO> list);

    PageResult<SeedlingSubjectRespVO> convertPage(PageResult<SeedlingSubjectDO> page);

    PageResult<SeedlingSubjectMerchantProfileVO> convertListPage(PageResult<SeedlingSubjectMerchantProfileVO> page);



}
