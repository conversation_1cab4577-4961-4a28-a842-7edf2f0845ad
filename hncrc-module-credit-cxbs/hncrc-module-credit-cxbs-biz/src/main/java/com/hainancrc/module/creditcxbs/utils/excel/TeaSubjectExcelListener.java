package com.hainancrc.module.creditcxbs.utils.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellData;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.module.creditcxbs.api.teasubject.dto.TeaSubjectExportListDTO;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class TeaSubjectExcelListener extends AnalysisEventListener<TeaSubjectExportListDTO> {
    @Override
    public void invoke(TeaSubjectExportListDTO data, AnalysisContext context) {

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    @Override
    public void invokeHead(Map<Integer, CellData> headMap, AnalysisContext context) {
        // 导入数据Excel表头
        List<String> importHeader = getImportExcelHeader(headMap);
        // 模板表头
        List<String> templateHeader = getTemplateHeader();

        if (!importHeader.equals(templateHeader)) {
            throw new RuntimeException("导入失败，模板内容不正确！");
        }
    }

    private List<String> getImportExcelHeader(Map<Integer, CellData> headMap) {
        List<String> importHeader = new ArrayList<>();
        for (Map.Entry<Integer, CellData> entry : headMap.entrySet()) {
            CellData cellData = entry.getValue();
            if (cellData.getType() == CellDataTypeEnum.STRING) {
                importHeader.add(cellData.getStringValue());
            } else {
                importHeader.add(String.valueOf(cellData.getNumberValue()));
            }
        }

        return importHeader;
    }

    private List<String> getTemplateHeader() {
        List<String> header = new ArrayList<>();
        Class<TeaSubjectExportListDTO> clazz = TeaSubjectExportListDTO.class;
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(ExcelProperty.class)) {
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                header.add(excelProperty.value()[0]);
            }
        }
        return header;
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        //throw new ServiceException(-1, exception.getMessage());
        throw new RuntimeException("导入失败，模板内容不正确！");
    }


}
