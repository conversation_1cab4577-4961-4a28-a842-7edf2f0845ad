package com.hainancrc.module.creditcxbs.service.originviewdown;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.originviewdown.dto.OriginViewDownCreateDTO;
import com.hainancrc.module.creditcxbs.api.originviewdown.dto.OriginViewDownPageDTO;
import com.hainancrc.module.creditcxbs.api.originviewdown.dto.OriginViewDownUpdateDTO;
import com.hainancrc.module.creditcxbs.entity.OriginViewDownDO;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-20
 */

public interface OriginViewDownService {

    /**
     * 创建
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    Long create(@Valid OriginViewDownCreateDTO createDTO);


    /**
     * 修改
     *
     * @param updateDTO 更新信息
     */
    void update(@Valid OriginViewDownUpdateDTO updateDTO);


    /**
     * 获得列表
     *
     * @return 列表
     */
    List<OriginViewDownDO> getList();

    /**
     * 获得分页
     *
     * @param pageDTO 分页查询
     * @return 分页
     */
    PageResult<OriginViewDownDO> getPage(OriginViewDownPageDTO pageDTO);


    /**
     * 删除
     *
     * @param id id
     */
    void delete(Long id);

}
