package com.hainancrc.module.creditcxbs.service.businessfunding;

import java.util.*;
import javax.validation.*;
import com.hainancrc.module.creditcxbs.api.businessfunding.dto.*;
import com.hainancrc.module.creditcxbs.api.businessfunding.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;
import org.springframework.web.multipart.MultipartFile;

/**
*  Service 接口
*
*/
public interface BusinessFundingService {
    
    
    /**
    * 创建
    *
    * @param createDTO 创建信息
    * @return 编号
    */
    Long create(@Valid BusinessFundingCreateDTO createDTO);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid BusinessFundingUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    List<BusinessFundingDO> getList();
    
    /**
    * 获得分页
    *
    * @param pageDTO 分页查询
    * @return 分页
    */
    PageResult<BusinessFundingDO> getPage(BusinessFundingPageDTO pageDTO);
    
    
    /**
    * 删除
    *
    * @param id 主键
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id 主键
    * @return
    */
    BusinessFundingDO get(Long id);


    BusinessfundingStatisticsRespVO getStatistics();

    /**
     * 根据金融产品融资信息id获得列表
     *
     * @return 列表
     */
    List<BusinessFundingDO> getListByFinancialProductFundingId(Long id);

}

