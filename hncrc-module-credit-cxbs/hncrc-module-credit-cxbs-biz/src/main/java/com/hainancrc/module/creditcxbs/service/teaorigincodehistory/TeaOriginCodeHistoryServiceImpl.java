package com.hainancrc.module.creditcxbs.service.teaorigincodehistory;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.teaorigincodehistory.dto.*;
import com.hainancrc.module.creditcxbs.convert.teaorigincodehistory.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.teaorigincodehistory.*;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;
/**
*  Service 实现类
*
*/
@Service
@Validated
public class TeaOriginCodeHistoryServiceImpl implements TeaOriginCodeHistoryService {
    
    @Resource
    private TeaOriginCodeHistoryMapper teaOriginCodeHistoryMapper;
    
    
    
    private void validateExists(Long id) {
        if (teaOriginCodeHistoryMapper.selectById(id) == null) {
            throw exception(TEA_ORIGIN_CODE_HISTORY_NOT_EXISTS);
        }
    }
    
    
    @Override
    public Long create(TeaOriginCodeHistoryCreateDTO createDTO) {
        
        
        // 插入
        TeaOriginCodeHistoryDO teaOriginCodeHistory = TeaOriginCodeHistoryConvert.INSTANCE.convert(createDTO);
        teaOriginCodeHistoryMapper.insert(teaOriginCodeHistory);
        // 返回
        return teaOriginCodeHistory.getId();
    }
    
    @Override
    public void update(TeaOriginCodeHistoryUpdateDTO updateDTO) {
        
        
        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        TeaOriginCodeHistoryDO updateObj = TeaOriginCodeHistoryConvert.INSTANCE.convert(updateDTO);
        teaOriginCodeHistoryMapper.updateById(updateObj);
    }
    
    
    @Override
    public PageResult<TeaOriginCodeHistoryDO> getPage(TeaOriginCodeHistoryPageDTO pageDTO) {
        return teaOriginCodeHistoryMapper.selectPage(pageDTO);
    }
    
    @Override
    public List<TeaOriginCodeHistoryDO> getList() {
        return teaOriginCodeHistoryMapper.selectList();
    }

    @Override
    public List<TeaOriginCodeHistoryDO> getListByRecordId(Long teaOriginCodeId) {
        return teaOriginCodeHistoryMapper.selectListByRecordId(teaOriginCodeId);
    }


    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        teaOriginCodeHistoryMapper.deleteById(id);
    }
    
    
    @Override
    public TeaOriginCodeHistoryDO get(Long id) {
        return teaOriginCodeHistoryMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(Long codeRecordId, List<TeaOriginCodeHistoryCreateDTO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return;
        }

        List<TeaOriginCodeHistoryDO> convert = TeaOriginCodeHistoryConvert.INSTANCE.convertToList(recordList);
        for (TeaOriginCodeHistoryDO teaOriginCodeHistoryDO : convert) {
            teaOriginCodeHistoryDO.setCodeRecordId(codeRecordId);
        }
        teaOriginCodeHistoryMapper.insertBatch(convert);
    }


}

