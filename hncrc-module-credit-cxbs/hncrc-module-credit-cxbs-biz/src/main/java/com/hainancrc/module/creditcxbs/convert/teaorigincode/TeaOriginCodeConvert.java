package com.hainancrc.module.creditcxbs.convert.teaorigincode;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.teaorigincode.dto.*;
import com.hainancrc.module.creditcxbs.api.teaorigincode.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface TeaOriginCodeConvert {

    TeaOriginCodeConvert INSTANCE = Mappers.getMapper(TeaOriginCodeConvert.class);


    TeaOriginCodeDO convert(TeaOriginCodeCreateDTO bean);


    TeaOriginCodeDO convert(TeaOriginCodeUpdateDTO bean);


   TeaOriginCodeRespVO convert(TeaOriginCodeDO bean);

    List<TeaOriginCodeRespVO> convertList(List<TeaOriginCodeDO> list);

    PageResult<TeaOriginCodeRespVO> convertPage(PageResult<TeaOriginCodeDO> page);



}
