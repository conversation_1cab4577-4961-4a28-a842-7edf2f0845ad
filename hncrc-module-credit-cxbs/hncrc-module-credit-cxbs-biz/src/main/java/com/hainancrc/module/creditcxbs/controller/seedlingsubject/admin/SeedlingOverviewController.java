package com.hainancrc.module.creditcxbs.controller.seedlingsubject.admin;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.vo.SeedlingOverviewVO;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.vo.SeedlingCreditStatsVO;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.vo.SeedlingSubjectTypeStatsVO;
import com.hainancrc.module.creditcxbs.entity.OriginViewDownDO;
import com.hainancrc.module.creditcxbs.service.seedlingcategory.SeedlingCategoryService;
import com.hainancrc.module.creditcxbs.service.seedlingsubject.SeedlingSubjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@Api(tags = "苗木数据总览")
@RestController
@RequestMapping("/seedling/overview")
public class SeedlingOverviewController {

    @Resource
    private SeedlingSubjectService seedlingSubjectService;
    
    @Resource 
    private SeedlingCategoryService seedlingCategoryService;

    @GetMapping("/statistics")
    @ApiOperation("获取数据总览统计")
    public CommonResult<SeedlingOverviewVO> getStatistics() {
        SeedlingOverviewVO overview = new SeedlingOverviewVO();
        
        // 统计苗木种类数量
        overview.setCategoryCount(seedlingCategoryService.countDistinctCategory());
        
        // 统计苗木品种数量
        overview.setVarietyCount(seedlingCategoryService.countDistinctVariety());

        // 统计入驻主体数量
        overview.setSubjectCount(seedlingSubjectService.totalCount());

        OriginViewDownDO viewDownloadCount = seedlingSubjectService.getViewDownloadCount();

        if (viewDownloadCount != null) {
            // 统计一户一码下载和查看次数
            overview.setOneCodeDownloadCount(viewDownloadCount.getFamilyCodeDownload() == null ? 0 : viewDownloadCount.getFamilyCodeDownload());

            overview.setOneCodeViewCount(viewDownloadCount.getFamilyCodeView() == null ? 0 : viewDownloadCount.getFamilyCodeView());

            // 统计一苗一码下载和查看次数
            overview.setOneSeedCodeDownloadCount(viewDownloadCount.getSeedlingCodeDownload() == null ? 0 : viewDownloadCount.getSeedlingCodeDownload());

            overview.setOneSeedCodeViewCount(viewDownloadCount.getSeedlingCodeView() == null ? 0 : viewDownloadCount.getSeedlingCodeView());

        }

        return CommonResult.success(overview);
    }

    @GetMapping("/creditStats")
    @ApiOperation("苗木企业信用评价指标等级数据分析")
    public CommonResult<SeedlingCreditStatsVO> getCreditStats() {
        return CommonResult.success(seedlingSubjectService.getCreditLevelStats());
    }

    @GetMapping("/subjectTypeStats")
    @ApiOperation("苗木入驻经营主体分析")
    public CommonResult<SeedlingSubjectTypeStatsVO> getSubjectTypeStats() {
        return CommonResult.success(seedlingSubjectService.getSubjectTypeStats());
    }
} 