package com.hainancrc.module.creditcxbs.service.oilteasubsidysubject;

import java.util.*;
import javax.validation.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
*  Service 接口
*
*/
public interface OilTeaSubsidySubjectService {
    
    
    /**
    * 创建
    *
    * @param createDTO 创建信息
    * @return 编号
    */
    Long create(@Valid OilTeaSubsidySubjectCreateDTO createDTO);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid OilTeaSubsidySubjectUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    List<OilTeaSubsidySubjectDO> getList();
    
    /**
    * 获得分页
    *
    * @param pageDTO 分页查询
    * @return 分页
    */
    PageResult<OilTeaSubsidySubjectDO> getPage(OilTeaSubsidySubjectPageDTO pageDTO);
    
    
    /**
    * 删除
    *
    * @param id 主键
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id 主键
    * @return
    */
    OilTeaSubsidySubjectDO get(Long id);

    /**
     * 批量保存油茶补贴主体信息
     * @param noteId 补贴公示信息id
     * @param excelDTOS 补贴主体信息
     */
    void saveBatch(Long noteId, List<OilTeaSubsidySubjectExcelDTO> excelDTOS);


    /**
     * 根据补贴公示信息id查询补贴主体信息
     * @param noteId 补贴公示信息id
     * @return 补贴主体信息
     */
    List<OilTeaSubsidySubjectDO> getSubsidySubjectList(Long noteId);

    /**
     * 查询补贴主体名单
     * @return 补贴主体名单
     */
    List<OilTeaSubsidySubjectDO> SubsidySubjectList();
}

