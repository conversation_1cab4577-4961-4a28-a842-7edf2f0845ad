package com.hainancrc.module.creditcxbs.utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

public class HmacAuthUtil {
    private static final String ALGORITHM = "HmacSHA256";

    public static String generateHmacSignature(String secretKey, String message) throws NoSuchAlgorithmException, InvalidKeyException {
        SecretKeySpec signingKey = new SecretKeySpec(secretKey.getBytes(), ALGORITHM);
        Mac mac = Mac.getInstance(ALGORITHM);
        mac.init(signingKey);
        byte[] rawHmac = mac.doFinal(message.getBytes());
        return Base64.getEncoder().encodeToString(rawHmac);
    }

    public static String generateSignStr(String method, String url, String accessKey, String timestamp) throws MalformedURLException {
        URL urlObj = new URL(url);
        String urlPath = urlObj.getPath();
        String urlQuery = urlObj.getQuery();
        String sortedQuery = "";
        if (urlQuery != null && !urlQuery.isEmpty()) {
            // 拆分查询字符串为键值对数组
            String[] keyValuePairs = urlQuery.split("&");

            // 创建一个列表，用于存储键值对
            List<String> sortedPairs = new ArrayList<>(Arrays.asList(keyValuePairs));

            // 对键值对进行排序
            Collections.sort(sortedPairs, new Comparator<String>() {
                public int compare(String pair1, String pair2) {
                    String key1 = pair1.split("=")[0];
                    String key2 = pair2.split("=")[0];
                    return key1.compareTo(key2);
                }
            });

            // 重新组合排序后的键值对为查询字符串
            sortedQuery = String.join("&", sortedPairs);
        }

        List<String> stringList = new ArrayList<>();
        stringList.add(method);
        stringList.add(urlPath);
        stringList.add(sortedQuery);
        stringList.add(accessKey);
        stringList.add(timestamp);
        String signStr = String.join("\n", stringList) + "\n";
        return signStr;
    }

}
