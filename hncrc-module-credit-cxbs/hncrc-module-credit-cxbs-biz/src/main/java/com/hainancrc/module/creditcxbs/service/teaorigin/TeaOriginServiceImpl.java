package com.hainancrc.module.creditcxbs.service.teaorigin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.enums.TeaOriginStatus;
import com.hainancrc.module.creditcxbs.api.teaorigin.dto.*;
import com.hainancrc.module.creditcxbs.api.teaorigin.vo.ApplySubjectPageRespVO;
import com.hainancrc.module.creditcxbs.api.teaorigin.vo.TeaOriginListVO;
import com.hainancrc.module.creditcxbs.api.teaorigin.vo.TeaOriginPageRespVO;
import com.hainancrc.module.creditcxbs.api.teaorigin.vo.TeaOriginRespVO;
import com.hainancrc.module.creditcxbs.convert.teaorigin.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.teaorigin.*;

import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;
/**
*  Service 实现类
*
*/
@Service
@Validated
public class TeaOriginServiceImpl implements TeaOriginService {
    
    @Resource
    private TeaOriginMapper teaOriginMapper;
    
    
    
    private void validateExists(Long id) {
        if (teaOriginMapper.selectById(id) == null) {
            throw exception(TEA_ORIGIN_NOT_EXISTS);
        }
    }
    
    
    @Override
    public Long create(TeaOriginCreateDTO createDTO) {

        if (teaOriginMapper.exists(new LambdaQueryWrapper<TeaOriginDO>()
                .eq(TeaOriginDO::getTeaEstate, createDTO.getTeaEstate()))) {
            throw new ServiceException(-1, "茶园名字已存在");
        }
        
        createDTO.setOriginPic(StringEscapeUtils.unescapeHtml(createDTO.getOriginPic()));
        // 插入
        TeaOriginDO teaOrigin = TeaOriginConvert.INSTANCE.convert(createDTO);
        teaOrigin.setOriginStatus(TeaOriginStatus.ONLINE.name());
        teaOriginMapper.insert(teaOrigin);
        // 返回
        return teaOrigin.getId();
    }
    
    @Override
    public void update(TeaOriginUpdateDTO updateDTO) {
        
        
        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        TeaOriginDO updateObj = TeaOriginConvert.INSTANCE.convert(updateDTO);
        teaOriginMapper.updateById(updateObj);
    }
    
    
    @Override
    public PageResult<TeaOriginPageRespVO> getPage(TeaOriginPageDTO pageDTO) {
        Page<TeaOriginPageRespVO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        IPage<TeaOriginPageRespVO> pageResult = teaOriginMapper.selectPage(page, pageDTO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }
    
    @Override
    public List<TeaOriginDO> getList() {
        return teaOriginMapper.selectTeaOriginList();
    }
    
    
    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        teaOriginMapper.deleteById(id);
    }
    
    
    @Override
    public TeaOriginDO get(Long id) {
        return teaOriginMapper.selectById(id);
    }

    @Override
    public PageResult<ApplySubjectPageRespVO> getApplyPage(TeaOriginPageDTO pageDTO) {
        Page<ApplySubjectPageRespVO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        IPage<ApplySubjectPageRespVO> pageResult = teaOriginMapper.selectApplyPage(page, pageDTO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public List<TeaOriginListVO> getOriginList() {
        List<TeaOriginListVO> respVOList = new ArrayList<>();
        List<TeaOriginDO> list = this.getList();
        List<TeaOriginRespVO> convertList = TeaOriginConvert.INSTANCE.convertList(list);

        Map<String, List<TeaOriginRespVO>> collect = convertList.stream()
                .collect(Collectors.groupingBy(TeaOriginRespVO::getOriginName));

        if (!CollectionUtils.isEmpty(collect)) {
            for (Map.Entry<String, List<TeaOriginRespVO>> entry : collect.entrySet()) {
                TeaOriginListVO respVO = new TeaOriginListVO();
                respVO.setOriginName(entry.getKey());
                respVO.setOriginList(entry.getValue());
                respVOList.add(respVO);
            }
        }

        return respVOList;
    }


}

