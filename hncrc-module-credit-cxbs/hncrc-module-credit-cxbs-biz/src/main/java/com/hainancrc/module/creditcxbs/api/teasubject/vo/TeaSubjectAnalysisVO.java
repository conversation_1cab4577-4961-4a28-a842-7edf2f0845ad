package com.hainancrc.module.creditcxbs.api.teasubject.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@ApiModel("茶叶主体分析 Response VO")
@Data
public class TeaSubjectAnalysisVO {

    @ApiModelProperty(value = "企业数量")
    private Long enterpriseCount;

    @ApiModelProperty(value = "企业占比")
    private String enterprisePercentage;

    @ApiModelProperty(value = "合作社数量")
    private Long cooperativeCount;

    @ApiModelProperty(value = "合作社占比")
    private String cooperativePercentage;

    @ApiModelProperty(value = "个体工商户数量")
    private Long planterCount;

    @ApiModelProperty(value = "个体工商户占比")
    private String planterPercentage;

    @ApiModelProperty(value = "农户数量")
    private Long farmerCount;

    @ApiModelProperty(value = "农户占比")
    private String farmerPercentage;
} 