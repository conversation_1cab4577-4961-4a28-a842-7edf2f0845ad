package com.hainancrc.module.creditcxbs.service.seedlingsubjectcategory;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.seedlingsubjectcategory.dto.*;
import com.hainancrc.module.creditcxbs.convert.seedlingsubjectcategory.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.seedlingsubjectcategory.*;

import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;
/**
*  Service 实现类
*
*/
@Service
@Validated
public class SeedlingSubjectCategoryServiceImpl implements SeedlingSubjectCategoryService {
    
    @Resource
    private SeedlingSubjectCategoryMapper seedlingSubjectCategoryMapper;
    
    
    
    private void validateExists(Long id) {
        if (seedlingSubjectCategoryMapper.selectById(id) == null) {
            throw exception(SEEDLING_SUBJECT_CATEGORY_NOT_EXISTS);
        }
    }
    
    
    @Override
    public Long create(SeedlingSubjectCategoryCreateDTO createDTO) {
        
        
        // 插入
        SeedlingSubjectCategoryDO seedlingSubjectCategory = SeedlingSubjectCategoryConvert.INSTANCE.convert(createDTO);
        seedlingSubjectCategoryMapper.insert(seedlingSubjectCategory);
        // 返回
        return seedlingSubjectCategory.getId();
    }
    
    @Override
    public void update(SeedlingSubjectCategoryUpdateDTO updateDTO) {
        
        
        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        SeedlingSubjectCategoryDO updateObj = SeedlingSubjectCategoryConvert.INSTANCE.convert(updateDTO);
        seedlingSubjectCategoryMapper.updateById(updateObj);
    }
    
    
    @Override
    public PageResult<SeedlingSubjectCategoryDO> getPage(SeedlingSubjectCategoryPageDTO pageDTO) {
        return seedlingSubjectCategoryMapper.selectPage(pageDTO);
    }
    
    @Override
    public List<SeedlingSubjectCategoryDO> getList() {
        return seedlingSubjectCategoryMapper.selectList();
    }
    
    
    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        seedlingSubjectCategoryMapper.deleteById(id);
    }
    
    
    @Override
    public SeedlingSubjectCategoryDO get(Long id) {
        return seedlingSubjectCategoryMapper.selectById(id);
    }



}

