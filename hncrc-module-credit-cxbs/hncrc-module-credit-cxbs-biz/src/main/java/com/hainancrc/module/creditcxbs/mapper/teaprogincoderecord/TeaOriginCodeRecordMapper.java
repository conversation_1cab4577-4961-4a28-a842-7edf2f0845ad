package com.hainancrc.module.creditcxbs.mapper.teaprogincoderecord;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordPageRespVO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.dto.TeaOriginCodeRecordPageDTO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordDetailRespVO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordRespVO;
import com.hainancrc.module.creditcxbs.entity.TeaOriginCodeRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface TeaOriginCodeRecordMapper extends BaseMapperX<TeaOriginCodeRecordDO> {
    IPage<TeaOriginCodeRecordPageRespVO> selectRecordPage(@Param("page") Page<TeaOriginCodeRecordPageRespVO> page,
                                                          @Param("dto") TeaOriginCodeRecordPageDTO pageDTO);

    IPage<TeaOriginCodeRecordDetailRespVO> selectApplyRecordPage(@Param("page") Page<TeaOriginCodeRecordDetailRespVO> page,
                                                                 @Param("dto") TeaOriginCodeRecordPageDTO pageDTO);

    TeaOriginCodeRecordRespVO selectByRecordId(Long id);

    List<Map<String, Object>> selectMaps(@Param("codeIds") List<Long> codeIds);

}
