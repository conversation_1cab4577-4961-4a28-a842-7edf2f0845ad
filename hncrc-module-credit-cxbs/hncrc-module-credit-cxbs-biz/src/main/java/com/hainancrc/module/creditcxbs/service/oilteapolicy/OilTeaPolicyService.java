package com.hainancrc.module.creditcxbs.service.oilteapolicy;

import java.util.*;
import javax.validation.*;
import com.hainancrc.module.creditcxbs.api.oilteapolicy.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteapolicy.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
*  Service 接口
*
*/
public interface OilTeaPolicyService {
    
    
    /**
    * 创建
    *
    * @param createDTO 创建信息
    * @return 编号
    */
    Long create(@Valid OilTeaPolicyCreateDTO createDTO);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid OilTeaPolicyUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    List<OilTeaPolicyDO> getList();
    
    /**
    * 获得分页
    *
    * @param pageDTO 分页查询
    * @return 分页
    */
    PageResult<OilTeaPolicyDO> getPage(OilTeaPolicyPageDTO pageDTO);
    
    
    /**
    * 删除
    *
    * @param id 主键
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id 主键
    * @return
    */
    OilTeaPolicyDO get(Long id);
    
    
}

