package com.hainancrc.module.creditcxbs.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

@TableName("code_ca16")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CodeCa16DO {

    private Integer id;

    private String code;

    private String parent;

    private String name;

    private String level;

    private String fulltitle;
}
