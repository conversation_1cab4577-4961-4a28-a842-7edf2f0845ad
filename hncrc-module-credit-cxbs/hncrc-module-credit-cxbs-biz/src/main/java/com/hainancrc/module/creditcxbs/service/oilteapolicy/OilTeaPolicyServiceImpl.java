package com.hainancrc.module.creditcxbs.service.oilteapolicy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaPolicyStatus;
import com.hainancrc.module.creditcxbs.api.oilteapolicy.dto.*;
import com.hainancrc.module.creditcxbs.convert.oilteapolicy.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.oilteapolicy.*;

import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;
/**
*  Service 实现类
*
*/
@Service
@Validated
public class OilTeaPolicyServiceImpl implements OilTeaPolicyService {
    
    @Resource
    private OilTeaPolicyMapper oilTeaPolicyMapper;
    
    
    
    private void validateExists(Long id) {
        if (oilTeaPolicyMapper.selectById(id) == null) {
            throw exception(OIL_TEA_POLICY_NOT_EXISTS);
        }
    }
    
    
    @Override
    public Long create(OilTeaPolicyCreateDTO createDTO) {
        
        
        // 插入
        OilTeaPolicyDO oilTeaPolicy = OilTeaPolicyConvert.INSTANCE.convert(createDTO);
        oilTeaPolicy.setPolicyStatus(OilTeaPolicyStatus.ONLINE);
        oilTeaPolicyMapper.insert(oilTeaPolicy);
        // 返回
        return oilTeaPolicy.getId();
    }
    
    @Override
    public void update(OilTeaPolicyUpdateDTO updateDTO) {
        
        
        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        OilTeaPolicyDO updateObj = OilTeaPolicyConvert.INSTANCE.convert(updateDTO);
        oilTeaPolicyMapper.updateById(updateObj);
    }
    
    
    @Override
    public PageResult<OilTeaPolicyDO> getPage(OilTeaPolicyPageDTO pageDTO) {
        Page<OilTeaPolicyDO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        IPage<OilTeaPolicyDO> pageResult = oilTeaPolicyMapper.selectPage(page, pageDTO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }
    
    @Override
    public List<OilTeaPolicyDO> getList() {
        return oilTeaPolicyMapper.selectOilTeaPolicyList();
    }
    
    
    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        oilTeaPolicyMapper.deleteById(id);
    }
    
    
    @Override
    public OilTeaPolicyDO get(Long id) {
        return oilTeaPolicyMapper.selectById(id);
    }
    
    
    
}

