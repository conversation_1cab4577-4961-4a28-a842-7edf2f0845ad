package com.hainancrc.module.creditcxbs.mapper.teaorigincodehistory;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.teaorigincodehistory.dto.*;

import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface TeaOriginCodeHistoryMapper extends BaseMapperX<TeaOriginCodeHistoryDO> {
    
    
    PageResult<TeaOriginCodeHistoryDO> selectPage(@Param("dto") TeaOriginCodeHistoryPageDTO reqDTO);

    default List<TeaOriginCodeHistoryDO> selectListByRecordId(Long codeRecordId) {
        return selectList(new LambdaQueryWrapperX<TeaOriginCodeHistoryDO>()
                .eq(TeaOriginCodeHistoryDO::getCodeRecordId, codeRecordId)
                .orderByDesc(TeaOriginCodeHistoryDO::getCreateTime));
    };
}

