package com.hainancrc.module.creditcxbs.controller.cxbsmodelcontroller.admin;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.creditcxbs.service.model.CxbsModelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-22
 */

@Api(tags = "诚信白沙模型接口")
@RestController
@RequestMapping("/cxbsModel")
@Validated
public class CxbsModelController {

    @Resource
    private CxbsModelService cxbsModelService;

    @PostMapping("/getCompanies")
    @ApiOperation("获取所有企业评分信息")
    public CommonResult<Long> getCompaniesPoint() {
        return CommonResult.success(cxbsModelService.getCompanies());
    }

    @PostMapping("/getCompanyPoint")
    @ApiOperation("获取单个企业评分")
    public CommonResult<Boolean> getCompanyPoint(@RequestParam("creditCode") String creditCode) {
        return CommonResult.success(cxbsModelService.getCompaniesPoint(creditCode));
    }

}
