package com.hainancrc.module.creditcxbs.entity;

import java.math.BigDecimal;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaSubsidyPublicNoteFileStatus;


/**
 * 油茶补贴公示信息 DO
 */
@TableName("cxbs_oil_tea_subsidy_public_note")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OilTeaSubsidyPublicNoteDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 补贴公式文件名称
     */
    private String publicFileName;

    /**
     * 补贴公示文件url
     */
    private String publicFileUrl;

    /**
     * 补贴公示文件key
     */
    private String publicFileKey;

    /**
     * 文件状态(ENUMS:ONLINE-公示中,OFFLINE-已下线)(列表,新增,编辑)ONLINE=公示中,OFFLINE=已下线
     */
    private OilTeaSubsidyPublicNoteFileStatus fileStatus;


}
