package com.hainancrc.module.creditcxbs.service.oilteasubsidypublicnote;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaSubsidyPublicNoteFileStatus;
import com.hainancrc.module.creditcxbs.api.oilteasubsidypublicnote.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.dto.OilTeaSubsidySubjectExcelDTO;
import com.hainancrc.module.creditcxbs.convert.oilteasubsidypublicnote.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.oilteasubsidypublicnote.*;

import com.hainancrc.module.creditcxbs.mapper.oilteasubsidysubject.OilTeaSubsidySubjectMapper;
import com.hainancrc.module.creditcxbs.service.oilteasubsidysubject.OilTeaSubsidySubjectService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;
/**
*  Service 实现类
*
*/
@Service
@Validated
public class OilTeaSubsidyPublicNoteServiceImpl implements OilTeaSubsidyPublicNoteService {
    
    @Resource
    private OilTeaSubsidyPublicNoteMapper oilTeaSubsidyPublicNoteMapper;

    @Resource
    private OilTeaSubsidySubjectService oilTeaSubsidySubjectService;
    
    
    
    private void validateExists(Long id) {
        if (oilTeaSubsidyPublicNoteMapper.selectById(id) == null) {
            throw exception(OIL_TEA_SUBSIDY_PUBLIC_NOTE_NOT_EXISTS);
        }
    }
    
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(OilTeaSubsidyPublicNoteCreateDTO createDTO, List<OilTeaSubsidySubjectExcelDTO> excelDTOS) {
        
        
        // 插入
        OilTeaSubsidyPublicNoteDO oilTeaSubsidyPublicNote = OilTeaSubsidyPublicNoteConvert.INSTANCE.convert(createDTO);
        oilTeaSubsidyPublicNote.setFileStatus(OilTeaSubsidyPublicNoteFileStatus.ONLINE);
        oilTeaSubsidyPublicNoteMapper.insert(oilTeaSubsidyPublicNote);
        oilTeaSubsidySubjectService.saveBatch(oilTeaSubsidyPublicNote.getId(), excelDTOS);

        // 返回
        return oilTeaSubsidyPublicNote.getId();
    }
    
    @Override
    public void update(OilTeaSubsidyPublicNoteUpdateDTO updateDTO) {
        
        
        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        OilTeaSubsidyPublicNoteDO updateObj = OilTeaSubsidyPublicNoteConvert.INSTANCE.convert(updateDTO);
        oilTeaSubsidyPublicNoteMapper.updateById(updateObj);
    }
    
    
    @Override
    public PageResult<OilTeaSubsidyPublicNoteDO> getPage(OilTeaSubsidyPublicNotePageDTO pageDTO) {
        return oilTeaSubsidyPublicNoteMapper.selectPage(pageDTO);
    }
    
    @Override
    public List<OilTeaSubsidyPublicNoteDO> getList() {
        return oilTeaSubsidyPublicNoteMapper.selectList();
    }
    
    
    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        oilTeaSubsidyPublicNoteMapper.deleteById(id);
    }
    
    
    @Override
    public OilTeaSubsidyPublicNoteDO get(Long id) {
        return oilTeaSubsidyPublicNoteMapper.selectById(id);
    }
    
    
    
}

