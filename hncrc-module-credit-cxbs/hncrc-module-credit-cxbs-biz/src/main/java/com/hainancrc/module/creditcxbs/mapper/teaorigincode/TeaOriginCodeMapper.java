package com.hainancrc.module.creditcxbs.mapper.teaorigincode;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.module.creditcxbs.api.teaorigincode.dto.TeaOriginCodePageDTO;
import com.hainancrc.module.creditcxbs.api.teaorigincode.vo.TeaOriginCodePageRespVO;
import com.hainancrc.module.creditcxbs.entity.TeaOriginCodeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TeaOriginCodeMapper extends BaseMapperX<TeaOriginCodeDO> {
    IPage<TeaOriginCodePageRespVO> selectOriginCodePage(@Param("page") Page<TeaOriginCodePageRespVO> page,
                                                        @Param("dto") TeaOriginCodePageDTO reqDTO);

    Long selectTeaGardenCount();
    
    Long selectApplySubjectCount();
    
    Long selectTeaLeafAmount();
    
    Long selectOriginCodeApplyCount();
    
    Long selectOriginCodeViewCount();
}

