package com.hainancrc.module.creditcxbs.entity;

import java.math.BigDecimal;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 茶叶经营主体信息 DO
 */
@TableName("cxbs_tea_subject")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeaSubjectDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 商户名称(主体名称)
     */
    private String subjectName;

    /**
     * 主题类型
     */
    private String subjectType;

    /**
     * 统一信用代码
     */
    private String uniscid;

    /**
     * 法定代表人
     */
    private String legalName;

    /**
     * 地址
     */
    private String subjectAddress;

    /**
     * 所属乡镇
     */
    private String belongTownship;

    /**
     * 联系电话
     */
    private String telephone;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 种植面积
     */
    private BigDecimal plantingArea;

    /**
     * 茶叶品种
     */
    private String teaType;

    /**
     * 茶青产量(吨)
     */
    private BigDecimal teaYield;

    /**
     * 主体简介
     */
    private String introduction;

    /**
     * 企业头像
     */
    private String avatar;

    /**
     * 购买链接
     */
    private String purchaseLink;

    /**
     * 购买二维码
     */
    private String purchaseQrcode;

}
