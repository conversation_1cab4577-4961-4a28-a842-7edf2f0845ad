package com.hainancrc.module.creditcxbs.mapper.originviewdown;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.originviewdown.dto.OriginViewDownPageDTO;
import com.hainancrc.module.creditcxbs.entity.OriginViewDownDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-20
 */

@Mapper
public interface OriginViewDownMapper extends BaseMapperX<OriginViewDownDO> {

    PageResult<OriginViewDownDO> selectPage(@Param("dto") OriginViewDownPageDTO reqDTO);

    default OriginViewDownDO selectView(Long companyId, String viewDate){
        return selectOne(new LambdaQueryWrapperX<OriginViewDownDO>()
                                 .eq(OriginViewDownDO::getCompanyId, companyId)
                                 .between(OriginViewDownDO::getViewDate, viewDate + " 00:00:00", viewDate + " 23:59:59"));
    }

    Long selectFamilyCodeViewCount();

    Long selectFamilyCodeDownload();

    Long selectSeedlingCodeDownload();

    Long selectSeedlingCodeViewCount();

    OriginViewDownDO getViewDownloadCount();
}
