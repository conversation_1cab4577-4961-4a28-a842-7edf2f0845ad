package com.hainancrc.module.creditcxbs.service.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hainancrc.module.creditcxbs.api.model.dto.PointModelDTO;
import com.hainancrc.module.creditcxbs.emuns.LevelEnum;
import com.hainancrc.module.creditcxbs.entity.SeedlingSubjectDO;
import com.hainancrc.module.creditcxbs.mapper.seedlingsubject.SeedlingSubjectMapper;
import com.hainancrc.module.creditcxbs.utils.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.COMPANY_NOT_EXISTS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-22
 */

@Service
@Validated
@Slf4j
public class CxbsModelServiceImpl implements CxbsModelService {

    @Resource
    private SeedlingSubjectMapper seedlingSubjectMapper;

    @Override
    public Long getCompanies() {
        LambdaQueryWrapper<SeedlingSubjectDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(SeedlingSubjectDO::getId);
        List<SeedlingSubjectDO> companyDOS = seedlingSubjectMapper.selectList(wrapper);

        Long successCount = 0L;

        for (SeedlingSubjectDO companyDO : companyDOS) {
            getCompaniesPoint(companyDO.getUniscid());
            successCount++;
        }

        return successCount;
    }

    @Override
    public Boolean getCompaniesPoint(String creditCode) {
//        SeedlingSubjectDO companyDO = seedlingSubjectMapper.selectByCreditCode(creditCode);
//        if (companyDO == null) {
//            throw exception(COMPANY_NOT_EXISTS);
//        }
//
//        Map<String, String> params = new HashMap<>();
//        params.put("uniscid", companyDO.getUniscid());
//        params.put("entName", companyDO.getSubjectName());
//
//        PointModelDTO model= null;
//        JSONObject dataPointModel = null;
//
//        log.info("入参：{}", params);
//
//        // 发送请求
//        try {
//            String result = HttpClientUtil.doPost4Json(pointModelUrl, params);
//            log.info("评分模型结果，{}", result);
//
//            dataPointModel = JSON.parseObject(result).getJSONObject("data");
//
//            if (dataPointModel != null) {
//                JSONObject pointModel = dataPointModel.getJSONObject("pointModel");
//                model = JSON.toJavaObject(pointModel, PointModelDTO.class);
//                log.info("model:{}", model);
//            }
//
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//
//
//        if (model != null) {
//            Float totalPoint = model.getTotalPoint() == null ? 0F : model.getTotalPoint();
//            String level = model.getLevel() == null ? "A" : model.getLevel();
//            if (LevelEnum.D.getCode().equals(level)) {
//                totalPoint = 0F;
//            } else {
//                if (totalPoint >= 120) {
//                    level = LevelEnum.A.getCode();
//                } else if (totalPoint >= 105) {
//                    level = LevelEnum.B.getCode();
//                } else if (totalPoint >= 90) {
//                    level = LevelEnum.C.getCode();
//                } else {
//                    level = LevelEnum.D.getCode();
//                }
//            }
//
//            if(totalPoint < 0) {
//                totalPoint = 0F;
//            }
//
//            companyDO.setCreditLevel(level);
//            companyDO.setEvaluationScore(totalPoint.toString());
//            companyDO.setUpdateTime(new Date());
//            seedlingSubjectMapper.updateById(companyDO);
//        }

        return true;
    }
}
