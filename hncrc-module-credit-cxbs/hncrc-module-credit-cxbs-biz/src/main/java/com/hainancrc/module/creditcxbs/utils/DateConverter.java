package com.hainancrc.module.creditcxbs.utils;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.hainancrc.framework.common.exception.ServiceException;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;

public class DateConverter implements Converter<Date> {

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public Class supportJavaTypeKey() {
        return null;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Date convertToJavaData(CellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {

        if (cellData.getType() == CellDataTypeEnum.STRING) {
            return dateFormat.parse(cellData.getStringValue());
        } else if (cellData.getType() == CellDataTypeEnum.NUMBER) {
            return convertExcelNumericToDate(cellData.getNumberValue().toString());
        }

        return null;
    }

    @Override
    public CellData convertToExcelData(Date value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }

    private Date convertExcelNumericToDate(String excelNumericDate) {
        long daysSince1900 = Long.parseLong(excelNumericDate);
        long millisecondsSinceEpoch = (daysSince1900 - 2) * 24 * 60 * 60 * 1000L; // Adjusting for Excel's offset
        return new Date(millisecondsSinceEpoch);
    }
}
