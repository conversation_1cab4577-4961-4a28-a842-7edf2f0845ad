package com.hainancrc.module.creditcxbs.service.financialpolicy;

import java.util.*;
import javax.validation.*;
import com.hainancrc.module.creditcxbs.api.financialpolicy.dto.*;
import com.hainancrc.module.creditcxbs.api.financialpolicy.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
*  Service 接口
*
*/
public interface FinancialPolicyService {
    
    
    /**
    * 创建
    *
    * @param createDTO 创建信息
    * @return 编号
    */
    Long create(@Valid FinancialPolicyCreateDTO createDTO);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid FinancialPolicyUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    List<FinancialPolicyDO> getList();
    
    /**
    * 获得分页
    *
    * @param pageDTO 分页查询
    * @return 分页
    */
    PageResult<FinancialPolicyDO> getPage(FinancialPolicyPageDTO pageDTO);
    
    
    /**
    * 删除
    *
    * @param id 主键id
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id 主键id
    * @return
    */
    FinancialPolicyDO get(Long id);


    /**
     * 获取金融政策列表
     * @return
     */
    List<FinancialPolicyDO> getFinancialPolicyList();


    List<FinancialPolicyDO> getListByPolicyName(String policyName);
}

