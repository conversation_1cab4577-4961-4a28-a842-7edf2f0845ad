package com.hainancrc.module.creditcxbs.service.teasubject;

import java.io.IOException;
import java.util.*;
import javax.validation.*;

import com.hainancrc.module.creditcxbs.api.teaorigin.vo.TeaOriginRespVO;
import com.hainancrc.module.creditcxbs.api.teasubject.dto.*;
import com.hainancrc.module.creditcxbs.api.teasubject.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;
import org.springframework.web.multipart.MultipartFile;

/**
*  Service 接口
*
*/
public interface TeaSubjectService {
    
    
    /**
    * 创建
    *
    * @param createDTO 创建信息
    * @return 编号
    */
    Long create(@Valid TeaSubjectCreateDTO createDTO);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid TeaSubjectUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    List<TeaSubjectDO> getList();
    
    /**
    * 获得分页
    *
    * @param pageDTO 分页查询
    * @return 分页
    */
    PageResult<TeaSubjectDO> getPage(TeaSubjectPageDTO pageDTO);
    
    
    /**
    * 删除
    *
    * @param id 主键
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id 主键
    * @return
    */
    TeaSubjectDO get(Long id);
    
    /**
     * 获取茶叶统计信息
     * @return 统计信息
     */
    TeaStatisticsRespVO getStatistics();
    
    /**
     * 获取茶叶主体分析数据
     */
    TeaSubjectAnalysisVO getAnalysis();

    /**
     * 茶叶产地主体申领数据分析
     * @param dto
     * @return
     */
    List<TeaSubjectDataVO> listByTeaSubjectData(DataOverViewSelectDTO dto);
    /**
     * 茶叶产地主体申领数据分析2
     * @param dto
     * @return
     */
    List<TeaSubjectDataVO> listByTeaSubjectData2(DataOverViewSelectDTO dto);

    /**
     * 导入茶叶经营主体
     * @param file
     * @return
     */
    String importFile(MultipartFile file) throws IOException;

    /**
     * 获取茶叶经营主体列表
     * @return
     */
    List<TeaSubjectRespVO> getTeaSubjectList();

    /**
     * 获取茶叶产地主体信息
     * @param originName
     * @param id
     * @return
     */
    TeaOriginRespVO getTeaInfo(String originName, String id);
}

