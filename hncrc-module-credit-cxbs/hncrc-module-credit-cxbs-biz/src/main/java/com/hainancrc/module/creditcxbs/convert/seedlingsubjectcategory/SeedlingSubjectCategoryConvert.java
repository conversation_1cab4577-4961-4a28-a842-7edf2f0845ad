package com.hainancrc.module.creditcxbs.convert.seedlingsubjectcategory;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.seedlingsubjectcategory.dto.*;
import com.hainancrc.module.creditcxbs.api.seedlingsubjectcategory.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface SeedlingSubjectCategoryConvert {

    SeedlingSubjectCategoryConvert INSTANCE = Mappers.getMapper(SeedlingSubjectCategoryConvert.class);


    SeedlingSubjectCategoryDO convert(SeedlingSubjectCategoryCreateDTO bean);


    SeedlingSubjectCategoryDO convert(SeedlingSubjectCategoryUpdateDTO bean);


   SeedlingSubjectCategoryRespVO convert(SeedlingSubjectCategoryDO bean);

    List<SeedlingSubjectCategoryRespVO> convertList(List<SeedlingSubjectCategoryDO> list);

    PageResult<SeedlingSubjectCategoryRespVO> convertPage(PageResult<SeedlingSubjectCategoryDO> page);



}
