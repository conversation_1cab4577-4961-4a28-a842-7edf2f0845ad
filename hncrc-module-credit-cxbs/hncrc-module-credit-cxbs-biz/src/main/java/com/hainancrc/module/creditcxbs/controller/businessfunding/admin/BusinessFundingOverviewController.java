package com.hainancrc.module.creditcxbs.controller.businessfunding.admin;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.creditcxbs.api.businessfunding.vo.BusinessfundingStatisticsRespVO;
import com.hainancrc.module.creditcxbs.service.businessfunding.BusinessFundingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-20
 */

@Api(tags = "经营主体融资数据总览")
@RestController
@RequestMapping("/businessfunding/overview")
@Validated
public class BusinessFundingOverviewController {

    @Resource
    private BusinessFundingService businessFundingService;

    @GetMapping("/statistics")
    @ApiOperation("融资主体统计信息")
    public CommonResult<BusinessfundingStatisticsRespVO> getStatistics() {
        return success(businessFundingService.getStatistics());
    }
}
