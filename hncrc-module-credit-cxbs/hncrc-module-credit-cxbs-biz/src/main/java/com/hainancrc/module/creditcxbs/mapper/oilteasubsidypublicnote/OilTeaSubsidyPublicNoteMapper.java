package com.hainancrc.module.creditcxbs.mapper.oilteasubsidypublicnote;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidypublicnote.dto.*;

import org.apache.ibatis.annotations.*;

@Mapper
public interface OilTeaSubsidyPublicNoteMapper extends BaseMapperX<OilTeaSubsidyPublicNoteDO> {
    
    
    default PageResult<OilTeaSubsidyPublicNoteDO> selectPage(OilTeaSubsidyPublicNotePageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<OilTeaSubsidyPublicNoteDO>()
                .likeIfPresent(OilTeaSubsidyPublicNoteDO::getPublicFileName, reqDTO.getPublicFileName())
                .orderByDesc(OilTeaSubsidyPublicNoteDO::getCreateTime));
    };
}

