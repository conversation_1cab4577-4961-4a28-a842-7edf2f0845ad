package com.hainancrc.module.creditcxbs.service.businessfundingview;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.businessfundingview.dto.BusinessFundingViewCreateDTO;
import com.hainancrc.module.creditcxbs.api.businessfundingview.dto.BusinessFundingViewPageDTO;
import com.hainancrc.module.creditcxbs.api.businessfundingview.dto.BusinessFundingViewUpdateDTO;
import com.hainancrc.module.creditcxbs.convert.businessfundingview.BusinessFundingViewConvert;
import com.hainancrc.module.creditcxbs.entity.BusinessFundingViewDO;
import com.hainancrc.module.creditcxbs.mapper.businessfundingview.BusinessFundingViewMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-23
 */

@Service
public class BusinessFundingViewServiceImpl implements BusinessFundingViewService {

    @Resource
    private BusinessFundingViewMapper businessFundingViewMapper;

    @Override
    public Long create(BusinessFundingViewCreateDTO createDTO) {

        //查询当天是否有查看记录
        Date date = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String viewDate = formatter.format(date);
        BusinessFundingViewDO businessFundingViewDO = businessFundingViewMapper.selectReportView(createDTO.getCompanyId(), viewDate);
        if (businessFundingViewDO == null) {
            BusinessFundingViewDO businessFundingViewDO1 = new BusinessFundingViewDO();
            businessFundingViewDO1.setCompanyId(createDTO.getCompanyId());
            businessFundingViewDO1.setViewDate(new Date());
            businessFundingViewMapper.insert(businessFundingViewDO1);
            return businessFundingViewDO1.getId();
        }
        recordViewCount(createDTO, businessFundingViewDO);
        return businessFundingViewDO.getId();
    }

    private void recordViewCount(BusinessFundingViewCreateDTO createDTO, BusinessFundingViewDO businessFundingViewDO) {
        businessFundingViewDO.setViewCount(businessFundingViewDO.getViewCount() + 1);
        businessFundingViewDO.setCompanyId(createDTO.getCompanyId());
        businessFundingViewDO.setViewDate(new Date());
        businessFundingViewMapper.updateById(businessFundingViewDO);
    }
    @Override
    public void update(BusinessFundingViewUpdateDTO updateDTO) {
        // 更新
        BusinessFundingViewDO updateObj = BusinessFundingViewConvert.INSTANCE.convert(updateDTO);
        businessFundingViewMapper.updateById(updateObj);
    }

    @Override
    public List<BusinessFundingViewDO> getList() {
        return businessFundingViewMapper.selectList();
    }

    @Override
    public PageResult<BusinessFundingViewDO> getPage(BusinessFundingViewPageDTO pageDTO) {
        return businessFundingViewMapper.selectPage(pageDTO);
    }

    @Override
    public void delete(Long id) {
        businessFundingViewMapper.deleteById(id);
    }

    @Override
    public BusinessFundingViewDO get(Long id) {
        return businessFundingViewMapper.selectById(id);
    }
}
