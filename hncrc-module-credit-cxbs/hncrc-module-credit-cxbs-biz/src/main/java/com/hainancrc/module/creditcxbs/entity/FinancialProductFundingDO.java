package com.hainancrc.module.creditcxbs.entity;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import com.hainancrc.module.creditcxbs.api.enums.FinancialProductFundingFileStatus;

import javax.validation.constraints.Size;

/**
 * 金融产品融资信息表 DO
 */
@TableName("cxbs_financial_product_funding")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinancialProductFundingDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 文件名称(列表,新增,编辑)
     */
    private String fileName;

    /**
     * 文件内容描述(新增,编辑)
     */
    private String fileContent;

    /**
     * 文件状态(ENUMS:ONLINE-公示中,OFFLINE-已下线)(列表)
     */
    private FinancialProductFundingFileStatus fileStatus;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

}
