package com.hainancrc.module.creditcxbs.api.teasubject.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

@ApiModel("茶叶统计信息 Response VO")
@Data
public class TeaStatisticsRespVO {
    
    @ApiModelProperty(value = "茶叶经营主体入驻数量")
    private Long subjectCount;
    
    @ApiModelProperty(value = "茶叶产地个数")
    private Long originCount;
    
//    @ApiModelProperty(value = "茶叶茶园个数")
//    private Long teaGardenCount;
    
    @ApiModelProperty(value = "产地码申请主体数")
    private Long originCodeApplyCount;
    
    @ApiModelProperty(value = "申领茶青数量(斤)")
    private Long teaLeafAmount;
    
    @ApiModelProperty(value = "产地码申领数")
    private Long originCodeApplyNum;
    
    @ApiModelProperty(value = "产地码查看次数")
    private Long originCodeViewCount;
} 