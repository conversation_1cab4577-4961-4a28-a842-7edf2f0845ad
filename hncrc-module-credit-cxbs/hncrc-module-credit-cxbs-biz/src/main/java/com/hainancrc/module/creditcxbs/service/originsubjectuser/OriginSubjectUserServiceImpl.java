package com.hainancrc.module.creditcxbs.service.originsubjectuser;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.originsubjectuser.dto.*;
import com.hainancrc.module.creditcxbs.convert.originsubjectuser.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.originsubjectuser.*;

import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;
/**
*  Service 实现类
*
*/
@Service
@Validated
public class OriginSubjectUserServiceImpl implements OriginSubjectUserService {
    
    @Resource
    private OriginSubjectUserMapper originSubjectUserMapper;
    
    
    
    private void validateExists(Long id) {
        if (originSubjectUserMapper.selectById(id) == null) {
            throw exception(ORIGIN_SUBJECT_USER_NOT_EXISTS);
        }
    }
    
    
    @Override
    public Long create(OriginSubjectUserCreateDTO createDTO) {
        
        
        // 插入
        OriginSubjectUserDO originSubjectUser = OriginSubjectUserConvert.INSTANCE.convert(createDTO);
        originSubjectUserMapper.insert(originSubjectUser);
        // 返回
        return originSubjectUser.getId();
    }
    
    @Override
    public void update(OriginSubjectUserUpdateDTO updateDTO) {
        
        
        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        OriginSubjectUserDO updateObj = OriginSubjectUserConvert.INSTANCE.convert(updateDTO);
        originSubjectUserMapper.updateById(updateObj);
    }
    
    
    @Override
    public PageResult<OriginSubjectUserDO> getPage(OriginSubjectUserPageDTO pageDTO) {
        return originSubjectUserMapper.selectPage(pageDTO);
    }
    
    @Override
    public List<OriginSubjectUserDO> getList() {
        return originSubjectUserMapper.selectList();
    }
    
    
    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        originSubjectUserMapper.deleteById(id);
    }
    
    
    @Override
    public OriginSubjectUserDO get(Long id) {
        return originSubjectUserMapper.selectById(id);
    }
    
    
    
}

