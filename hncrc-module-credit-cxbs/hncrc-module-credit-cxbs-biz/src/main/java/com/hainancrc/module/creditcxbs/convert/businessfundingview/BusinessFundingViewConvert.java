
package com.hainancrc.module.creditcxbs.convert.businessfundingview;


import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.businessfundingview.dto.BusinessFundingViewCreateDTO;
import com.hainancrc.module.creditcxbs.api.businessfundingview.dto.BusinessFundingViewUpdateDTO;
import com.hainancrc.module.creditcxbs.api.businessfundingview.vo.BusinessFundingViewRespVO;
import com.hainancrc.module.creditcxbs.entity.BusinessFundingViewDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-06
 */

@Mapper
public interface BusinessFundingViewConvert {

    BusinessFundingViewConvert INSTANCE = Mappers.getMapper(BusinessFundingViewConvert.class);

    BusinessFundingViewDO convert(BusinessFundingViewCreateDTO bean);


    BusinessFundingViewDO convert(BusinessFundingViewUpdateDTO bean);


    BusinessFundingViewRespVO convert(BusinessFundingViewDO bean);

    List<BusinessFundingViewRespVO> convertList(List<BusinessFundingViewDO> list);

    PageResult<BusinessFundingViewRespVO> convertPage(PageResult<BusinessFundingViewDO> page);
}
