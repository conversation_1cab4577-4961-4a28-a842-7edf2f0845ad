package com.hainancrc.module.creditcxbs.service.oilteaestate;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaEstateEstateStatus;
import com.hainancrc.module.creditcxbs.api.oilteaestate.dto.*;
import com.hainancrc.module.creditcxbs.convert.oilteaestate.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.oilteaestate.*;

import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;
/**
*  Service 实现类
*
*/
@Service
@Validated
public class OilTeaEstateServiceImpl implements OilTeaEstateService {
    
    @Resource
    private OilTeaEstateMapper oilTeaEstateMapper;
    
    
    
    private void validateExists(Long id) {
        if (oilTeaEstateMapper.selectById(id) == null) {
            throw exception(OIL_TEA_ESTATE_NOT_EXISTS);
        }
    }
    
    
    @Override
    public Long create(OilTeaEstateCreateDTO createDTO) {
        
        
        // 插入
        OilTeaEstateDO oilTeaEstate = OilTeaEstateConvert.INSTANCE.convert(createDTO);
        oilTeaEstate.setEstateStatus(OilTeaEstateEstateStatus.ONLINE);
        oilTeaEstateMapper.insert(oilTeaEstate);
        // 返回
        return oilTeaEstate.getId();
    }
    
    @Override
    public void update(OilTeaEstateUpdateDTO updateDTO) {
        
        
        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        OilTeaEstateDO updateObj = OilTeaEstateConvert.INSTANCE.convert(updateDTO);
        oilTeaEstateMapper.updateById(updateObj);
    }
    
    
    @Override
    public PageResult<OilTeaEstateDO> getPage(OilTeaEstatePageDTO pageDTO) {
        Page<OilTeaEstateDO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        IPage<OilTeaEstateDO> pageResult = oilTeaEstateMapper.selectPage(page, pageDTO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }
    
    @Override
    public List<OilTeaEstateDO> getList() {
        return oilTeaEstateMapper.selectEstateList();
    }

    @Override
    public List<OilTeaEstateDO> getStatisticsList() {
        return oilTeaEstateMapper.selectStatisticsList();
    }


    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        oilTeaEstateMapper.deleteById(id);
    }
    
    
    @Override
    public OilTeaEstateDO get(Long id) {
        return oilTeaEstateMapper.selectById(id);
    }
    
    
    
}

