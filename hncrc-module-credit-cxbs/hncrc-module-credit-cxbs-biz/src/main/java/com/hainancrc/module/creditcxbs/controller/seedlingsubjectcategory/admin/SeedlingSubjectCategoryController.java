package com.hainancrc.module.creditcxbs.controller.seedlingsubjectcategory.admin;

import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.module.creditcxbs.api.seedlingsubjectcategory.dto.*;
import com.hainancrc.module.creditcxbs.api.seedlingsubjectcategory.vo.*;
import com.hainancrc.module.creditcxbs.convert.seedlingsubjectcategory.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.seedlingsubjectcategory.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "苗木类")
@RestController
@RequestMapping("/seedlingsubjectcategory")
@Validated
public class SeedlingSubjectCategoryController {
    
    @Resource
    private SeedlingSubjectCategoryService seedlingSubjectCategoryService;


    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Long> create(@Valid @RequestBody SeedlingSubjectCategoryCreateDTO createDTO) {
        return success(seedlingSubjectCategoryService.create(createDTO));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody SeedlingSubjectCategoryUpdateDTO updateDTO) {
        seedlingSubjectCategoryService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<SeedlingSubjectCategoryRespVO>> getList() {
        List<SeedlingSubjectCategoryDO> list = seedlingSubjectCategoryService.getList();
        return success(SeedlingSubjectCategoryConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<SeedlingSubjectCategoryRespVO>> getPage(@Valid SeedlingSubjectCategoryPageDTO pageDTO) {
        PageResult<SeedlingSubjectCategoryDO> pageResult = seedlingSubjectCategoryService.getPage(pageDTO);
        return success(SeedlingSubjectCategoryConvert.INSTANCE.convertPage(pageResult));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        seedlingSubjectCategoryService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<SeedlingSubjectCategoryRespVO> get(@RequestParam("id") Long id) {
        SeedlingSubjectCategoryDO seedlingSubjectCategory = seedlingSubjectCategoryService.get(id);
        return success(SeedlingSubjectCategoryConvert.INSTANCE.convert(seedlingSubjectCategory));
    }


}
