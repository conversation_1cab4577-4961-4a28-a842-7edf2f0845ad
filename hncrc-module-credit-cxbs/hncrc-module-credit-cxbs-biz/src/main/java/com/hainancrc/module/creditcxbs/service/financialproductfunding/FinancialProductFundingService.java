package com.hainancrc.module.creditcxbs.service.financialproductfunding;

import java.io.IOException;
import java.util.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.*;
import com.hainancrc.module.creditcxbs.api.financialproductfunding.dto.*;
import com.hainancrc.module.creditcxbs.api.financialproductfunding.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;
import org.springframework.web.multipart.MultipartFile;

/**
*  Service 接口
*
*/
public interface FinancialProductFundingService {
    
    
    /**
    * 创建
    *
    * @return 编号
    */
    Boolean create(@Valid MultipartFile file);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid FinancialProductFundingUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    List<FinancialProductFundingDO> getList();
    
    /**
    * 获得分页
    *
    * @param pageDTO 分页查询
    * @return 分页
    */
    PageResult<FinancialProductFundingDO> getPage(FinancialProductFundingPageDTO pageDTO);
    
    
    /**
    * 删除
    *
    * @param id 主键
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id 主键
    * @return
    */
    FinancialProductFundingDO get(Long id);


}

