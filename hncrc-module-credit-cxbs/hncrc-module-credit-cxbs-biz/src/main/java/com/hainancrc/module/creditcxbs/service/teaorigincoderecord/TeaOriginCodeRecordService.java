package com.hainancrc.module.creditcxbs.service.teaorigincoderecord;


import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordPageRespVO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.dto.TeaOriginCodeRecordCreateDTO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.dto.TeaOriginCodeRecordPageDTO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.dto.TeaOriginCodeRecordUpdateDTO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordDetailRespVO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordRespVO;
import com.hainancrc.module.creditcxbs.entity.TeaOriginCodeRecordDO;


import javax.validation.Valid;
import java.util.List;

/**
 *  Service 接口
 *
 */
public interface TeaOriginCodeRecordService {

    /**
     * 创建产地码申领记录
     * @param createDTO 创建信息
     * @return id
     */
    Long create(@Valid TeaOriginCodeRecordCreateDTO createDTO);

    /**
     * 继续申领产地码
     * @param createDTO 申领信息
     * @return
     */
    Long apply(@Valid TeaOriginCodeRecordCreateDTO createDTO);

    /**
     * 修改
     * @param updateDTO 修改信息
     */
    void update(@Valid TeaOriginCodeRecordUpdateDTO updateDTO);

    /**
     * 获得列表
     *
     * @return 列表
     */
    List<TeaOriginCodeRecordDO> getList();

    /**
     * 获得分页
     *
     * @param pageDTO 分页信息
     * @return 分页
     */
    PageResult<TeaOriginCodeRecordPageRespVO> getPage(TeaOriginCodeRecordPageDTO pageDTO);

    /**
     * 获得申领记录分页
     *
     * @param pageDTO 分页信息
     * @return 分页
     */
    PageResult<TeaOriginCodeRecordDetailRespVO> getRecordPage(TeaOriginCodeRecordPageDTO pageDTO);

    /**
     * 删除
     * @param id id
     *
     */
    void delete(Long id);


    /**
     * 获得
     * @param id id
     * @return 申领信息
     */
    TeaOriginCodeRecordRespVO get(Long id);

}
