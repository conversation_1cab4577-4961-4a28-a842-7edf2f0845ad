package com.hainancrc.module.creditcxbs.controller.oilteasubsidysubject.admin;

import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.vo.*;
import com.hainancrc.module.creditcxbs.convert.oilteasubsidysubject.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.oilteasubsidysubject.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "油茶补贴主体名单")
@RestController
@RequestMapping("/oilteasubsidysubject")
@Validated
public class OilTeaSubsidySubjectController {
    
    @Resource
    private OilTeaSubsidySubjectService oilTeaSubsidySubjectService;


    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Long> create(@Valid @RequestBody OilTeaSubsidySubjectCreateDTO createDTO) {
        return success(oilTeaSubsidySubjectService.create(createDTO));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody OilTeaSubsidySubjectUpdateDTO updateDTO) {
        oilTeaSubsidySubjectService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<OilTeaSubsidySubjectRespVO>> getList() {
        List<OilTeaSubsidySubjectDO> list = oilTeaSubsidySubjectService.getList();
        return success(OilTeaSubsidySubjectConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<OilTeaSubsidySubjectRespVO>> getPage(@Valid OilTeaSubsidySubjectPageDTO pageDTO) {
        PageResult<OilTeaSubsidySubjectDO> pageResult = oilTeaSubsidySubjectService.getPage(pageDTO);
        return success(OilTeaSubsidySubjectConvert.INSTANCE.convertPage(pageResult));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        oilTeaSubsidySubjectService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<OilTeaSubsidySubjectRespVO> get(@RequestParam("id") Long id) {
        OilTeaSubsidySubjectDO oilTeaSubsidySubject = oilTeaSubsidySubjectService.get(id);
        return success(OilTeaSubsidySubjectConvert.INSTANCE.convert(oilTeaSubsidySubject));
    }

    @GetMapping("/getSubsidySubjectList")
    @ApiOperation("首页-补贴落实公示名单列表")
    public CommonResult<List<OilTeaSubsidySubjectRespVO>> getSubsidySubjectList() {
        List<OilTeaSubsidySubjectDO> list = oilTeaSubsidySubjectService.SubsidySubjectList();
        return success(OilTeaSubsidySubjectConvert.INSTANCE.convertList(list));
    }


}
