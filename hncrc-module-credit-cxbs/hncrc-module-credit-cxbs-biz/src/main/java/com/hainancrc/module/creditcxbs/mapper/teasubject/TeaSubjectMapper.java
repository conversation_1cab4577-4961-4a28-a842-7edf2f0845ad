package com.hainancrc.module.creditcxbs.mapper.teasubject;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.teasubject.dto.*;
import com.hainancrc.module.creditcxbs.api.teasubject.vo.TeaSubjectDataVO;

import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

@Mapper
public interface TeaSubjectMapper extends BaseMapperX<TeaSubjectDO> {
    
    
    // PageResult<TeaSubjectDO> selectPage(@Param("dto") TeaSubjectPageDTO reqDTO);

    default PageResult<TeaSubjectDO> selectPage(TeaSubjectPageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<TeaSubjectDO>()
                .likeIfPresent(TeaSubjectDO::getSubjectName, reqDTO.getSubjectName())
                .eqIfPresent(TeaSubjectDO::getSubjectType, reqDTO.getSubjectType())
                .orderByDesc(TeaSubjectDO::getUpdateTime));
    }

    /**
     * 查询各类型主体数量
     */
    List<Map<String, Object>> selectSubjectTypeCount();

    List<TeaSubjectDataVO> listByTeaSubjectData(@Param("dto") DataOverViewSelectDTO dto);

    List<TeaSubjectDataVO> listByTeaSubjectData2(@Param("dto") DataOverViewSelectDTO dto);
}

