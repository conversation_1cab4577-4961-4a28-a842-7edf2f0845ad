package com.hainancrc.module.creditcxbs.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "cxbs", ignoreUnknownFields = false)
public class GskConfig {

    /**
     * 专利信息
     */
    private String zljbxx;

    /**
     * 行政处罚
     */
    private String xzcf;

    /**
     * 工商基本信息
     */
    private String qyzb;

    /**
     * 严重违法失信
     */
    private String yzwfsx;

    /**
     * 失信被执行人
     */
    private String sxbzxr;

    /**
     * 商标局基本信息
     */ 
    private String sbjjbxx;

    /**
     * 林木种子生产经营许可证
     */
    private String sdsjgxpt;

    private String ak;

    private String sk;

}
