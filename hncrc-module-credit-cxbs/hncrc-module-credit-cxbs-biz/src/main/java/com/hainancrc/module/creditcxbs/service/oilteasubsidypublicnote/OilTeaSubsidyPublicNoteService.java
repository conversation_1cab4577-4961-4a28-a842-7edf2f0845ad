package com.hainancrc.module.creditcxbs.service.oilteasubsidypublicnote;

import java.util.*;
import javax.validation.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidypublicnote.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidypublicnote.vo.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.dto.OilTeaSubsidySubjectExcelDTO;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
*  Service 接口
*
*/
public interface OilTeaSubsidyPublicNoteService {
    
    
    /**
     * 创建
     *
     * @param createDTO 创建信息
     * @param excelDTOS 补贴主体名单
     * @return 编号
     */
    Long create(@Valid OilTeaSubsidyPublicNoteCreateDTO createDTO, List<OilTeaSubsidySubjectExcelDTO> excelDTOS);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid OilTeaSubsidyPublicNoteUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    List<OilTeaSubsidyPublicNoteDO> getList();
    
    /**
    * 获得分页
    *
    * @param pageDTO 分页查询
    * @return 分页
    */
    PageResult<OilTeaSubsidyPublicNoteDO> getPage(OilTeaSubsidyPublicNotePageDTO pageDTO);
    
    
    /**
    * 删除
    *
    * @param id 主键
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id 主键
    * @return
    */
    OilTeaSubsidyPublicNoteDO get(Long id);
    
    
}

