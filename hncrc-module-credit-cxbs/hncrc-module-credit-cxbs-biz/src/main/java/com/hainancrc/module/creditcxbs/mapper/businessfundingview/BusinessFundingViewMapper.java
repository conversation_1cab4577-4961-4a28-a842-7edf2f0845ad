package com.hainancrc.module.creditcxbs.mapper.businessfundingview;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.businessfundingview.dto.BusinessFundingViewPageDTO;
import com.hainancrc.module.creditcxbs.api.quarantineinformationview.dto.QuarantineInformationViewPageDTO;
import com.hainancrc.module.creditcxbs.entity.BusinessFundingViewDO;
import com.hainancrc.module.creditcxbs.entity.QuarantineInformationViewDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface BusinessFundingViewMapper extends BaseMapperX<BusinessFundingViewDO> {

    PageResult<BusinessFundingViewDO> selectPage(@Param("dto") BusinessFundingViewPageDTO reqDTO);

    default BusinessFundingViewDO selectReportView(Long companyId, String viewDate){
        return selectOne(new LambdaQueryWrapperX<BusinessFundingViewDO>()
                                 .eq(BusinessFundingViewDO::getCompanyId, companyId)
                                 .between(BusinessFundingViewDO::getViewDate, viewDate + " 00:00:00", viewDate + " 23:59:59"));
    }

    Long selectLookCount();

    Long selectLookSubjectCount();

}

