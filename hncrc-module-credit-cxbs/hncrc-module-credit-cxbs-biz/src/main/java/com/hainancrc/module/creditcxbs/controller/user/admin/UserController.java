package com.hainancrc.module.creditcxbs.controller.user.admin;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.creditcxbs.service.user.UserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@Api(tags = "用户管理")
@RestController
@RequestMapping("/user")
public class UserController {

    @Resource
    private UserService userService;

    @PostMapping("/setTeaSubjectId")
    @ApiOperation("设置产地主体ID")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "teaSubjectId", value = "产地主体ID", required = true, dataType = "Long"),
        @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "String")
    })
    public CommonResult<Boolean> setTeaSubjectId(@RequestParam String userId, @RequestParam Long teaSubjectId) {
        return CommonResult.success(userService.setTeaSubjectId(userId, teaSubjectId));
    }

    @PostMapping("/getTeaSubjectId")
    @ApiOperation("获取产地主体ID")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "String")
    public CommonResult<Long> getTeaSubjectId(@RequestParam String userId) {
        return CommonResult.success(userService.getTeaSubjectId(userId));
    }

}
