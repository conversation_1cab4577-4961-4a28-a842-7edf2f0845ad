package com.hainancrc.module.creditcxbs.convert.teasubject;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.teasubject.dto.*;
import com.hainancrc.module.creditcxbs.api.teasubject.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface TeaSubjectConvert {

    TeaSubjectConvert INSTANCE = Mappers.getMapper(TeaSubjectConvert.class);


    TeaSubjectDO convert(TeaSubjectCreateDTO bean);


    TeaSubjectDO convert(TeaSubjectUpdateDTO bean);

    @Mapping(target = "subjectType", expression = "java(com.hainancrc.module.creditcxbs.api.enums.TeaSubjectType.getEnumByValue(bean.getSubjectType()))")
    TeaSubjectDO convert(TeaSubjectExportListDTO bean);

   TeaSubjectRespVO convert(TeaSubjectDO bean);

    List<TeaSubjectRespVO> convertList(List<TeaSubjectDO> list);

    PageResult<TeaSubjectRespVO> convertPage(PageResult<TeaSubjectDO> page);

    List<TeaSubjectDO> convertImportList(List<TeaSubjectExportListDTO> list);

}
