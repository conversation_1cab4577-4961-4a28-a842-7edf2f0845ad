package com.hainancrc.module.creditcxbs.entity;

import java.math.BigDecimal;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;


/**
 * 油茶经营主体信息 DO
 */
@TableName("cxbs_oil_tea_subject")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OilTeaSubjectDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 统一信用代码(列表,编辑)
     */
    private String uniscid;

    /**
     * 商户名称(列表,编辑)
     */
    private String subjectName;

    /**
     * 主体类型(列表,编辑)
     */
    private String subjectType;

    /**
     * 法定代表人(列表,编辑)
     */
    private String legal;

    /**
     * 所属乡镇(列表,编辑)
     */
    private String belongTownship;

    /**
     * 联系电话(列表,编辑)
     */
    private String contactPhone;

    /**
     * 经营地址(列表,编辑)
     */
    private String opAddress;

    /**
     * 种植类型
     */
    private String plantType;

    /**
     * 种植模式
     */
    private String plantMode;

    /**
     * 种植总面积(亩)
     */
    private BigDecimal plantTotalArea;

    /**
     * 种植株数（株）
     */
    private Long plantCount;

    /**
     * 种植苗木是否良种树苗
     */
    private Integer isEliteSeed;

    /**
     * 是否领取补贴
     */
    private Integer hasReceivedSubsidy;

    /**
     * 领取政策补贴
     */
    private BigDecimal receivedSubsidy;

    /**
     * 补贴时间
     */
    private String subsidyTime;


}
