package com.hainancrc.module.creditcxbs.service.pipeline;

import com.hainancrc.module.creditcxbs.api.code.vo.CodeRespVO;

public interface PipelineService {
    
    /**
     * 获取公司数量
     *
     * @return 公司数量
     */
    Long getCompanies();

    /**
     * 获取公司信息
     *
     * @param creditCode 统一社会信用代码
     * @return 是否存在该公司
     */
    Boolean getCompany(String creditCode);

    /**
     * @param code:
     * @return Object
     * <AUTHOR>
     * @description TODO
     * @date 2024/12/26 0026 15:09
     */

    String getByCode(String code);

    /**
     * 获取码类型
     * @param codeUrl: codeUrl
     */
    CodeRespVO getCodeType(String codeUrl);
}
