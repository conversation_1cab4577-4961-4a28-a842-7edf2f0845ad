package com.hainancrc.module.creditcxbs.entity;

import java.math.BigDecimal;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;


/**
 * 产地码申报历史记录 DO
 */
@TableName("cxbs_tea_origin_code_history")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeaOriginCodeHistoryDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 产地码申领id
     */
    private Long codeRecordId;

    /**
     * 贴标产品名称
     */
    private String productName;

    /**
     * 产品规格（净含量）
     */
    private String productSpecs;

    /**
     * 产地码数量
     */
    private Integer codeCount;


}
