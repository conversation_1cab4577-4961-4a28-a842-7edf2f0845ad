package com.hainancrc.module.creditcxbs.service.seedlingcategory;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.enums.SeedlingCategorySeedlingStatus;
import com.hainancrc.module.creditcxbs.api.financialpolicy.dto.FinancialPolicyFileDTO;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.dto.*;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.vo.SeedlingCategoryDistinctRespVO;
import com.hainancrc.module.creditcxbs.convert.seedlingcategory.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.seedlingcategory.*;

import com.hainancrc.module.creditcxbs.mapper.seedlingsubjectcategory.SeedlingSubjectCategoryMapper;
import net.sf.jsqlparser.statement.select.Distinct;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;
import static com.hainancrc.module.creditcxbs.constant.Constants.IMAGE_URL_STRING;

/**
*  Service 实现类
*
*/
@Service
@Validated
public class SeedlingCategoryServiceImpl implements SeedlingCategoryService {
    
    @Resource
    private SeedlingCategoryMapper seedlingCategoryMapper;

    @Resource
    private SeedlingSubjectCategoryMapper seedlingSubjectCategoryMapper;
    
    
    
    private void validateExists(Long id) {
        if (seedlingCategoryMapper.selectById(id) == null) {
            throw exception(SEEDLING_CATEGORY_NOT_EXISTS);
        }
    }
    
    
    @Override
    public Long create(SeedlingCategoryCreateDTO createDTO) {

        LambdaQueryWrapperX<SeedlingCategoryDO> queryWrapperX = new LambdaQueryWrapperX<SeedlingCategoryDO>()
                .eq(SeedlingCategoryDO::getSeedlingCategory, createDTO.getSeedlingCategory())
                .eq(!Objects.isNull(createDTO.getSeedlingVariety()), SeedlingCategoryDO::getSeedlingVariety, createDTO.getSeedlingVariety());

        if (seedlingCategoryMapper.exists(queryWrapperX)) {
            throw exception(SEEDLING_CATEGORY_EXISTS);
        }


        List<SeedlingCategoryPolicyDTO> policyList = createDTO.getPolicyList();
        // 判空policyList
        if (!ObjectUtil.isEmpty(policyList)) {
            for (SeedlingCategoryPolicyDTO policy : policyList) {
                for (FinancialPolicyFileDTO file : policy.getPolicyAttachments()) {
                    file.setUrl(StringEscapeUtils.unescapeHtml(file.getUrl()));
                }
            }
        }
        // 插入
        SeedlingCategoryDO seedlingCategory = SeedlingCategoryConvert.INSTANCE.convert(createDTO);
        seedlingCategory.setRelatedPolicy(createDTO.getPolicyList().size());
        seedlingCategory.setRelatedPolicyListJson(JSON.toJSONString(createDTO.getPolicyList()));
        seedlingCategory.setSeedlingStatus(SeedlingCategorySeedlingStatus.valueOf(SeedlingCategorySeedlingStatus.ONLINE.name()));
        seedlingCategoryMapper.insert(seedlingCategory);
        // 返回
        return seedlingCategory.getId();
    }
    
    @Override
    public void update(SeedlingCategoryUpdateDTO updateDTO) {        
        // 校验存在
        this.validateExists(updateDTO.getId());

        List<SeedlingCategoryPolicyDTO> policyList = updateDTO.getPolicyList();
        if (!CollectionUtils.isEmpty(policyList)) {
            for (SeedlingCategoryPolicyDTO policy : policyList) {
                if (!CollectionUtils.isEmpty(policy.getPolicyAttachments())) {
                    for (FinancialPolicyFileDTO file : policy.getPolicyAttachments()) {
                        file.setUrl(file.getUrl() == null ? null : StringEscapeUtils.unescapeHtml(file.getUrl()));
                    }
                }
            }
        }

        // 更新
        SeedlingCategoryDO updateObj = SeedlingCategoryConvert.INSTANCE.convert(updateDTO);
        updateObj.setRelatedPolicyListJson(JSON.toJSONString(updateDTO.getPolicyList()));
        updateObj.setRelatedPolicy(updateDTO.getPolicyList().size());
        seedlingCategoryMapper.updateById(updateObj);
    }


    @Override
    public PageResult<SeedlingCategoryDO> getPage(SeedlingCategoryPageDTO pageDTO) {
        PageResult<SeedlingCategoryDO> pageResult = seedlingCategoryMapper.selectPage(pageDTO);
        return pageResult;
    }

    @Override
    public PageResult<SeedlingCategoryDO> getPageHome(SeedlingCategoryPageDTO pageDTO) {
        // 判断是否有值
        if (ObjectUtil.isNotEmpty(pageDTO.getSubjectId())) {
            // 根据subjectId查询出所有该主体所拥有的苗木种类id
            List<SeedlingSubjectCategoryDO> categoryIdList = seedlingSubjectCategoryMapper.selectList(
                    new LambdaQueryWrapperX<SeedlingSubjectCategoryDO>()
                    .eqIfPresent(SeedlingSubjectCategoryDO::getSubjectId, pageDTO.getSubjectId())
                    .select(SeedlingSubjectCategoryDO::getCategoryId));
            // 把categoryIdList里边的id集合拿出来
            pageDTO.setCategoryIds(categoryIdList.stream()
                    .map(SeedlingSubjectCategoryDO::getCategoryId).collect(Collectors.toSet()));


        }
        PageResult<SeedlingCategoryDO> pageResult = seedlingCategoryMapper.selectPageHome(pageDTO);

        pageResult.getList().forEach(seedlingCategoryDO -> {
            // 处理seedlingPictureJson字段获取URL字符串
            if (ObjectUtil.isNotEmpty(seedlingCategoryDO.getSeedlingPictureJson())) {
                seedlingCategoryDO.setSeedlingPictureJson(JSONUtil.parseObj(seedlingCategoryDO.getSeedlingPictureJson())
                        .getStr(IMAGE_URL_STRING));
            }

            // 处理relatedPolicyListJson字段，去掉key
            String relatedPolicyListJson = seedlingCategoryDO.getRelatedPolicyListJson();
            if (ObjectUtil.isNotEmpty(relatedPolicyListJson)) {
                List<SeedlingCategoryPolicyDTO> policyList = JSONUtil.parseArray(relatedPolicyListJson).toList(SeedlingCategoryPolicyDTO.class);
                if (ObjectUtil.isNotEmpty(policyList)) {
                    List<SeedlingCategoryPolicyDTO> updatedPolicyList = policyList.stream()
                            .map(policyDTO -> {
                                if (ObjectUtil.isNotEmpty(policyDTO.getPolicyAttachments())) {
                                    List<FinancialPolicyFileDTO> updatedAttachments = policyDTO.getPolicyAttachments().stream()
                                            .peek(attachmentDTO -> attachmentDTO.setKey(null))
                                            .collect(Collectors.toList());
                                    policyDTO.setPolicyAttachments(updatedAttachments);
                                }
                                return policyDTO;
                            })
                            .collect(Collectors.toList());
                    seedlingCategoryDO.setRelatedPolicyListJson(JSONUtil.toJsonStr(updatedPolicyList));
                }
            }
        });

        return pageResult;
    }

    @Override
    public List<SeedlingCategoryDO> getList() {
        List<SeedlingCategoryDO> seedlingCategoryDOS = seedlingCategoryMapper.selectList();
        seedlingCategoryDOS.forEach(seedlingCategoryDO -> {
            JSONArray jsonArray = JSON.parseArray(seedlingCategoryDO.getSeedlingPictureJson());
            List<String> urls = jsonArray.stream()
                    .map(item -> ((JSONObject) item).getString(IMAGE_URL_STRING))
                    .collect(Collectors.toList());
            seedlingCategoryDO.setSeedlingPictureJson(urls.toString());
        });
        return seedlingCategoryDOS;
    }
    
    
    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        seedlingCategoryMapper.deleteById(id);
    }
    
    
    @Override
    public SeedlingCategoryDO get(Long id) {
        return seedlingCategoryMapper.selectById(id);
    }
    
    @Override
    public Long countDistinctCategory() {
        LambdaQueryWrapper<SeedlingCategoryDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(SeedlingCategoryDO::getSeedlingCategory);
        wrapper.eq(SeedlingCategoryDO::getSeedlingStatus, SeedlingCategorySeedlingStatus.ONLINE);
        wrapper.isNotNull(SeedlingCategoryDO::getSeedlingCategory);
        List<SeedlingCategoryDO> dos = seedlingCategoryMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(dos)){
            return 0L;
        }

        Set<String> categories = dos
                .stream()
                .map(SeedlingCategoryDO::getSeedlingCategory)
                .collect(Collectors.toSet());
        return Long.valueOf(categories.size());
    }

    @Override
    public Long countDistinctVariety() {
        LambdaQueryWrapper<SeedlingCategoryDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(SeedlingCategoryDO::getSeedlingVariety);
        wrapper.eq(SeedlingCategoryDO::getSeedlingStatus, SeedlingCategorySeedlingStatus.ONLINE);
        wrapper.isNotNull(SeedlingCategoryDO::getSeedlingVariety);
        List<SeedlingCategoryDO> dos = seedlingCategoryMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(dos)){
            return 0L;
        }
        // 计算getSeedlingVariety 不为空的总数
        Set<String> varieties = dos
                .stream()
                .map(SeedlingCategoryDO::getSeedlingVariety)
                .collect(Collectors.toSet());
        return Long.valueOf(varieties.size());
    }

    @Override
    public SeedlingCategoryDO getBySubjectId(Long subjectId) {
        SeedlingSubjectCategoryDO seedlingSubjectCategoryDO =
                seedlingSubjectCategoryMapper.selectOne(new LambdaQueryWrapperX<SeedlingSubjectCategoryDO>()
                .eqIfPresent(SeedlingSubjectCategoryDO::getSubjectId, subjectId).last("limit 1"));
        // 用hutools 判断 seedlingSubjectCategoryDO 非空
        if (ObjectUtil.isEmpty(seedlingSubjectCategoryDO)) {
            //
            return null;
        }
        return seedlingCategoryMapper.selectById(seedlingSubjectCategoryDO.getCategoryId());
    }

    @Override
    public SeedlingCategoryDistinctRespVO getSeedlingTypesAndVarieties() {
        SeedlingCategoryDistinctRespVO distinctRespVO = new SeedlingCategoryDistinctRespVO();
        distinctRespVO.setVarietyList(seedlingCategoryMapper.selectVarietyList());
        distinctRespVO.setCategoryList(seedlingCategoryMapper.selectCategoryList());
        return distinctRespVO;

    }

    @Override
    public PageResult<SeedlingCategoryDO> getSubjectSeedlingPage(SeedlingCategoryPageDTO pageDTO) {
        // 获取经营主体下的苗木种类id集合
        List<SeedlingSubjectCategoryDO> subjectCategoryDOS = seedlingSubjectCategoryMapper.selectList(new LambdaQueryWrapper<SeedlingSubjectCategoryDO>()
                .eq(SeedlingSubjectCategoryDO::getSubjectId, pageDTO.getSubjectId()));

        if (!CollectionUtils.isEmpty(subjectCategoryDOS)) {
            Set<Long> categoryIds = subjectCategoryDOS.stream().map(SeedlingSubjectCategoryDO::getCategoryId)
                    .collect(Collectors.toSet());
            return seedlingCategoryMapper.selectPage(pageDTO, new LambdaQueryWrapper<SeedlingCategoryDO>()
                    .eq(SeedlingCategoryDO::getSeedlingStatus, SeedlingCategorySeedlingStatus.ONLINE)
                    .in(SeedlingCategoryDO::getId, categoryIds));
        }
        return PageResult.empty();
    }

    @Override
    public List<SeedlingCategoryDO> getSelectList() {
        return seedlingCategoryMapper.selectList(new LambdaQueryWrapper<SeedlingCategoryDO>()
                .eq(SeedlingCategoryDO::getSeedlingStatus, SeedlingCategorySeedlingStatus.ONLINE));
    }

}

