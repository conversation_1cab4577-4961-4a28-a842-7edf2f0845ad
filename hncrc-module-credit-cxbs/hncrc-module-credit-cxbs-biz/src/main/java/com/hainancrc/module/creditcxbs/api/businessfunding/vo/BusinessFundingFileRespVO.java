package com.hainancrc.module.creditcxbs.api.businessfunding.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.hainancrc.module.creditcxbs.utils.DateConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
* 经营主体融资信息
*/
@Data
public class BusinessFundingFileRespVO {
    
    /**
    * 申请主体
    */
    @ApiModelProperty(value = "申请主体")
    @Size(max = 100, message = "申请主体长度不能大于 100")
    private String applySubject;

    /**
     * 主体类型(列表,编辑)
     */
    @ApiModelProperty(value = "主体类型(列表,编辑)")
    private String subjectType;
    
    /**
    * 申请时间
    */
    @ApiModelProperty(value = "申请时间")
    private Date applyTime;
    
    /**
    * 申请产品
    */
    @ApiModelProperty(value = "申请产品")
    @Size(max = 100, message = "申请产品长度不能大于 100")
    private String applyProduct;
    
    /**
    * 申请融资额(万元)
    */
    @ApiModelProperty(value = "申请融资额(万元)")
    private BigDecimal applyAmount;
    
    /**
    * 发放融资额(万元)
    */
    @ApiModelProperty(value = "发放融资额(万元)")
    private BigDecimal disburseAmount;

    /**
     * 金融产品融资信息id
     */
    @ApiModelProperty(value = "金融产品融资信息id")
    private Long financialProductFundingId;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @Size(max = 50, message = "创建人长度不能大于 50")
    private String creator;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @Size(max = 50, message = "更新人长度不能大于 50")
    private String updater;
    

}

