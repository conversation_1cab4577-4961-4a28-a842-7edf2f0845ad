package com.hainancrc.module.creditcxbs.convert.originsubjectuser;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.originsubjectuser.dto.*;
import com.hainancrc.module.creditcxbs.api.originsubjectuser.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface OriginSubjectUserConvert {

    OriginSubjectUserConvert INSTANCE = Mappers.getMapper(OriginSubjectUserConvert.class);


    OriginSubjectUserDO convert(OriginSubjectUserCreateDTO bean);


    OriginSubjectUserDO convert(OriginSubjectUserUpdateDTO bean);


   OriginSubjectUserRespVO convert(OriginSubjectUserDO bean);

    List<OriginSubjectUserRespVO> convertList(List<OriginSubjectUserDO> list);

    PageResult<OriginSubjectUserRespVO> convertPage(PageResult<OriginSubjectUserDO> page);



}
