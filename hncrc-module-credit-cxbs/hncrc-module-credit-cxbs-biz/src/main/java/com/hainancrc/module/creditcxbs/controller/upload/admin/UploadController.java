package com.hainancrc.module.creditcxbs.controller.upload.admin;

import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.creditcxbs.api.upload.vo.UploadRespVO;
import com.hainancrc.module.upload.api.upload.UploadFileApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.FileCopyUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;


@Api(tags = "文件上传")
@RestController
@RequestMapping("/upload")
@Validated
@Slf4j
public class UploadController {
    @Resource
    private UploadFileApi uploadFileApi;

//    private UploadService uploadService;

    /**
     * 文件上传接口
     *
     * @param file 文件
     * @return 上传结果
     * @throws Exception 异常
     */
    @PostMapping("/uploadFile")
    @ApiOperation("文件上传接口")
    public CommonResult upload(@ApiParam(value = "文件流", required = true) @RequestPart("file") MultipartFile file) throws Exception{

        //文件不能大于20MB，
        if (file.getSize() > 20 * 1024 * 1024) {
            throw new ServiceException(-1, "上传文件不能大于20MB");
        }

        return uploadFileApi.staticUpload(file);
    }

    /**
     * 上传文件（本地测试）
     *
     * @param file 文件
     * @return 上传结果
     * @throws Exception 异常
     */
//    @PostMapping("/uploadTest")
//    @Operation(summary = "上传文件")
//    public CommonResult<String> uploadFile(MultipartFile file) throws Exception {
////        return success(uploadService.upload(file.getOriginalFilename(), null, IoUtil.readBytes(file.getInputStream())));
//        return null;
//    }

    /**
     * 上传文件（本地测试）
     *
     * @param file 文件
     * @return 上传结果
     * @throws Exception 异常
     */
    // 定义文件保存的基础路径，需根据实际情况调整为你的绝对路径  帮我做成相对路径
    @PostMapping("/uploadTest")
    @ApiOperation("文件上传本地联调测试")
    public CommonResult<UploadRespVO> uploadFile(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return CommonResult.error(-1,"上传的文件不能为空");
        }
        // 写死的文件保存路径
        String baseUploadPath = "D:\\Workspace\\诚信白沙\\hncrc-module-credit-cxbs\\hncrc-module-credit-cxbs-biz\\src/main/resources/documents";

        // 创建保存文件的目录（如果不存在的话）
        Path uploadDir = Paths.get(baseUploadPath);
        try {
            Files.createDirectories(uploadDir);
        } catch (IOException e) {
            return CommonResult.error(-1,"创建文件保存目录失败: " + e.getMessage());
        }
        // 生成唯一的文件名（使用UUID）
        String uniqueFileName = UUID.randomUUID().toString();
        // 获取文件扩展名
        String fileExtension = "";
        String originalFilename = file.getOriginalFilename();
        if (originalFilename!= null && originalFilename.lastIndexOf(".")!= -1) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        uniqueFileName += fileExtension;

        // 构建文件保存的完整路径
        Path filePath = Paths.get(baseUploadPath, uniqueFileName);

        // 使用Spring提供的工具类进行文件内容复制，更简洁且能更好地处理流读写异常
        try {
            FileCopyUtils.copy(file.getInputStream(), Files.newOutputStream(filePath));
        } catch (IOException e) {
            return CommonResult.error(-1,"文件上传过程中出现错误: " + e.getMessage());

        }
        // 返回可访问该文件的相对路径（相对于当前模块下的路径基准）
        UploadRespVO uploadRespVO = new UploadRespVO();
        uploadRespVO.setKey(uniqueFileName);
        uploadRespVO.setUrl("http://172.16.101.49:8180/cxbs/documents/" + uniqueFileName);
        return CommonResult.success(uploadRespVO);
    }

}
