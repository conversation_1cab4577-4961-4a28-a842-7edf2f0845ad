package com.hainancrc.module.creditcxbs.controller.originviewdown.admin;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.creditcxbs.api.originviewdown.dto.OriginViewDownCreateDTO;
import com.hainancrc.module.creditcxbs.api.quarantineinformationview.dto.QuarantineInformationViewCreateDTO;
import com.hainancrc.module.creditcxbs.service.originviewdown.OriginViewDownService;
import com.hainancrc.module.creditcxbs.service.quarantineinformationview.QuarantineInformationViewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-20
 */

@Api(tags = "企业查看下载按日统计表")
@RestController
@RequestMapping("/origin_view_down")
@Validated
public class OriginViewDownController {

    @Resource
    private OriginViewDownService originViewDownService;

    @Resource
    private QuarantineInformationViewService quarantineInformationViewService;

    @PostMapping("/add_origin_view_down")
    @ApiOperation("新增企业下载查看访问量按日统计")
    public CommonResult<Long> addOriginViewDown(@Valid @RequestBody OriginViewDownCreateDTO createDTO) {
        return success(originViewDownService.create(createDTO));
    }

    @PostMapping("/addQuarantineView")
    @ApiOperation("新增产地码访问量按日统计")
    public CommonResult<Long> addQuarantineView(@Valid @RequestBody QuarantineInformationViewCreateDTO createDTO) {
        return success(quarantineInformationViewService.create(createDTO));
    }
}
