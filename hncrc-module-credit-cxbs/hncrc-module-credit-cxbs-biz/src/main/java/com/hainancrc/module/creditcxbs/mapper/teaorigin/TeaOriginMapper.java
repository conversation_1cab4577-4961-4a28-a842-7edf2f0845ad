package com.hainancrc.module.creditcxbs.mapper.teaorigin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.enums.TeaOriginStatus;
import com.hainancrc.module.creditcxbs.api.teaorigin.vo.ApplySubjectPageRespVO;
import com.hainancrc.module.creditcxbs.api.teaorigin.vo.TeaOriginPageRespVO;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.teaorigin.dto.*;

import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface TeaOriginMapper extends BaseMapperX<TeaOriginDO> {
    
    
    IPage<TeaOriginPageRespVO> selectPage(@Param("page") Page<TeaOriginPageRespVO> page,
                                          @Param("dto") TeaOriginPageDTO reqDTO);

    IPage<ApplySubjectPageRespVO> selectApplyPage(@Param("page") Page<ApplySubjectPageRespVO> page,
                                                  @Param("dto") TeaOriginPageDTO pageDTO);

    default List<TeaOriginDO> selectTeaOriginList() {
        return selectList(new LambdaQueryWrapperX<TeaOriginDO>()
                .eq(TeaOriginDO::getOriginStatus, TeaOriginStatus.ONLINE)
                .orderByDesc(TeaOriginDO::getUpdateTime));
    };

}

