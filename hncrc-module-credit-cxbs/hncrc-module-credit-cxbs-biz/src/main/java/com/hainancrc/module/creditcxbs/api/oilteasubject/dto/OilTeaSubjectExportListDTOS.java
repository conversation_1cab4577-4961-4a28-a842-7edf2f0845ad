package com.hainancrc.module.creditcxbs.api.oilteasubject.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.hainancrc.module.creditcxbs.utils.excel.YesNoConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
* 油茶经营主体信息
*/
@Data
public class OilTeaSubjectExportListDTOS {
    
    /**
    * 商户名称(列表,编辑)
    */
    @ApiModelProperty(value = "商户名称(列表,编辑)")
    @ExcelProperty(value = "种植主体名称（必填）")
    private String subjectName;
    
    /**
    * 主体类型(列表,编辑)
    */
    @ApiModelProperty(value = "主体类型(列表,编辑)")
    @ExcelProperty(value = "主体类型（必填）")
    private String subjectType;

    /**
     * 统一信用代码(列表,编辑)
     */
    @ApiModelProperty(value = "统一信用代码(列表,编辑)")
    @ExcelProperty(value = "统一社会信用代码（必填：主体类型为农户时非必填）")
    private String uniscid;
    
    /**
    * 法定代表人(列表,编辑)
    */
    @ApiModelProperty(value = "法定代表人(列表,编辑)")
    @ExcelProperty(value = "法人/经营者")
    private String legal;

    /**
    * 所属乡镇(列表,编辑)
    */
    @ApiModelProperty(value = "所属乡镇(列表,编辑)")
    @ExcelProperty(value = "所属乡镇（必填）")
    private String belongTownship;

    /**
     * 经营地址(列表,编辑)
     */
    @ApiModelProperty(value = "经营地址(列表,编辑)")
    @ExcelProperty(value = "经营地址（必填）")
    private String opAddress;

    /**
    * 联系电话(列表,编辑)
    */
    @ApiModelProperty(value = "联系电话(列表,编辑)")
    @ExcelProperty(value = "联系电话")
    private String contactPhone;
    
    /**
    * 种植模式
    */
    @ApiModelProperty(value = "种植模式")
    @Size(max = 100, message = "种植模式长度不能大于 100")
    @ExcelProperty(value = "种植模式")
    private String plantMode;
    
    /**
    * 种植总面积(亩)
    */
    @ApiModelProperty(value = "种植总面积(亩)")
    @ExcelProperty(value = "种植面（亩）")
    @Digits(integer = 10, fraction = 2, message = "种植总面积(亩)长度不能大于 10")
    private BigDecimal plantTotalArea;
    
    /**
    * 种植株数（株）
    */
    @ApiModelProperty(value = "种植株数（株）")
    @ExcelProperty(value = "种植株数（株）")
    private Long plantCount;

    /**
     * 种植类型
     */
    @ApiModelProperty(value = "种植类型")
    @ExcelProperty(value = "种植苗木品种")
    private String plantType;
    
    /**
    * 种植苗木是否良种树苗
    */
    @ApiModelProperty(value = "种植苗木是否良种树苗")
    @ExcelProperty(value = "种植苗木是否良种苗木", converter = YesNoConverter.class)
    private Integer isEliteSeed;
    
    /**
    * 是否领取补贴
    */
    @ApiModelProperty(value = "是否领取补贴")
    @ExcelProperty(value = "是否领取政策补贴", converter = YesNoConverter.class)
    private Integer hasReceivedSubsidy;
    
    /**
    * 领取政策补贴
    */
    @ApiModelProperty(value = "领取政策补贴")
    @ExcelProperty(value = "领取政策补贴（万元）")
    private BigDecimal receivedSubsidy;
    
    /**
    * 补贴时间
    */
    @ApiModelProperty(value = "补贴时间")
    @ExcelProperty(value = "补贴时间")
    private String subsidyTime;

}

