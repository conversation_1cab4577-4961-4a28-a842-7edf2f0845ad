package com.hainancrc.module.creditcxbs.service.businessfunding;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.businessfunding.dto.*;
import com.hainancrc.module.creditcxbs.api.businessfunding.vo.BusinessFundingFileRespVO;
import com.hainancrc.module.creditcxbs.api.businessfunding.vo.BusinessfundingStatisticsRespVO;
import com.hainancrc.module.creditcxbs.api.enums.FinancialProductFundingFileStatus;
import com.hainancrc.module.creditcxbs.api.enums.FinancialStatisticsType;
import com.hainancrc.module.creditcxbs.convert.businessfunding.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.businessfunding.*;

import com.hainancrc.module.creditcxbs.mapper.businessfundingview.BusinessFundingViewMapper;
import com.hainancrc.module.creditcxbs.mapper.financialproductfunding.FinancialProductFundingMapper;
import com.hainancrc.module.creditcxbs.mapper.financialproductviewstatistics.FinancialProductViewStatisticsMapper;
import com.hainancrc.module.creditcxbs.service.financialproductviewstatistics.FinancialProductViewStatisticsService;
import lombok.extern.slf4j.Slf4j;
import lombok.extern.slf4j.XSlf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;
/**
*  Service 实现类
*
*/
@Slf4j
@Service
@Validated
public class BusinessFundingServiceImpl implements BusinessFundingService {
    
    @Resource
    private BusinessFundingMapper businessFundingMapper;

    @Resource
    private BusinessFundingViewMapper businessFundingViewMapper;

    @Resource
    private FinancialProductFundingMapper financialProductFundingMapper;

    @Resource
    private FinancialProductViewStatisticsMapper financialProductViewStatisticsMapper;
    
    private void validateExists(Long id) {
        if (businessFundingMapper.selectById(id) == null) {
            throw exception(BUSINESS_FUNDING_NOT_EXISTS);
        }
    }
    
    
    @Override
    public Long create(BusinessFundingCreateDTO createDTO) {
        
        
        // 插入
        BusinessFundingDO businessFunding = BusinessFundingConvert.INSTANCE.convert(createDTO);
        businessFundingMapper.insert(businessFunding);
        // 返回
        return businessFunding.getId();
    }
    
    @Override
    public void update(BusinessFundingUpdateDTO updateDTO) {
        
        
        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        BusinessFundingDO updateObj = BusinessFundingConvert.INSTANCE.convert(updateDTO);
        businessFundingMapper.updateById(updateObj);
    }
    
    
    @Override
    public PageResult<BusinessFundingDO> getPage(BusinessFundingPageDTO pageDTO) {
        return businessFundingMapper.selectPage(pageDTO);
    }
    
    @Override
    public List<BusinessFundingDO> getList() {

        List<FinancialProductFundingDO> productFundingList = financialProductFundingMapper.selectList(new LambdaQueryWrapperX<FinancialProductFundingDO>()
                .eq(FinancialProductFundingDO::getFileStatus, FinancialProductFundingFileStatus.ONLINE));

        List<Long> idList = productFundingList.stream().map(FinancialProductFundingDO::getId).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(idList)) {
            return null;
        }

        return businessFundingMapper.selectList(new LambdaQueryWrapperX<BusinessFundingDO>()
                .in(BusinessFundingDO::getFinancialProductFundingId, idList));
    }
    
    
    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        businessFundingMapper.deleteById(id);
    }
    
    
    @Override
    public BusinessFundingDO get(Long id) {
        return businessFundingMapper.selectById(id);
    }

    @Override
    public BusinessfundingStatisticsRespVO getStatistics() {
        BusinessfundingStatisticsRespVO statisticsRespVO = new BusinessfundingStatisticsRespVO();
        // 金融产品个数(目前暂定为1)
        statisticsRespVO.setFinancialProductCount(1L);

        // 金融产品浏览数
        FinancialProductViewStatisticsDO viewCount = financialProductViewStatisticsMapper.selectOne(new LambdaQueryWrapperX<FinancialProductViewStatisticsDO>()
                .eq(FinancialProductViewStatisticsDO::getStatisticsType, FinancialStatisticsType.Views.name()));
        statisticsRespVO.setFinancialProductViewCount(viewCount.getStatisticsCount());
//        statisticsRespVO.setFinancialProductViewCount(businessFundingViewMapper.selectLookCount());

        // 浏览经营主体数
        FinancialProductViewStatisticsDO subjectViewCount = financialProductViewStatisticsMapper.selectOne(new LambdaQueryWrapperX<FinancialProductViewStatisticsDO>()
                .eq(FinancialProductViewStatisticsDO::getStatisticsType, FinancialStatisticsType.ViewSubject.name()));
        statisticsRespVO.setSubjectViewCount(subjectViewCount.getStatisticsCount());
//        statisticsRespVO.setSubjectViewCount(businessFundingViewMapper.selectLookSubjectCount());

        // 申请经营主体数
        statisticsRespVO.setApplySubjectCount(businessFundingMapper.selectApplySubjectCount());

        // 申请经成功营主体数
        statisticsRespVO.setApplySuccessSubjectCount(businessFundingMapper.selectApplySuccessSubjectCount());

        // 融资总金额
        statisticsRespVO.setTotalAmount(businessFundingMapper.selectTotalAmount());

        return statisticsRespVO;
    }

    @Override
    public List<BusinessFundingDO> getListByFinancialProductFundingId(Long id) {
        return businessFundingMapper.selectList(new LambdaQueryWrapperX<BusinessFundingDO>()
                .eqIfPresent(BusinessFundingDO::getFinancialProductFundingId, id));
    }

}

