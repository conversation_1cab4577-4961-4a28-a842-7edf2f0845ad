package com.hainancrc.module.creditcxbs.entity;

import java.math.BigDecimal;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;


/**
 * 经营主体融资信息 DO
 */
@TableName("cxbs_business_funding")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessFundingDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 金融产品融资信息id
     */
    private Long financialProductFundingId;

    /**
     * 申请主体
     */
    private String applySubject;

    /**
     * 主体类型(列表,编辑)
     */
    private String subjectType;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 申请产品
     */
    private String applyProduct;

    /**
     * 申请融资额(万元)
     */
    private BigDecimal applyAmount;

    /**
     * 发放融资额(万元)
     */
    private BigDecimal disburseAmount;


}
