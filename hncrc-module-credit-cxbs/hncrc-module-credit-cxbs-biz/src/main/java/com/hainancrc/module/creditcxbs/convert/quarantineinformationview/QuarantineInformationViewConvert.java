package com.hainancrc.module.creditcxbs.convert.quarantineinformationview;


import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.quarantineinformationview.dto.QuarantineInformationViewCreateDTO;
import com.hainancrc.module.creditcxbs.api.quarantineinformationview.dto.QuarantineInformationViewUpdateDTO;
import com.hainancrc.module.creditcxbs.api.quarantineinformationview.vo.QuarantineInformationViewRespVO;
import com.hainancrc.module.creditcxbs.entity.QuarantineInformationViewDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-06
 */

@Mapper
public interface QuarantineInformationViewConvert {
    QuarantineInformationViewConvert INSTANCE = Mappers.getMapper(QuarantineInformationViewConvert.class);

    QuarantineInformationViewDO convert(QuarantineInformationViewCreateDTO bean);


    QuarantineInformationViewDO convert(QuarantineInformationViewUpdateDTO bean);


    QuarantineInformationViewRespVO convert(QuarantineInformationViewDO bean);

    List<QuarantineInformationViewRespVO> convertList(List<QuarantineInformationViewDO> list);

    PageResult<QuarantineInformationViewRespVO> convertPage(PageResult<QuarantineInformationViewDO> page);
}
