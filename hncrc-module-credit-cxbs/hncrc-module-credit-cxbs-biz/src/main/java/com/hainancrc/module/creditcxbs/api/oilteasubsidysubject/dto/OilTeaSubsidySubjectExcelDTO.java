package com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OilTeaSubsidySubjectExcelDTO {

    @ColumnWidth(value = 15)
    @ExcelProperty(value = "乡镇")
    private String township;

    @ColumnWidth(value = 15)
    @ExcelProperty(value = "种植户名称")
    private String planterName;

    @ColumnWidth(value = 15)
    @ExcelProperty(value = "种植面积")
    private BigDecimal plantArea;

    @ColumnWidth(value = 15)
    @ExcelProperty(value = "种植株数")
    private Long plantCount;

    @ColumnWidth(value = 10)
    @ExcelProperty(value = "存活率")
    private BigDecimal survivalRate;

    @ColumnWidth(value = 15)
    @ExcelProperty(value = "检查情况")
    private String checkStatus;

    @ColumnWidth(value = 18)
    @ExcelProperty(value = "补贴金额(元)")
    private BigDecimal subsidyAmount;

    @ColumnWidth(value = 20)
    @ExcelProperty(value = "备注")
    private String remark;
}
