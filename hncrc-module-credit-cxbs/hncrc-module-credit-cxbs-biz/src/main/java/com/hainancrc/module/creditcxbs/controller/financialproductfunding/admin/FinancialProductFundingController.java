package com.hainancrc.module.creditcxbs.controller.financialproductfunding.admin;

import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.module.creditcxbs.api.financialproductfunding.dto.*;
import com.hainancrc.module.creditcxbs.api.financialproductfunding.vo.*;
import com.hainancrc.module.creditcxbs.convert.financialproductfunding.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.financialproductfunding.*;
import com.hainancrc.module.creditcxbs.utils.ExcelKits;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "金融产品融资信息表")
@RestController
@RequestMapping("/financialproductfunding")
@Validated
public class FinancialProductFundingController {
    
    @Resource
    private FinancialProductFundingService financialProductFundingService;

    @PostMapping("/create")
    @ApiOperation("金融产品融资信息")
    public CommonResult<Boolean> create(@Valid @RequestParam(value = "file", required = false) MultipartFile file) {
        return success(financialProductFundingService.create(file));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody FinancialProductFundingUpdateDTO updateDTO) {
        financialProductFundingService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<FinancialProductFundingRespVO>> getList() {
        List<FinancialProductFundingDO> list = financialProductFundingService.getList();
        return success(FinancialProductFundingConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<FinancialProductFundingRespVO>> getPage(@Valid FinancialProductFundingPageDTO pageDTO) {
        PageResult<FinancialProductFundingDO> pageResult = financialProductFundingService.getPage(pageDTO);
        return success(FinancialProductFundingConvert.INSTANCE.convertPage(pageResult));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        financialProductFundingService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<FinancialProductFundingRespVO> get(@RequestParam("id") Long id) {
        FinancialProductFundingDO financialProductFunding = financialProductFundingService.get(id);
        return success(FinancialProductFundingConvert.INSTANCE.convert(financialProductFunding));
    }

    // 获取金融产品融资信息文件模板
    @GetMapping("/template")
    @ApiOperation("获取金融产品融资信息文件模板")
    public void getTemplate(HttpServletResponse response) {
        ExcelKits.getTemplate("templates/诚信白沙-金融产品融资信息上传模版.xlsx",response, "诚信白沙-金融产品融资信息上传模版");
    }

}
