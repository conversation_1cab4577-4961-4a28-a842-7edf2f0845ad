package com.hainancrc.module.creditcxbs.controller.teaorigincoderecord.admin;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordPageRespVO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.dto.TeaOriginCodeRecordCreateDTO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.dto.TeaOriginCodeRecordPageDTO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.dto.TeaOriginCodeRecordUpdateDTO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordDetailRespVO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordRespVO;
import com.hainancrc.module.creditcxbs.convert.teaorigincoderecord.TeaOriginCodeRecordConvert;
import com.hainancrc.module.creditcxbs.entity.TeaOriginCodeRecordDO;
import com.hainancrc.module.creditcxbs.service.teaorigincoderecord.TeaOriginCodeRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Slf4j
@Api(tags = "产地码申报记录")
@RestController
@RequestMapping("/teaorigincoderecord")
@Validated
public class TeaOriginCodeRecordController {

    @Resource
    private TeaOriginCodeRecordService teaOriginCodeRecordService;


    @PostMapping("/create")
    @ApiOperation("新增产地码")
    public CommonResult<Long> create(@Valid @RequestBody TeaOriginCodeRecordCreateDTO createDTO) {
        return success(teaOriginCodeRecordService.create(createDTO));
    }

    @PostMapping("/apply")
    @ApiOperation("继续申领产地码")
    public CommonResult<Long> apply(@Valid @RequestBody TeaOriginCodeRecordCreateDTO createDTO) {
        return success(teaOriginCodeRecordService.apply(createDTO));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody TeaOriginCodeRecordUpdateDTO updateDTO) {
        teaOriginCodeRecordService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<TeaOriginCodeRecordRespVO>> getList() {
        List<TeaOriginCodeRecordDO> list = teaOriginCodeRecordService.getList();
        return success(TeaOriginCodeRecordConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<TeaOriginCodeRecordPageRespVO>> getPage(@Valid TeaOriginCodeRecordPageDTO pageDTO) {
        return success(teaOriginCodeRecordService.getPage(pageDTO));
    }

    @GetMapping("/recordPage")
    @ApiOperation("申领记录分页")
    public CommonResult<PageResult<TeaOriginCodeRecordDetailRespVO>> getRecordPage(@Valid TeaOriginCodeRecordPageDTO pageDTO) {
        return success(teaOriginCodeRecordService.getRecordPage(pageDTO));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        teaOriginCodeRecordService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得记录详情")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<TeaOriginCodeRecordRespVO> get(@RequestParam("id") Long id) {
        return success(teaOriginCodeRecordService.get(id));
    }

    @GetMapping("/promiseLetter")
    @ApiOperation(value = "下载产地申领承诺书")
    public void downloadPromiseLetter(HttpServletResponse response) {
        ClassPathResource promiseLetter = new ClassPathResource("templates/产地码申请承诺书.docx");
        try (InputStream inputStream = promiseLetter.getInputStream()) {
            if (promiseLetter.exists()) {
                String filename = URLEncoder.encode("产地码申请承诺书.docx", "UTF-8").replaceAll("\\+", "%20");
                response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
                response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename);

                IOUtils.copy(inputStream, response.getOutputStream());
                response.flushBuffer();
            } else {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            }

        } catch (IOException e) {
            log.info("下载模板文件错误", e);
        }
    }
}
