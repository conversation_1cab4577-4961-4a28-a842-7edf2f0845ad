package com.hainancrc.module.creditcxbs.controller.oilteapolicy.admin;

import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.module.creditcxbs.api.oilteapolicy.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteapolicy.vo.*;
import com.hainancrc.module.creditcxbs.convert.oilteapolicy.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.oilteapolicy.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "油茶政策信息")
@RestController
@RequestMapping("/oilteapolicy")
@Validated
public class OilTeaPolicyController {
    
    @Resource
    private OilTeaPolicyService oilTeaPolicyService;


    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Long> create(@Valid @RequestBody OilTeaPolicyCreateDTO createDTO) {
        return success(oilTeaPolicyService.create(createDTO));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody OilTeaPolicyUpdateDTO updateDTO) {
        oilTeaPolicyService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<OilTeaPolicyRespVO>> getList() {
        List<OilTeaPolicyDO> list = oilTeaPolicyService.getList();
        return success(OilTeaPolicyConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<OilTeaPolicyRespVO>> getPage(@Valid OilTeaPolicyPageDTO pageDTO) {
        PageResult<OilTeaPolicyDO> pageResult = oilTeaPolicyService.getPage(pageDTO);
        return success(OilTeaPolicyConvert.INSTANCE.convertPage(pageResult));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        oilTeaPolicyService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<OilTeaPolicyRespVO> get(@RequestParam("id") Long id) {
        OilTeaPolicyDO oilTeaPolicy = oilTeaPolicyService.get(id);
        return success(OilTeaPolicyConvert.INSTANCE.convert(oilTeaPolicy));
    }


}
