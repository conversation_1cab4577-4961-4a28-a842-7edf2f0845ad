package com.hainancrc.module.creditcxbs.mapper.oilteaestate;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaEstateEstateStatus;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.oilteaestate.dto.*;

import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface OilTeaEstateMapper extends BaseMapperX<OilTeaEstateDO> {
    
    
    IPage<OilTeaEstateDO> selectPage(@Param("page") Page<OilTeaEstateDO> page,
                                     @Param("dto") OilTeaEstatePageDTO reqDTO);

    default List<OilTeaEstateDO> selectEstateList() {
        return selectList(new LambdaQueryWrapperX<OilTeaEstateDO>()
                .eq(OilTeaEstateDO::getEstateStatus, OilTeaEstateEstateStatus.ONLINE)
                .orderByDesc(OilTeaEstateDO::getUpdateTime)
                .last(" limit 5"));
    };

    default List<OilTeaEstateDO> selectStatisticsList() {
        return selectList(new LambdaQueryWrapperX<OilTeaEstateDO>()
                .eq(OilTeaEstateDO::getEstateStatus, OilTeaEstateEstateStatus.ONLINE)
                .orderByDesc(OilTeaEstateDO::getUpdateTime));
    };

}

