package com.hainancrc.module.creditcxbs.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.util.Date;


/**
 * 场地码查看按日统计表 DO
 */
@TableName("cxbs_tea_origin_code_view")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuarantineInformationViewDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 商户id
     */
    private Long companyId;

    /**
     * 茶叶产地码ID
     */
    private Long teaOriginCodeId;

    /**
     * 查看次数
     */
    private Long viewCount;

    /**
     * 查看日期
     */
    private Date viewDate;

}
