package com.hainancrc.module.creditcxbs.entity;

import java.math.BigDecimal;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;


/**
 * 产地主体对应用户表 DO
 */
@TableName("cxbs_origin_subject_user")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OriginSubjectUserDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 产地主体id
     */
    private Long teaSubjectId;

    /**
     * 用户id
     */
    private String userId;


}
