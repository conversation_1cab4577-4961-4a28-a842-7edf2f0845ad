package com.hainancrc.module.creditcxbs.controller.teasubject.admin;

import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.module.creditcxbs.api.teasubject.dto.*;
import com.hainancrc.module.creditcxbs.api.teasubject.vo.*;
import com.hainancrc.module.creditcxbs.convert.teasubject.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.teasubject.*;
import com.hainancrc.module.creditcxbs.utils.ExcelKits;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "茶叶经营主体信息")
@RestController
@RequestMapping("/teaSubject")
@Validated
public class TeaSubjectController {
    
    @Resource
    private TeaSubjectService teaSubjectService;


    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Long> create(@Valid @RequestBody TeaSubjectCreateDTO createDTO) {
        return success(teaSubjectService.create(createDTO));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody TeaSubjectUpdateDTO updateDTO) {
        teaSubjectService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<TeaSubjectRespVO>> getList() {
        List<TeaSubjectDO> list = teaSubjectService.getList();
        return success(TeaSubjectConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<TeaSubjectRespVO>> getPage(@Valid TeaSubjectPageDTO pageDTO) {
        PageResult<TeaSubjectDO> pageResult = teaSubjectService.getPage(pageDTO);
        return success(TeaSubjectConvert.INSTANCE.convertPage(pageResult));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        teaSubjectService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<TeaSubjectRespVO> get(@RequestParam("id") Long id) {
        TeaSubjectDO teaSubject = teaSubjectService.get(id);
        return success(TeaSubjectConvert.INSTANCE.convert(teaSubject));
    }

    @GetMapping("/template")
    @ApiOperation("获取模板")
    public void getTemplate(HttpServletResponse response) {
        ExcelKits.getTemplate("templates/tea.xlsx",response,"导入茶叶经营主体模板");
    }

    @PostMapping("/importFile")
    @ApiOperation("导入茶叶经营主体")
    public CommonResult<String> importFile(@ApiParam(value = "文件流", required = true) @RequestPart("file") MultipartFile file) throws Exception {
        return success(teaSubjectService.importFile(file));
    }

}
