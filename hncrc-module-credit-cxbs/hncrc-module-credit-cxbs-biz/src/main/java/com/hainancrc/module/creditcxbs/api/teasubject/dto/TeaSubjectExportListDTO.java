package com.hainancrc.module.creditcxbs.api.teasubject.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-23
 */

@Data
public class TeaSubjectExportListDTO {

    /**
     * 商户名称(主体名称)
     */
    @ApiModelProperty(value = "商户名称(主体名称)")
    @ExcelProperty(value = "经营主体名称（必填）")
    @NotBlank(message = "经营主体名称不能为空")
    @Size(max = 100, message = "经营主体名称不能超过100个字符")
    private String subjectName;

    /**
     * 主题类型
     */
    @ApiModelProperty(value = "主体类型")
    @ExcelProperty(value = "主体类型（必填）")
    @NotBlank(message = "主体类型不能为空")
    @Pattern(regexp = "^(农户|合作社|企业|个体工商户)$", message = "主体类型只能是农户、合作社、企业、个体工商户")
    @Size(max = 10, message = "主体类型不能超过10个字符")
    private String subjectType;

    /**
     * 统一信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    @ExcelProperty(value = "统一社会信用代码（必填：主体类型为农户时非必填）")
    @Size(max = 18, message = "统一社会信用代码不能超过18个字符")
    private String uniscid;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法人/经营者")
    @ExcelProperty(value = "法人/经营者（必填）")
    @NotBlank(message = "法人/经营者不能为空")
    @Size(max = 100, message = "法人/经营者不能超过100个字符")
    private String legalName;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    @ExcelProperty(value = "经营地址")
    @Size(max = 200, message = "经营地址不能超过200个字符")
    private String subjectAddress;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @ExcelProperty(value = "联系电话")
    @Size(max = 20, message = "联系电话不能超过20个字符")
    private String telephone;

//    /**
//     * 种植面积
//     */
//    @ApiModelProperty(value = "种植面积（亩）")
//    @ExcelProperty(value = "种植面积（亩）")
//    private BigDecimal plantingArea;

    /**
     * 茶叶品种
     */
    @ApiModelProperty(value = "茶叶品种")
    @ExcelProperty(value = "经营茶叶品种")
    @Size(max = 100, message = "经营茶叶品种不能超过100个字符")
    private String teaType;

//    /**
//     * 茶青产量(吨)
//     */
//    @ApiModelProperty(value = "茶青产量（吨）")
//    @ExcelProperty(value = "茶青产量（吨）")
//    private BigDecimal teaYield;
}
