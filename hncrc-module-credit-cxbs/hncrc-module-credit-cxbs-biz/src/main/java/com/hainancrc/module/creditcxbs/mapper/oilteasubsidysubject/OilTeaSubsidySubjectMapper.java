package com.hainancrc.module.creditcxbs.mapper.oilteasubsidysubject;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.dto.*;

import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface OilTeaSubsidySubjectMapper extends BaseMapperX<OilTeaSubsidySubjectDO> {
    
    
    default PageResult<OilTeaSubsidySubjectDO> selectPage(OilTeaSubsidySubjectPageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<OilTeaSubsidySubjectDO>()
                .orderByDesc(OilTeaSubsidySubjectDO::getCreateTime));
    };

    default List<OilTeaSubsidySubjectDO> selectSubsidySubjectByNoteId(Long noteId) {
        return selectList(new LambdaQueryWrapperX<OilTeaSubsidySubjectDO>()
                .eq(OilTeaSubsidySubjectDO::getNoteId, noteId)
                .orderByDesc(OilTeaSubsidySubjectDO::getCreateTime));
    };

    List<OilTeaSubsidySubjectDO> selectSubsidySubjectList();

}

