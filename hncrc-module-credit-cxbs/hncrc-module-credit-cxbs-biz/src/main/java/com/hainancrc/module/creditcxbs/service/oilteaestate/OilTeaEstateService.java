package com.hainancrc.module.creditcxbs.service.oilteaestate;

import java.util.*;
import javax.validation.*;
import com.hainancrc.module.creditcxbs.api.oilteaestate.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteaestate.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
*  Service 接口
*
*/
public interface OilTeaEstateService {
    
    
    /**
    * 创建
    *
    * @param createDTO 创建信息
    * @return 编号
    */
    Long create(@Valid OilTeaEstateCreateDTO createDTO);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid OilTeaEstateUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    List<OilTeaEstateDO> getList();

    /**
     * 数据总览 获得列表
     * @return 列表
     */
    List<OilTeaEstateDO> getStatisticsList();
    
    /**
    * 获得分页
    *
    * @param pageDTO 分页查询
    * @return 分页
    */
    PageResult<OilTeaEstateDO> getPage(OilTeaEstatePageDTO pageDTO);
    
    
    /**
    * 删除
    *
    * @param id 主键
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id 主键
    * @return
    */
    OilTeaEstateDO get(Long id);
    

}

