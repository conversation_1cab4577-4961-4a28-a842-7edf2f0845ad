package com.hainancrc.module.creditcxbs.controller.seedlingsubject.admin;

import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.dto.*;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.vo.*;
import com.hainancrc.module.creditcxbs.convert.seedlingsubject.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.seedlingsubject.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "苗木经营主体信息")
@RestController
@RequestMapping("/seedlingSubject")
@Validated
public class SeedlingSubjectController {
    
    @Resource
    private SeedlingSubjectService seedlingSubjectService;


    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Long> create(@Valid @RequestBody SeedlingSubjectCreateDTO createDTO) {
        return success(seedlingSubjectService.create(createDTO));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody SeedlingSubjectUpdateDTO updateDTO) {
// !!REVIEW
// TODO 可能的问题: 在update方法中，如果updateDTO验证失败，可能会抛出异常，从而影响返回的响应结果，需要在Controller层中进行异常处理。
// 修改建议：建议添加全局异常处理器，或者对updateDTO的有效性进行处理。
        seedlingSubjectService.update(updateDTO);
        return success(true);
    }


//    @GetMapping("/list")
//    @ApiOperation("获得商户简介列表")
//    public CommonResult<List<SeedlingSubjectMerchantProfileVO>> getList(@Valid @RequestBody SeedlingSubjectSelectDTO selectDTO) {
//        List<SeedlingSubjectMerchantProfileVO> list = seedlingSubjectService.getList(selectDTO);
//        return success(list);

//    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<SeedlingSubjectRespVO>> getPage(@Valid SeedlingSubjectPageDTO pageDTO) {
        return success(seedlingSubjectService.getPage(pageDTO));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        seedlingSubjectService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("经营主体基础信息")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<SeedlingSubjectRespVO> get(@RequestParam("id") Long id) throws Exception{
        return success(seedlingSubjectService.get(id));
    }

    @GetMapping("/getByCode")
    @ApiOperation("根据code值获取经营主体信息")
    public CommonResult<SeedlingSubjectRespVO> getByCode(@RequestParam("code") String code) {
        return success(seedlingSubjectService.getByCode(code));
    }

    // 根据苗木id获取经营主体信息列表
    @GetMapping("/getSubjectListByCategoryId")
    @ApiOperation("根据苗木id获取经营主体信息列表")
    public CommonResult<PageResult<SeedlingSubjectRespVO>> getSubjectListBySeedlingId(@Valid SeedlingSubjectPageDTO pageDTO,@RequestParam("CategoryId") Long CategoryId) {
        PageResult<SeedlingSubjectDO> pageResult = seedlingSubjectService.getSubjectListBySeedlingId(pageDTO,CategoryId);
        return success(SeedlingSubjectConvert.INSTANCE.convertPage(pageResult));
    }

    @ApiOperation("获取苗木种类列表")
    @GetMapping("/getSeedlingCategory")
    public CommonResult<List<SeedlingCategoryVO>> getSeedlingCategory() {
        List<SeedlingCategoryVO> list = seedlingSubjectService.getSeedlingCategory();
        return success(list);
    }

    @ApiOperation("获取苗木主体信用评价信息")
    @GetMapping("/getScoreAndLevel")
    public CommonResult<SeedlingScoreLevelVO> getScoreAndLevel(@RequestParam("id") Long id, @RequestParam("scoreList") List<Integer> scoreList) {
        return success(seedlingSubjectService.getScoreAndLevel(id, scoreList));
    }

}
