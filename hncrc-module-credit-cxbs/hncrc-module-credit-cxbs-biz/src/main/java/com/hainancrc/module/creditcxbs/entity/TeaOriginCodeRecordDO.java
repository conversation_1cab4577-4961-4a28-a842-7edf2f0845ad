package com.hainancrc.module.creditcxbs.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;

/**
 * 产地码申领 DO
 */
@TableName("cxbs_tea_origin_code_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeaOriginCodeRecordDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 产地码id
     */
    private Long originCodeId;

    /**
     * 产地id
     */
    private Long OriginId;

    /**
     * 茶叶品种
     */
    private String teaType;

    /**
     * 查看次数
     */
    private Long viewCount;

    /**
     * 用码数（次）
     */
    private Long useCount;

    /**
     * 当月茶青总数（斤）
     */
    private BigDecimal totalTeaWeight;

    /**
     * 承诺书文件key
     */
    private String promiseFileKey;

    /**
     * 承诺书文件url
     */
    private String promiseFileUrl;
}
