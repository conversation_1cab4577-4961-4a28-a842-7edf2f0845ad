package com.hainancrc.module.creditcxbs.mapper.financialproductfunding;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.financialproductfunding.dto.*;

import org.apache.ibatis.annotations.*;

@Mapper
public interface FinancialProductFundingMapper extends BaseMapperX<FinancialProductFundingDO> {
    
    
//    PageResult<FinancialProductFundingDO> selectPage(@Param("dto") FinancialProductFundingPageDTO reqDTO);
}

