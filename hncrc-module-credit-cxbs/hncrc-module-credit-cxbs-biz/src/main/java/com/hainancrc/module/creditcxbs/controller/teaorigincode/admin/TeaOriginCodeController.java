package com.hainancrc.module.creditcxbs.controller.teaorigincode.admin;

import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.module.creditcxbs.api.teaorigincode.dto.*;
import com.hainancrc.module.creditcxbs.api.teaorigincode.vo.*;
import com.hainancrc.module.creditcxbs.convert.teaorigincode.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.teaorigincode.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "产地码")
@RestController
@RequestMapping("/teaorigincode")
@Validated
public class TeaOriginCodeController {
    
    @Resource
    private TeaOriginCodeService teaOriginCodeService;


    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Long> create(@Valid @RequestBody TeaOriginCodeCreateDTO createDTO) {
        return success(teaOriginCodeService.create(createDTO));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody TeaOriginCodeUpdateDTO updateDTO) {
        teaOriginCodeService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<TeaOriginCodeRespVO>> getList() {
        List<TeaOriginCodeDO> list = teaOriginCodeService.getList();
        return success(TeaOriginCodeConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<TeaOriginCodePageRespVO>> getPage(@Valid TeaOriginCodePageDTO pageDTO) {
        return success(teaOriginCodeService.getPage(pageDTO));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        teaOriginCodeService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<TeaOriginCodeRespVO> get(@RequestParam("id") Long id) {
        TeaOriginCodeDO teaOriginCode = teaOriginCodeService.get(id);
        return success(TeaOriginCodeConvert.INSTANCE.convert(teaOriginCode));
    }

    @GetMapping("/getByCode")
    @ApiOperation("通过码值获取产地信息")
    public CommonResult<TeaOriginCodeSubjectRespVO> getByCode(@RequestParam(value = "code") String code) {
        return success(teaOriginCodeService.getByCode(code));
    }


}
