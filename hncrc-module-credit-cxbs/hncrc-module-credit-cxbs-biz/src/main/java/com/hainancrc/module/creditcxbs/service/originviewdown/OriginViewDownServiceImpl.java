package com.hainancrc.module.creditcxbs.service.originviewdown;

import com.alibaba.fastjson.JSON;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.originviewdown.dto.OriginViewDownCreateDTO;
import com.hainancrc.module.creditcxbs.api.originviewdown.dto.OriginViewDownPageDTO;
import com.hainancrc.module.creditcxbs.api.originviewdown.dto.OriginViewDownUpdateDTO;
import com.hainancrc.module.creditcxbs.convert.originviewdown.OriginViewDownConvert;
import com.hainancrc.module.creditcxbs.entity.OriginViewDownDO;
import com.hainancrc.module.creditcxbs.mapper.originviewdown.OriginViewDownMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-20
 */

@Service
@Slf4j
public class OriginViewDownServiceImpl implements OriginViewDownService {

    @Resource
    private OriginViewDownMapper originViewDownMapper;
    @Override
    public Long create(OriginViewDownCreateDTO createDTO) {
        log.info("扫码查看入参-create: {}", JSON.toJSONString(createDTO));

        //查询当天是否有查看记录
        Date date = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String viewDate = formatter.format(date);
        OriginViewDownDO originViewDownDO = originViewDownMapper.selectView(createDTO.getCompanyId(), viewDate);
        if (originViewDownDO == null) {
            OriginViewDownDO aDo = new OriginViewDownDO();
            switch (createDTO.getType()){
                case "familyCodeDownload":
                    aDo.setFamilyCodeView(0L);
                    aDo.setFamilyCodeDownload(1L);
                    aDo.setSeedlingCodeView(0L);
                    aDo.setSeedlingCodeDownload(0L);
                    break;
                case "familyCodeView":
                    aDo.setFamilyCodeDownload(1L);
                    aDo.setFamilyCodeView(0L);
                    aDo.setSeedlingCodeView(0L);
                    aDo.setSeedlingCodeDownload(0L);
                    break;
                case "seedlingCodeDownload":
                    aDo.setSeedlingCodeDownload(1L);
                    aDo.setSeedlingCodeDownload(0L);
                    aDo.setFamilyCodeView(0L);
                    aDo.setSeedlingCodeDownload(0L);
                    break;
                 case "seedlingCodeView":
                    aDo.setSeedlingCodeView(1L);
                    aDo.setFamilyCodeView(0L);
                    aDo.setSeedlingCodeView(0L);
                    aDo.setFamilyCodeDownload(0L);
                    break;
            }
            aDo.setViewDate(new Date());
            aDo.setCompanyId(createDTO.getCompanyId());
            aDo.setDeleted(false);
            originViewDownMapper.insert(aDo);
            return aDo.getId();
        }
        recordViewCount(createDTO, originViewDownDO);
        return originViewDownDO.getId();
    }

    private void recordViewCount(OriginViewDownCreateDTO createDTO, OriginViewDownDO originViewDownDO){
        switch (createDTO.getType()){
            case "familyCodeDownload":
                Long download = originViewDownDO.getFamilyCodeDownload();
                originViewDownDO.setFamilyCodeDownload(download + 1);
                break;
            case "familyCodeView":
                Long view = originViewDownDO.getFamilyCodeView();
                originViewDownDO.setFamilyCodeView(view + 1);
                break;
            case "seedlingCodeDownload":
                Long seedlingCodeDownload = originViewDownDO.getSeedlingCodeDownload();
                originViewDownDO.setSeedlingCodeDownload(seedlingCodeDownload + 1);
                break;
            case "seedlingCodeView":
                Long seedlingCodeView = originViewDownDO.getSeedlingCodeView();
                originViewDownDO.setSeedlingCodeView(seedlingCodeView + 1);
                break;
        }
        originViewDownDO.setCompanyId(createDTO.getCompanyId());
        originViewDownDO.setViewDate(new Date());
        originViewDownMapper.updateById(originViewDownDO);
    }

    @Override
    public void update(OriginViewDownUpdateDTO updateDTO) {
        OriginViewDownDO updateDO =  OriginViewDownConvert.INSTANCE.convert(updateDTO);
        originViewDownMapper.updateById(updateDO);
    }

    @Override
    public List<OriginViewDownDO> getList() {
        return originViewDownMapper.selectList();
    }

    @Override
    public PageResult<OriginViewDownDO> getPage(OriginViewDownPageDTO pageDTO) {
        return originViewDownMapper.selectPage(pageDTO);
    }

    @Override
    public void delete(Long id) {
        originViewDownMapper.deleteById(id);
    }

}
