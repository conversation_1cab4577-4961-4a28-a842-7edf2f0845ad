package com.hainancrc.module.creditcxbs.mapper.oilteasubject;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.oiltea.vo.OilTeaOverviewVO;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.oilteasubject.dto.*;

import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

@Mapper
public interface OilTeaSubjectMapper extends BaseMapperX<OilTeaSubjectDO> {
    default PageResult<OilTeaSubjectDO> selectPage(OilTeaSubjectPageDTO pageDTO) {
        return selectPage(pageDTO, new LambdaQueryWrapperX<OilTeaSubjectDO>()
                .likeIfPresent(OilTeaSubjectDO::getSubjectName, pageDTO.getSubjectName())
                .eqIfPresent(OilTeaSubjectDO::getSubjectType, pageDTO.getSubjectType())
                .orderByDesc(OilTeaSubjectDO::getUpdateTime));
    }


//    PageResult<OilTeaSubjectDO> selectPage(@Param("dto") OilTeaSubjectPageDTO reqDTO);

    /**
     * 查询油茶统计数据
     */
    OilTeaOverviewVO selectStatistics();

    List<Map<String, Object>> selectSubjectTypeCount();
}

