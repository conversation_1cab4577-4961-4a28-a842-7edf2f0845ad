package com.hainancrc.module.creditcxbs.entity;

import java.math.BigDecimal;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;


/**
 * 油茶补贴主体名单 DO
 */
@TableName("cxbs_oil_tea_subsidy_subject")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OilTeaSubsidySubjectDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 补贴公示信息id
     */
    private Long noteId;

    /**
     * 乡镇
     */
    private String township;

    /**
     * 种植户名称
     */
    private String planterName;

    /**
     * 种植面积(亩)
     */
    private BigDecimal plantArea;

    /**
     * 种植株数(株)
     */
    private Long plantCount;

    /**
     * 存活率
     */
    private BigDecimal survivalRate;

    /**
     * 检查情况
     */
    private String checkStatus;

    /**
     * 补贴金额(元)
     */
    private BigDecimal subsidyAmount;

    /**
     * 备注
     */
    private String remark;


}
