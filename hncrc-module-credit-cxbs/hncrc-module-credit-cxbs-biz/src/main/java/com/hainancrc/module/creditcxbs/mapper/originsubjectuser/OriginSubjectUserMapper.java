package com.hainancrc.module.creditcxbs.mapper.originsubjectuser;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.originsubjectuser.dto.*;

import org.apache.ibatis.annotations.*;

@Mapper
public interface OriginSubjectUserMapper extends BaseMapperX<OriginSubjectUserDO> {
    
    
    PageResult<OriginSubjectUserDO> selectPage(@Param("dto") OriginSubjectUserPageDTO reqDTO);
}

