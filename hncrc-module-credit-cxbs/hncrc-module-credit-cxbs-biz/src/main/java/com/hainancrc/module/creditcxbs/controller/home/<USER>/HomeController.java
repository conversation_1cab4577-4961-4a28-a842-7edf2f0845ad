package com.hainancrc.module.creditcxbs.controller.home.admin;

import javax.validation.Valid;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.financialpolicy.vo.FinancialPolicyRespVO;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.dto.SeedlingCategoryPageDTO;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.vo.SeedlingCategoryRespVO;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.dto.SeedlingSubjectPageDTO;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.dto.SeedlingSubjectSelectDTO;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.vo.*;
import com.hainancrc.module.creditcxbs.api.teaorigin.vo.TeaOriginRespVO;
import com.hainancrc.module.creditcxbs.api.teasubject.vo.TeaSubjectRespVO;
import com.hainancrc.module.creditcxbs.convert.financialpolicy.FinancialPolicyConvert;
import com.hainancrc.module.creditcxbs.convert.seedlingcategory.SeedlingCategoryConvert;
import com.hainancrc.module.creditcxbs.convert.seedlingsubject.SeedlingSubjectConvert;
import com.hainancrc.module.creditcxbs.service.financialproductviewstatistics.FinancialProductViewStatisticsService;
import io.swagger.annotations.ApiImplicitParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.creditcxbs.entity.FinancialPolicyDO;
import com.hainancrc.module.creditcxbs.entity.SeedlingCategoryDO;
import com.hainancrc.module.creditcxbs.entity.SeedlingSubjectDO;
import com.hainancrc.module.creditcxbs.service.financialpolicy.FinancialPolicyService;
import com.hainancrc.module.creditcxbs.service.seedlingcategory.SeedlingCategoryService;
import com.hainancrc.module.creditcxbs.service.seedlingsubject.SeedlingSubjectService;
import com.hainancrc.module.creditcxbs.service.teasubject.TeaSubjectService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "首页")
@RestController
@RequestMapping("/home")
@Validated
public class HomeController {

    @Autowired
    private FinancialPolicyService financialPolicyService;

    @Autowired
    private SeedlingCategoryService seedlingCategoryService;

    @Autowired
    private SeedlingSubjectService seedlingSubjectService;

    @Autowired
    private TeaSubjectService teaSubjectService;

    @Autowired
    private FinancialProductViewStatisticsService financialProductViewStatisticsService;

    @ApiOperation("首页获取金融政策列表")
    @GetMapping("/getFinancialPolicyList")
    public CommonResult<List<FinancialPolicyRespVO>> getFinancialPolicyList() {
        List<FinancialPolicyDO> list = financialPolicyService.getFinancialPolicyList();
        // 更新统计量
        financialProductViewStatisticsService.updateStatistics();
        return CommonResult.success(FinancialPolicyConvert.INSTANCE.convertList(list));
    }

    @ApiOperation("查看金融政策详情")
    @GetMapping("/getFinancialPolicyDetail")
    public CommonResult<FinancialPolicyDO> getFinancialPolicyDetail(@RequestParam("id") Long id) {
        return CommonResult.success(financialPolicyService.get(id));
    }

    @GetMapping("/getSeedlingCategoryList")
    @ApiOperation("获取首页特色苗木列表")
    public CommonResult<PageResult<SeedlingCategoryRespVO>> getPage(@Valid SeedlingCategoryPageDTO pageDTO) {
        PageResult<SeedlingCategoryDO> pageResult = seedlingCategoryService.getPageHome(pageDTO);
        return success(SeedlingCategoryConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/getSeedlingSubjectList")
    @ApiOperation("获得商户简介列表")
    public CommonResult<PageResult<SeedlingSubjectMerchantProfileVO>> getList(@Valid SeedlingSubjectSelectDTO selectDTO) {
        return success(seedlingSubjectService.getList(selectDTO));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<SeedlingSubjectRespVO>> getPage(@Valid SeedlingSubjectPageDTO pageDTO) {
        return success(seedlingSubjectService.getPage(pageDTO));
    }

    @ApiOperation("获取首页苗木主体数量统计信息")
    @GetMapping("/getSubjectStatistics")
    public CommonResult<SubjectStatisticsVO> getSubjectStatistics() {
        return CommonResult.success(seedlingSubjectService.getSubjectStatistics());
    }

    @ApiOperation("获取首页A级主体数量统计信息")
    @GetMapping("/getALevelSubjectStatistics")
    public CommonResult<ALevelSubjectStatisticsVO> getALevelSubjectStatistics() {
        return CommonResult.success(seedlingSubjectService.getALevelSubjectStatistics());
    }

    // 扫码统计
    @ApiOperation("获取首页A级主体扫码数量统计信息")
    @GetMapping("/getALevelSubjectScanStatistics")
    public CommonResult<ALevelSubjectScanStatisticsVO> getALevelSubjectScanStatistics() {
        return CommonResult.success(seedlingSubjectService.getALevelSubjectScanStatistics());
    }


    @GetMapping("/getSubjectById")
    @ApiOperation("经营主体基础信息首页")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<SeedlingSubjectRespVO> getHome(@RequestParam("id") Long id) {
        SeedlingSubjectDO seedlingSubject = seedlingSubjectService.getHome(id);
        return success(SeedlingSubjectConvert.INSTANCE.convert(seedlingSubject));
    }

    @GetMapping("/getTeaSubjectList")
    @ApiOperation("获取茶叶经营主体列表")
    public CommonResult<List<TeaSubjectRespVO>> getTeaSubjectList() {
        return success(teaSubjectService.getTeaSubjectList());
    }

    @GetMapping("/getTeaInfo")
    @ApiOperation("获取茶园信息")
    public CommonResult<TeaOriginRespVO> getTeaInfo(@RequestParam("teaOrigin") String originName,
                                                    @RequestParam("id") String id) {
        return success(teaSubjectService.getTeaInfo(originName, id));
    }




}


