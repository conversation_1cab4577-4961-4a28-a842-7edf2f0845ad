package com.hainancrc.module.creditcxbs.convert.teaorigincoderecord;

import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.dto.TeaOriginCodeRecordCreateDTO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.dto.TeaOriginCodeRecordUpdateDTO;
import com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordRespVO;
import com.hainancrc.module.creditcxbs.entity.TeaOriginCodeRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 *  Convert
 *
 */
@Mapper
public interface TeaOriginCodeRecordConvert {

    TeaOriginCodeRecordConvert INSTANCE = Mappers.getMapper(TeaOriginCodeRecordConvert.class);

    TeaOriginCodeRecordDO convert(TeaOriginCodeRecordCreateDTO bean);

    TeaOriginCodeRecordDO convert(TeaOriginCodeRecordUpdateDTO bean);

    List<TeaOriginCodeRecordRespVO> convertList(List<TeaOriginCodeRecordDO> list);


}
