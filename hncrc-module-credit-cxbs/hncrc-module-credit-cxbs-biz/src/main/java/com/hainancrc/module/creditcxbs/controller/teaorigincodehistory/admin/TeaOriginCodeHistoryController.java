package com.hainancrc.module.creditcxbs.controller.teaorigincodehistory.admin;

import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.module.creditcxbs.api.teaorigincodehistory.dto.*;
import com.hainancrc.module.creditcxbs.api.teaorigincodehistory.vo.*;
import com.hainancrc.module.creditcxbs.convert.teaorigincodehistory.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.teaorigincodehistory.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "产地码申报历史记录")
@RestController
@RequestMapping("/teaorigincodehistory")
@Validated
public class TeaOriginCodeHistoryController {
    
    @Resource
    private TeaOriginCodeHistoryService teaOriginCodeHistoryService;


    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Long> create(@Valid @RequestBody TeaOriginCodeHistoryCreateDTO createDTO) {
        return success(teaOriginCodeHistoryService.create(createDTO));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody TeaOriginCodeHistoryUpdateDTO updateDTO) {
        teaOriginCodeHistoryService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<TeaOriginCodeHistoryRespVO>> getList() {
        List<TeaOriginCodeHistoryDO> list = teaOriginCodeHistoryService.getList();
        return success(TeaOriginCodeHistoryConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<TeaOriginCodeHistoryRespVO>> getPage(@Valid TeaOriginCodeHistoryPageDTO pageDTO) {
        PageResult<TeaOriginCodeHistoryDO> pageResult = teaOriginCodeHistoryService.getPage(pageDTO);
        return success(TeaOriginCodeHistoryConvert.INSTANCE.convertPage(pageResult));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        teaOriginCodeHistoryService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<TeaOriginCodeHistoryRespVO> get(@RequestParam("id") Long id) {
        TeaOriginCodeHistoryDO teaOriginCodeHistory = teaOriginCodeHistoryService.get(id);
        return success(TeaOriginCodeHistoryConvert.INSTANCE.convert(teaOriginCodeHistory));
    }


}
