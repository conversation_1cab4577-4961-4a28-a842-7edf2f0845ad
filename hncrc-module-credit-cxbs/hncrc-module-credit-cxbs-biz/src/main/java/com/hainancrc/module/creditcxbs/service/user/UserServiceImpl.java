package com.hainancrc.module.creditcxbs.service.user;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.redis.service.RedisService;
import com.hainancrc.module.creditcxbs.entity.OriginSubjectUserDO;
import com.hainancrc.module.creditcxbs.entity.TeaSubjectDO;
import com.hainancrc.module.creditcxbs.mapper.originsubjectuser.OriginSubjectUserMapper;
import com.hainancrc.module.creditcxbs.mapper.teasubject.TeaSubjectMapper;

import cn.hutool.core.util.ObjectUtil;

@Service
public class UserServiceImpl implements UserService {

    @Resource
    private OriginSubjectUserMapper originSubjectUserMapper;

    @Resource
    private TeaSubjectMapper teaSubjectMapper;

    @Resource
    private RedisService redisService;

    // private SystemLoginUser getSystemLoginUser() {
    //     SystemLoginUser sysLoginUser = TokenUtils.getSysLoginUser(redisService);
    //     if (ObjectUtil.isNull(sysLoginUser)) {
    //         throw new ServiceException(-1, "未获得系统登录用户");
    //     }
    //     return sysLoginUser;
    // }

    @Override
    public boolean setTeaSubjectId(String userId, Long teaSubjectId) {

        if (ObjectUtil.isNull(userId)) {
            throw new ServiceException(-1, "用户ID不能为空");
        }

        if (ObjectUtil.isNull(teaSubjectId)) {
            throw new ServiceException(-1, "产地主体ID不能为空");
        }

        TeaSubjectDO teaSubject = teaSubjectMapper.selectOne(new LambdaQueryWrapper<TeaSubjectDO>()
                .eq(TeaSubjectDO::getId, teaSubjectId));

        if (ObjectUtil.isNull(teaSubject)) {
            throw new ServiceException(-1, "产地主体不存在");
        }

        OriginSubjectUserDO originSubjectUser = originSubjectUserMapper
                .selectOne(new LambdaQueryWrapper<OriginSubjectUserDO>()
                        .eq(OriginSubjectUserDO::getUserId, userId));

        if (!ObjectUtil.isNull(originSubjectUser)) {

            originSubjectUser.setTeaSubjectId(teaSubjectId);
            return originSubjectUserMapper.updateById(originSubjectUser) > 0;

        } else {

            OriginSubjectUserDO insertObj = new OriginSubjectUserDO();
            insertObj.setUserId(userId);
            insertObj.setTeaSubjectId(teaSubjectId);

            return originSubjectUserMapper.insert(insertObj) > 0;
        }


    }

    @Override
    public Long getTeaSubjectId(String userId) {

        if (ObjectUtil.isNull(userId)) {
            throw new ServiceException(-1, "用户ID不能为空");
        }

        OriginSubjectUserDO originSubjectUser = originSubjectUserMapper.selectOne(new LambdaQueryWrapper<OriginSubjectUserDO>()
                .eq(OriginSubjectUserDO::getUserId, userId));

        if (ObjectUtil.isNull(originSubjectUser)) {
            return null;
        }

        return originSubjectUser.getTeaSubjectId();
    }
}
