package com.hainancrc.module.creditcxbs.mapper.seedlingsubjectcategory;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.seedlingsubjectcategory.dto.*;

import org.apache.ibatis.annotations.*;

@Mapper
public interface SeedlingSubjectCategoryMapper extends BaseMapperX<SeedlingSubjectCategoryDO> {
    
    
    PageResult<SeedlingSubjectCategoryDO> selectPage(@Param("dto") SeedlingSubjectCategoryPageDTO reqDTO);
}

