package com.hainancrc.module.creditcxbs;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;

@EnableEurekaClient
@SpringBootApplication(scanBasePackages = {"com.hainancrc"})
public class CreditCxbsApplication {
    public static void main(String[] args) {
        SpringApplication.run(CreditCxbsApplication.class, args);
    }
}
