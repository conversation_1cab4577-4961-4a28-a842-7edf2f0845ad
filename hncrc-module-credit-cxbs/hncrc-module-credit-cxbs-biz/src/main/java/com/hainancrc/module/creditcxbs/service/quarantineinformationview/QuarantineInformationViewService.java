package com.hainancrc.module.creditcxbs.service.quarantineinformationview;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.creditcxbs.api.quarantineinformationview.dto.QuarantineInformationViewCreateDTO;
import com.hainancrc.module.creditcxbs.api.quarantineinformationview.dto.QuarantineInformationViewPageDTO;
import com.hainancrc.module.creditcxbs.api.quarantineinformationview.dto.QuarantineInformationViewUpdateDTO;
import com.hainancrc.module.creditcxbs.entity.QuarantineInformationViewDO;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-06
 */

public interface QuarantineInformationViewService {

    /**
     * 创建
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    Long create(@Valid QuarantineInformationViewCreateDTO createDTO);


    /**
     * 修改
     *
     * @param updateDTO 更新信息
     */
    void update(@Valid QuarantineInformationViewUpdateDTO updateDTO);


    /**
     * 获得列表
     *
     * @return 列表
     */
    List<QuarantineInformationViewDO> getList();

    /**
     * 获得分页
     *
     * @param pageDTO 分页查询
     * @return 分页
     */
    PageResult<QuarantineInformationViewDO> getPage(QuarantineInformationViewPageDTO pageDTO);


    /**
     * 删除
     *
     * @param id id
     */
    void delete(Long id);



    /**
     * 获得
     *
     * @param id id
     * @return
     */
    QuarantineInformationViewDO get(Long id);

}
