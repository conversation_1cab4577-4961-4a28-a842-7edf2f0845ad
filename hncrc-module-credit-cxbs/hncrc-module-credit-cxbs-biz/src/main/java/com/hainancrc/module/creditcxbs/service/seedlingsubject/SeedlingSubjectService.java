package com.hainancrc.module.creditcxbs.service.seedlingsubject;

import java.io.IOException;
import java.util.*;
import javax.validation.*;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.dto.*;
import com.hainancrc.module.creditcxbs.api.seedlingsubject.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
*  Service 接口
*
*/
public interface SeedlingSubjectService {
    
    
    /**
    * 创建
    *
    * @param createDTO 创建信息
    * @return 编号
    */
    Long create(@Valid SeedlingSubjectCreateDTO createDTO);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid SeedlingSubjectUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    PageResult<SeedlingSubjectMerchantProfileVO> getList(SeedlingSubjectSelectDTO selectDTO);

    /**
    * 获得分页
    *
    * @param pageDTO 分页查询
    * @return 分页
    */
    PageResult<SeedlingSubjectRespVO> getPage(SeedlingSubjectPageDTO pageDTO);
    
    
    /**
    * 删除
    *
    * @param id 主键
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id 主键
    * @return
     * @throws IOException 
    */
    SeedlingSubjectRespVO get(Long id) throws IOException;

    SeedlingSubjectDO getHome(Long id);

    /**
     * 入驻经营主体数量
     * @return
     */
    Long totalCount();

    /**
     * 苗木企业信用评价指标等级数据分析
     */
    SeedlingCreditStatsVO getCreditLevelStats();

    /**
     * 获取主体类型统计数据
     */
    SeedlingSubjectTypeStatsVO getSubjectTypeStats();

    /**
     * 根据苗木id获取经营主体信息列表
     */
    PageResult<SeedlingSubjectDO> getSubjectListBySeedlingId(SeedlingSubjectPageDTO pageDTO,Long categoryId);

    /**
     * 获取A级苗木经营主体列表
     * @return
     */
    List<SeedlingSubjectDO> getASeedlingSubjectList();

    /**
     * 获取首页苗木商户简介
     * @return
     */
    List<SeedlingSubjectVO> getSeedlingSubjectInfo();

    /**
     * 获取主体统计信息
     */
    SubjectStatisticsVO getSubjectStatistics();

    /**
     * 获取A级主体统计信息
     */
    ALevelSubjectStatisticsVO getALevelSubjectStatistics();

    /**
     * 获取A级主体扫码统计信息
     */
    ALevelSubjectScanStatisticsVO getALevelSubjectScanStatistics();

    /**
     * 下载查看数量统计
     */
    OriginViewDownDO getViewDownloadCount();

    SeedlingSubjectRespVO getByCode(String code);

    List<SeedlingCategoryVO> getSeedlingCategory();

    /**
     * 获取苗木主体信用评价信息
     * @param id
     * @param scoreList
     * @return
     */
    SeedlingScoreLevelVO getScoreAndLevel(Long id, List<Integer> scoreList);
}

