package com.hainancrc.module.creditcxbs.entity;

import java.math.BigDecimal;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import com.hainancrc.module.creditcxbs.api.enums.FinancialPolicyPolicyStatus;


/**
 * 金融政策信息 DO
 */
@TableName("cxbs_financial_policy")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinancialPolicyDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 政策名称(列表,新增,编辑)
     */
    private String policyName;

    /**
     * 政策内容描述(新增,编辑)
     */
    private String policyContent;

    /**
     * 政策附件key(新增,编辑)
     */
    private String policyFileKey;

    /**
     * 政策附件url(新增,编辑)
     */
    private String policyFileUrl;

    /**
     * 政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表)ONLINE=使用中,OFFLINE=已下线
     */
    private FinancialPolicyPolicyStatus policyStatus;

    /**
     * 政策附件json
     */
    private String policyFileJson;


}
