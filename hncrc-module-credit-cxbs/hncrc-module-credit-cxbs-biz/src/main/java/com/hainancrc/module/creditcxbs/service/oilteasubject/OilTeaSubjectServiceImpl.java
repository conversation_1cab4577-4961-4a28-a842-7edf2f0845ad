package com.hainancrc.module.creditcxbs.service.oilteasubject;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.read.listener.ReadListener;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.excel.core.utils.ExcelUtils;
import com.hainancrc.module.creditcxbs.api.oiltea.vo.OilTeaOverviewVO;
import com.hainancrc.module.creditcxbs.api.oilteasubject.dto.*;
import com.hainancrc.module.creditcxbs.convert.oilteasubject.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.mapper.oilteasubject.*;
import com.hainancrc.module.creditcxbs.api.oilteasubject.vo.OilTeaSubjectTypeStatVO;

import com.hainancrc.module.creditcxbs.utils.excel.OilTeaSubjectExcelListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.creditcxbs.api.enums.ErrorCodeConstants.*;

/**
 * Service 实现类
 */
@Slf4j
@Service
@Validated
public class OilTeaSubjectServiceImpl implements OilTeaSubjectService {

    @Resource
    private OilTeaSubjectMapper oilTeaSubjectMapper;


    private void validateExists(Long id) {
        if (oilTeaSubjectMapper.selectById(id) == null) {
            throw exception(OIL_TEA_SUBJECT_NOT_EXISTS);
        }
    }


    @Override
    public Long create(OilTeaSubjectCreateDTO createDTO) {


        // 插入
        OilTeaSubjectDO oilTeaSubject = OilTeaSubjectConvert.INSTANCE.convert(createDTO);
        oilTeaSubjectMapper.insert(oilTeaSubject);
        // 返回
        return oilTeaSubject.getId();
    }

    @Override
    public void update(OilTeaSubjectUpdateDTO updateDTO) {


        // 校验存在
        this.validateExists(updateDTO.getId());
        // 更新
        OilTeaSubjectDO updateObj = OilTeaSubjectConvert.INSTANCE.convert(updateDTO);
        oilTeaSubjectMapper.updateById(updateObj);
    }


    @Override
    public PageResult<OilTeaSubjectDO> getPage(OilTeaSubjectPageDTO pageDTO) {
        return oilTeaSubjectMapper.selectPage(pageDTO);
    }

    @Override
    public List<OilTeaSubjectDO> getList() {
        return oilTeaSubjectMapper.selectList();
    }


    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateExists(id);
        // 删除
        oilTeaSubjectMapper.deleteById(id);
    }


    @Override
    public OilTeaSubjectDO get(Long id) {
        return oilTeaSubjectMapper.selectById(id);
    }

    @Override
    public String importFile(MultipartFile file) {
        if (ObjectUtils.isEmpty(file)) {
            throw new ServiceException(-1, "上传文件为空");
        }

        try {
            List<OilTeaSubjectExportListDTOS> read = EasyExcel.read(file.getInputStream(), OilTeaSubjectExportListDTOS.class, new OilTeaSubjectExcelListener())
                    .autoCloseStream(false)
                    .doReadAllSync();

            List<OilTeaSubjectDO> list = OilTeaSubjectConvert.INSTANCE.convertImportList(read);
            List<OilTeaSubjectDO> filterList = list.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() ->
                                    new TreeSet<>(Comparator.comparing(OilTeaSubjectDO::getSubjectName, Comparator.nullsLast(Comparator.naturalOrder()))
                                            .thenComparing(OilTeaSubjectDO::getSubjectType, Comparator.nullsLast(Comparator.naturalOrder()))
                                            .thenComparing(OilTeaSubjectDO::getUniscid, Comparator.nullsLast(Comparator.naturalOrder())))),
                            ArrayList::new));
            oilTeaSubjectMapper.insertBatch(filterList);
            return "导入成功";
        } catch (ExcelAnalysisException e) {
            throw new ServiceException(-1, e.getCause().getMessage());
        } catch (Exception e) {
            log.error("导入失败", e);
        }

        return "导入失败";
    }

    @Override
    public OilTeaOverviewVO getStatistics() {
        OilTeaOverviewVO vo = new OilTeaOverviewVO();

        // 获取统计数据
        OilTeaOverviewVO statistics = oilTeaSubjectMapper.selectStatistics();

        // 设置各类型数量和占比
        vo.setSubjectCount(statistics.getSubjectCount());
        vo.setEstateCount(statistics.getEstateCount());
        vo.setSubsidySubjectCount(statistics.getSubsidySubjectCount());
        vo.setSubsidyAmount(statistics.getSubsidyAmount());

        return vo;
    }

    @Override
    public OilTeaSubjectTypeStatVO getSubjectTypeStat() {
        OilTeaSubjectTypeStatVO vo = new OilTeaSubjectTypeStatVO();
        
        // 查询各类型主体数量及占比
        List<Map<String, Object>> typeStats = oilTeaSubjectMapper.selectSubjectTypeCount();
        
        // 设置各类型数量和占比
        for (Map<String, Object> stat : typeStats) {
            String type = stat.get("percentage_key").toString();
            Integer count = ((Number) stat.get("count")).intValue();
            String percentage = stat.get("percentage") + "";
            switch (type) {
                case "Enterprise":
                    vo.setEnterpriseCount(count);
                    vo.setEnterprisePercentage(percentage);
                    break;
                case "Cooperative":
                    vo.setCooperativeCount(count);
                    vo.setCooperativePercentage(percentage);
                    break;
                case "IndvBusiness":
                    vo.setPlanterCount(count);
                    vo.setPlanterPercentage(percentage);
                    break;
                case "Farmer":
                    vo.setFarmerCount(count);
                    vo.setFarmerPercentage(percentage);
                    break;
            }
        }
        
        return vo;
    }

}

