package com.hainancrc.module.creditcxbs.service.teaorigincodehistory;

import java.util.*;
import javax.validation.*;
import com.hainancrc.module.creditcxbs.api.teaorigincodehistory.dto.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
*  Service 接口
*
*/
public interface TeaOriginCodeHistoryService {
    
    
    /**
    * 创建
    *
    * @param createDTO 创建信息
    * @return 编号
    */
    Long create(@Valid TeaOriginCodeHistoryCreateDTO createDTO);
    
    
    /**
    * 修改
    *
    * @param updateDTO 更新信息
    */
    void update(@Valid TeaOriginCodeHistoryUpdateDTO updateDTO);
    
    
    /**
    * 获得列表
    *
    * @return 列表
    */
    List<TeaOriginCodeHistoryDO> getList();

    /**
     * 通过产地码申领id查询列表
     *
     * @return 列表
     */
    List<TeaOriginCodeHistoryDO> getListByRecordId(Long codeRecordId);
    
    /**
    * 获得分页
    *
    * @param pageDTO 分页查询
    * @return 分页
    */
    PageResult<TeaOriginCodeHistoryDO> getPage(TeaOriginCodeHistoryPageDTO pageDTO);
    
    
    /**
    * 删除
    *
    * @param id 主键
    */
    void delete(Long id);
    
    
    
    /**
    * 获得
    *
    * @param id 主键
    * @return
    */
    TeaOriginCodeHistoryDO get(Long id);


    /**
     * 批量保存
     * @param codeRecordId 产地码申领id
     * @param recordList 产品规格说明列表
     */
    void saveBatch(Long codeRecordId, List<TeaOriginCodeHistoryCreateDTO> recordList);

}

