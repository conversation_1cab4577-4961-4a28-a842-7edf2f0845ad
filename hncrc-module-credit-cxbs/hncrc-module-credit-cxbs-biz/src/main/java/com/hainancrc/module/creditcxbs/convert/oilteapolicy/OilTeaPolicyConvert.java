package com.hainancrc.module.creditcxbs.convert.oilteapolicy;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.oilteapolicy.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteapolicy.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface OilTeaPolicyConvert {

    OilTeaPolicyConvert INSTANCE = Mappers.getMapper(OilTeaPolicyConvert.class);


    OilTeaPolicyDO convert(OilTeaPolicyCreateDTO bean);


    OilTeaPolicyDO convert(OilTeaPolicyUpdateDTO bean);


   OilTeaPolicyRespVO convert(OilTeaPolicyDO bean);

    List<OilTeaPolicyRespVO> convertList(List<OilTeaPolicyDO> list);

    PageResult<OilTeaPolicyRespVO> convertPage(PageResult<OilTeaPolicyDO> page);



}
