package com.hainancrc.module.creditcxbs.mapper.oilteapolicy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaPolicyStatus;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.api.oilteapolicy.dto.*;

import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface OilTeaPolicyMapper extends BaseMapperX<OilTeaPolicyDO> {
    
    
    IPage<OilTeaPolicyDO> selectPage(@Param("page") Page<OilTeaPolicyDO> page,
                                     @Param("dto") OilTeaPolicyPageDTO reqDTO);

    default List<OilTeaPolicyDO> selectOilTeaPolicyList() {
        return selectList(new LambdaQueryWrapperX<OilTeaPolicyDO>()
                .eq(OilTeaPolicyDO::getPolicyStatus, OilTeaPolicyStatus.ONLINE)
                .orderByDesc(OilTeaPolicyDO::getCreateTime)
                .last(" limit 6"));
    };

}

