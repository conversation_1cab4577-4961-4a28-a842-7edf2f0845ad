package com.hainancrc.module.creditcxbs.convert.oilteasubject;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.oilteasubject.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteasubject.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface OilTeaSubjectConvert {

    OilTeaSubjectConvert INSTANCE = Mappers.getMapper(OilTeaSubjectConvert.class);


    OilTeaSubjectDO convert(OilTeaSubjectCreateDTO bean);


    OilTeaSubjectDO convert(OilTeaSubjectUpdateDTO bean);

    @Mapping(target = "subjectType", expression = "java(com.hainancrc.module.creditcxbs.api.enums.OilTeaSubjectType.getEnumByValue(bean.getSubjectType()))")
    OilTeaSubjectDO convert(OilTeaSubjectExportListDTOS bean);

   OilTeaSubjectRespVO convert(OilTeaSubjectDO bean);

    List<OilTeaSubjectRespVO> convertList(List<OilTeaSubjectDO> list);

    PageResult<OilTeaSubjectRespVO> convertPage(PageResult<OilTeaSubjectDO> page);

    List<OilTeaSubjectDO> convertImportList(List<OilTeaSubjectExportListDTOS> list);

}
