package com.hainancrc.module.creditcxbs.controller.businessfunding.admin;

import com.hainancrc.framework.common.pojo.*;
import com.hainancrc.module.creditcxbs.api.businessfunding.dto.*;
import com.hainancrc.module.creditcxbs.api.businessfunding.vo.*;
import com.hainancrc.module.creditcxbs.convert.businessfunding.*;
import com.hainancrc.module.creditcxbs.entity.*;
import com.hainancrc.module.creditcxbs.service.businessfunding.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "经营主体融资信息")
@RestController
@RequestMapping("/businessfunding")
@Validated
public class BusinessFundingController {
    
    @Resource
    private BusinessFundingService businessFundingService;


    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Long> create(@Valid @RequestBody BusinessFundingCreateDTO createDTO) {
        return success(businessFundingService.create(createDTO));
    }


    @PutMapping("/update")
    @ApiOperation("更新")
    public CommonResult<Boolean> update(@Valid @RequestBody BusinessFundingUpdateDTO updateDTO) {
        businessFundingService.update(updateDTO);
        return success(true);
    }


    @GetMapping("/list")
    @ApiOperation("获得列表")
    public CommonResult<List<BusinessFundingRespVO>> getList() {
        List<BusinessFundingDO> list = businessFundingService.getList();
        return success(BusinessFundingConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<BusinessFundingRespVO>> getPage(@Valid BusinessFundingPageDTO pageDTO) {
        PageResult<BusinessFundingDO> pageResult = businessFundingService.getPage(pageDTO);
        return success(BusinessFundingConvert.INSTANCE.convertPage(pageResult));
    }


    @DeleteMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号Id", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        businessFundingService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<BusinessFundingRespVO> get(@RequestParam("id") Long id) {
        BusinessFundingDO businessFunding = businessFundingService.get(id);
        return success(BusinessFundingConvert.INSTANCE.convert(businessFunding));
    }

    @GetMapping("/getListByFinancialProductFundingId")
    @ApiOperation("根据金融产品融资信息id获得列表")
    public CommonResult<List<BusinessFundingRespVO>> getListByFinancialProductFundingId(@RequestParam("financialProductFundingId") Long id) {
        List<BusinessFundingDO> list = businessFundingService.getListByFinancialProductFundingId(id);
        return success(BusinessFundingConvert.INSTANCE.convertList(list));
    }


}
