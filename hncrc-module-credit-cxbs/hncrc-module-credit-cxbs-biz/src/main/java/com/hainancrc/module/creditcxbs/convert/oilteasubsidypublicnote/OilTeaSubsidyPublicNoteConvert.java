package com.hainancrc.module.creditcxbs.convert.oilteasubsidypublicnote;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.creditcxbs.api.oilteasubsidypublicnote.dto.*;
import com.hainancrc.module.creditcxbs.api.oilteasubsidypublicnote.vo.*;
import com.hainancrc.module.creditcxbs.entity.*;

/**
 *  Convert
 *
 */
@Mapper
public interface OilTeaSubsidyPublicNoteConvert {

    OilTeaSubsidyPublicNoteConvert INSTANCE = Mappers.getMapper(OilTeaSubsidyPublicNoteConvert.class);


    OilTeaSubsidyPublicNoteDO convert(OilTeaSubsidyPublicNoteCreateDTO bean);


    OilTeaSubsidyPublicNoteDO convert(OilTeaSubsidyPublicNoteUpdateDTO bean);


   OilTeaSubsidyPublicNoteRespVO convert(OilTeaSubsidyPublicNoteDO bean);

    List<OilTeaSubsidyPublicNoteRespVO> convertList(List<OilTeaSubsidyPublicNoteDO> list);

    PageResult<OilTeaSubsidyPublicNoteRespVO> convertPage(PageResult<OilTeaSubsidyPublicNoteDO> page);



}
