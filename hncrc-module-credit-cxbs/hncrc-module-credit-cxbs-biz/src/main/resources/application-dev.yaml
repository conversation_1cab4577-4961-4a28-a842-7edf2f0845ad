server:
  port: 8180
  servlet:
    context-path: /cxbs
#    context-path: /credithklh
--- #################### 数据库相关配置 ####################

spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: admin # 控制台管理用户名和密码
        login-password: admin_!@#qwe
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 5 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          name: hncrc_credit_cxbs
          url: *****************************************************************************************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          username: dev
          password: dev123

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  redis:
    host: 127.0.0.1
    port: 6379
    database: 0 # 数据库索引
    timeout: 10000

# Actuator 监控端点的配置项
#management:
#  endpoints:
#    web:
#      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
#      exposure:
#        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。
#  metrics:
#    tags:
#      application: ${spring.application.name}
--- #################### 定时任务相关配置 ####################

# Spring Boot Admin 配置项
#spring:
#  boot:
#    admin:
#      # Spring Boot Admin Client 客户端的相关配置
#      client:
#        url: http://127.0.0.1:${server.port}/${spring.boot.admin.context-path} # 设置 Spring Boot Admin Server 地址
#        instance:
#          prefer-ip: true # 注册实例时，优先使用 IP
#      # Spring Boot Admin Server 服务端的相关配置
#      context-path: /admin # 配置 Spring

# 日志文件配置
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径
  level:
    # 配置自己写的 MyBatis Mapper 打印日志
    com.hainancrc.module.verify.mapper: debug
    com.hainancrc.module.corp.mapper: debug
    com.hainancrc.module.creditcxbs.mapper: debug

--- #########注册中心##########
eureka:
  client:
    service-url:
      defaultZone: http://127.0.0.1:8098/eureka
    registry-fetch-interval-seconds: 30
    register-with-eureka: true
    enabled: false
  instance:
    prefer-ip-address: true
    lease-expiration-duration-in-seconds: 30
    lease-renewal-interval-in-seconds: 30

codeengine:
  application: ZX0000029
  channel: HTML5
  origin-code-scene: CJ0000077
  credit-code-scene: CJ0000079

openapi:
  apiId: ******************
  queryAccount: cxbs

cxbs:
  zljbxx: https://test-api.hainancrc.com/api/gsk/zljbxx # 知识产权局专利基本信息查询接口
  xzcf: https://test-api.hainancrc.com/api/gsk/xzcf # 工商行政处罚查询接口
  qyzb: https://test-api.hainancrc.com/api/gsk/qyzb # 工商企业基本信息查询接口
  yzwfsx: https://test-api.hainancrc.com/api/gsk/yzwfsx # 工商严重违法失信查询接口
  sxbzxr: https://test-api.hainancrc.com/api/gsk/sxbzxr # 工商失信被执行人查询接口
  sbjjbxx: https://test-api.hainancrc.com/api/gsk/sbjjbxx # 商标局基本信息查询接口
  sdsjgxpt: https://test-api.hainancrc.com/api/sdsjgxpt/cxbs/o_hn041_dzzz_jg_seedlicence # 三林木种子生产经营许可证
  AK: AK3ApFG2ZTb5X7LK2tkM5Rz2v810WUIDFGleOzpr+3as4=
  SK: SKZbFleZprx73HpM5YueAigdaskglbtuPeiM5F1Ln0R9F=
