server:
  port: 80

--- #################### 数据库相关配置 ####################

spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: admin # 控制台管理用户名和密码
        login-password: admin_!@#qwe
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 5 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          name: hncrc
          url: ${DB_URL}&serverTimezone=Asia/Shanghai
          driver-class-name: com.mysql.jdbc.Driver
          username: ${DB_NAME}
          password: ${DB_PASSWORD}
  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    password: ${REDIS_PASSWORD}
    database: 0 # 数据库索引



--- #########注册中心##########
eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_DEFAULT_ZONE}
    registry-fetch-interval-seconds: 30
    register-with-eureka: true
  instance:
    prefer-ip-address: true
    lease-expiration-duration-in-seconds: 30
    lease-renewal-interval-in-seconds: 30


# 日志文件配置
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径
  level:
    # 配置自己写的 MyBatis Mapper 打印日志
    com.hainancrc.module.*.mapper: debug

codeengine:
  application: *********
  channel: HTML5
  origin-code-scene: *********
  credit-code-scene: *********

openapi:
  apiId: ******************
  queryAccount: cxbs

cxbs:
  zljbxx: https://test-api.hainancrc.com/api/gsk/zljbxx # 知识产权局专利基本信息查询接口
  xzcf: https://test-api.hainancrc.com/api/gsk/xzcf # 工商行政处罚查询接口
  qyzb: https://test-api.hainancrc.com/api/gsk/qyzb # 工商企业基本信息查询接口
  yzwfsx: https://test-api.hainancrc.com/api/gsk/yzwfsx # 工商严重违法失信查询接口
  sxbzxr: https://test-api.hainancrc.com/api/gsk/sxbzxr # 工商失信被执行人查询接口
  sbjjbxx: https://test-api.hainancrc.com/api/gsk/sbjjbxx # 商标局基本信息查询接口
  sdsjgxpt: https://test-api.hainancrc.com/api/sdsjgxpt/cxbs/o_hn041_dzzz_jg_seedlicence # 三林木种子生产经营许可证
  AK: AK3ApFG2ZTb5X7LK2tkM5Rz2v810WUIDFGleOzpr+3as4=
  SK: SKZbFleZprx73HpM5YueAigdaskglbtuPeiM5F1Ln0R9F=

