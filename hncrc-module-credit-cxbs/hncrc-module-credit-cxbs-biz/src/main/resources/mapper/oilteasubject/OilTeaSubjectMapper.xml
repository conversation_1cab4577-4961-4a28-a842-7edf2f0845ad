<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.oilteasubject.OilTeaSubjectMapper">

<!--    <select id="selectPage" resultType="com.hainancrc.module.creditcxbs.entity.OilTeaSubjectDO">-->
<!--        SELECT `cxbs_oil_tea_subject`.*-->
<!--        FROM `cxbs_oil_tea_subject`-->

<!--        WHERE deleted = 0-->
<!--        -->
<!--        ORDER BY  id DESC-->
<!--    </select>-->

    <select id="selectStatistics" resultType="com.hainancrc.module.creditcxbs.api.oiltea.vo.OilTeaOverviewVO">
        SELECT
                (SELECT COUNT(1) FROM cxbs_oil_tea_subject WHERE deleted = 0) AS subjectCount,
                (SELECT COUNT(1) FROM cxbs_oil_tea_estate WHERE deleted = 0 AND estate_status = 'ONLINE') AS estateCount,
                (SELECT COUNT(1)
                 FROM cxbs_oil_tea_subsidy_public_note spn
                          LEFT JOIN cxbs_oil_tea_subsidy_subject s ON spn.id = s.note_id
                 WHERE spn.deleted = 0 AND spn.file_status = 'ONLINE') AS subsidySubjectCount,
                (SELECT COALESCE(ROUND(SUM(s.subsidy_amount)/10000, 2), 0)
                 FROM cxbs_oil_tea_subsidy_public_note spn
                          LEFT JOIN cxbs_oil_tea_subsidy_subject s ON spn.id = s.note_id
                 WHERE spn.deleted = 0 AND spn.file_status = 'ONLINE') AS subsidyAmount;
    </select>
    <select id="selectSubjectTypeCount" resultType="java.util.Map">
        SELECT
            COUNT(*) as count,
            CONCAT(ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM cxbs_oil_tea_subject), 1), '%') as percentage,
        CASE
            WHEN subject_type = 'Enterprise' THEN 'Enterprise_COUNT'
            WHEN subject_type = 'Cooperative' THEN 'Cooperative_COUNT'
            WHEN subject_type = 'IndvBusiness' THEN 'IndvBusiness_COUNT'
            WHEN subject_type = 'Farmer' THEN 'Farmer_COUNT'
        END as count_key,
        CASE
            when subject_type is null
                or subject_type = '' then 'UNKNOWN'
            WHEN subject_type = 'Enterprise' THEN 'Enterprise'
            WHEN subject_type = 'Cooperative' THEN 'Cooperative'
            WHEN subject_type = 'IndvBusiness' THEN 'IndvBusiness'
            WHEN subject_type = 'Farmer' THEN 'Farmer'
            ELSE 'UNKNOWN'  -- 处理其他未列明的类型
        END as percentage_key
    FROM cxbs_oil_tea_subject
    WHERE deleted = 0
    GROUP BY subject_type
    </select>

</mapper>

