<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.teaorigincodehistory.TeaOriginCodeHistoryMapper">

    <select id="selectPage" resultType="com.hainancrc.module.creditcxbs.entity.TeaOriginCodeHistoryDO">
        SELECT `cxbs_tea_origin_code_history`.*
        FROM `cxbs_tea_origin_code_history`

        WHERE deleted = 0
        
        ORDER BY  id DESC
    </select>

</mapper>

