<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.businessfunding.BusinessFundingMapper">

    <select id="selectPage" resultType="com.hainancrc.module.creditcxbs.entity.BusinessFundingDO">
        SELECT `cxbs_business_funding`.*
        FROM `cxbs_business_funding`

        WHERE deleted = 0
        
        ORDER BY  id DESC
    </select>

    <!-- 申请经营主体数 -->
    <select id="selectApplySubjectCount" resultType="java.lang.Long">
        SELECT COUNT(bf.apply_subject)
        FROM cxbs_financial_product_funding fp
            LEFT JOIN cxbs_business_funding bf ON fp.id = bf.financial_product_funding_id
        WHERE fp.deleted = 0 AND fp.file_status = 'ONLINE'
    </select>
    <!-- 申请经成功营主体数 -->
    <select id="selectApplySuccessSubjectCount" resultType="java.lang.Long">
        SELECT COUNT(bf.apply_subject)
        FROM cxbs_financial_product_funding fp
            LEFT JOIN cxbs_business_funding bf ON fp.id = bf.financial_product_funding_id
        WHERE fp.deleted = 0 AND fp.file_status = 'ONLINE' AND bf.disburse_amount > 0
    </select>
    <!-- 融资总金额 -->
    <select id="selectTotalAmount" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(bf.disburse_amount), 0)
        FROM cxbs_financial_product_funding fp
            LEFT JOIN cxbs_business_funding bf ON fp.id = bf.financial_product_funding_id
        WHERE fp.deleted = 0 AND fp.file_status = 'ONLINE'
    </select>

</mapper>

