<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.oilteaestate.OilTeaEstateMapper">

    <select id="selectPage" resultType="com.hainancrc.module.creditcxbs.entity.OilTeaEstateDO">
        SELECT `cxbs_oil_tea_estate`.*
        FROM `cxbs_oil_tea_estate`
        WHERE deleted = 0
        <if test="dto.estateName != null and dto.estateName != ''">
            AND estate_name LIKE CONCAT('%', #{dto.estateName}, '%')
        </if>
        ORDER BY update_time DESC
    </select>

</mapper>

