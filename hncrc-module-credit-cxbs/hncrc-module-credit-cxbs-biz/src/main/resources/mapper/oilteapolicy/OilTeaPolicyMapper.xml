<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.oilteapolicy.OilTeaPolicyMapper">

    <select id="selectPage" resultType="com.hainancrc.module.creditcxbs.entity.OilTeaPolicyDO">
        SELECT
            p.id,
            p.policy_name,
            p.policy_content,
            p.policy_status,
            p.policy_file_list_json,
            p.create_time,
            p.update_time
        FROM `cxbs_oil_tea_policy` p
        WHERE deleted = 0
        <if test="dto.policyName != null and dto.policyName != ''">
            AND p.policy_name LIKE concat('%',#{dto.policyName},'%')
        </if>
        ORDER BY p.create_time DESC
    </select>

</mapper>

