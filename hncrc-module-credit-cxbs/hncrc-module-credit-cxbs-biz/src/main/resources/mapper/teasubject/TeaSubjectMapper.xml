<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.teasubject.TeaSubjectMapper">

    <!-- 查询各类型主体数量及占比 -->
    <select id="selectSubjectTypeCount" resultType="map">
        SELECT
            case
                when subject_type is null
                    or subject_type = '' then 'UNKNOWN'
                else subject_type
                end as subjectType,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM cxbs_tea_subject WHERE deleted = 0), 2) as percentage
        FROM cxbs_tea_subject
        WHERE deleted = 0
        GROUP BY subject_type
    </select>

    <!-- listByTeaSubjectData -->

    <select id="listByTeaSubjectData" resultType="com.hainancrc.module.creditcxbs.api.teasubject.vo.TeaSubjectDataVO">
        SELECT
            s.subject_name AS subjectName,
            IFNULL(SUM(r.use_count), 0) AS totalUseCodeCount,
            IFNULL(SUM(r.total_tea_weight), 0) AS totalTeaCount,
            IFNULL((SELECT SUM(view_count) FROM cxbs_tea_origin_code_view v WHERE v.deleted = 0 AND v.tea_origin_code_id IN (
        SELECT
            c.id
        FROM
            cxbs_tea_origin_code c
        WHERE
            c.deleted = 0 and s.deleted = 0 and c.tea_subject_id = s.id
        <choose>
            <when test="dto.startTime != null and dto.startTime != '' and dto.endTime != null and dto.endTime != ''">
                AND DATE_FORMAT(v.view_date, '%Y-%m-%d') BETWEEN #{dto.startTime} AND #{dto.endTime}
            </when>
            <otherwise>
                AND MONTH(v.view_date) = MONTH(CURRENT_DATE())
                AND YEAR(v.view_date) = YEAR(CURRENT_DATE())
            </otherwise>
        </choose>
        )), 0) AS totalViewCount
        FROM
        cxbs_origin_subject_user su
        LEFT JOIN
        cxbs_tea_subject s ON su.tea_subject_id = s.id
        LEFT JOIN
        cxbs_tea_origin_code c ON c.tea_subject_id = s.id
        LEFT JOIN
        cxbs_tea_origin_code_record r ON c.id = r.origin_code_id
        <where>
            <choose>
                <when test="dto.startTime != null and dto.startTime != '' and dto.endTime != null and dto.endTime != ''">
                    DATE_FORMAT(r.create_time, '%Y-%m-%d') BETWEEN #{dto.startTime} AND #{dto.endTime}
                </when>
                <otherwise>
                    MONTH(r.create_time) = MONTH(CURRENT_DATE()) AND YEAR(r.create_time) = YEAR(CURRENT_DATE())
                </otherwise>
            </choose>
        </where>
        GROUP BY
            s.subject_name
        ORDER BY
            totalTeaCount DESC
    </select>

    <select id="listByTeaSubjectData2" resultType="com.hainancrc.module.creditcxbs.api.teasubject.vo.TeaSubjectDataVO">
        SELECT
        c.subject_name as subjectName,
        SUM(DISTINCT v.view_count) as totalViewCount,
        IFNULL(SUM(DISTINCT qt.use_count), 0) as totalUseCodeCount,
        IFNULL(SUM(DISTINCT qt.total_tea_weight), 0) as totalTeaCount
        FROM
        cxbs_tea_subject c
        LEFT JOIN
        cxbs_tea_origin_code qt ON c.id = qt.tea_origin_id
        AND qt.deleted = 0
        LEFT JOIN
        cxbs_tea_origin_code_view v ON c.id = v.company_id
        WHERE v.view_count  > 0
        <if test="(dto.startTime != null and dto.startTime != '') and (dto.endTime != null and dto.endTime != '') ">
            AND DATE_FORMAT(view_date, '%Y-%m-%d') BETWEEN #{dto.startTime} AND #{dto.endTime}
            AND DATE_FORMAT(qt.create_time, '%Y-%m-%d') BETWEEN #{dto.startTime} AND #{dto.endTime}
        </if>
        GROUP BY
        c.subject_name;
    </select>
</mapper>

