<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.teaorigin.TeaOriginMapper">

    <select id="selectPage" resultType="com.hainancrc.module.creditcxbs.api.teaorigin.vo.TeaOriginPageRespVO">
        SELECT
            o.id,
            o.origin_name,
            o.tea_estate,
            o.tea_type,
            o.origin_status,
            COUNT(r.id) AS apply_count,
            o.update_time
        FROM `cxbs_tea_origin` o
            LEFT JOIN `cxbs_tea_origin_code_record` r ON o.id = r.origin_id
        WHERE o.deleted = 0
        <if test="dto.originName != null and dto.originName != ''">
            AND o.origin_name LIKE CONCAT('%',#{dto.originName},'%')
        </if>
        GROUP BY o.id
        ORDER BY o.create_time DESC
    </select>

    <select id="selectApplyPage"
            resultType="com.hainancrc.module.creditcxbs.api.teaorigin.vo.ApplySubjectPageRespVO">
        SELECT
            s.subject_name,
            s.subject_type,
            s.uniscid,
            c.create_time
        FROM `cxbs_tea_origin_code` c
            LEFT JOIN `cxbs_tea_subject` s ON c.tea_subject_id = s.id
        WHERE c.deleted = 0 AND c.tea_origin_id = #{dto.originId}
    </select>

</mapper>

