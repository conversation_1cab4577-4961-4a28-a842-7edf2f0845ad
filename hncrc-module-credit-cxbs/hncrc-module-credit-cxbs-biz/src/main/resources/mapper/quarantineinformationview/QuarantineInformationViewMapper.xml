<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.quarantineinformationview.QuarantineInformationViewMapper">

    <select id="selectPage" resultType="com.hainancrc.module.creditcxbs.entity.QuarantineInformationViewDO">
        SELECT `creditcxwc_quarantine_information_view`.*
        FROM `creditcxwc_quarantine_information_view`

        WHERE deleted = 0

        ORDER BY  id DESC
    </select>

</mapper>

