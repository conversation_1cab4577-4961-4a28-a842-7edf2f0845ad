<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.originsubjectuser.OriginSubjectUserMapper">

    <select id="selectPage" resultType="com.hainancrc.module.creditcxbs.entity.OriginSubjectUserDO">
        SELECT `cxbs_origin_subject_user`.*
        FROM `cxbs_origin_subject_user`

        WHERE deleted = 0
        
        ORDER BY  id DESC
    </select>

</mapper>

