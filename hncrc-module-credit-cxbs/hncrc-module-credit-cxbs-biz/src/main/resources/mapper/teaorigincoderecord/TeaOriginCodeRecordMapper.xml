<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.teaprogincoderecord.TeaOriginCodeRecordMapper">


    <select id="selectRecordPage"
            resultType="com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordPageRespVO">
        SELECT
            r.origin_code_id AS codeId,
            o.origin_name AS tea_origin_name,
            o.tea_estate,
            r.tea_type,
            IFNULL(SUM(c.view_count), 0) AS view_count,
            IFNULL(SUM(r.use_count), 0) AS use_count,
            IFNULL(SUM(r.total_tea_weight), 0) AS total_tea_weight,
            r.update_time,
            c.code_status
        FROM `cxbs_tea_origin_code_record` r
            LEFT JOIN `cxbs_tea_origin` o ON r.origin_id = o.id
            LEFT JOIN `cxbs_tea_origin_code` c ON r.origin_code_id = c.id
        WHERE r.deleted = 0
            <if test="dto.originName != null and dto.originName != ''">
                AND o.origin_name LIKE CONCAT('%', #{dto.originName}, '%')
            </if>
            <if test="dto.teaSubjectId != null">
                AND c.tea_subject_id = #{dto.teaSubjectId}
            </if>
        GROUP BY r.origin_id, r.tea_type
        ORDER BY r.create_time DESC
    </select>

    <select id="selectApplyRecordPage"
            resultType="com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordDetailRespVO">
        SELECT
            r.id,
            r.use_count,
            r.total_tea_weight,
            r.create_time
        FROM `cxbs_tea_origin_code_record` r
        WHERE r.deleted = 0 AND r.origin_code_id = #{dto.originCodeId}
        <if test="dto.startTime != null and dto.startTime != ''">
            AND r.create_time &gt; #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND r.create_time &lt; #{dto.endTime}
        </if>
    </select>


    <select id="selectByRecordId"
            resultType="com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo.TeaOriginCodeRecordRespVO">
        SELECT
            r.id,
            r.origin_id,
            o.origin_name AS tea_origin_name,
            o.tea_estate,
            r.tea_type,
            r.use_count,
            r.total_tea_weight
        FROM `cxbs_tea_origin_code_record` r
            LEFT JOIN `cxbs_tea_origin` o ON r.origin_id = o.id
        WHERE r.deleted = 0 AND r.id = #{id}
    </select>

    <select id="selectMaps" resultType="java.util.Map">
        SELECT
            r.origin_code_id,
            IFNULL(SUM(r.use_count), 0) AS use_count,
            IFNULL(SUM(r.total_tea_weight), 0) AS total_tea_weight
        FROM `cxbs_tea_origin_code_record` r
        WHERE r.deleted = 0 AND r.origin_code_id IN
        <foreach collection="codeIds" item="codeId" index="index" open="(" separator="," close=")">
            #{codeId}
        </foreach>
        GROUP BY r.origin_code_id;
    </select>


</mapper>
