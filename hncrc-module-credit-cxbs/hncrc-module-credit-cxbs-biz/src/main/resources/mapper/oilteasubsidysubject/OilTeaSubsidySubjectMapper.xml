<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.oilteasubsidysubject.OilTeaSubsidySubjectMapper">

    <select id="selectSubsidySubjectList" resultType="com.hainancrc.module.creditcxbs.entity.OilTeaSubsidySubjectDO">
        SELECT ss.*
        FROM cxbs_oil_tea_subsidy_public_note pt
            INNER JOIN cxbs_oil_tea_subsidy_subject ss on pt.id = ss.note_id
        WHERE pt.deleted = 0 AND pt.file_status = 'ONLINE'
    </select>

</mapper>

