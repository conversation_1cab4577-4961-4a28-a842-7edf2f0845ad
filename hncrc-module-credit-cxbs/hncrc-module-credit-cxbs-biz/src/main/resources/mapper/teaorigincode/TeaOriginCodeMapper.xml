<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.teaorigincode.TeaOriginCodeMapper">

    <select id="selectOriginCodePage" resultType="com.hainancrc.module.creditcxbs.api.teaorigincode.vo.TeaOriginCodePageRespVO">
        SELECT
            c.id,
            s.uniscid,
            s.subject_name AS tea_subject_name,
            o.origin_name AS tea_origin_name,
            o.tea_estate,
            ctocr.tea_type,
            c.code,
            c.code_num,
            IFNULL(c.view_count, 0) AS view_count,
            c.promise_file_key,
            c.promise_file_url,
            c.expire_time,
            c.update_time,
            c.code_status
        FROM `cxbs_tea_origin_code` c
            LEFT JOIN `cxbs_tea_subject` s ON c.tea_subject_id = s.id
            LEFT JOIN `cxbs_tea_origin` o ON c.tea_origin_id = o.id
            LEFT JOIN `cxbs_tea_origin_code_record` ctocr on ctocr.origin_code_id = c.id
        WHERE c.deleted = 0
            <if test="dto.teaOriginName != null and dto.teaOriginName != ''">
                AND o.origin_name LIKE CONCAT('%', #{dto.teaOriginName}, '%')
            </if>
        GROUP BY c.id
        ORDER BY c.id DESC
    </select>

    <!-- 查询茶园数量 -->
    <select id="selectTeaGardenCount" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT tea_estate)
        FROM cxbs_tea_origin
        WHERE deleted = 0
    </select>

    <!-- 查询产地码申请主体数 -->
    <select id="selectApplySubjectCount" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT tea_subject_id)
        FROM cxbs_tea_origin_code
        WHERE deleted = 0 AND code_status = 'ONLINE'
    </select>

    <!-- 查询申领茶青数量 -->
    <select id="selectTeaLeafAmount" resultType="java.lang.Long">
        SELECT IFNULL(SUM(ocr.total_tea_weight), 0)
        FROM cxbs_tea_origin_code oc
            LEFT JOIN cxbs_tea_origin_code_record ocr ON oc.id = ocr.origin_code_id AND oc.code_status = 'ONLINE'
        WHERE oc.deleted = 0
    </select>

    <!-- 查询产地码申领数 -->
    <select id="selectOriginCodeApplyCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM cxbs_tea_origin_code
        WHERE deleted = 0 AND code_status = 'ONLINE'
    </select>

    <!-- 查询产地码查看次数 -->
    <select id="selectOriginCodeViewCount" resultType="java.lang.Long">
        SELECT IFNULL(SUM(view_count), 0)
        FROM cxbs_tea_origin_code
        WHERE deleted = 0 AND code_status = 'ONLINE'
    </select>

</mapper>

