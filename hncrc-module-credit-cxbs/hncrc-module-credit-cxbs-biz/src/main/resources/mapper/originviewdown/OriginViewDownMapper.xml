<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.originviewdown.OriginViewDownMapper">

    <select id="selectPage" resultType="com.hainancrc.module.creditcxbs.entity.OriginViewDownDO">
        SELECT `cxbs_origin_view_down`.*
        FROM `cxbs_origin_view_down`

        WHERE deleted = 0

        ORDER BY  id DESC
    </select>

    <select id="selectFamilyCodeViewCount" resultType="java.lang.Long">
        SELECT
            COALESCE(SUM( family_code_view ),0)
        FROM
            cxbs_origin_view_down AS covd
                LEFT JOIN (select * from cxbs_seedling_subject where deleted = 0 and credit_level = 'A') AS css ON covd.company_id = css.id
    </select>
    <select id="selectFamilyCodeDownload" resultType="java.lang.Long">
        SELECT
            COALESCE(SUM( family_code_download ),0)
        FROM
            cxbs_origin_view_down AS covd
                LEFT JOIN (select * from cxbs_seedling_subject where deleted = 0 and credit_level = 'A') AS css ON covd.company_id = css.id
    </select>
    <select id="selectSeedlingCodeDownload" resultType="java.lang.Long">
        SELECT
            COALESCE(SUM( seedling_code_download ),0)
        FROM
            cxbs_origin_view_down AS covd
                LEFT JOIN (select * from cxbs_seedling_subject where deleted = 0 and credit_level = 'A') AS css ON covd.company_id = css.id
    </select>
    <select id="selectSeedlingCodeViewCount" resultType="java.lang.Long">
        SELECT
            COALESCE(SUM( seedling_code_view ),0)
        FROM
            cxbs_origin_view_down AS covd
                LEFT JOIN (select * from cxbs_seedling_subject where deleted = 0 and credit_level = 'A') AS css ON covd.company_id = css.id
    </select>

    <select id="getViewDownloadCount" resultType="com.hainancrc.module.creditcxbs.entity.OriginViewDownDO">
        SELECT
            SUM(IFNULL(family_code_download, 0)) as family_code_download,
            SUM(IFNULL(family_code_view, 0)) as family_code_view,
            SUM(IFNULL(seedling_code_download, 0)) as seedling_code_download,
            SUM(IFNULL(seedling_code_view, 0)) as seedling_code_view
        FROM
            cxbs_origin_view_down
        WHERE
            deleted = 0
    </select>

</mapper>

