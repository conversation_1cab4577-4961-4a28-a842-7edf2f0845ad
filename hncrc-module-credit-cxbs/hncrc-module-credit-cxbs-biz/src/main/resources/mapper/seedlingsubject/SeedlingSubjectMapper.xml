<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.seedlingsubject.SeedlingSubjectMapper">

<!--    <select id="selectPage" resultType="com.hainancrc.module.creditcxbs.entity.SeedlingSubjectDO">-->
<!--        SELECT `cxbs_seedling_subject`.*-->
<!--        FROM `cxbs_seedling_subject`-->

<!--        WHERE deleted = 0-->
<!--        -->
<!--        ORDER BY  id DESC-->
<!--    </select>-->

    <select id="getCreditLevelStats" resultType="java.util.Map">
        SELECT 
            case
                when credit_level is null
                    or credit_level = '' then 'A'
                else credit_level
                end as creditLevel,
            COUNT(*) as count,
            CONCAT(ROUND(COUNT(*) * 100.0 / (
                SELECT COUNT(*) 
                FROM cxbs_seedling_subject 
                WHERE deleted = 0 
                AND subject_type != 'Farmer'
            ), 2), '%') as percentage
        FROM cxbs_seedling_subject
        WHERE deleted = 0 AND subject_type != 'Farmer'
        GROUP BY credit_level
    </select>

    <select id="getSubjectTypeStats" resultType="java.util.Map">
        SELECT
            case
                when subject_type is null
                    or subject_type = '' then 'UNKNOWN'
                else subject_type
                end as subjectType,
            case
                when credit_level is null
                    or credit_level = '' then 'A'
                else credit_level
                end as creditLevel,
            COUNT(*) as count,
            CONCAT(ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM cxbs_seedling_subject), 2), '%') as percentage
        FROM cxbs_seedling_subject
        WHERE deleted = 0
        GROUP BY subject_type
    </select>

    <!-- getSeedlingSubjectInfo --> 

    <select id="getSeedlingSubjectInfo" resultType="com.hainancrc.module.creditcxbs.api.seedlingsubject.vo.SeedlingSubjectVO">
        SELECT 
            s.id,
            s.subject_name,
            s.subject_type,
            c.seedling_category,
            c.seedling_variety,
            c.piece_range,
            s.contact_phone,
            s.subject_address,
            s.subject_introduction,
            s.subject_picture_list_json
        FROM cxbs_seedling_subject s
        LEFT JOIN cxbs_seedling_subject_category sc ON s.id = sc.subject_id
        LEFT JOIN cxbs_seedling_category c ON sc.category_id = c.id
        WHERE s.deleted = 0 AND sc.deleted = 0 AND c.deleted = 0
    </select>

    <!-- 统计各类型主体数量 -->
    <select id="countSubjectStatistics" resultType="com.hainancrc.module.creditcxbs.api.seedlingsubject.vo.SubjectStatisticsVO">
        SELECT 
            COUNT(*) as totalCount,
            SUM(CASE WHEN subject_type = 'Enterprise' THEN 1 ELSE 0 END) as enterpriseCount,
            SUM(CASE WHEN subject_type = 'Cooperative' THEN 1 ELSE 0 END) as cooperativeCount,
            SUM(CASE WHEN subject_type = 'IndvBusiness' THEN 1 ELSE 0 END) as growerCount,
            SUM(CASE WHEN subject_type = 'Farmer' THEN 1 ELSE 0 END) as farmerCount
        FROM cxbs_seedling_subject
        WHERE deleted = 0
    </select>

    <!-- 获取A级主体统计信息 -->
    <select id="getALevelSubjectStatistics" resultType="com.hainancrc.module.creditcxbs.api.seedlingsubject.vo.ALevelSubjectStatisticsVO">
        SELECT 
            COUNT(*) as totalCount,
            COUNT(CASE WHEN subject_type = 'Enterprise' THEN 1 END) as enterpriseCount,
            COUNT(CASE WHEN subject_type = 'Cooperative' THEN 1 END) as cooperativeCount,
            COUNT(CASE WHEN subject_type = 'IndvBusiness' THEN 1 END) as planterCount
        FROM cxbs_seedling_subject 
        WHERE credit_level = 'A'
          AND subject_type IN ('Enterprise', 'Cooperative', 'IndvBusiness')
        AND deleted = 0
    </select>
</mapper>

