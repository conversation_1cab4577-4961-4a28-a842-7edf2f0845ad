<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.seedlingsubjectcategory.SeedlingSubjectCategoryMapper">

    <select id="selectPage" resultType="com.hainancrc.module.creditcxbs.entity.SeedlingSubjectCategoryDO">
        SELECT `cxbs_seedling_subject_category`.*
        FROM `cxbs_seedling_subject_category`

        WHERE deleted = 0
        
        ORDER BY  id DESC
    </select>

</mapper>

