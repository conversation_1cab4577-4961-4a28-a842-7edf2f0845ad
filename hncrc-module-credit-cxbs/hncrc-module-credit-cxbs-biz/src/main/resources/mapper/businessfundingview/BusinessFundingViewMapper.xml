<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.businessfundingview.BusinessFundingViewMapper">

    <select id="selectPage" resultType="com.hainancrc.module.creditcxbs.entity.BusinessFundingViewDO">
        SELECT `cxbs_business_funding_view`.*
        FROM `cxbs_business_funding_view`

        WHERE deleted = 0

        ORDER BY  id DESC
    </select>

    <!-- 浏览量 -->
    <select id="selectLookCount" resultType="java.lang.Long">
        SELECT IFNULL(SUM(view_count), 0)
        FROM cxbs_business_funding_view
        WHERE deleted = 0
    </select>
    <!-- 浏览经营主体数 -->
    <select id="selectLookSubjectCount" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT company_id)
        FROM cxbs_business_funding_view
        WHERE deleted = 0
    </select>
</mapper>

