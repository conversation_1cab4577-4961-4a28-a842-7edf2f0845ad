<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.creditcxbs.mapper.seedlingcategory.SeedlingCategoryMapper">

<!--    <select id="selectPage" resultType="com.hainancrc.module.creditcxbs.entity.SeedlingCategoryDO">-->
<!--        SELECT `cxbs_seedling_category`.*-->
<!--        FROM `cxbs_seedling_category`-->

<!--        WHERE deleted = 0-->
<!--        -->
<!--        ORDER BY  id DESC-->
<!--    </select>-->

    <!--  查询苗木种类列表  -->
    <select id="selectVarietyList" resultType="java.lang.String">
        SELECT DISTINCT
            seedling_variety
        FROM
            cxbs_seedling_category
    </select>

    <!--  查询苗木品种列表  -->
    <select id="selectCategoryList" resultType="java.lang.String">
        SELECT DISTINCT
            seedling_category
        FROM
            cxbs_seedling_category
    </select>
    <!--  查询苗木种类列表  -->
    <select id="getSeedlingCategory" resultType="com.hainancrc.module.creditcxbs.api.seedlingsubject.dto.SeedlingCategoryDTO">
        SELECT DISTINCT
            id,seedling_category,seedling_variety
        FROM
            cxbs_seedling_category
        WHERE
            seedling_category IS NOT NULL
          and deleted = 0
          and seedling_status = 'ONLINE'
    </select>

    <!--  查询苗木品种id  -->
    <select id="getSeedlingCategoryIds" resultType="com.hainancrc.module.creditcxbs.api.seedlingcategory.vo.SeedlingCategorySelectVO">
        SELECT DISTINCT
            sc.id,
            sc.seedling_category
        FROM
            cxbs_seedling_subject_category ssc
                left join
            cxbs_seedling_category sc
            on sc.id  = ssc.category_id
        where
            ssc.subject_id  = #{subjectId}
          and ssc.deleted = 0
          and sc.deleted = 0
          and sc.seedling_status = 'ONLINE'
    </select>
</mapper>

