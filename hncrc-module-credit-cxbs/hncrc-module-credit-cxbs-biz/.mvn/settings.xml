<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
  <localRepository>/Volumes/workspace/SDK/repository_hncrc</localRepository>
  <proxies>
    <proxy>
      <id>xxproxy</id>
      <active>true</active>
      <protocol>socks5</protocol>
      <host>127.0.0.1</host>
      <port>7890</port>
      <nonProxyHosts>maven.aliyun.com|repo.huaweicloud.com|mirrors.cloud.tencent.com|mirrors.163.com|repo1.maven.org</nonProxyHosts>
    </proxy>
  </proxies>
  <mirrors>
    <mirror>
      <id>nexus</id>
      <mirrorOf>central</mirrorOf>
      <name>hncrc maven</name>
      <url>http://*************:8065/repository/maven-public/</url>
    </mirror>
  </mirrors>
  <profiles>
    <profile>
      <id>hncrc-profile</id>
      <repositories>
        <repository>
          <id>nexus</id>
          <url>http://*************:8065/repository/maven-public/</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </repository>
      </repositories>
    </profile>
  </profiles>
  <activeProfiles>
    <activeProfile>hncrc-profile</activeProfile>
  </activeProfiles>
</settings>