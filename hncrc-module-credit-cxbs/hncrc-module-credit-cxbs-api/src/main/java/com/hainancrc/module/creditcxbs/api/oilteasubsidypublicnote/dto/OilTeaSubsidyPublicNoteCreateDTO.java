package com.hainancrc.module.creditcxbs.api.oilteasubsidypublicnote.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaSubsidyPublicNoteFileStatus;

/**
* 油茶补贴公示信息
*/
@Data
public class OilTeaSubsidyPublicNoteCreateDTO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 补贴公式文件名称
    */
    @ApiModelProperty(value = "补贴公式文件名称")
    @Size(max = 100, message = "补贴公式文件名称长度不能大于 100")
    private String publicFileName;
    
    /**
    * 补贴公示文件url
    */
    @ApiModelProperty(value = "补贴公示文件url")
    @Size(max = 200, message = "补贴公示文件url长度不能大于 200")
    private String publicFileUrl;
    
    /**
    * 补贴公示文件key
    */
    @ApiModelProperty(value = "补贴公示文件key")
    @Size(max = 200, message = "补贴公示文件key长度不能大于 200")
    private String publicFileKey;
    
    /**
    * 文件状态(ENUMS:ONLINE-公示中,OFFLINE-已下线)(列表,新增,编辑)
    */
    @ApiModelProperty(value = "文件状态(ENUMS:ONLINE-公示中,OFFLINE-已下线)(列表,新增,编辑)")
    private OilTeaSubsidyPublicNoteFileStatus fileStatus;

}

