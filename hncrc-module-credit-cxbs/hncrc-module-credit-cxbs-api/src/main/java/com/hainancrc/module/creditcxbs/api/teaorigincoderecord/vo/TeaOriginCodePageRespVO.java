package com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.Date;

/**
* 产地码
*/
@Data
public class TeaOriginCodePageRespVO {
    
    /**
    * id
    */
    @ApiModelProperty(value = "id")
    private Long id;
    
    /**
    * 产地ID
    */
    @ApiModelProperty(value = "产地ID")
    private Long teaOriginId;

    @ApiModelProperty(value = "产地名称")
    private String teaOriginName;
    
    /**
    * 码内容(新增:必填)
    */
    @ApiModelProperty(value = "码内容")
    @Size(max = 255, message = "码内容长度不能大于 255")
    private String code;
    
    /**
    * 码编号(新增)
    */
    @ApiModelProperty(value = "码编号(新增)")
    @Size(max = 600, message = "码编号长度不能大于 600")
    private String codeNum;
    
    /**
    * 查看次数
    */
    @ApiModelProperty(value = "查看次数")
    private Integer viewCount;
    
    /**
    * 用码累计数
    */
    @ApiModelProperty(value = "用码累计数")
    private Integer useCount;
    
    /**
    * 累计茶青总数（斤）
    */
    @ApiModelProperty(value = "累计茶青总数（斤）")
    private Integer totalTeaWeight;
    
    /**
    * 承诺书文件key(新增)
    */
    @ApiModelProperty(value = "承诺书文件key")
    @Size(max = 300, message = "承诺书文件key长度不能大于 300")
    private String promiseFileKey;
    
    /**
    * 承诺书文件url
    */
    @ApiModelProperty(value = "承诺书文件url")
    @Size(max = 500, message = "承诺书文件url长度不能大于 500")
    private String promiseFileUrl;


    /**
    * 数据更新时间
    */
    @ApiModelProperty(value = "数据更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;

}

