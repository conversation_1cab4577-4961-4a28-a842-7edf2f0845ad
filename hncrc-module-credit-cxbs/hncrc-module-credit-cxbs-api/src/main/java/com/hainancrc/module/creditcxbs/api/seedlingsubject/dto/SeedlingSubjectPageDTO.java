package com.hainancrc.module.creditcxbs.api.seedlingsubject.dto;

import lombok.*;
import io.swagger.annotations.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 苗木经营主体信息
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SeedlingSubjectPageDTO  extends PageParam {
    
    @ApiModelProperty(value = "商户名称")
    private String subjectName;

    @ApiModelProperty(value = "主体类型")
    private String subjectType;

    @ApiModelProperty(value = "信用等级")
    private String creditLevel;

}

