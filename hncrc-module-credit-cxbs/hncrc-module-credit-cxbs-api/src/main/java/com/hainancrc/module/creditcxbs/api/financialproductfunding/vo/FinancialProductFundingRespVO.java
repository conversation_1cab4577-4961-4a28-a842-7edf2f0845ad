package com.hainancrc.module.creditcxbs.api.financialproductfunding.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.FinancialProductFundingFileStatus;

/**
* 金融产品融资信息表
*/
@Data
public class FinancialProductFundingRespVO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 文件名称(列表,新增,编辑)
    */
    @ApiModelProperty(value = "文件名称(列表,新增,编辑)")
    @Size(max = 100, message = "文件名称(列表,新增,编辑)长度不能大于 100")
    private String fileName;
    
    /**
    * 文件内容描述(新增,编辑)
    */
    @ApiModelProperty(value = "文件内容描述(新增,编辑)")
    private String fileContent;
    
    /**
    * 文件状态(ENUMS:ONLINE-公示中,OFFLINE-已下线)(列表)
    */
    @ApiModelProperty(value = "文件状态(ENUMS:ONLINE-公示中,OFFLINE-已下线)(列表)")
    private FinancialProductFundingFileStatus fileStatus;
    
    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    @Size(max = 50, message = "创建人长度不能大于 50")
    private String creator;
    
    /**
    * 更新人
    */
    @ApiModelProperty(value = "更新人")
    @Size(max = 50, message = "更新人长度不能大于 50")
    private String updater;
    
    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**
    * 更新时间
    */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /**
    * 逻辑删除标志位
    */
    @ApiModelProperty(value = "逻辑删除标志位")
    private Boolean deleted;
    
    
}

