package com.hainancrc.module.creditcxbs.api.financialpolicy.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.FinancialPolicyPolicyStatus;

/**
* 金融政策信息
*/
@Data
public class FinancialPolicyRespVO {
    
    /**
    * 主键id
    */
    @ApiModelProperty(value = "主键id")
    private Long id;
    
    /**
    * 政策名称(列表,新增,编辑)
    */
    @ApiModelProperty(value = "政策名称(列表,新增,编辑)")
    private String policyName;

    /**
     * 政策内容描述
     */
    @ApiModelProperty(value = "政策内容描述")
    private String policyContent;

    /**
     * 政策附件url policy_file_url
     */
    @ApiModelProperty(value = "政策附件url")
    private String policyFileUrl;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
    * 政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表)
    */
    @ApiModelProperty(value = "政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表)")
    private FinancialPolicyPolicyStatus policyStatus;

    /**
     * 政策附件json串
     */
    @ApiModelProperty(value = "政策附件json")
    private String policyFileJson;


    
}

