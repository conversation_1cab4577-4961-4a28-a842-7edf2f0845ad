package com.hainancrc.module.creditcxbs.api.oilteapolicy.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaPolicyPolicyStatus;

/**
* 油茶政策信息
*/
@Data
public class OilTeaPolicyRespVO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 政策名称(列表,新增,编辑)
    */
    @ApiModelProperty(value = "政策名称(列表,新增,编辑)")
    @Size(max = 100, message = "政策名称(列表,新增,编辑)长度不能大于 100")
    private String policyName;
// !!REVIEW
// TODO 可能的问题: 政策名称字段没有强制限制不允许为空，可能会导致在使用过程中接受到空值。
// 修改建议：在字段上增加@NotBlank注解以确保字段不允许为空或空白。
    
    /**
    * 政策内容描述(新增,编辑)
    */
    @ApiModelProperty(value = "政策内容描述(新增,编辑)")
    private String policyContent;
// !!REVIEW
// TODO 可能的问题: 政策内容描述字段没有强制限制不允许为空，可能会导致在使用过程中接受到空值。
// 修改建议：在字段上增加@NotBlank注解以确保字段不允许为空或空白。
    
    /**
    * 政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)
    */
    @ApiModelProperty(value = "政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)")
    private OilTeaPolicyPolicyStatus policyStatus;
    
    /**
    * 政策附件(新增,编辑)
    */
    @ApiModelProperty(value = "政策附件(新增,编辑)")
    private String policyFileListJson;
// !!REVIEW
// TODO 可能的问题: 政策附件字段没有强制限制不允许为空，可能会导致在使用过程中接受到空值。
// 修改建议：在字段上增加@NotBlank注解以确保字段不允许为空或空白。

    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;
    
    /**
    * 更新时间
    */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;
    

}

