package com.hainancrc.module.creditcxbs.api.originviewdown.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-20
 */

@Data
public class OriginViewDownRespVO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;


    /**
     * 商户id
     */
    @ApiModelProperty(value = "商户id")
    private Long companyId;

    /**
     * 一户一码下载
     */
    @ApiModelProperty(value = "一户一码下载")
    private Long familyCodeDownload;

    /**
     * 一户一码查看
     */
    @ApiModelProperty(value = "一户一码查看")
    private Long familyCodeView;

    /**
     * 一苗一码下载
     */
    @ApiModelProperty(value = "一苗一码下载")
    private Long seedlingCodeDownload;

    /**
     * 一苗一码查看
     */
    @ApiModelProperty(value = "一苗一码查看")
    private Long seedlingCodeView;

    /**
     * 查看日期
     */
    @ApiModelProperty(value = "查看日期")
    private Date viewDate;

}
