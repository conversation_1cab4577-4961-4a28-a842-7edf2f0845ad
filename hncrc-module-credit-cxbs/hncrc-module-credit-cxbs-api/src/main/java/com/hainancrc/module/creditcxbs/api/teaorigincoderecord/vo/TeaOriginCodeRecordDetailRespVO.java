package com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hainancrc.module.creditcxbs.api.teaorigincodehistory.vo.TeaOriginCodeHistoryRespVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
* 产地码
*/
@Data
public class TeaOriginCodeRecordDetailRespVO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "用码数（次）")
    private Integer useCount;
    
    @ApiModelProperty(value = "茶青总数（斤）")
    private Integer totalTeaWeight;

    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}

