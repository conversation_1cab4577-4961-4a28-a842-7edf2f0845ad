package com.hainancrc.module.creditcxbs.api.seedlingsubject.vo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("主体统计信息VO")
public class SubjectStatisticsVO {
    
    @ApiModelProperty("已入驻经营主体总数")
    private Integer totalCount;
    
    @ApiModelProperty("入驻企业数")
    private Integer enterpriseCount;
    
    @ApiModelProperty("入驻合作社数")
    private Integer cooperativeCount;
    
    @ApiModelProperty("入驻种植户数")
    private Integer growerCount;
    
    @ApiModelProperty("入驻农户数")
    private Integer farmerCount;
} 