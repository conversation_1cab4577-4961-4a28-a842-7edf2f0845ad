package com.hainancrc.module.creditcxbs.api.oilteasubsidysubject.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 油茶补贴主体名单
*/
@Data
public class OilTeaSubsidySubjectCreateDTO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 补贴公示信息id
    */
    @ApiModelProperty(value = "补贴公示信息id")
    private Long noteId;
    
    /**
    * 乡镇
    */
    @ApiModelProperty(value = "乡镇")
    @Size(max = 100, message = "乡镇长度不能大于 100")
    private String township;
    
    /**
    * 种植户名称
    */
    @ApiModelProperty(value = "种植户名称")
    @Size(max = 100, message = "种植户名称长度不能大于 100")
    private String planterName;
    
    /**
    * 种植面积(亩)
    */
    @ApiModelProperty(value = "种植面积(亩)")
    private BigDecimal plantArea;
    
    /**
    * 种植株数(株)
    */
    @ApiModelProperty(value = "种植株数(株)")
    private Long plantCount;
    
    /**
    * 存活率
    */
    @ApiModelProperty(value = "存活率")
    private BigDecimal survivalRate;
    
    /**
    * 检查情况
    */
    @ApiModelProperty(value = "检查情况")
    @Size(max = 10, message = "检查情况长度不能大于 10")
    private String checkStatus;
    
    /**
    * 补贴金额(元)
    */
    @ApiModelProperty(value = "补贴金额(元)")
    private BigDecimal subsidyAmount;
    
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    @Size(max = 200, message = "备注长度不能大于 200")
    private String remark;
    
    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    @Size(max = 50, message = "创建人长度不能大于 50")
    private String creator;
    
    /**
    * 更新人
    */
    @ApiModelProperty(value = "更新人")
    @Size(max = 50, message = "更新人长度不能大于 50")
    private String updater;
    
    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**
    * 更新时间
    */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /**
    * 逻辑删除标志位
    */
    @ApiModelProperty(value = "逻辑删除标志位")
    private Boolean deleted;
    
    
}

