package com.hainancrc.module.creditcxbs.api.seedlingcategory.vo;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.module.creditcxbs.api.enums.SeedlingCategorySeedlingStatus;

/**
* 苗木信息
*/
@Data
public class SeedlingCategoryRespVO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 苗木种类(列表,新增,编辑)
    */
    @ApiModelProperty(value = "苗木种类(列表,新增,编辑)")
    private String seedlingCategory;
    
    /**
    * 苗木品种(列表,新增,编辑)
    */
    @ApiModelProperty(value = "苗木品种(列表,新增,编辑)")
    private String seedlingVariety;
    
    /**
    * 规格情况(列表,新增,编辑)
    */
    @ApiModelProperty(value = "规格情况(列表,新增,编辑)")
    @Size(max = 100, message = "规格情况(列表,新增,编辑)长度不能大于 100")
// !!REVIEW
// TODO 可能的问题: `@Size`注解的参数`max`没有限制`seedlingSpecs`和`seedlingAdvantages`的最小值，可能导致空字符串或未定义的行为。
// 修改建议：建议添加`@Size(min = 1, message = "规格情况(列表,新增,编辑)不能为空")`确保字段不为空。
    private String seedlingSpecs;
    
    /**
    * 特点优势(列表,新增,编辑)
    */
    @ApiModelProperty(value = "特点优势(列表,新增,编辑)")
    @Size(max = 100, message = "特点优势(列表,新增,编辑)长度不能大于 100")
    private String seedlingAdvantages;
    
    /**
    * 苗木状态(列表)(ENUMS:ONLINE-使用中,OFFLINE-已下架)
    */
    @ApiModelProperty(value = "苗木状态(列表)(ENUMS:ONLINE-使用中,OFFLINE-已下架)")
    private SeedlingCategorySeedlingStatus seedlingStatus;

    /**
     * 价格范围
     */
    @ApiModelProperty(value = "价格范围")
    private String pieceRange;

    /**
     * 相关政策(条)
     */
    @ApiModelProperty(value = "相关政策(条)")
    private Integer relatedPolicy;

    /**
     * 苗木图片列表
     */
    @ApiModelProperty(value = "苗木图片列表")
    private String seedlingPictureJson;

    /**
     * 相关政策列表
     */
    @ApiModelProperty(value = "相关政策列表")
    private String relatedPolicyListJson;

    /**
     * 附加信息JSON
     */
    @ApiModelProperty(value = "附加信息JSON")
    private String extraInfo;


}

