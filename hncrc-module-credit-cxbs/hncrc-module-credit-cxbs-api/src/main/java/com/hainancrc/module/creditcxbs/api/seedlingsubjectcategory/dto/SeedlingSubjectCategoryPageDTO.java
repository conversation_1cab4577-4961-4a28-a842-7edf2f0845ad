package com.hainancrc.module.creditcxbs.api.seedlingsubjectcategory.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 苗木类
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SeedlingSubjectCategoryPageDTO  extends PageParam {
    
    
}

