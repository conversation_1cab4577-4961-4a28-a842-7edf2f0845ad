package com.hainancrc.module.creditcxbs.api.financialpolicy.dto;

import com.hainancrc.module.creditcxbs.api.enums.FinancialPolicyPolicyStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
* 金融政策信息
*/
@Data
public class FinancialPolicyFileDTO {
    /**
    * 政策附件key(新增,编辑)
    */
    @ApiModelProperty(value = "政策附件key(新增,编辑)")
    private String key;
    
    /**
    * 政策附件url(新增,编辑)
    */
    @ApiModelProperty(value = "政策附件url(新增,编辑)")
    private String url;
    
}

