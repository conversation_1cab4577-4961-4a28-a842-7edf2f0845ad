package com.hainancrc.module.creditcxbs.api.teasubject.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
 * 茶叶经营主体信息
 */
@Data
public class TeaSubjectUpdateDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 商户名称(主体名称)
     */
    @ApiModelProperty(value = "商户名称(主体名称)")
    @Size(max = 100, message = "商户名称(主体名称)长度不能大于 100")
    private String subjectName;

    /**
     * 主题类型
     */
    @ApiModelProperty(value = "主体类型")
    @Size(max = 20, message = "主体类型长度不能大于 20")
    private String subjectType;

    /**
     * 统一信用代码
     */
    @ApiModelProperty(value = "统一信用代码")
    @Size(max = 30, message = "统一信用代码长度不能大于 30")
    private String uniscid;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    @Size(max = 20, message = "法定代表人长度不能大于 20")
    private String legalName;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    @Size(max = 100, message = "地址长度不能大于 100")
    private String subjectAddress;

    /**
     * 所属乡镇
     */
    @ApiModelProperty(value = "所属乡镇")
    @Size(max = 100, message = "所属乡镇长度不能大于 100")
    private String belongTownship;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @Size(max = 11, message = "联系电话长度不能大于 11")
    private String telephone;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * 种植面积
     */
    @ApiModelProperty(value = "种植面积")
    @Digits(integer = 8, fraction = 2, message = "种植面积整数部分长度不能超过8位，小数点后保留两位")
    private BigDecimal plantingArea;

    /**
     * 茶叶品种
     */
    @ApiModelProperty(value = "茶叶品种")
    @Size(max = 150, message = "茶叶品种长度不能大于 150")
    private String teaType;

    /**
     * 茶青产量(吨)
     */
    @ApiModelProperty(value = "茶青产量(吨)")
    @Digits(integer = 8, fraction = 2, message = "茶青产量(吨)整数部分长度不能超过8位，小数点后保留两位")
    private BigDecimal teaYield;

    /**
     * 主体简介
     */
    @ApiModelProperty(value = "主体简介")
    @Size(max = 2000, message = "主体简介长度不能大于 2000")
    @NotBlank(message = "主体简介不能为空")
    private String introduction;

    /**
     * 企业头像
     */
    @ApiModelProperty(value = "企业头像")
    @NotBlank(message = "企业头像不能为空")
    private String avatar;

    /**
     * 购买链接
     */
    @ApiModelProperty(value = "购买链接")
    private String purchaseLink;

    /**
     * 购买二维码
     */
    @ApiModelProperty(value = "购买二维码")
    private String purchaseQrcode;

}
