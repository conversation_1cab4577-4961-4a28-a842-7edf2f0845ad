package com.hainancrc.module.creditcxbs.api.oilteaestate.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaEstateEstateStatus;

/**
 * 油茶茶园信息
 */
@Data
public class OilTeaEstateUpdateDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 茶园名称(列表,新增,编辑)
     */
    @ApiModelProperty(value = "茶园名称(列表,新增,编辑)")
    @Size(max = 100, message = "茶园名称(列表,新增,编辑)长度不能大于 100")
    private String estateName;

    /**
     * 种植品种(列表,新增,编辑)
     */
    @ApiModelProperty(value = "种植品种(列表,新增,编辑)")
    @Size(max = 100, message = "种植品种(列表,新增,编辑)长度不能大于 100")
    private String plantType;

    /**
     * 油茶园面积(亩)(列表,新增,编辑)
     */
    @ApiModelProperty(value = "油茶园面积(亩)(列表,新增,编辑)")
    @Digits(integer = 8, fraction = 2, message = "油茶园面积(亩)整数部分长度为8位，小数部分为2位")
    private BigDecimal estateArea;

    /**
     * 联系电话(列表,新增,编辑)
     */
    @ApiModelProperty(value = "联系电话(列表,新增,编辑)")
    @Size(max = 15, message = "联系电话(列表,新增,编辑)长度不能大于 15")
    private String contactPhone;

    /**
     * 油茶园地址(列表,新增,编辑)
     */
    @ApiModelProperty(value = "油茶园地址(列表,新增,编辑)")
    @Size(max = 100, message = "油茶园地址(列表,新增,编辑)长度不能大于 100")
    private String estateAddress;

    /**
     * 油茶茶园简介
     */
    @ApiModelProperty(value = "油茶茶园简介")
    @Size(max = 500, message = "油茶茶园简介长度不能大于 500")
    private String estateIntroduction;

    /**
     * 状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)
     */
    @ApiModelProperty(value = "状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)")
    private OilTeaEstateEstateStatus estateStatus;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * 茶园图片(列表,新增,编辑)
     */
    @ApiModelProperty(value = "茶园图片(列表,新增,编辑)")
    private String estatePictureListJson;

}
