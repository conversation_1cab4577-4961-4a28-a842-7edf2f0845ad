package com.hainancrc.module.creditcxbs.api.teaorigincode.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TeaOriginCodeSubjectRespVO {
    @ApiModelProperty(value = "产地码id")
    private Long teaOriginCodeId;

    @ApiModelProperty(value = "主体名称")
    private String subjectName;

    @ApiModelProperty(value = "所属产地")
    private String teaOrigin;

    @ApiModelProperty(value = "茶园名称")
    private String teaEstate;

    @ApiModelProperty(value = "茶叶品种")
    private String teaType;

    @ApiModelProperty(value = "产地介绍")
    private String originIntroduction;

    @ApiModelProperty(value = "产地图片")
    private String originPic;
}
