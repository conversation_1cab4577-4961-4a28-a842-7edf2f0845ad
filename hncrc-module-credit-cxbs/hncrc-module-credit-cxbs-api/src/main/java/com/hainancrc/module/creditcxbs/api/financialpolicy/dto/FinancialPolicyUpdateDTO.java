package com.hainancrc.module.creditcxbs.api.financialpolicy.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.FinancialPolicyPolicyStatus;

/**
* 金融政策信息
*/
@Data
public class FinancialPolicyUpdateDTO {
    
    /**
    * 主键id
    */
    @ApiModelProperty(value = "主键id")
    private Long id;
    
    /**
    * 政策名称(列表,新增,编辑)
    */
    @ApiModelProperty(value = "政策名称(列表,新增,编辑)")
    @Size(max = 100, message = "政策名称(列表,新增,编辑)长度不能大于 100")
    private String policyName;
    
    /**
    * 政策内容描述(新增,编辑)
    */
    @ApiModelProperty(value = "政策内容描述(新增,编辑)")
    private String policyContent;
    
    /**
    * 政策附件key(新增,编辑)
    */
    @ApiModelProperty(value = "政策附件key(新增,编辑)")
    @Size(max = 100, message = "政策附件key(新增,编辑)长度不能大于 100")
    private String policyFileKey;
    
    /**
    * 政策附件url(新增,编辑)
    */
    @ApiModelProperty(value = "政策附件url(新增,编辑)")
//    @Size(max = 200, message = "政策附件url(新增,编辑)长度不能大于 200")
    private String policyFileUrl;
    
    /**
    * 政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表)
    */
    @ApiModelProperty(value = "政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表)")
    private FinancialPolicyPolicyStatus policyStatus;
    
    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    @Size(max = 50, message = "创建人长度不能大于 50")
    private String creator;
    
    /**
    * 更新人
    */
    @ApiModelProperty(value = "更新人")
    @Size(max = 50, message = "更新人长度不能大于 50")
    private String updater;
    
    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**
    * 更新时间
    */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /**
    * 逻辑删除标志位
    */
    @ApiModelProperty(value = "逻辑删除标志位")
    private Boolean deleted;
    
    
}

