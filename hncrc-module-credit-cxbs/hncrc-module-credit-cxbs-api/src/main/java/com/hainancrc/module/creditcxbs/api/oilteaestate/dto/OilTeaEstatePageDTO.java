package com.hainancrc.module.creditcxbs.api.oilteaestate.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 油茶茶园信息
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OilTeaEstatePageDTO  extends PageParam {

    @ApiModelProperty(value = "茶园名称")
    private String estateName;
    
}

