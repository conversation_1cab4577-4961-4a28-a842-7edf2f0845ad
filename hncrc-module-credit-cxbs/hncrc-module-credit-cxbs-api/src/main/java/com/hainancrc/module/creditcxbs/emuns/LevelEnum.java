
package com.hainancrc.module.creditcxbs.emuns;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LevelEnum {

    A("A","等级A"),
    B("B","等级B"),
    C("C","等级C"),
    D("D","等级D");

    public static LevelEnum match(String key) {
        LevelEnum result = null;
        for (LevelEnum s : values()) {
            if (s.getCode().equals(key)) {
                result = s;
                break;
            }
        }
        return result;
    }


    @JsonValue
    private String code;

    private String msg;
}
