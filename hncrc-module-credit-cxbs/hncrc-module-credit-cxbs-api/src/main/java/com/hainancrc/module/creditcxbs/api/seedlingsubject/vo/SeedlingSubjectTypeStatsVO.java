package com.hainancrc.module.creditcxbs.api.seedlingsubject.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@ApiModel("苗木经营主体类型分析 Response VO")
@Data
public class SeedlingSubjectTypeStatsVO {

    @ApiModelProperty(value = "总数量")
    private Long totalCount;

    @Schema(description = "企业数量")
    private Long enterpriseCount;

    @Schema(description = "企业占比")
    private String enterprisePercentage;

    @Schema(description = "合作社数量")
    private Long cooperativeCount;

    @Schema(description = "合作社占比")
    private String cooperativePercentage;

    @Schema(description = "个体工商户数量")
    private Long planterCount;

    @Schema(description = "个体工商户占比")
    private String planterPercentage;

    @Schema(description = "农户数量")
    private Long farmerCount;

    @Schema(description = "农户占比")
    private String farmerPercentage;

} 