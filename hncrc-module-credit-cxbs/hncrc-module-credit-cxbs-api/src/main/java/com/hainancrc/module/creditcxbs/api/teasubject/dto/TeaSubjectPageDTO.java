package com.hainancrc.module.creditcxbs.api.teasubject.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 茶叶经营主体信息
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeaSubjectPageDTO  extends PageParam {
    
    @ApiModelProperty(value = "商户名称")
    private String subjectName;

    @ApiModelProperty(value = "主体类型")
    private String subjectType;
    
}


