package com.hainancrc.module.creditcxbs.api.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* 苗木经营主体信息主体类型
*/
@Getter
@AllArgsConstructor
public enum SeedlingSubjectSubjectType {


    Cooperative("Cooperative","合作社"),
    IndvBusiness("IndvBusiness","个体工商户"),
    Enterprise("Enterprise","企业"),
    Farmer("Farmer","农户");

    /**
     * 类型编码
     */
    @JsonValue
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;
}


