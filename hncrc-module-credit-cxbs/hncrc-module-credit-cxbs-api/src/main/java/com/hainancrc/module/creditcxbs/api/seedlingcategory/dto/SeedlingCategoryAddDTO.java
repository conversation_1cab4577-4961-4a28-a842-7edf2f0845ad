package com.hainancrc.module.creditcxbs.api.seedlingcategory.dto;

import com.hainancrc.module.creditcxbs.api.enums.SeedlingCategorySeedlingStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.Date;

/**
* 苗木信息
*/
@Data
public class SeedlingCategoryAddDTO {

    /**
    * 苗木种类(列表,新增,编辑)
    */
    @ApiModelProperty(value = "苗木种类(列表,新增,编辑)")
    @Size(max = 50, message = "苗木种类(列表,新增,编辑)长度不能大于 50")
    private String seedlingCategory;
    
    /**
    * 苗木品种(列表,新增,编辑)
    */
    @ApiModelProperty(value = "苗木品种(列表,新增,编辑)")
    @Size(max = 50, message = "苗木品种(列表,新增,编辑)长度不能大于 50")
    private String seedlingVariety;

}

