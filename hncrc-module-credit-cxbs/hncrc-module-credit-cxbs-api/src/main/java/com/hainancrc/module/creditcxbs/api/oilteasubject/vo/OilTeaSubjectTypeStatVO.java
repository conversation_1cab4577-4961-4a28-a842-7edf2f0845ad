package com.hainancrc.module.creditcxbs.api.oilteasubject.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "油茶主体分类统计 VO")
@Data
public class OilTeaSubjectTypeStatVO {
    
    @Schema(description = "企业数量")
    private Integer enterpriseCount;
    
    @Schema(description = "企业占比")
    private String enterprisePercentage;
    
    @Schema(description = "合作社数量")
    private Integer cooperativeCount;
    
    @Schema(description = "合作社占比")
    private String cooperativePercentage;
    
    @Schema(description = "个体工商户数量")
    private Integer planterCount;
    
    @Schema(description = "个体工商户占比")
    private String planterPercentage;
    
    @Schema(description = "农户数量")
    private Integer farmerCount;
    
    @Schema(description = "农户占比")
    private String farmerPercentage;

} 