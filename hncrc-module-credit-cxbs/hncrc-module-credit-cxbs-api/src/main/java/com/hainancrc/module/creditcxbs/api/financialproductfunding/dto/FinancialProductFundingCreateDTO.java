package com.hainancrc.module.creditcxbs.api.financialproductfunding.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.FinancialProductFundingFileStatus;
import org.springframework.web.multipart.MultipartFile;

/**
* 金融产品融资信息表
*/
@Data
public class FinancialProductFundingCreateDTO {

    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 文件名称(列表,新增,编辑)
    */
    @ApiModelProperty(value = "文件名称(列表,新增,编辑)")
    @Size(max = 100, message = "文件名称(列表,新增,编辑)长度不能大于 100")
    private String fileName;
    
    /**
    * 文件内容描述(新增,编辑)
    */
    @ApiModelProperty(value = "文件内容描述(新增,编辑)")
    private String fileContent;
    
    /**
    * 文件状态(ENUMS:ONLINE-公示中,OFFLINE-已下线)(列表)
    */
    @ApiModelProperty(value = "文件状态(ENUMS:ONLINE-公示中,OFFLINE-已下线)(列表)")
    private FinancialProductFundingFileStatus fileStatus;

}

