package com.hainancrc.module.creditcxbs.api.seedlingsubject.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("苗木企业信用等级统计 Response VO")
@Data
public class    SeedlingCreditStatsVO {

    @ApiModelProperty(value = "苗木企业总数")
    private Long totalCount;

    @ApiModelProperty(value = "A级企业数量")
    private Long levelACount;
    
    @ApiModelProperty(value = "A级企业占比")
    private String levelAPercent;
// !!REVIEW
// TODO 可能的问题: 企业的占比应该是一个数值类型，而不是字符串。这可能导致计算错误或不一致的问题。
// 修改建议：将`levelAPercent`及其相关字段类型从`String`改为`Double`或`BigDecimal`，以更准确地表示数值。
    
    @ApiModelProperty(value = "B级企业数量")
    private Long levelBCount;
    
    @ApiModelProperty(value = "B级企业占比")
    private String levelBPercent;
    
    @ApiModelProperty(value = "C级企业数量")
    private Long levelCCount;
    
    @ApiModelProperty(value = "C级企业占比")
    private String levelCPercent;
    
    @ApiModelProperty(value = "D级企业数量")
    private Long levelDCount;
    
    @ApiModelProperty(value = "D级企业占比")
    private String levelDPercent;
} 