package com.hainancrc.module.creditcxbs.api.seedlingsubject.vo;

import com.hainancrc.module.creditcxbs.api.enums.SeedlingSubjectSubjectType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.Date;

/**
* 苗木经营主体信息
*/
@Data
public class SeedlingSubjectMerchantProfileVO extends SeedlingSubjectRespPageVO{

    /**
     * 苗木种类(列表,新增,编辑)
     */
    @ApiModelProperty(value = "苗木种类(列表,新增,编辑)")
    private String seedlingCategory;

    /**
     * 苗木品种(列表,新增,编辑)
     */
    @ApiModelProperty(value = "苗木品种(列表,新增,编辑)")
    private String seedlingVariety;

    /**
     * 价格范围
     */
    @ApiModelProperty(value = "价格范围 piece_range")
// !!REVIEW
// TODO 可能的问题: 注释内容"piece_range"与字段名称不一致，可能导致接收端理解错误。
// 修改建议：将注释中的内容更新为与字段名称一致，例如`@ApiModelProperty(value = "价格范围")`。
    private String priceRange;

    /**
     * 经营主体简介 subject_introduction
     */
    @ApiModelProperty(value = "经营主体简介")
    @Size(max = 500, message = "经营主体简介长度不能大于 500")
    private String subjectIntroduction;

    /**
     * 规格情况
     */
    @ApiModelProperty(value = "规格情况")
    private String seedlingSpecs;

    /**
     * 特点优势
     */
    @ApiModelProperty(value = "特点优势")
    private String seedlingAdvantages;


    
}

