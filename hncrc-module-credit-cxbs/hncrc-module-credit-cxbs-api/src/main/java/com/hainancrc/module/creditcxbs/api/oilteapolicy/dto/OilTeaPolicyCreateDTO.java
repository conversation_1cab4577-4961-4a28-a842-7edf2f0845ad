package com.hainancrc.module.creditcxbs.api.oilteapolicy.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaPolicyPolicyStatus;

/**
* 油茶政策信息
*/
@Data
public class OilTeaPolicyCreateDTO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 政策名称(列表,新增,编辑)
    */
    @ApiModelProperty(value = "政策名称(列表,新增,编辑)")
    @Size(max = 100, message = "政策名称(列表,新增,编辑)长度不能大于 100")
    private String policyName;
    
    /**
    * 政策内容描述(新增,编辑)
    */
    @ApiModelProperty(value = "政策内容描述(新增,编辑)")
    private String policyContent;
    
    /**
    * 政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)
    */
    @ApiModelProperty(value = "政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)")
    private OilTeaPolicyPolicyStatus policyStatus;
    
    /**
    * 政策附件(新增,编辑)
    */
    @ApiModelProperty(value = "政策附件(新增,编辑)")
    private String policyFileListJson;

}

