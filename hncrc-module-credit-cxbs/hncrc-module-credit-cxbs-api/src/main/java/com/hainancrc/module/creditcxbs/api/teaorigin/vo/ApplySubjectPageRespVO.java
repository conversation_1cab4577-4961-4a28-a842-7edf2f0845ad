package com.hainancrc.module.creditcxbs.api.teaorigin.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ApplySubjectPageRespVO {

    @ApiModelProperty(value = "主体名称")
    private String subjectName;

    @ApiModelProperty(value = "主体类型")
    private String subjectType;

    @ApiModelProperty(value = "主体标识")
    private String uniscid;

    @ApiModelProperty(value = "申领日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
