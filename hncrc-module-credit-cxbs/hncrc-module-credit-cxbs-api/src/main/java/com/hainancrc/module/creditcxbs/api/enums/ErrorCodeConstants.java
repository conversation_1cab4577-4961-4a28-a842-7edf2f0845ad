package com.hainancrc.module.creditcxbs.api.enums;

import com.hainancrc.framework.common.exception.ErrorCode;

public interface ErrorCodeConstants {

    ErrorCode BUSINESS_FUNDING_NOT_EXISTS = new ErrorCode(100001, "经营主体融资信息不存在");

    ErrorCode FINANCIAL_POLICY_NOT_EXISTS = new ErrorCode(200001, "金融政策信息不存在");

    ErrorCode FINANCIAL_PRODUCT_FUNDING_NOT_EXISTS = new ErrorCode(300001, "金融产品融资信息表不存在");

    ErrorCode OIL_TEA_ESTATE_NOT_EXISTS = new ErrorCode(400001, "油茶茶园信息不存在");

    ErrorCode OIL_TEA_POLICY_NOT_EXISTS = new ErrorCode(500001, "油茶政策信息不存在");

    ErrorCode OIL_TEA_SUBJECT_NOT_EXISTS = new ErrorCode(600001, "油茶经营主体信息不存在");

    ErrorCode OIL_TEA_SUBSIDY_PUBLIC_NOTE_NOT_EXISTS = new ErrorCode(700001, "油茶补贴公示信息不存在");

    ErrorCode OIL_TEA_SUBSIDY_SUBJECT_NOT_EXISTS = new ErrorCode(800001, "油茶补贴主体名单不存在");

    ErrorCode ORIGIN_SUBJECT_USER_NOT_EXISTS = new ErrorCode(900001, "产地主体对应用户表不存在");

    ErrorCode SEEDLING_CATEGORY_NOT_EXISTS = new ErrorCode(1000001, "苗木信息不存在");

    ErrorCode SEEDLING_CATEGORY_EXISTS = new ErrorCode(1000002, "苗木信息已存在");

    ErrorCode SEEDLING_SUBJECT_NOT_EXISTS = new ErrorCode(1100001, "苗木经营主体信息不存在");

    ErrorCode SEEDLING_SUBJECT_EXISTS = new ErrorCode(1100002, "苗木经营主体信息已存在");

    ErrorCode SEEDLING_SUBJECT_CATEGORY_NOT_EXISTS = new ErrorCode(1200001, "苗木类不存在");

    ErrorCode TEA_ORIGIN_NOT_EXISTS = new ErrorCode(1300001, "茶叶产地信息不存在");

    ErrorCode TEA_ORIGIN_CODE_NOT_EXISTS = new ErrorCode(1400001, "产地码不存在");

    ErrorCode TEA_ORIGIN_CODE_HISTORY_NOT_EXISTS = new ErrorCode(1500001, "产地码申报历史记录不存在");

    ErrorCode TEA_SUBJECT_NOT_EXISTS = new ErrorCode(1600001, "茶叶经营主体信息不存在");

    ErrorCode TEMPLATE_DOWNLOAD_FAIL = new ErrorCode(1700001, "获取下载模板失败");

    ErrorCode READ_FILE_FAIL = new ErrorCode(1700002, "读取文件失败");

    ErrorCode COMPANY_NOT_EXISTS = new ErrorCode(800001, "商户不存在");

    ErrorCode CATEGORY_STAT_EMPTY = new ErrorCode(1800000, "分类统计数据为空");

    ErrorCode STAT_EMPTY = new ErrorCode(1900000, "统计数据为空");
    ErrorCode COMPANY_JSON_OBJECT_PARSE_ERROR = new ErrorCode(2000000, "查询失败 by parse companyJsonObject");

    ErrorCode ARCHIVE_REQ_BODY_PARSE_ERROR = new ErrorCode(2100000, "查询失败 by decode archiveReqBody");

    ErrorCode FINANCIAL_PRODUCT_FUNDING_EXISTS = new ErrorCode(2200000, "金融产品融资信息已存在");
    ErrorCode OIL_TEA_SUBJECT_EXISTS = new ErrorCode(2300000, "产地码id不存在");
}
