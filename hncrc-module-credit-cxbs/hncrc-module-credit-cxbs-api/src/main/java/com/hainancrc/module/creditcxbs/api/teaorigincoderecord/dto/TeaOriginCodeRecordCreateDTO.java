package com.hainancrc.module.creditcxbs.api.teaorigincoderecord.dto;

import com.hainancrc.module.creditcxbs.api.teaorigincodehistory.dto.TeaOriginCodeHistoryCreateDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
* 产地码
*/
@Data
public class TeaOriginCodeRecordCreateDTO {

    @ApiModelProperty(value = "产地码id")
    private Long originCodeId;

    @ApiModelProperty(value = "产地id")
    private Long originId;

    @ApiModelProperty(value = "茶叶品种")
    private String teaType;

    @ApiModelProperty(value = "用码数（次）")
    private Long useCount;

    @ApiModelProperty(value = "累计茶青总数（斤）")
    private Integer totalTeaWeight;

    @ApiModelProperty(value = "承诺书文件key")
    private String promiseFileKey;

    @ApiModelProperty(value = "承诺书文件url")
    private String promiseFileUrl;

    @ApiModelProperty(value = "产品规格说明")
    private List<TeaOriginCodeHistoryCreateDTO> teaOriginCodeHistoryList;
    
    
}

