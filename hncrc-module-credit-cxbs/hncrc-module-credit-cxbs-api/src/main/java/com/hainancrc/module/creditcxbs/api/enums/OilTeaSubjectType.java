package com.hainancrc.module.creditcxbs.api.enums;

import lombok.Getter;

@Getter
public enum OilTeaSubjectType {
    Enterprise("企业"),
    Cooperative("合作社"),
    IndvBusiness("基层组织"),
    Farmer("农户");
    ;

    private final String description;

    OilTeaSubjectType(String description) {
        this.description = description;
    }

    public static String getEnumByValue(String value) {
        for (OilTeaSubjectType item : OilTeaSubjectType.values()) {
            if (item.getDescription().equals(value)) {
                return item.name();
            }
        }
        return null;
    }
}
