package com.hainancrc.module.creditcxbs.api.quarantineinformationview.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* 产地码查看按日统计表
*/
@Data
public class QuarantineInformationViewRespVO {

    /**
    * id
    */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
    * 商户id
    */
    @ApiModelProperty(value = "商户id")
    private Long companyId;

    /**
    * 查看次数
    */
    @ApiModelProperty(value = "查看次数")
    private Long viewCount;

    /**
    * 查看日期
    */
    @ApiModelProperty(value = "查看日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date viewDate;

}

