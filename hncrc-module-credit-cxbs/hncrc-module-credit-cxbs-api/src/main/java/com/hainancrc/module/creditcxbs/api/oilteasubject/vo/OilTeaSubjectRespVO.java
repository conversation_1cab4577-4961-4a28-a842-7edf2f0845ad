package com.hainancrc.module.creditcxbs.api.oilteasubject.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 油茶经营主体信息
*/
@Data
public class OilTeaSubjectRespVO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 统一信用代码(列表,编辑)
    */
    @ApiModelProperty(value = "统一信用代码(列表,编辑)")
    private String uniscid;
    
    /**
    * 商户名称(列表,编辑)
    */
    @ApiModelProperty(value = "商户名称(列表,编辑)")
    private String subjectName;
    
    /**
    * 主体类型(列表,编辑)
    */
    @ApiModelProperty(value = "主体类型(列表,编辑)")
    private String subjectType;
    
    /**
    * 法定代表人(列表,编辑)
    */
    @ApiModelProperty(value = "法定代表人(列表,编辑)")
    private String legal;

    /**
    * 经营地址(列表,编辑)
    */
    @ApiModelProperty(value = "经营地址(列表,编辑)")
    @Size(max = 100, message = "经营地址(列表,编辑)长度不能大于 100")
    private String opAddress;
    
    /**
    * 更新时间(列表)
    */
    @ApiModelProperty(value = "更新时间(列表)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 所属乡镇(列表,编辑)
     */
    @ApiModelProperty(value = "所属乡镇(列表,编辑)")
    private String belongTownship;

    /**
     * 联系电话(列表,编辑)
     */
    @ApiModelProperty(value = "联系电话(列表,编辑)")
    private String contactPhone;

    /**
     * 种植类型
     */
    @ApiModelProperty(value = "种植类型")
    private String plantType;

    /**
     * 种植模式
     */
    @ApiModelProperty(value = "种植模式")
    private String plantMode;

    /**
     * 种植总面积(亩)
     */
    @ApiModelProperty(value = "种植总面积(亩)")
    private BigDecimal plantTotalArea;

    /**
     * 种植株数（株）
     */
    @ApiModelProperty(value = "种植植株数（株）")
    private Long plantCount;

    /**
     * 种植苗木是否良种树苗
     */
    @ApiModelProperty(value = "种植苗木是否良种树苗")
    private Integer isEliteSeed;

    /**
     * 是否领取补贴
     */
    @ApiModelProperty(value = "是否领取补贴")
    private Integer hasReceivedSubsidy;

    /**
     * 领取政策补贴
     */
    @ApiModelProperty(value = "领取政策补贴")
    private BigDecimal receivedSubsidy;

    /**
     * 补贴时间
     */
    @ApiModelProperty(value = "补贴时间")
    private String subsidyTime;

}

