package com.hainancrc.module.creditcxbs.api.financialproductfunding.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 金融产品融资信息表
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FinancialProductFundingPageDTO  extends PageParam {
    @ApiModelProperty(value = "文件名称")
    private String fileName;
    
    
}

