package com.hainancrc.module.creditcxbs.api.teaorigincoderecord.dto;

import com.hainancrc.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
* 产地码
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeaOriginCodeRecordPageDTO extends PageParam {

    @ApiModelProperty(value = "产地名称")
    private String originName;

    @ApiModelProperty(value = "产地码ID")
    private Long originCodeId;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "产地主体id")
    private Long teaSubjectId;
}

