package com.hainancrc.module.creditcxbs.api.quarantineinformationview.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* 产地码查看按日统计表
*/
@Data
public class QuarantineInformationViewUpdateDTO {

    /**
    * id
    */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
    * 商户id
    */
    @ApiModelProperty(value = "商户id")
    private Long companyId;

    /**
    * 查看次数
    */
    @ApiModelProperty(value = "查看次数")
    private Long viewCount;

    /**
    * 查看日期
    */
    @ApiModelProperty(value = "查看日期")
    private Date viewDate;



}

