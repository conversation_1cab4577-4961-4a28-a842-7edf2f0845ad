package com.hainancrc.module.creditcxbs.api.seedlingcategory.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.SeedlingCategorySeedlingStatus;

/**
 * 苗木信息
 */
@Data
public class SeedlingCategoryUpdateDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 苗木种类(列表,新增,编辑)
     */
    @ApiModelProperty(value = "苗木种类(列表,新增,编辑)")
    @Size(max = 50, message = "苗木种类(列表,新增,编辑)长度不能大于 50")
    private String seedlingCategory;

    /**
     * 苗木品种(列表,新增,编辑)
     */
    @ApiModelProperty(value = "苗木品种(列表,新增,编辑)")
    @Size(max = 50, message = "苗木品种(列表,新增,编辑)长度不能大于 50")
    private String seedlingVariety;

    /**
     * 价格范围(列表,新增,编辑)
     */
    @ApiModelProperty(value = "价格范围(列表,新增,编辑)")
    @Size(max = 100, message = "价格范围(列表,新增,编辑)长度不能大于 100")
    private String pieceRange;

    /**
     * 规格情况(列表,新增,编辑)
     */
    @ApiModelProperty(value = "规格情况(列表,新增,编辑)")
    @Size(max = 500, message = "规格情况长度不能大于 500")
    private String seedlingSpecs;

    /**
     * 特点优势(列表,新增,编辑)
     */
    @ApiModelProperty(value = "特点优势(列表,新增,编辑)")
    @Size(max = 500, message = "特点优势(长度不能大于 500")
    private String seedlingAdvantages;

    /**
     * 苗木状态(列表)(ENUMS:ONLINE-使用中,OFFLINE-已下架)
     */
    @ApiModelProperty(value = "苗木状态(列表)(ENUMS:ONLINE-使用中,OFFLINE-已下架)")
    private SeedlingCategorySeedlingStatus seedlingStatus;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * 相关政策(条)(列表,新增,编辑)
     */
    @ApiModelProperty(value = "相关政策(条)(列表,新增,编辑)")
    private Integer relatedPolicy;

    /**
     * 苗木图片列表
     */
    @ApiModelProperty(value = "苗木图片列表")
    private String seedlingPictureJson;

    /**
     * 相关政策列表
     */
    @ApiModelProperty(value = "相关政策列表")
    private List<SeedlingCategoryPolicyDTO> policyList;

    /**
     * 附加信息JSON
     */
    @ApiModelProperty(value = "附加信息JSON")
    private String extraInfo;

}
