package com.hainancrc.module.creditcxbs.api.teaorigincodehistory.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 产地码申报历史记录
*/
@Data
public class TeaOriginCodeHistoryUpdateDTO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 产地码id
    */
    @ApiModelProperty(value = "产地码id")
    private Long teaOriginCodeId;
    
    /**
    * 贴标产品名称
    */
    @ApiModelProperty(value = "贴标产品名称")
    @Size(max = 100, message = "贴标产品名称长度不能大于 100")
    private String productName;
    
    /**
    * 产品规格（净含量）
    */
    @ApiModelProperty(value = "产品规格（净含量）")
    @Size(max = 100, message = "产品规格（净含量）长度不能大于 100")
    private String productSpecs;
    
    /**
    * 产地码数量
    */
    @ApiModelProperty(value = "产地码数量")
    private Integer codeCount;
    
    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    @Size(max = 50, message = "创建人长度不能大于 50")
    private String creator;
    
    /**
    * 更新人
    */
    @ApiModelProperty(value = "更新人")
    @Size(max = 50, message = "更新人长度不能大于 50")
    private String updater;
    
    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**
    * 更新时间
    */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /**
    * 逻辑删除标志位
    */
    @ApiModelProperty(value = "逻辑删除标志位")
    private Boolean deleted;
    
    
}

