package com.hainancrc.module.creditcxbs.api.businessfunding.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 经营主体融资信息
*/
@Data
public class BusinessFundingUpdateDTO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 金融产品融资信息id
    */
    @ApiModelProperty(value = "金融产品融资信息id")
    private Long financialProductFundingId;
    
    /**
    * 申请主体
    */
    @ApiModelProperty(value = "申请主体")
    @Size(max = 100, message = "申请主体长度不能大于 100")
    private String applySubject;

    /**
     * 主体类型(列表,编辑)
     */
    @ApiModelProperty(value = "主体类型(列表,编辑)")
    private String subjectType;
    
    /**
    * 申请时间
    */
    @ApiModelProperty(value = "申请时间")
    private Date applyTime;
    
    /**
    * 申请产品
    */
    @ApiModelProperty(value = "申请产品")
    @Size(max = 100, message = "申请产品长度不能大于 100")
    private String applyProduct;
    
    /**
    * 申请融资额(万元)
    */
    @ApiModelProperty(value = "申请融资额(万元)")
    private BigDecimal applyAmount;
    
    /**
    * 发放融资额(万元)
    */
    @ApiModelProperty(value = "发放融资额(万元)")
    private BigDecimal disburseAmount;
    
    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    @Size(max = 50, message = "创建人长度不能大于 50")
    private String creator;
    
    /**
    * 更新人
    */
    @ApiModelProperty(value = "更新人")
    @Size(max = 50, message = "更新人长度不能大于 50")
    private String updater;
    
    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**
    * 更新时间
    */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /**
    * 逻辑删除标志位
    */
    @ApiModelProperty(value = "逻辑删除标志位")
    private Boolean deleted;
    
    
}

