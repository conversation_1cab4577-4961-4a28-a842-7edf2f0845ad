package com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* 产地码申领 分页数据
*/
@Data
public class TeaOriginCodeRecordPageRespVO {

    @ApiModelProperty(value = "id")
    private Long codeId;

    @ApiModelProperty(value = "产地名称")
    private String teaOriginName;

    @ApiModelProperty(value = "茶园名称")
    private String teaEstate;

    @ApiModelProperty(value = "茶叶品种")
    private String teaType;

    @ApiModelProperty(value = "扫码查看次数")
    private Long viewCount;

    @ApiModelProperty(value = "用码累计数")
    private Long useCount;

    @ApiModelProperty(value = "茶青总数（斤）")
    private Long totalTeaWeight;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "状态")
    private String codeStatus;

}

