package com.hainancrc.module.creditcxbs.api.oiltea.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "油茶数据总览 Response VO")
@Data
public class OilTeaOverviewVO {

    @Schema(description = "油茶经营主体人数")
    private Long subjectCount;

    @Schema(description = "油茶园个数") 
    private Long estateCount;

    @Schema(description = "油茶补贴主体数")
    private Long subsidySubjectCount;

    @Schema(description = "油茶补贴金额(万)")
    private Double subsidyAmount;
} 