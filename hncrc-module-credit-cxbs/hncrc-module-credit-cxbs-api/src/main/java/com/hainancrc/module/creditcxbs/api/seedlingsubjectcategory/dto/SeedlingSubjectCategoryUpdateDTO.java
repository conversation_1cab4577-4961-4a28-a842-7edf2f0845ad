package com.hainancrc.module.creditcxbs.api.seedlingsubjectcategory.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 苗木类
*/
@Data
public class SeedlingSubjectCategoryUpdateDTO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 经营主体id
    */
    @ApiModelProperty(value = "经营主体id")
    private Long subjectId;
    
    /**
    * 苗木类id
    */
    @ApiModelProperty(value = "苗木类id")
    private Long categoryId;
    
    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    @Size(max = 50, message = "创建人长度不能大于 50")
    private String creator;
    
    /**
    * 更新人
    */
    @ApiModelProperty(value = "更新人")
    @Size(max = 50, message = "更新人长度不能大于 50")
    private String updater;
    
    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**
    * 更新时间
    */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /**
    * 逻辑删除标志位
    */
    @ApiModelProperty(value = "逻辑删除标志位")
    private Boolean deleted;
    
    
}

