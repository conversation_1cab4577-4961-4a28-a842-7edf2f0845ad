package com.hainancrc.module.creditcxbs.api.seedlingsubject.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("A级主体统计信息VO")
public class ALevelSubjectStatisticsVO {
    
    @ApiModelProperty("A级经营主体总量")
    private Integer totalCount;
    
    @ApiModelProperty("A级企业数量")
    private Integer enterpriseCount;
    
    @ApiModelProperty("A级合作社数量")
    private Integer cooperativeCount;
    
    @ApiModelProperty("A级种植户数量")
    private Integer planterCount;
} 