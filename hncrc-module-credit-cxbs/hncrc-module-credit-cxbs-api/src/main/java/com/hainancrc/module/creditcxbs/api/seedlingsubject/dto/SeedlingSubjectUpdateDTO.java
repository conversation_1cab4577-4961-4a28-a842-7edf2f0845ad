package com.hainancrc.module.creditcxbs.api.seedlingsubject.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.SeedlingSubjectSubjectType;

/**
 * 苗木经营主体信息
 */
@Data
public class SeedlingSubjectUpdateDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotNull(message = "主键id不能为空")
    private Long id;

    /**
     * 商户名称(列表)
     */
    @ApiModelProperty(value = "商户名称(列表)")
    @Size(max = 100, message = "商户名称(列表)长度不能大于 100")
    private String subjectName;

    /**
     * 主体类型(列表)(ENUMS: Cooperative-合作社, Grower-种植户, Enterprise-企业, Others-其它)
     */
    @ApiModelProperty(value = "主体类型(列表)(ENUMS: Cooperative-合作社, Grower-种植户, Enterprise-企业, Others-其它)")
    @Size(max = 20, message = "主体类型长度不能大于 20")
    private String subjectType;

    /**
     * 统一信用代码(列表)
     */
    @ApiModelProperty(value = "统一信用代码(列表)")
    @Size(max = 30, message = "统一信用代码(列表)长度不能大于 30")
    private String uniscid;

    /**
     * 法定代表人(列表)
     */
    @ApiModelProperty(value = "法定代表人(列表)")
    @Size(max = 20, message = "法定代表人(列表)长度不能大于 20")
    private String legalName;

    /**
     * 地址(列表)
     */
    @ApiModelProperty(value = "地址(列表)")
    @Size(max = 100, message = "地址(列表)长度不能大于 100")
    private String subjectAddress;

    /**
     * 联系电话(列表)
     */
    @ApiModelProperty(value = "联系电话(列表)")
    @Size(max = 15, message = "联系电话(列表)长度不能大于 15")
    private String contactPhone;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * 是否按规定建立经营档案
     */
    @ApiModelProperty(value = "是否按规定建立经营档案")
    private Integer hasStandardArchive;

    /**
     * 档案内容是否完整及时
     */
    @ApiModelProperty(value = "档案内容是否完整及时")
    private Integer hasArchiveComplete;

    /**
     * 种苗是否已检疫
     */
    @ApiModelProperty(value = "种苗是否已检疫")
    private Integer hasSeedQuarantine;

    /**
     * 种苗是否已检验
     */
    @ApiModelProperty(value = "种苗是否已检验")
    private Integer hasSeedInspection;

    /**
     * 包装是否有标签或使用说明
     */
    @ApiModelProperty(value = "包装是否有标签或使用说明")
    private Integer hasPackageStandard;

    /**
     * 包装标签或使用说明是否规范完整
     */
    @ApiModelProperty(value = "包装标签或使用说明是否规范完整")
    private Integer hasPackageComplete;

    /**
     * 是否超范围营业（种子生产经营许可）
     */
    @ApiModelProperty(value = "是否超范围营业（种子生产经营许可）")
    private Integer hasBeyondScope;

    /**
     * 经营主体照片
     */
    @ApiModelProperty(value = "经营主体照片")
    private String subjectPictureListJson;

    /**
     * 经营主体简介
     */
    @ApiModelProperty(value = "经营主体简介")
    private String subjectIntroduction;

    /**
     * 信用等级(列表)
     */
    @ApiModelProperty(value = "信用等级(列表)")
    @Size(max = 255, message = "信用等级(列表)长度不能大于 255")
    private String creditLevel;

    /**
     * 评价得分(列表)
     */
    @ApiModelProperty(value = "评价得分(列表)")
    @Size(max = 255, message = "评价得分(列表)长度不能大于 255")
    private String evaluationScore;

    /**
     * 评价日期(列表)
     */
    @ApiModelProperty(value = "评价日期(列表)")
    private Date evaluationDate;

    /**
     * 信用查看次数(列表)
     */
    @ApiModelProperty(value = "信用查看次数(列表)")
    private Integer viewCount;

    /**
     * 一户一码(列表)
     */
    @ApiModelProperty(value = "一户一码(列表)")
    @Size(max = 100, message = "一户一码(列表)长度不能大于 100")
    private String oneCode;

    /**
     * 一苗一码(列表)
     */
    @ApiModelProperty(value = "一苗一码(列表)")
    @Size(max = 100, message = "一苗一码(列表)长度不能大于 100")
    private String oneSeedCode;

    /**
     * 附加信息JSON
     */
    @ApiModelProperty(value = "附加信息JSON")
    private String extraInfo;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @Size(max = 50, message = "创建人长度不能大于 50")
    private String creator;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @Size(max = 50, message = "更新人长度不能大于 50")
    private String updater;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 逻辑删除标志位
     */
    @ApiModelProperty(value = "逻辑删除标志位")
    private Boolean deleted;

    /**
     * 苗木品种(列表,新增,编辑)
     */
    @ApiModelProperty(value = "苗木品种id")
    private List<Long> seedlingVarietyIds;

    @ApiModelProperty(value = "销售渠道")
    private String saleChannel;

    @ApiModelProperty(value = "二维码链接")
    private String qrCodeUrl;

    @ApiModelProperty(value = "种子生产经营许可证号")
    private String xkzh;

    @ApiModelProperty(value = "种子生产经营许可证生产经营范围")
    private String scjyzl;

    @ApiModelProperty(value = "种子生产经营许可证起始时间")
    private Date zzyxqqsrq;

    @ApiModelProperty(value = "种子生产经营许可证结束时间")
    private Date zzyxqjzrq;

}
