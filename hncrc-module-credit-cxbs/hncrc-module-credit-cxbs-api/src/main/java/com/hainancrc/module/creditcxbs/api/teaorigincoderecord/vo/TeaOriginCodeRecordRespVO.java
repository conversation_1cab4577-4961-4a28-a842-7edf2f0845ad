package com.hainancrc.module.creditcxbs.api.teaorigincoderecord.vo;

import com.hainancrc.module.creditcxbs.api.teaorigincodehistory.vo.TeaOriginCodeHistoryRespVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
* 产地码
*/
@Data
public class TeaOriginCodeRecordRespVO {
    

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "产地名称")
    private String teaOriginName;

    @ApiModelProperty(value = "茶园名称")
    private String teaEstate;

    @ApiModelProperty(value = "茶叶品种")
    private String teaType;

    @ApiModelProperty(value = "查看次数")
    private Integer viewCount;

    @ApiModelProperty(value = "用码累计数")
    private Integer useCount;

    @ApiModelProperty(value = "累计茶青总数（斤）")
    private Integer totalTeaWeight;

    private List<TeaOriginCodeHistoryRespVO> teaOriginCodeHistoryList;

    
}

