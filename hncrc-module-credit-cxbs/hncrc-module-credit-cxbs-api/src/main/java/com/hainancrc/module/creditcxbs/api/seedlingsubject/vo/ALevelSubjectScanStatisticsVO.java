package com.hainancrc.module.creditcxbs.api.seedlingsubject.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("A级主体统计信息VO")
public class ALevelSubjectScanStatisticsVO {

    @ApiModelProperty("A级经营主体总量")
    private int totalCount;
    
    @ApiModelProperty("一户一码下载")
    private int familyCodeDownload;

    @ApiModelProperty("一户一码查看")
    private int familyCodeView;

    @ApiModelProperty("一苗一码下载")
    private int seedlingCodeDownload;

    @ApiModelProperty("一苗一码查看")
    private int seedlingCodeView;

}