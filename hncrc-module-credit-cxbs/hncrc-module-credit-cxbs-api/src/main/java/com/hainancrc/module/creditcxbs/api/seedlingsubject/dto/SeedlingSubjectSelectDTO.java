package com.hainancrc.module.creditcxbs.api.seedlingsubject.dto;

import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.SeedlingSubjectSubjectType;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.dto.SeedlingCategoryAddDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
* 苗木经营主体信息
*/
@Data
public class SeedlingSubjectSelectDTO extends PageParam {

    /**
     * 信用等级(列表) credit_level
     */
    @ApiModelProperty(value = "信用等级(列表)")
    private String creditLevel;

    @ApiModelProperty(value = "商户名称(列表)")
    private String subjectName;



}

