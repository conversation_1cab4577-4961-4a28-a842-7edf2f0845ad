package com.hainancrc.module.creditcxbs.api.teasubject.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class TeaSubjectDataVO {
    @ApiModelProperty(value = "产地主体名称")
    private String subjectName;

    @ApiModelProperty(value = "用码数")
    private Long totalUseCodeCount;

    @ApiModelProperty(value = "产地码查看数")
    private Long totalViewCount;

    @ApiModelProperty(value = "当月茶青总数")
    private Double totalTeaCount;
}
