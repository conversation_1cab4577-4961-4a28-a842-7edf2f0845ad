package com.hainancrc.module.creditcxbs.api.originsubjectuser.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 产地主体对应用户表
*/
@Data
public class OriginSubjectUserRespVO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 产地主体id
    */
    @ApiModelProperty(value = "产地主体id")
    private Long teaSubjectId;
    
    /**
    * 用户id
    */
    @ApiModelProperty(value = "用户id")
    @Size(max = 50, message = "用户id长度不能大于 50")
    private String userId;
    
    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    @Size(max = 50, message = "创建人长度不能大于 50")
    private String creator;
    
    /**
    * 更新人
    */
    @ApiModelProperty(value = "更新人")
    @Size(max = 50, message = "更新人长度不能大于 50")
    private String updater;
    
    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**
    * 更新时间
    */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /**
    * 逻辑删除标志位
    */
    @ApiModelProperty(value = "逻辑删除标志位")
    private Boolean deleted;
    
    
}

