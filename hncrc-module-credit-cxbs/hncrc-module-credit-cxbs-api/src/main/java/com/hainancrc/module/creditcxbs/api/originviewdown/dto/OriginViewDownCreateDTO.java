package com.hainancrc.module.creditcxbs.api.originviewdown.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create 2024-12-20
 */

@Data
public class OriginViewDownCreateDTO {

    /**
     * 商户id
     */
    @ApiModelProperty(value = "商户id")
    @NotNull(message = "商户id不能为空")
    private Long companyId;

    /**
     * 一户一码下载
     */
    @ApiModelProperty(value = "一户一码下载")
    private Long familyCodeDownload;

    /**
     * 一户一码查看
     */
    @ApiModelProperty(value = "一户一码查看")
    private Long familyCodeView;

    /**
     * 一苗一码下载
     */
    @ApiModelProperty(value = "一苗一码下载")
    private Long seedlingCodeDownload;

    /**
     * 一苗一码查看
     */
    @ApiModelProperty(value = "一苗一码查看")
    private Long seedlingCodeView;

    /**
     * 查看日期
     */
    @ApiModelProperty(value = "查看日期")
    private Date viewDate;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型 一户一码下载-familyCodeDownload 一户一码查看-familyCodeView 一苗一码下载-seedlingCodeDownload 一苗一码查看-seedlingCodeView")
    @NotNull(message = "类型不能为空")
    private String type;

}
