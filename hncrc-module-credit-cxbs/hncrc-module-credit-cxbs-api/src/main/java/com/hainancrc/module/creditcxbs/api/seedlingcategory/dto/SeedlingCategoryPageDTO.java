package com.hainancrc.module.creditcxbs.api.seedlingcategory.dto;

import lombok.*;
import io.swagger.annotations.*;
import com.hainancrc.framework.common.pojo.PageParam;

import java.util.List;
import java.util.Set;

/**
* 苗木信息
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SeedlingCategoryPageDTO  extends PageParam {
    
    @ApiModelProperty(value = "苗木品种")
    private String seedlingVariety;

    @ApiModelProperty(value = "苗木种类")
    private String seedlingCategory;

    @ApiModelProperty(value = "经营主体id")
    private Long subjectId;

    @ApiModelProperty(value = "苗木种类id集合",required = false)
    private Set<Long> categoryIds;

}

