package com.hainancrc.module.creditcxbs.api.teaorigincodehistory.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 产地码申报历史记录
*/
@Data
public class TeaOriginCodeHistoryRespVO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 贴标产品名称
    */
    @ApiModelProperty(value = "贴标产品名称")
    @Size(max = 100, message = "贴标产品名称长度不能大于 100")
    private String productName;
    
    /**
    * 产品规格（净含量）
    */
    @ApiModelProperty(value = "产品规格（净含量）")
    @Size(max = 100, message = "产品规格（净含量）长度不能大于 100")
    private String productSpecs;
    
    /**
    * 产地码数量
    */
    @ApiModelProperty(value = "产地码数量")
    private Integer codeCount;

    
}

