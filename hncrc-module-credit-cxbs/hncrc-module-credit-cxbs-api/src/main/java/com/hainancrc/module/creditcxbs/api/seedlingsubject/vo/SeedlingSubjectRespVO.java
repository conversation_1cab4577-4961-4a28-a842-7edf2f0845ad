package com.hainancrc.module.creditcxbs.api.seedlingsubject.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;

import io.swagger.annotations.*;

import javax.validation.constraints.*;

import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.SeedlingSubjectSubjectType;

/**
 * 苗木经营主体信息
 */
@Data
public class SeedlingSubjectRespVO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 商户名称(列表)
     */
    @ApiModelProperty(value = "商户名称(列表)")
    private String subjectName;

    /**
     * 主体类型(列表)(ENUMS: Cooperative-合作社, IndvBusiness-个体工商户, Enterprise-企业,
     * Others-其它)
     */
    @ApiModelProperty(value = "主体类型(列表)(ENUMS: Cooperative-合作社, IndvBusiness-个体工商户, Enterprise-企业, Others-其它)")
    private SeedlingSubjectSubjectType subjectType;

    /**
     * 统一信用代码(列表)
     */
    @ApiModelProperty(value = "统一信用代码(列表)")
    private String uniscid;

    /**
     * 法定代表人(列表)
     */
    @ApiModelProperty(value = "法定代表人(列表)")
    private String legalName;

    /**
     * 地址(列表)
     */
    @ApiModelProperty(value = "地址(列表)")
    private String subjectAddress;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * 是否按规定建立经营档案
     */
    @ApiModelProperty(value = "是否按规定建立经营档案")
    private Integer hasStandardArchive;

    /**
     * 档案内容是否完整及时
     */
    @ApiModelProperty(value = "档案内容是否完整及时")
    private Integer hasArchiveComplete;

    /**
     * 种苗是否已检疫
     */
    @ApiModelProperty(value = "种苗是否已检疫")
    private Integer hasSeedQuarantine;

    /**
     * 种苗是否已检验
     */
    @ApiModelProperty(value = "种苗是否已检验")
    private Integer hasSeedInspection;

    /**
     * 包装是否有标签或使用说明
     */
    @ApiModelProperty(value = "包装是否有标签或使用说明")
    private Integer hasPackageStandard;

    /**
     * 包装标签或使用说明是否规范完整
     */
    @ApiModelProperty(value = "包装标签或使用说明是否规范完整")
    private Integer hasPackageComplete;

    /**
     * 经营主体照片
     */
    @ApiModelProperty(value = "经营主体照片")
    private String subjectPictureListJson;

    /**
     * 经营主体简介
     */
    @ApiModelProperty(value = "经营主体简介")
    @Size(max = 1000, message = "经营主体简介 长度不能大于 1000")
    private String subjectIntroduction;

    /**
     * 信用等级(列表)
     */
    @ApiModelProperty(value = "信用等级(列表)")
    @Size(max = 255, message = "信用等级(列表)长度不能大于 255")
    private String creditLevel;

    /**
     * 评价得分(列表)
     */
    @ApiModelProperty(value = "评价得分(列表)")
    @Size(max = 255, message = "评价得分(列表)长度不能大于 255")
    private String evaluationScore;

    /**
     * 评价日期(列表)
     */
    @ApiModelProperty(value = "评价日期(列表)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date evaluationDate;

    /**
     * 信用查看次数(列表)
     */
    @ApiModelProperty(value = "信用查看次数(列表)")
    private Integer viewCount;

    /**
     * 一户一码(列表)
     */
    @ApiModelProperty(value = "一户一码(列表)")
    private String oneCode;

    /**
     * 一苗一码(列表)
     */
    @ApiModelProperty(value = "一苗一码(列表)")
    private String oneSeedCode;

    /**
     * 附加信息JSON
     */
    @ApiModelProperty(value = "附加信息JSON")
    private String extraInfo;

    /**
     * codeNum
     */
    @ApiModelProperty(value = "codeNum")
    private String codeNum;

    /**
     * 是否超范围营业（种子生产经营许可）
     */
    private Integer hasBeyondScope;

    /**
     * 苗木品种(列表,新增,编辑)
     */
    @ApiModelProperty(value = "苗木品种名称")
    private String seedlingVariety;

    /**
     * 苗木品种(列表,新增,编辑)
     */
    @ApiModelProperty(value = "苗木品种id")
    private List<Long> seedlingVarietyIds;

    @ApiModelProperty(value = "登记状态")
    private String entStatus;

    @ApiModelProperty(value = "注册资金")
    private Double regCap;

    @ApiModelProperty(value = "成立日期")
    private String esDate;

    @ApiModelProperty(value = "登记机关")
    private String regOrg;

    @ApiModelProperty(value = "许可证号")
    private String xkzh;

    @ApiModelProperty(value = "生产经营范围")
    private String opScope;

    @ApiModelProperty(value = "种子生产经营许可证生产经营范围")
    private String scjyzl;

    @ApiModelProperty(value = "种子生产经营许可证起始时间")
    private String zzyxqqsrq;

    @ApiModelProperty(value = "种子生产经营许可证结束时间")
    private String zzyxqjzrq;

    @ApiModelProperty(value = "行业资质")
    private List<PatentInfoVo> patentInfoVoList;

    @ApiModelProperty(value = "行政处罚")
    private List<XzcfInfoVo> xzcfInfoVoList;

    @ApiModelProperty(value = "严重违法失信")
    private List<YzwfInfoVo> yzwfInfoVoList;

    @ApiModelProperty(value = "失信被执行人信息")
    private List<SxbzxrInfoVo> sxbzxrInfoVoList;

    @ApiModelProperty(value = "销售渠道")
    private String saleChannel;

    @ApiModelProperty(value = "二维码链接")
    private String qrCodeUrl;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PatentInfoVo {

        @ApiModelProperty(value = "申请日期")
        String sqrq;

        @ApiModelProperty(value = "商标或专利名称")
        String patName;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class XzcfInfoVo {

        @ApiModelProperty(value = "处罚名称")
        String illegFact;

        @ApiModelProperty(value = "处罚类型")
        String penType;

        @ApiModelProperty(value = "处罚结果")
        String penResult;

        @ApiModelProperty(value = "处罚事由")
        String penBasis;

        @ApiModelProperty(value = "处罚日期")
        String penDecissDate;

        @ApiModelProperty(value = "处罚机构")
        String penAuthName;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class YzwfInfoVo {

        @ApiModelProperty(value = "列入日期")
        String inDate;

        @ApiModelProperty(value = "列入原因")
        String inReason;

        @ApiModelProperty(value = "决定列入机关")
        String inOrg;

        @ApiModelProperty(value = "移出日期")
        String outDate;

        @ApiModelProperty(value = "移出原因")
        String outReason;

        @ApiModelProperty(value = "决定移出机关")
        String outOrg;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SxbzxrInfoVo {

        @ApiModelProperty(value = "失信被执行人")
        String fsxName;

        @ApiModelProperty(value = "统一社会信用代码")
        String uniscid;

        @ApiModelProperty(value = "执行法院")
        String fsxZxfyName;

        @ApiModelProperty(value = "执行依据文号")
        String fsxZxyj;

        @ApiModelProperty(value = "立案时间")
        String fsxLasj;

        @ApiModelProperty(value = "案号")
        String fsxAh;

        @ApiModelProperty(value = "做出执行的依据单位")
        String fsxZczxdw;

        @ApiModelProperty(value = "被执行人履行情况")
        String fsxLxqk;

        @ApiModelProperty(value = "具体情形")
        String fsxSxjtqx;

        @ApiModelProperty(value = "发布时间")
        String fsxFbDate;

    }

}
