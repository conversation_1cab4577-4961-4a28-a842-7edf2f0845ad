package com.hainancrc.module.creditcxbs.api.teaorigin.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 茶叶产地信息
 */
@Data
public class TeaOriginPageRespVO {

    private Long id;

    @ApiModelProperty(value = "产地名称")
    private String originName;

    @ApiModelProperty(value = "茶园名称")
    private String teaEstate;

    @ApiModelProperty(value = "茶叶品种")
    private String teaType;

    @ApiModelProperty(value = "被申领次数")
    private Integer applyCount;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "状态")
    private String originStatus;
}
