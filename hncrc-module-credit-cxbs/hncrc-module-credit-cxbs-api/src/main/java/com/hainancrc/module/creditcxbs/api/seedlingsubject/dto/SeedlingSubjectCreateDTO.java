package com.hainancrc.module.creditcxbs.api.seedlingsubject.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.module.creditcxbs.api.enums.SeedlingSubjectSubjectType;
import com.hainancrc.module.creditcxbs.api.seedlingcategory.dto.SeedlingCategoryAddDTO;

/**
 * 苗木经营主体信息
 */
@Data
public class SeedlingSubjectCreateDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 商户名称(列表)
     */
    @ApiModelProperty(value = "商户名称")
    @Size(max = 100, message = "商户名称长度不能大于 100")
    @NotBlank(message = "商户名称不能为空")
    private String subjectName;

    /**
     * 主体类型(列表)(ENUMS: Cooperative-合作社, Grower-种植户, Enterprise-企业, Others-其它)
     */
    @ApiModelProperty(value = "主体类型(ENUMS: Cooperative-合作社, Farmer-农户, Enterprise-企业, IndvBusiness-个体工商户)")
    @NotNull(message = "主体类型不能为空")
    @Size(max = 20, message = "主体类型长度不能大于 20")
    private String subjectType;

    /**
     * 统一信用代码(列表)
     */
    @ApiModelProperty(value = "统一信用代码")
    @Size(max = 30, message = "统一信用代码长度不能大于 30")
    // @NotBlank(message = "统一信用代码不能为空")
    private String uniscid;

    /**
     * 法定代表人(列表)
     */
    @ApiModelProperty(value = "法定代表人")
    @Size(max = 20, message = "法定代表人长度不能大于 20")
    private String legalName;

    /**
     * 地址(列表)
     */
    @ApiModelProperty(value = "地址")
    @Size(max = 100, message = "地址长度不能大于 100")
    private String subjectAddress;

    /**
     * 联系电话(列表)
     */
    @ApiModelProperty(value = "联系电话")
    @Size(max = 15, message = "联系电话长度不能大于 15")
    @NotBlank(message = "联系电话不能为空")
    private String contactPhone;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * 是否按规定建立经营档案
     */
    @ApiModelProperty(value = "是否按规定建立经营档案")
    private Integer hasStandardArchive;

    /**
     * 档案内容是否完整及时
     */
    @ApiModelProperty(value = "档案内容是否完整及时")
    private Integer hasArchiveComplete;

    /**
     * 种苗是否已检疫
     */
    @ApiModelProperty(value = "种苗是否已检疫")
    private Integer hasSeedQuarantine;

    /**
     * 种苗是否已检验
     */
    @ApiModelProperty(value = "种苗是否已检验")
    private Integer hasSeedInspection;

    /**
     * 包装是否有标签或使用说明
     */
    @ApiModelProperty(value = "包装是否有标签或使用说明")
    private Integer hasPackageStandard;

    /**
     * 包装标签或使用说明是否规范完整
     */
    @ApiModelProperty(value = "包装标签或使用说明是否规范完整")
    private Integer hasPackageComplete;

    /**
     * 是否超范围营业（种子生产经营许可）
     */
    @ApiModelProperty(value = "是否超范围营业（种子生产经营许可）")
    private Integer hasBeyondScope;

    /**
     * 经营主体照片
     */
    @ApiModelProperty(value = "经营主体照片")
    private String subjectPictureListJson;

    /**
     * 经营主体简介
     */
    @ApiModelProperty(value = "经营主体简介")
    @Size(max = 1000, message = "经营主体简介长度不能大于 1000")
    @NotBlank(message = "经营主体简介不能为空")
    private String subjectIntroduction;

    /**
     * 苗木类型
     */
    @ApiModelProperty(value = "苗木类型")
    private List<SeedlingCategoryAddDTO> list;

    /**
     * 苗木品种(列表,新增,编辑)
     */
    @ApiModelProperty(value = "苗木品种id")
    private List<Long> seedlingVarietyIds;

    @ApiModelProperty(value = "销售渠道")
    private String saleChannel;

    @ApiModelProperty(value = "二维码链接")
    private String qrCodeUrl;

    @ApiModelProperty(value = "种子生产经营许可证号")
    private String xkzh;

    @ApiModelProperty(value = "种子生产经营许可证生产经营范围")
    private String scjyzl;

    @ApiModelProperty(value = "种子生产经营许可证起始时间")
    private Date zzyxqqsrq;

    @ApiModelProperty(value = "种子生产经营许可证结束时间")
    private Date zzyxqjzrq;
}
