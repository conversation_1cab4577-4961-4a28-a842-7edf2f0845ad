package com.hainancrc.module.creditcxbs.api.quarantineinformationview.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
* 产地码码查看按日统计表
*/
@Data
public class QuarantineInformationViewCreateDTO {

    /**
    * id
    */
    @ApiModelProperty(value = "产地码id")
    @NotNull(message = "id不能为空")
    private Long id;

    /**
    * 查看次数
    */
    @ApiModelProperty(value = "查看次数")
    private Long viewCount;

    /**
    * 查看日期
    */
    @ApiModelProperty(value = "查看日期")
    private Date viewDate;

}

