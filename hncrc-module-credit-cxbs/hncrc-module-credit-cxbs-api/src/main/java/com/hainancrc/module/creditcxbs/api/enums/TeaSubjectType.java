package com.hainancrc.module.creditcxbs.api.enums;

import lombok.Getter;

@Getter
public enum TeaSubjectType {
    Enterprise("企业"),
    Cooperative("合作社"),
    IndvBusiness("个体工商户"),
    Farmer("农户");
    ;

    private final String description;

    TeaSubjectType(String description) {
        this.description = description;
    }

    public static String getEnumByValue(String value) {
        for (TeaSubjectType item : TeaSubjectType.values()) {
            if (item.getDescription().equals(value)) {
                return item.name();
            }
        }
        return null;
    }
}
