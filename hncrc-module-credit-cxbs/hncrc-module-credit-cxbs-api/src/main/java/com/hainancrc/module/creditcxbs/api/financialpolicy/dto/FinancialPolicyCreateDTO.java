package com.hainancrc.module.creditcxbs.api.financialpolicy.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.FinancialPolicyPolicyStatus;

/**
* 金融政策信息
*/
@Data
public class FinancialPolicyCreateDTO {
    
    /**
    * 主键id
    */
    @ApiModelProperty(value = "主键id")
    private Long id;
    
    /**
    * 政策名称(列表,新增,编辑)
    */
    @ApiModelProperty(value = "政策名称(列表,新增,编辑)")
    @Size(max = 100, message = "政策名称(列表,新增,编辑)长度不能大于 100")
    @NotBlank(message = "政策名称不能为空")
    private String policyName;
    
    /**
    * 政策内容描述(新增,编辑)
    */
    @ApiModelProperty(value = "政策内容描述(新增,编辑)")
//    @Size(max = 1000, message = "政策内容描述(新增,编辑)长度不能大于 1000")
    @NotBlank(message = "政策内容描述不能为空")
    private String policyContent;

    /**
     * 文件
     */
    @ApiModelProperty(value = "上传文件")
    private List<FinancialPolicyFileDTO> files;

    /**
     * 政策附件url(新增,编辑)
     */
    @ApiModelProperty(value = "政策附件url(新增,编辑)")
//    @Size(max = 200, message = "政策附件url(新增,编辑)长度不能大于 200")
    private String policyFileUrl;

    
    /**
    * 政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表)
    */
    @ApiModelProperty(value = "政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表)")
    private FinancialPolicyPolicyStatus policyStatus;
    
}

