package com.hainancrc.module.creditcxbs.api.seedlingcategory.vo;

import com.hainancrc.module.creditcxbs.api.enums.SeedlingCategorySeedlingStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
* 苗木信息
*/
@Data
public class SeedlingCategoryDistinctRespVO {
    

    /**
    * 苗木种类  categoryList  用集合类型
    */
    @ApiModelProperty(value = "苗木种类")
    private List<String> categoryList;

    /**
    * 苗木品种(列表,新增,编辑)
    */
    @ApiModelProperty(value = "苗木品种")
    private List<String> varietyList;


}

