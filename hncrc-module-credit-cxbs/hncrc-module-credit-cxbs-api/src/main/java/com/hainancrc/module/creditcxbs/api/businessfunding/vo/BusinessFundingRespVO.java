package com.hainancrc.module.creditcxbs.api.businessfunding.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 经营主体融资信息
*/
@Data
public class BusinessFundingRespVO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 金融产品融资信息id
    */
    @ApiModelProperty(value = "金融产品融资信息id")
    private Long financialProductFundingId;
    
    /**
    * 申请主体
    */
    @ApiModelProperty(value = "申请主体")
    @Size(max = 100, message = "申请主体长度不能大于 100")
    private String applySubject;

    /**
     * 主体类型(列表,编辑)
     */
    @ApiModelProperty(value = "主体类型(列表,编辑)")
    private String subjectType;
    
    /**
    * 申请时间
    */
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date applyTime;
    
    /**
    * 申请产品
    */
    @ApiModelProperty(value = "申请产品")
    @Size(max = 100, message = "申请产品长度不能大于 100")
    private String applyProduct;
    
    /**
    * 申请融资额(万元)
    */
    @ApiModelProperty(value = "申请融资额(万元)")
    private BigDecimal applyAmount;
    
    /**
    * 发放融资额(万元)
    */
    @ApiModelProperty(value = "发放融资额(万元)")
    private BigDecimal disburseAmount;
}

