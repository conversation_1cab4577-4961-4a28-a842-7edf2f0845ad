package com.hainancrc.module.creditcxbs.api.seedlingsubject.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("苗木数据总览 Response VO")
@Data
public class SeedlingOverviewVO {

    @ApiModelProperty(value = "苗木种类数量")
    private Long categoryCount;

    @ApiModelProperty(value = "苗木品种数量") 
    private Long varietyCount;

    @ApiModelProperty(value = "入驻主体数量")
    private Long subjectCount;

    @ApiModelProperty(value = "一户一码下载次数")
    private Long oneCodeDownloadCount;

    @ApiModelProperty(value = "一户一码查看次数")
    private Long oneCodeViewCount;

    @ApiModelProperty(value = "一苗一码下载次数")
    private Long oneSeedCodeDownloadCount;

    @ApiModelProperty(value = "一苗一码查看次数")
    private Long oneSeedCodeViewCount;
} 