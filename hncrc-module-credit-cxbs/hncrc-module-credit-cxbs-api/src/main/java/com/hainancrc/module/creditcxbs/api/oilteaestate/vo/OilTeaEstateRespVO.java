package com.hainancrc.module.creditcxbs.api.oilteaestate.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaEstateEstateStatus;

/**
* 油茶茶园信息
*/
@Data
public class OilTeaEstateRespVO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 茶园名称(列表,新增,编辑)
    */
    @ApiModelProperty(value = "茶园名称(列表,新增,编辑)")
    @Size(max = 100, message = "茶园名称(列表,新增,编辑)长度不能大于 100")
    private String estateName;
    
    /**
    * 种植品种(列表,新增,编辑)
    */
    @ApiModelProperty(value = "种植品种(列表,新增,编辑)")
    @Size(max = 100, message = "种植品种(列表,新增,编辑)长度不能大于 100")
    private String plantType;
    
    /**
    * 油茶园面积(亩)(列表,新增,编辑)
    */
    @ApiModelProperty(value = "油茶园面积(亩)(列表,新增,编辑)")
    private BigDecimal estateArea;
    
    /**
    * 联系电话(列表,新增,编辑)
    */
    @ApiModelProperty(value = "联系电话(列表,新增,编辑)")
    @Size(max = 15, message = "联系电话(列表,新增,编辑)长度不能大于 15")
    private String contactPhone;
    
    /**
    * 油茶园地址(列表,新增,编辑)
    */
    @ApiModelProperty(value = "油茶园地址(列表,新增,编辑)")
    @Size(max = 100, message = "油茶园地址(列表,新增,编辑)长度不能大于 100")
    private String estateAddress;
    
    /**
    * 油茶茶园简介
    */
    @ApiModelProperty(value = "油茶茶园简介")
    @Size(max = 500, message = "油茶茶园简介长度不能大于 500")
    private String estateIntroduction;

    /**
    * 状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)
    */
    @ApiModelProperty(value = "状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)")
    private OilTeaEstateEstateStatus estateStatus;
    
    /**
    * 茶园图片(列表,新增,编辑)
    */
    @ApiModelProperty(value = "茶园图片(列表,新增,编辑)")
    private String estatePictureListJson;

    /**
    * 更新时间(列表)
    */
    @ApiModelProperty(value = "更新时间(列表)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;

    
    
}

