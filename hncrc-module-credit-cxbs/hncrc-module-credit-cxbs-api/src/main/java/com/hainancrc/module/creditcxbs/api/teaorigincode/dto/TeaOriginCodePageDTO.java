package com.hainancrc.module.creditcxbs.api.teaorigincode.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 产地码
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeaOriginCodePageDTO  extends PageParam {

    @ApiModelProperty(value = "产地名称")
    private String teaOriginName;
}

