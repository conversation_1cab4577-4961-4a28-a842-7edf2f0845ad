package com.hainancrc.module.creditcxbs.api.teaorigincodehistory.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 产地码申报历史记录
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeaOriginCodeHistoryPageDTO  extends PageParam {
    
    
}

