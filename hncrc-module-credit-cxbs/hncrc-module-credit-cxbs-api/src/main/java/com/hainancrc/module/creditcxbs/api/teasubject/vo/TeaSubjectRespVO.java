package com.hainancrc.module.creditcxbs.api.teasubject.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;

/**
* 茶叶经营主体信息
*/
@Data
public class TeaSubjectRespVO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 商户名称(主体名称)
    */
    @ApiModelProperty(value = "商户名称(主体名称)")
    private String subjectName;
    
    /**
    * 主题类型
    */
    @ApiModelProperty(value = "主体类型")
    private String subjectType;
    
    /**
    * 统一信用代码
    */
    @ApiModelProperty(value = "统一信用代码")
    private String uniscid;
    
    /**
    * 法定代表人
    */
    @ApiModelProperty(value = "法定代表人")
    private String legalName;
    
    /**
    * 地址
    */
    @ApiModelProperty(value = "地址")
    private String subjectAddress;
    
    /**
    * 更新时间
    */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 所属乡镇
     */
    @ApiModelProperty(value = "所属乡镇")
    private String belongTownship;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String telephone;

    /**
     * 种植面积
     */
    @ApiModelProperty(value = "种植面积")
    private BigDecimal plantingArea;

    /**
     * 茶叶品种
     */
    @ApiModelProperty(value = "茶叶品种")
    private String teaType;

    /**
     * 茶青产量(吨)
     */
    @ApiModelProperty(value = "茶青产量(吨)")
    private BigDecimal teaYield;

    /**
     * 茶叶产地
     */
    @ApiModelProperty(value = "茶叶产地")
    private String teaOrigin;

    /**
     * 主体简介
     */
    @ApiModelProperty(value = "主体简介")
    private String introduction;

    /**
     * 企业头像
     */
    @ApiModelProperty(value = "企业头像")
    private String avatar;
    
    /**
     * 购买链接
     */
    @ApiModelProperty(value = "购买链接")
    private String purchaseLink;
    
    /**
     * 购买二维码
     */
    @ApiModelProperty(value = "购买二维码")
    private String purchaseQrcode;
    
}

