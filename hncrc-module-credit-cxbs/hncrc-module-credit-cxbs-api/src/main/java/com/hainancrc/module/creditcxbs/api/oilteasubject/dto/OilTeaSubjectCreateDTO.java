package com.hainancrc.module.creditcxbs.api.oilteasubject.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 油茶经营主体信息
*/
@Data
public class OilTeaSubjectCreateDTO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 统一信用代码(列表,编辑)
    */
    @ApiModelProperty(value = "统一信用代码(列表,编辑)")
    @Size(max = 30, message = "统一信用代码(列表,编辑)长度不能大于 30")
    private String uniscid;
    
    /**
    * 商户名称(列表,编辑)
    */
    @ApiModelProperty(value = "商户名称(列表,编辑)")
    @Size(max = 100, message = "商户名称(列表,编辑)长度不能大于 100")
    private String subjectName;
    
    /**
    * 主体类型(列表,编辑)
    */
    @ApiModelProperty(value = "主体类型(列表,编辑)")
    @Size(max = 100, message = "主体类型(列表,编辑)长度不能大于 100")
    private String subjectType;
    
    /**
    * 法定代表人(列表,编辑)
    */
    @ApiModelProperty(value = "法定代表人(列表,编辑)")
    @Size(max = 100, message = "法定代表人(列表,编辑)长度不能大于 100")
    private String legal;
    
    /**
    * 所属乡镇(列表,编辑)
    */
    @ApiModelProperty(value = "所属乡镇(列表,编辑)")
    @Size(max = 100, message = "所属乡镇(列表,编辑)长度不能大于 100")
    private String belongTownship;
    
    /**
    * 联系电话(列表,编辑)
    */
    @ApiModelProperty(value = "联系电话(列表,编辑)")
    @Size(max = 15, message = "联系电话(列表,编辑)长度不能大于 15")
    private String contactPhone;
    
    /**
    * 经营地址(列表,编辑)
    */
    @ApiModelProperty(value = "经营地址(列表,编辑)")
    @Size(max = 100, message = "经营地址(列表,编辑)长度不能大于 100")
    private String opAddress;
    
    /**
    * 种植类型
    */
    @ApiModelProperty(value = "种植类型")
    @Size(max = 100, message = "种植类型长度不能大于 100")
    private String plantType;
    
    /**
    * 种植模式
    */
    @ApiModelProperty(value = "种植模式")
    @Size(max = 100, message = "种植模式长度不能大于 100")
    private String plantMode;
    
    /**
    * 种植总面积(亩)
    */
    @ApiModelProperty(value = "种植总面积(亩)")
    private BigDecimal plantTotalArea;
    
    /**
    * 种植株数（株）
    */
    @ApiModelProperty(value = "种植株数（株）")
    private Long plantCount;
    
    /**
    * 种植苗木是否良种树苗
    */
    @ApiModelProperty(value = "种植苗木是否良种树苗")
    private Integer isEliteSeed;
    
    /**
    * 是否领取补贴
    */
    @ApiModelProperty(value = "是否领取补贴")
    private Integer hasReceivedSubsidy;
    
    /**
    * 领取政策补贴
    */
    @ApiModelProperty(value = "领取政策补贴")
    private BigDecimal receivedSubsidy;
    
    /**
    * 补贴时间
    */
    @ApiModelProperty(value = "补贴时间")
    private String subsidyTime;
    
    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    @Size(max = 50, message = "创建人长度不能大于 50")
    private String creator;
    
    /**
    * 更新人
    */
    @ApiModelProperty(value = "更新人")
    @Size(max = 50, message = "更新人长度不能大于 50")
    private String updater;
    
    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**
    * 更新时间(列表)
    */
    @ApiModelProperty(value = "更新时间(列表)")
    private Date updateTime;
    
    /**
    * 逻辑删除标志位
    */
    @ApiModelProperty(value = "逻辑删除标志位")
    private Boolean deleted;
    
    
}

