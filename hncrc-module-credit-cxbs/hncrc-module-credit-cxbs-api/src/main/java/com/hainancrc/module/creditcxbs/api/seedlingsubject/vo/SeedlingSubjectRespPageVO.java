package com.hainancrc.module.creditcxbs.api.seedlingsubject.vo;

import com.hainancrc.module.creditcxbs.api.enums.SeedlingSubjectSubjectType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 苗木经营主体信息
 */
@Data
public class SeedlingSubjectRespPageVO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 商户名称(列表)
     */
    @ApiModelProperty(value = "商户名称(列表)")
    private String subjectName;

    /**
     * 主体类型(列表)(ENUMS: Cooperative-合作社, Grower-种植户, Enterprise-企业, Others-其它)
     */
    @ApiModelProperty(value = "主体类型(列表)(ENUMS: Cooperative-合作社, Grower-种植户, Enterprise-企业, Others-其它)")
    private SeedlingSubjectSubjectType subjectType;

    /**
     * 统一信用代码(列表)
     */
    @ApiModelProperty(value = "统一信用代码(列表)")
    private String uniscid;

    /**
     * 法定代表人(列表)
     */
    @ApiModelProperty(value = "法定代表人(列表)")
    private String legalName;

    /**
     * 地址(列表)
     */
    @ApiModelProperty(value = "地址(列表)")
    private String subjectAddress;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 经营主体照片
     */
    @ApiModelProperty(value = "经营主体照片")
    private String subjectPictureListJson;

    /**
     * 经营主体简介
     */
    @ApiModelProperty(value = "经营主体简介")
    @Size(max = 1000, message = "经营主体简介 长度不能大于 1000")
    private String subjectIntroduction;


    /**
     * 信用等级(列表)
     */
    @ApiModelProperty(value = "信用等级(列表)")
    @Size(max = 255, message = "信用等级(列表)长度不能大于 255")
    private String creditLevel;

    /**
     * 评价得分(列表)
     */
    @ApiModelProperty(value = "评价得分(列表)")
    @Size(max = 255, message = "评价得分(列表)长度不能大于 255")
    private String evaluationScore;

    /**
     * 评价日期(列表)
     */
    @ApiModelProperty(value = "评价日期(列表)")
    private Date evaluationDate;

    @ApiModelProperty(value = "销售渠道")
    private String saleChannel;

    @ApiModelProperty(value = "二维码链接")
    private String qrCodeUrl;

}

