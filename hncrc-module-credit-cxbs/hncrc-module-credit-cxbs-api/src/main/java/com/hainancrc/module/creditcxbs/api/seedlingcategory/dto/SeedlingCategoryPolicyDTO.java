package com.hainancrc.module.creditcxbs.api.seedlingcategory.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

import com.hainancrc.module.creditcxbs.api.financialpolicy.dto.FinancialPolicyFileDTO;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;


@Data
public class SeedlingCategoryPolicyDTO {

    @ApiModelProperty(value = "政策名称")
//    @NotBlank(message = "政策名称不能为空")
    private String policyName;

    @ApiModelProperty(value = "政策内容")
//    @NotBlank(message = "政策内容不能为空")
    private String policyDetail;

    @ApiModelProperty(value = "政策附件")
    private List<FinancialPolicyFileDTO> policyAttachments;

}
