package com.hainancrc.module.creditcxbs.api.seedlingsubject.vo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("苗木主体信息VO")
public class SeedlingSubjectVO {

    @ApiModelProperty("主体名称")
    private String subjectName;

    @ApiModelProperty("主体类型")
    private String subjectType;

    @ApiModelProperty("苗木类型")
    private String seedlingType;
    @ApiModelProperty("苗木品种")
    private String seedlingVariety;
    
    @ApiModelProperty("价格范围")
    private String priceRange;
    
    @ApiModelProperty("联系电话")
    private String contactPhone;
    
    @ApiModelProperty("经营地址")
    private String businessAddress;
    
    @ApiModelProperty("商户简介")
    private String introduction;
    
    @ApiModelProperty("主图URL")
    private String mainImageUrl;
} 