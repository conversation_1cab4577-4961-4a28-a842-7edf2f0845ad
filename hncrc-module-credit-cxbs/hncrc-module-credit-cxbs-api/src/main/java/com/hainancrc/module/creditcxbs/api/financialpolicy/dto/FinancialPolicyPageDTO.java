package com.hainancrc.module.creditcxbs.api.financialpolicy.dto;

import com.hainancrc.module.creditcxbs.api.enums.FinancialPolicyPolicyStatus;
import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 金融政策信息
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FinancialPolicyPageDTO  extends PageParam {
    
    @ApiModelProperty(value = "政策名称")
    private String policyName;

    @ApiModelProperty(value = "政策状态")
    private FinancialPolicyPolicyStatus policyStatus;
    
}

