package com.hainancrc.module.creditcxbs.api.oilteaestate.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaEstateEstateStatus;

/**
 * 油茶茶园信息
 */
@Data
public class OilTeaEstateCreateDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    // !!REVIEW
    // TODO 可能的问题: 主键字段`id`不需要再添加在 DTO 中，因为`id`是自动生成的，不需要用户输入。
    // 修改建议：删除`id`字段

    /**
     * 茶园名称(列表,新增,编辑)
     */
    @ApiModelProperty(value = "茶园名称(列表,新增,编辑)")
    @Size(max = 100, message = "茶园名称(列表,新增,编辑)长度不能大于 100")
    private String estateName;

    /**
     * 种植品种(列表,新增,编辑)
     */
    @ApiModelProperty(value = "种植品种(列表,新增,编辑)")
    @Size(max = 100, message = "种植品种(列表,新增,编辑)长度不能大于 100")
    private String plantType;

    /**
     * 油茶园面积(亩)(列表,新增,编辑)
     */
    @ApiModelProperty(value = "油茶园面积(亩)(列表,新增,编辑)")
    private BigDecimal estateArea;

    /**
     * 联系电话(列表,新增,编辑)
     */
    @ApiModelProperty(value = "联系电话(列表,新增,编辑)")
    @Size(max = 15, message = "联系电话(列表,新增,编辑)长度不能大于 15")
    // !!REVIEW
    // TODO 可能的问题: 联系电话字段使用了`@Size`注解，但没有使用`@Pattern`注解来确保电话号码的格式有效，这可能导致无效电话号码被接受。
    // 修改建议：添加`@Pattern`注解以验证联系电话格式，例如，只允许数字和特定字符。
    private String contactPhone;

    /**
     * 油茶园地址(列表,新增,编辑)
     */
    @ApiModelProperty(value = "油茶园地址(列表,新增,编辑)")
    @Size(max = 100, message = "油茶园地址(列表,新增,编辑)长度不能大于 100")
    private String estateAddress;

    /**
     * 油茶茶园简介
     */
    @ApiModelProperty(value = "油茶茶园简介")
    @Size(max = 500, message = "油茶茶园简介长度不能大于 500")
    private String estateIntroduction;

    /**
     * 油茶茶园照片
     */
    @ApiModelProperty(value = "油茶茶园照片")
    @Size(max = 150, message = "油茶茶园照片长度不能大于 150")
    private String estateImg;

    /**
     * 状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)
     */
    @ApiModelProperty(value = "状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)")
    private OilTeaEstateEstateStatus estateStatus;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * 茶园图片(列表,新增,编辑)
     */
    @ApiModelProperty(value = "茶园图片(列表,新增,编辑)")
    private String estatePictureListJson;

}
