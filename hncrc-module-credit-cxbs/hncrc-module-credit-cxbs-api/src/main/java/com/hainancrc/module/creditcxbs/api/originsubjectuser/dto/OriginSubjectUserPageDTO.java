package com.hainancrc.module.creditcxbs.api.originsubjectuser.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 产地主体对应用户表
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OriginSubjectUserPageDTO  extends PageParam {
    
    
}

