package com.hainancrc.module.creditcxbs.api.oilteasubsidypublicnote.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.creditcxbs.api.enums.OilTeaSubsidyPublicNoteFileStatus;

/**
* 油茶补贴公示信息
*/
@Data
public class OilTeaSubsidyPublicNoteRespVO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 补贴公式文件名称
    */
    @ApiModelProperty(value = "补贴公式文件名称")
    @Size(max = 100, message = "补贴公式文件名称长度不能大于 100")
    private String publicFileName;

    
    /**
    * 文件状态(ENUMS:ONLINE-公示中,OFFLINE-已下线)(列表,新增,编辑)
    */
    @ApiModelProperty(value = "文件状态(ENUMS:ONLINE-公示中,OFFLINE-已下线)(列表,新增,编辑)")
    private OilTeaSubsidyPublicNoteFileStatus fileStatus;

    
    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    
}

