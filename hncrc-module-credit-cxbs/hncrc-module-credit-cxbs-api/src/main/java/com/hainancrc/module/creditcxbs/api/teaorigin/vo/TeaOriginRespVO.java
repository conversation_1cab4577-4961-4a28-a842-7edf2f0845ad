package com.hainancrc.module.creditcxbs.api.teaorigin.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 茶叶产地信息
*/
@Data
public class TeaOriginRespVO {
    
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
    * 产地名称
    */
    @ApiModelProperty(value = "产地名称")
    @Size(max = 100, message = "产地名称长度不能大于 100")
    private String originName;
    
    /**
    * 茶叶类型
    */
    @ApiModelProperty(value = "茶叶类型")
    @Size(max = 100, message = "茶叶类型长度不能大于 100")
    private String teaType;
    
    /**
    * 茶园名称
    */
    @ApiModelProperty(value = "茶园名称")
    @Size(max = 100, message = "茶园名称长度不能大于 100")
    private String teaEstate;
    
    /**
    * 茶园面积(亩)
    */
    @ApiModelProperty(value = "茶园面积(亩)")
    private BigDecimal estateArea;

    @ApiModelProperty(value = "茶青产量(吨)")
    private BigDecimal teaYouthYield;
    
    /**
    * 联系电话
    */
    @ApiModelProperty(value = "联系电话")
    @Size(max = 11, message = "联系电话长度不能大于 11")
    private String contactPhone;
    
    /**
    * 产地地址
    */
    @ApiModelProperty(value = "产地地址")
    @Size(max = 100, message = "产地地址长度不能大于 100")
    private String originAddress;
    
    /**
    * 产地简介
    */
    @ApiModelProperty(value = "产地简介")
    @Size(max = 500, message = "产地简介长度不能大于 500")
    private String originIntroduction;
    
    /**
    * 产地图片
    */
    @ApiModelProperty(value = "产地图片")
    @Size(max = 150, message = "产地图片长度不能大于 150")
    private String originPic;
    

    
    
}

