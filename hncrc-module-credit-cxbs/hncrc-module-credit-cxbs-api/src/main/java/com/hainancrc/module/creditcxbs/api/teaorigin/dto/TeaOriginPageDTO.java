package com.hainancrc.module.creditcxbs.api.teaorigin.dto;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 茶叶产地信息
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeaOriginPageDTO  extends PageParam {

    @ApiModelProperty(value = "产地id")
    private Long originId;

    @ApiModelProperty(value = "产地名称")
    private String originName;
}

