package com.hainancrc.module.creditcxbs.api.teaorigincode.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import com.hainancrc.framework.common.pojo.PageParam;

/**
* 产地码
*/
@Data
public class TeaOriginCodeRespVO {
    
    /**
    * id
    */
    @ApiModelProperty(value = "id")
    private Long id;
    
    /**
    * 产地ID(列表:关联tea_origin表id,新增:必填)
    */
    @ApiModelProperty(value = "产地ID(列表:关联tea_origin表id,新增:必填)")
    private Long teaOriginId;

    /**
    * 产地主体ID(新增:必填)
    */
    @ApiModelProperty(value = "产地主体ID(新增:必填)")
    private Long teaSubjectId;
    
    /**
    * 码内容(新增:必填)
    */
    @ApiModelProperty(value = "码内容(新增:必填)")
    @Size(max = 255, message = "码内容(新增:必填)长度不能大于 255")
    private String code;
    
    /**
    * 码编号(新增)
    */
    @ApiModelProperty(value = "码编号(新增)")
    @Size(max = 600, message = "码编号(新增)长度不能大于 600")
    private String codeNum;
    
    /**
    * 查看次数(列表,新增)
    */
    @ApiModelProperty(value = "查看次数(列表,新增)")
    private Integer viewCount;
    
    /**
    * 用码累计数(列表,新增)
    */
    @ApiModelProperty(value = "用码累计数(列表,新增)")
    private Integer useCount;
    
    /**
    * 累计茶青总数（斤）(列表,新增)
    */
    @ApiModelProperty(value = "累计茶青总数（斤）(列表,新增)")
    private Integer totalTeaWeight;
    
    /**
    * 承诺书文件key(新增)
    */
    @ApiModelProperty(value = "承诺书文件key(新增)")
    @Size(max = 300, message = "承诺书文件key(新增)长度不能大于 300")
    private String promiseFileKey;
    
    /**
    * 承诺书文件url(新增)
    */
    @ApiModelProperty(value = "承诺书文件url(新增)")
    @Size(max = 500, message = "承诺书文件url(新增)长度不能大于 500")
    private String promiseFileUrl;
    
    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    @Size(max = 50, message = "创建人长度不能大于 50")
    private String creator;
    
    /**
    * 修改人
    */
    @ApiModelProperty(value = "修改人")
    @Size(max = 50, message = "修改人长度不能大于 50")
    private String updater;
    
    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**
    * 修改时间（列表）
    */
    @ApiModelProperty(value = "修改时间（列表）")
    private Date updateTime;
    
    /**
    * 逻辑删除
    */
    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleted;
    
    /**
    * 租户编号
    */
    @ApiModelProperty(value = "租户编号")
    private Long tenantId;
    
    
}

