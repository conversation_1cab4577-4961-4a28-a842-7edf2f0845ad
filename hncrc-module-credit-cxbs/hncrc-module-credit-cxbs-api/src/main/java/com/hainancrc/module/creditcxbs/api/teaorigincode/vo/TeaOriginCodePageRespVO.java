package com.hainancrc.module.creditcxbs.api.teaorigincode.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
* 产地码
*/
@Data
public class TeaOriginCodePageRespVO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "产地码ID")
    private Long teaOriginCodeId;

    @ApiModelProperty(value = "产地ID")
    private Long teaOriginId;

    @ApiModelProperty(value = "统一社会信用代码")
    private String uniscid;

    @ApiModelProperty(value = "产地主体名称")
    private String teaSubjectName;

    @ApiModelProperty(value = "产地名称")
    private String teaOriginName;

    @ApiModelProperty(value = "茶园名称")
    private String teaEstate;

    @ApiModelProperty(value = "茶叶品种")
    private String teaType;

    @ApiModelProperty(value = "码内容")
    @Size(max = 255, message = "码内容长度不能大于 255")
    private String code;

    @ApiModelProperty(value = "码编号(新增)")
    @Size(max = 600, message = "码编号长度不能大于 600")
    private String codeNum;

    @ApiModelProperty(value = "查看次数")
    private Integer viewCount;

    @ApiModelProperty(value = "用码累计数")
    private Integer useCount;

    @ApiModelProperty(value = "累计茶青总数（斤）")
    private BigDecimal totalTeaWeight;

    @ApiModelProperty(value = "承诺书文件url")
    @Size(max = 500, message = "承诺书文件url长度不能大于 500")
    private String promiseFileUrl;

    @ApiModelProperty(value = "数据更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "产地码有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;

    @ApiModelProperty(value = "状态")
    private String codeStatus;

}

