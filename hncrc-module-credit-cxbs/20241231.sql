CREATE TABLE `cxbs_seedling_subject` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subject_name` varchar(100) DEFAULT NULL COMMENT '商户名称(列表)',
  `subject_type` varchar(20) DEFAULT NULL COMMENT '主体类型(列表)(ENUMS: Cooperative-合作社, IndvBusiness-个体工商户, Enterprise-企业, Farmer-农户)',
  `uniscid` varchar(30) DEFAULT NULL COMMENT '统一信用代码(列表)',
  `legal_name` varchar(20) DEFAULT NULL COMMENT '法定代表人(列表)',
  `subject_address` varchar(100) DEFAULT NULL COMMENT '地址(列表)',
  `contact_phone` varchar(50) DEFAULT NULL COMMENT '联系电话(列表)',
  
  `has_standard_archive` TINYINT(1) DEFAULT 0 COMMENT '是否按规定建立经营档案',
  `has_archive_complete` TINYINT(1) DEFAULT 0 COMMENT '档案内容是否完整及时',
  `has_seed_quarantine` TINYINT(1) DEFAULT 0 COMMENT '种苗是否已检疫',
  `has_seed_inspection` TINYINT(1) DEFAULT 0 COMMENT '种苗是否已检验',
  `has_package_standard` TINYINT(1) DEFAULT 0 COMMENT '包装是否有标签或使用说明',
  `has_package_complete` TINYINT(1) DEFAULT 0 COMMENT '包装标签或使用说明是否规范完整',
  `has_beyond_scope` TINYINT(1) DEFAULT 0 COMMENT '是否超范围营业（种子生产经营许可）',
  `subject_picture_list_json` varchar(500) DEFAULT NULL COMMENT '经营主体照片',
  `subject_introduction` TEXT DEFAULT NULL COMMENT '经营主体简介',

	`credit_level` VARCHAR(255) NULL DEFAULT NULL COMMENT '信用等级(列表)',
	`evaluation_score` VARCHAR(255) NULL DEFAULT NULL COMMENT '评价得分(列表)',
	`evaluation_date` DATETIME NULL DEFAULT NULL COMMENT '评价日期(列表)',
	`view_count` INT(11) NULL DEFAULT NULL COMMENT '信用查看次数(列表)',
  `one_code` varchar(100) DEFAULT NULL COMMENT '一户一码(列表)',
  `one_seed_code` varchar(100) DEFAULT NULL COMMENT '一苗一码(列表)',
  `extra_info` TEXT NULL DEFAULT NULL COMMENT '附加信息JSON',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='苗木经营主体信息';


CREATE TABLE `cxbs_seedling_subject_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subject_id` bigint DEFAULT NULL COMMENT '经营主体id',
  `category_id` bigint DEFAULT NULL COMMENT '苗木类id',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='苗木类';


CREATE TABLE `cxbs_seedling_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `seedling_category` varchar(50) DEFAULT NULL COMMENT '苗木种类(列表,新增,编辑)',
  `seedling_variety` varchar(50) DEFAULT NULL COMMENT '苗木品种(列表,新增,编辑)',
  `piece_range` varchar(100) DEFAULT NULL COMMENT '价格范围(列表,新增,编辑)',
  `seedling_specs` varchar(500) DEFAULT NULL COMMENT '规格情况(列表,新增,编辑)',
  `seedling_advantages` varchar(500) DEFAULT NULL COMMENT '特点优势(列表,新增,编辑)',
  `seedling_status` varchar(10) DEFAULT NULL COMMENT '苗木状态(列表)(ENUMS:ONLINE-使用中,OFFLINE-已下架)',
  `related_policy` int DEFAULT NULL COMMENT '相关政策(条)(列表,新增,编辑)',
  `seedling_picture_json` LONGTEXT DEFAULT NULL COMMENT '苗木图片列表',
  `related_policy_list_json` LONGTEXT DEFAULT NULL COMMENT '相关政策列表',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='苗木信息';


CREATE TABLE `cxbs_oil_tea_subject` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `uniscid` varchar(30) DEFAULT NULL COMMENT '统一信用代码(列表,编辑)',
  `subject_name` varchar(100) DEFAULT NULL COMMENT '商户名称(列表,编辑)',
  `subject_type` varchar(100) DEFAULT NULL COMMENT '主体类型(列表,编辑)',
  `legal` varchar(100) DEFAULT NULL COMMENT '法定代表人(列表,编辑)',
  `belong_township` varchar(100) DEFAULT NULL COMMENT '所属乡镇(列表,编辑)',
  `contact_phone` varchar(15) DEFAULT NULL COMMENT '联系电话(列表,编辑)',
  `op_address` varchar(100) DEFAULT NULL COMMENT '经营地址(列表,编辑)',
  `plant_type` varchar(100) DEFAULT NULL COMMENT '种植类型',
  `plant_mode` varchar(100) DEFAULT NULL COMMENT '种植模式',
  `plant_total_area` decimal(10,2) DEFAULT '0' COMMENT '种植总面积(亩)',
  `plant_count` bigint DEFAULT '0' COMMENT '种植株数（株）',
  `is_elite_seed` tinyint(1) DEFAULT '0' COMMENT '种植苗木是否良种树苗',
  `has_received_subsidy` tinyint(1) DEFAULT '0' COMMENT '是否领取补贴',
  `received_subsidy` decimal(10,2) DEFAULT '0' COMMENT '领取政策补贴',
  `subsidy_time` date DEFAULT NULL COMMENT '补贴时间',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间(列表)',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='油茶经营主体信息';



CREATE TABLE `cxbs_oil_tea_estate` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `estate_name` varchar(100) DEFAULT NULL COMMENT '茶园名称(列表,新增,编辑)',
  `plant_type` varchar(100) DEFAULT NULL COMMENT '种植品种(列表,新增,编辑)',
  `estate_area` decimal(10,2) DEFAULT '0' COMMENT '油茶园面积(亩)(列表,新增,编辑)',
  `contact_phone` varchar(15) DEFAULT NULL COMMENT '联系电话(列表,新增,编辑)',
  `estate_address` varchar(100) DEFAULT NULL COMMENT '油茶园地址(列表,新增,编辑)',
  `estate_introduction` varchar(500) DEFAULT NULL COMMENT '油茶茶园简介',
  `estate_status` varchar(10) DEFAULT NULL COMMENT '状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)',
  `estate_picture_list_json` LONGTEXT DEFAULT NULL COMMENT '茶园图片(列表,新增,编辑)',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间(列表)',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='油茶茶园信息';


CREATE TABLE `cxbs_oil_tea_policy` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `policy_name` varchar(100) DEFAULT NULL COMMENT '政策名称(列表,新增,编辑)',
  `policy_content` text COMMENT '政策内容描述(新增,编辑)',
  `policy_status` varchar(10) DEFAULT NULL COMMENT '政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)',
  `policy_file_list_json` LONGTEXT DEFAULT NULL COMMENT '政策附件(新增,编辑)',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='油茶政策信息';


CREATE TABLE `cxbs_oil_tea_subsidy_public_note` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `public_file_name` varchar(100) DEFAULT NULL COMMENT '补贴公式文件名称',
  `public_file_url` varchar(200) DEFAULT NULL COMMENT '补贴公示文件url',
  `public_file_key` varchar(200) DEFAULT NULL COMMENT '补贴公示文件key',
  `file_status` varchar(10) DEFAULT NULL COMMENT '文件状态(ENUMS:ONLINE-公示中,OFFLINE-已下线)(列表,新增,编辑)',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='油茶补贴公示信息';


CREATE TABLE `cxbs_oil_tea_subsidy_subject` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `note_id` bigint DEFAULT NULL COMMENT '补贴公示信息id',
  `township` varchar(100) DEFAULT NULL COMMENT '乡镇',
  `planter_name` varchar(100) DEFAULT NULL COMMENT '种植户名称',
  `plant_area` decimal(10,2) DEFAULT '0' COMMENT '种植面积(亩)',
  `plant_count` bigint DEFAULT '0' COMMENT '种植株数(株)',
  `survival_rate` decimal(10,2) DEFAULT '0' COMMENT '存活率',
  `check_status` varchar(10) DEFAULT NULL COMMENT '检查情况',
  `subsidy_amount` decimal(10,2) DEFAULT '0' COMMENT '补贴金额(元)',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='油茶补贴主体名单';


CREATE TABLE `cxbs_tea_subject` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subject_name` varchar(100) DEFAULT NULL COMMENT '商户名称(主体名称)',
  `subject_type` varchar(20) DEFAULT NULL COMMENT '主题类型',
  `uniscid` varchar(30) DEFAULT NULL COMMENT '统一信用代码',
  `legal_name` varchar(20) DEFAULT NULL COMMENT '法定代表人',
  `subject_address` varchar(100) DEFAULT NULL COMMENT '地址',
  `belong_township` varchar(100) DEFAULT NULL COMMENT '所属乡镇',
  `telephone` varchar(15) DEFAULT NULL COMMENT '联系电话',
  `planting_area` decimal(10,2) DEFAULT '0' COMMENT '种植面积',
  `tea_type` varchar(150) DEFAULT NULL COMMENT '茶叶品种',
  `tea_yield` decimal(10,2) DEFAULT '0' COMMENT '茶青产量(吨)',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='茶叶经营主体信息';

CREATE TABLE `cxbs_tea_origin` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `origin_name` varchar(100) DEFAULT NULL COMMENT '产地名称',
  `tea_type` varchar(100) DEFAULT NULL COMMENT '茶叶类型',
  `tea_estate` varchar(100) DEFAULT NULL COMMENT '茶园名称',
  `estate_area` decimal(10,2) DEFAULT '0' COMMENT '茶园面积(亩)',
  `tea_youth_yield` decimal(10,0) DEFAULT '0' COMMENT '茶青产量(吨)',
  `contact_phone` varchar(11) DEFAULT NULL COMMENT '联系电话',
  `origin_address` varchar(100) DEFAULT NULL COMMENT '产地地址',
  `origin_introduction` varchar(500) COMMENT '产地简介',
  `origin_pic` varchar(150) DEFAULT NULL COMMENT '产地图片',
  `origin_status` varchar(10) DEFAULT NULL COMMENT '产地状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表,新增,编辑)',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='茶叶产地信息';


CREATE TABLE `cxbs_tea_origin_code` (
	`id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
	`tea_origin_id` BIGINT(20) NULL DEFAULT NULL COMMENT '产地ID(列表:关联tea_origin表id,新增:必填)',
	`tea_subject_id` BIGINT(20) NULL DEFAULT NULL COMMENT '产地主体ID',
  `code` VARCHAR(255) NULL DEFAULT NULL COMMENT '码内容(新增:必填)',
	`code_num` VARCHAR(600) NULL DEFAULT NULL COMMENT '码编号(新增)',
	`view_count` INT(11) NULL DEFAULT NULL COMMENT '查看次数(列表,新增)',
  `use_count` INT(11) NULL DEFAULT NULL COMMENT '用码累计数(列表,新增)',
  `total_tea_weight` INT(11) NULL DEFAULT NULL COMMENT '累计茶青总数（斤）(列表,新增)',
  `promise_file_key` varchar(300) DEFAULT NULL COMMENT '承诺书文件key(新增)',
  `promise_file_url` varchar(500) DEFAULT NULL COMMENT '承诺书文件url(新增)',
    `expire_time` datetime DEFAULT NULL COMMENT '产地码有效期',
    `code_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产地码状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)',
    `creator` VARCHAR(50) NULL DEFAULT NULL COMMENT '创建人',
	`updater` VARCHAR(50) NULL DEFAULT NULL COMMENT '修改人',
	`create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间（列表）',
	`deleted` BIT(1) NULL DEFAULT b'0' COMMENT '逻辑删除 ',
	`tenant_id` BIGINT(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
	PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='产地码';

CREATE TABLE `cxbs_tea_origin_code_record` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `origin_code_id` bigint DEFAULT NULL COMMENT '产地码ID',
    `origin_id` bigint DEFAULT NULL COMMENT '产地ID',
    `view_count` bigint DEFAULT NULL COMMENT '查看次数',
    `use_count` bigint DEFAULT NULL COMMENT '用码数（次）',
    `total_tea_weight` decimal(20,2) DEFAULT NULL COMMENT '当月茶青总数（斤）',
    `promise_file_key` varchar(300)  DEFAULT NULL COMMENT '承诺书文件key',
    `promise_file_url` varchar(300)  DEFAULT NULL COMMENT '承诺书文件url',
    `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
    `updater` varchar(50) DEFAULT NULL COMMENT '修改人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除 ',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='产地码申码记录';


CREATE TABLE `cxbs_tea_origin_code_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tea_origin_code_id` bigint DEFAULT NULL COMMENT '产地码id',
  `product_name` varchar(100) DEFAULT NULL COMMENT '贴标产品名称',
  `product_specs` varchar(100) DEFAULT NULL COMMENT '产品规格（净含量）',
  `code_count` int DEFAULT '0' COMMENT '产地码数量',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='产地码申报历史记录';

CREATE TABLE `cxbs_financial_policy` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `policy_name` varchar(100) DEFAULT NULL COMMENT '政策名称(列表,新增,编辑)',
  `policy_content` text COMMENT '政策内容描述(新增,编辑)',
  `policy_file_key` varchar(100) DEFAULT NULL COMMENT '政策附件key(新增,编辑)',
  `policy_file_url` longtext COMMENT '政策附件url(新增,编辑)',
  `policy_status` varchar(10) DEFAULT NULL COMMENT '政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表)',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='金融政策信息';

CREATE TABLE `cxbs_financial_product_funding` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `file_name` varchar(100) DEFAULT NULL COMMENT '文件名称(列表,新增,编辑)',
  `file_content` text COMMENT '文件内容描述(新增,编辑)',
  `file_status` varchar(10) DEFAULT NULL COMMENT '文件状态(ENUMS:ONLINE-公示中,OFFLINE-已下线)(列表)',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='金融产品融资信息表';

CREATE TABLE `cxbs_business_funding` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `financial_product_funding_id` bigint DEFAULT NULL COMMENT '金融产品融资信息id',
  `apply_subject` varchar(100) DEFAULT NULL COMMENT '申请主体',
  `apply_time` datetime DEFAULT NULL COMMENT '申请时间',
  `apply_product` varchar(100) DEFAULT NULL COMMENT '申请产品',
  `apply_amount` decimal(10,2) DEFAULT '0' COMMENT '申请融资额(万元)',
  `disburse_amount` decimal(10,2) DEFAULT '0' COMMENT '发放融资额(万元)',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='经营主体融资信息';

CREATE TABLE `cxbs_origin_subject_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tea_subject_id` BIGINT(20) NULL DEFAULT NULL COMMENT '产地主体id',
  `user_id` varchar(50) DEFAULT NULL COMMENT '用户id',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) DEFAULT b'0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='产地主体对应用户表';


- /creditwc/app-api/flightWelfare/**
- /creditwc/app-api/flightNews/**
- /creditwc/app-api/flightWatch/**
- /creditwc/app-api/video/**
- /creditwc/app-api/flightIndustry/**
- /creditwc/app-api/flightIndustry/**
- /creditwc/app-api/chickenNews/**
- /creditwc/app-api/chickenHistory/**
- /creditwc/app-api/chickenCookbook/**
- /creditwc/app-api/chickenMall/**
- /creditwc/app-api/streetMall/**
- /creditwc/app-api/chickenfeatures/**
- /creditwc/app-api/chickenfeatures/**
- /creditwc/app-api/welfare/**
- /creditwc/app-api/news/**
- /creditwc/app-api/history/**
- /creditwc/app-api/redAndBlack/**
- /creditwc/app-api/navigation/**
- /creditwc/app-api/chickenMap/list
- /creditwc/app-api/chickenMap/get
- /creditwc/app-api/company/get
- /creditwc/app-api/clockTopic/list

- /creditwc/app-api/flightWelfare/**
- /creditwc/app-api/flightNews/**
- /creditwc/app-api/flightWatch/**
- /creditwc/app-api/video/**
- /creditwc/app-api/flightIndustry/**
- /creditwc/app-api/flightIndustry/**
- /creditwc/app-api/chickenNews/**
- /creditwc/app-api/chickenHistory/**
- /creditwc/app-api/chickenCookbook/**
- /creditwc/app-api/chickenMall/**
- /creditwc/app-api/streetMall/**
- /creditwc/app-api/chickenfeatures/**
- /creditwc/app-api/chickenfeatures/**
- /creditwc/app-api/welfare/**
- /creditwc/app-api/news/**
- /creditwc/app-api/history/**
- /creditwc/app-api/redAndBlack/**
- /creditwc/app-api/navigation/**
- /creditwc/app-api/chickenMap/list
- /creditwc/app-api/chickenMap/get
- /creditwc/app-api/company/get
- /creditwc/app-api/clockTopic/list
- /creditwc/app-api/homeUrlConfig/**
- /creditwc/app-api/banner/**
- /creditwc/app-api/appUrlConfig/**
- /creditwc/app-api/zone/**
- /creditwc/app-api/homeCreditUrlConfig/**
- /creditwc/app-api/agreement/**
- /creditwc/app-api/weather/**
