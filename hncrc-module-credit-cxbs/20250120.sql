ALTER TABLE `hncrc_credit_cxbs`.`cxbs_tea_origin_code_view`
    ADD COLUMN `tea_origin_code_id` bigint(20) NULL COMMENT '茶叶产地码ID' AFTER `company_id`;


ALTER TABLE hncrc_credit_cxbs.cxbs_tea_origin_code_history ADD code_record_id BIGINT NULL COMMENT '产地码记录id';
ALTER TABLE hncrc_credit_cxbs.cxbs_tea_origin_code_history CHANGE code_record_id code_record_id BIGINT NULL COMMENT '产地码记录id' AFTER tea_origin_code_id;

ALTER TABLE hncrc_credit_cxbs.cxbs_oil_tea_subject MODIFY COLUMN plant_total_area decimal(20,2) DEFAULT 0.00 NULL COMMENT '种植总面积(亩)';
ALTER TABLE hncrc_credit_cxbs.cxbs_tea_origin_code_record ADD tea_type varchar(200) NULL COMMENT '茶叶品种';
ALTER TABLE hncrc_credit_cxbs.cxbs_tea_origin_code_record CHANGE tea_type tea_type varchar(200) NULL COMMENT '茶叶品种' AFTER origin_id;



CREATE TABLE `cxbs_financial_product_view_statistics` (
                                                          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                          `statistics_type` varchar(15) DEFAULT NULL COMMENT '统计类型',
                                                          `statistics_count` bigint(20) DEFAULT '0' COMMENT '统计量',
                                                          `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
                                                          `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
                                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                          `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志位',
                                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='金融产品浏览量统计信息';

INSERT INTO hncrc_credit_cxbs.`cxbs_financial_product_view_statistics`(id, statistics_type, statistics_count, creator, updater, create_time, update_time, deleted) VALUES(1, 'Views', 0, NULL, NULL, sysdate(), sysdate(), 0);
INSERT INTO hncrc_credit_cxbs.`cxbs_financial_product_view_statistics`(id, statistics_type, statistics_count, creator, updater, create_time, update_time, deleted) VALUES(2, 'ViewSubject', 0, NULL, NULL, sysdate(), sysdate(), 0);
