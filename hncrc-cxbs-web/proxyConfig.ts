export const proxy = {
    '/api/creditbs/admin-api': {
        // '/api': {
        target: 'http://localhost:8180',
        changeOrigin: true,
        secure: false, // 如果是自签名证书，设置为false
        // rewrite: (path) => path.replace(/^\/api/, ''),
        // 在响应头显示真实请求地址
        bypass: (req, res, options) => {
            const proxy = new URL(options.rewrite?.(req.url || '') || '', String(options.target))
                .href;
            // res.setHeader('server-proxy', proxy);
            // 请求的完整链接
            const fullUrl = new URL(req.url || '', proxy).href;
            res.setHeader('server-proxy', proxy + '-' + fullUrl);
        },
        // 添加代理事件监听
        configure: (proxy, options) => {
            proxy.on('proxyReq', (proxyReq, req, res) => {
                console.log(`代理请求: ${req.url} -> ${proxyReq.path}`);
            });
            proxy.on('proxyRes', (proxyRes, req, res) => {
                console.log(`代理响应: ${req.url} -> ${proxyRes.statusCode}`);
            });
        },
    },
    '/api/home': {
        // target: 'https://sit.hainancrc.com',
        // target: 'https://223.6.6.6:53',
        target: 'https://sit-cxbs.hainancrc.com',
        changeOrigin: true,
        secure: false, // 如果是自签名证书，设置为false
        rewrite: (path) => path.replace(/^\/api/, ''),
        // 在响应头显示真实请求地址
        bypass: (req, res, options) => {
            const proxy = new URL(options.rewrite?.(req.url || '') || '', String(options.target))
                .href;
            // res.setHeader('server-proxy', proxy);
            // 请求的完整链接
            const fullUrl = new URL(req.url || '', proxy).href;
            res.setHeader('server-proxy', proxy + '-' + fullUrl);
        },
        // 添加代理事件监听
        configure: (proxy, options) => {
            proxy.on('proxyReq', (proxyReq, req, res) => {
                console.log(`代理请求: ${req.url} -> ${proxyReq.path}`);
            });
            proxy.on('proxyRes', (proxyRes, req, res) => {
                console.log(`代理响应: ${req.url} -> ${proxyRes.statusCode}`);
            });
        },
    },
    '/api/sys': {
        // target: 'https://sit.hainancrc.com',
        // target: 'https://223.6.6.6:53',
        target: 'https://sit-cxbs.hainancrc.com',
        secure: false, // 如果是自签名证书，设置为false
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/api/, ''),
        // 在响应头显示真实请求地址
        bypass: (req, res, options) => {
            const proxy = new URL(options.rewrite?.(req.url || '') || '', String(options.target))
                .href;
            // 请求的完整链接
            const fullUrl = new URL(req.url || '', proxy).href;
            res.setHeader('server-proxy', proxy + '-' + fullUrl);
        },
        // 添加代理事件监听
        configure: (proxy, options) => {
            proxy.on('proxyReq', (proxyReq, req, res) => {
                console.log(`代理请求: ${req.url} -> ${proxyReq.path}`);
            });
            proxy.on('proxyRes', (proxyRes, req, res) => {
                console.log(`代理响应: ${req.url} -> ${proxyRes.statusCode}`);
            });
        },
    },
};
