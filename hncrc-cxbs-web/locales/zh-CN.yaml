buttons:
  hsLoginOut: 退出系统
  hsfullscreen: 全屏
  hsexitfullscreen: 退出全屏
  hsrefreshRoute: 刷新路由
  hslogin: 登录
  hsadd: 新增
  hsmark: 标记/取消
  hssave: 保存
  hssearch: 搜索
  hsexpendAll: 全部展开
  hscollapseAll: 全部折叠
  hssystemSet: 打开项目配置
  hsdelete: 删除
  hsreload: 重新加载
  hscloseCurrentTab: 关闭当前标签页
  hscloseLeftTabs: 关闭左侧标签页
  hscloseRightTabs: 关闭右侧标签页
  hscloseOtherTabs: 关闭其他标签页
  hscloseAllTabs: 关闭全部标签页
  hswholeFullScreen: 全屏
  hswholeExitFullScreen: 退出全屏
  hscontentFullScreen: 内容区全屏
  hscontentExitFullScreen: 内容区退出全屏
menus:
  hshome: 首页
  hslogin: 登录
  hsempty: 无Layout页
  hssysManagement: 系统管理
  hsUser: 用户管理
  hsRole: 角色管理
  hsSystemMenu: 菜单管理
  hsDept: 部门管理
  hseditor: 编辑器
  hsabnormal: 异常页面
  hsfourZeroFour: "404"
  hsfourZeroOne: "403"
  hsFive: "500"
  hscomponents: 组件
  hsdialog: 函数式弹框
  hsmessage: 消息提示
  hsvideo: 视频
  hssegmented: 分段控制器
  hswaterfall: 瀑布流无限滚动
  hsmap: 地图
  hsdraggable: 拖拽
  hssplitPane: 切割面板
  hsText: 文本省略
  hsElButton: 按钮
  hsCheckButton: 可选按钮
  hsbutton: 按钮动效
  hscropping: 图片裁剪
  hsanimatecss: animate.css选择器
  hscountTo: 数字动画
  hsselector: 范围选择器
  hsflowChart: 流程图
  hsseamless: 无缝滚动
  hscontextmenu: 右键菜单
  hstypeit: 打字机
  hsjsoneditor: JSON编辑器
  hsColorPicker: 颜色选择器
  hsDatePicker: 日期选择器
  hsDateTimePicker: 日期时间选择器
  hsTimePicker: 时间选择器
  hsTag: 标签
  hsStatistic: 统计组件
  hsCollapse: 折叠面板
  hsProgress: 进度条
  hsUpload: 文件上传
  hsmenus: 多级菜单
  hsmenu1: 菜单1
  hsmenu1-1: 菜单1-1
  hsmenu1-2: 菜单1-2
  hsmenu1-2-1: 菜单1-2-1
  hsmenu1-2-2: 菜单1-2-2
  hsmenu1-3: 菜单1-3
  hsmenu2: 菜单2
  permission: 权限管理
  permissionPage: 页面权限
  permissionButton: 按钮权限
  hstabs: 标签页操作
  hsguide: 引导页
  hsAble: 功能
  hsMenuTree: 菜单树结构
  hsVideoFrame: 视频帧截取-wasm版
  hsWavesurfer: 音频可视化
  hsOptimize: 防抖、截流、复制、长按指令
  hsWatermark: 水印
  hsPrint: 打印
  hsDownload: 下载
  hsExternalPage: 外部页面
  hsExternalDoc: 文档外链
  hsEmbeddedDoc: 文档内嵌
  externalLink: hncrc-admin
  hsEpDocument: element-plus
  hsTailwindcssDocument: tailwindcss
  hsVueDocument: vue3
  hsViteDocument: vite
  hsPiniaDocument: pinia
  hsRouterDocument: vue-router
  hsAbout: 关于
  hsResult: 结果页面
  hsSuccess: 成功页面
  hsFail: 失败页面
  hsIconSelect: 图标选择器
  hsTimeline: 时间线
  hsLineTree: 树形连接线
  hsList: 列表页
  hsListCard: 卡片列表页
  hsDebounce: 防抖节流
  hsFormDesign: 表单设计器
  hsBarcode: 条形码
  hsQrcode: 二维码
  hsCascader: 区域级联选择器
  hsSwiper: Swiper插件
  hsVirtualList: 虚拟列表
  hsPdf: PDF预览
  hsExcel: 导出Excel
  hsInfiniteScroll: 表格无限滚动
  hsSensitive: 敏感词过滤
  hsPinyin: 汉语拼音
  hsdanmaku: 弹幕
  hsboard: 艺术画板
  hsMenuoverflow: 目录超出显示 Tooltip 文字提示
  hsChildMenuoverflow: 菜单超出显示 Tooltip 文字提示
status:
  hsLoad: 加载中...
login:
  username: 账号
  password: 密码
  verifyCode: 验证码
  remember: 天内免登录
  rememberInfo: 勾选并登录后，规定天数内无需输入用户名和密码会自动登入系统
  sure: 确认密码
  forget: 忘记密码?
  login: 登录
  thirdLogin: 第三方登录
  phoneLogin: 手机登录
  qRCodeLogin: 二维码登录
  register: 注册
  weChatLogin: 微信登录
  alipayLogin: 支付宝登录
  qqLogin: QQ登录
  weiboLogin: 微博登录
  phone: 手机号码
  smsVerifyCode: 短信验证码
  back: 返回
  test: 模拟测试
  tip: 扫码后点击"确认"，即可完成登录
  definite: 确定
  loginSuccess: 登录成功
  registerSuccess: 注册成功
  tickPrivacy: 请勾选隐私政策
  readAccept: 我已仔细阅读并接受
  privacyPolicy: 《隐私政策》
  getVerifyCode: 获取验证码
  info: 秒后重新获取
  usernameReg: 请输入账号
  passwordReg: 请输入密码
  verifyCodeReg: 请输入验证码
  verifyCodeCorrectReg: 请输入正确的验证码
  verifyCodeSixReg: 请输入6位数字验证码
  phoneReg: 请输入手机号码
  phoneCorrectReg: 请输入正确的手机号码格式
  passwordRuleReg: 密码格式应为8-18位数字、字母、符号的任意两种组合
  passwordSureReg: 请输入确认密码
  passwordDifferentReg: 两次密码不一致!
  passwordUpdateReg: 修改密码成功
