import dayjs from 'dayjs';

export const getUnknownId = () => {
    let id;
    try {
        id = window.webfunnyEventGetCustomerInfo().weCustomerKey;
    } catch (e) {
        id = dayjs().format('YYYYMMDDHHmmssSSS');
    }
    return `unknown-${id}`;
};

let reportServiceConfig = {
    projectVersion: '1.0.0',
    env: 'pro',
    platform: 'PC',
    userId: getUnknownId(),
};

export const updateReportUserInfo = (userId?: string) => {
    try {
        console.log('--------updateReportUserInfo--------,userId', userId);
        reportServiceConfig.userId = userId || getUnknownId();
        window.localStorage.setItem('wmUserInfo', JSON.stringify(reportServiceConfig));
    } catch (e) {
        console.error(e);
    }
};

export function initReport(params: { projectVersion: string; env: string; platform: string }) {
    try {
        Object.assign(reportServiceConfig, params);
        console.warn('--------initReport--------,config', reportServiceConfig);
        window.webfunnyEvent = function (pointId) {
            if (params.env !== 'pro') {
                return {
                    trackEvent: (...arg) => {
                        console.log('开发/测试环境，埋点不上报,上报参数为', arg);
                    },
                };
            }
            if (window._webfunnyEvent && typeof window._webfunnyEvent === 'object') {
                if (Object.prototype.hasOwnProperty.call(window._webfunnyEvent, pointId)) {
                    return window._webfunnyEvent[pointId];
                } else {
                    console.warn('【' + pointId + '】点位ID不存在！');
                    return {
                        trackEvent: function () {},
                    };
                }
            } else {
                console.warn('SDK异常，请检查！');
                return {
                    trackEvent: function () {},
                };
            }
        };

        // 监控
        if (window.WebfunnyMonitor) {
            window.WebfunnyMonitor.init({
                projectVersion: reportServiceConfig.projectVersion,
                env: reportServiceConfig.env,
                platform: reportServiceConfig.platform,
            });
        }
    } catch (e) {
        console.log('--------initReport error--------', e);
    }
}
