<template>
    <el-config-provider :locale="currentLocale">
        <router-view />
        <ReDialog />
    </el-config-provider>
</template>

<script lang="ts">
import { ElConfigProvider } from 'element-plus';
import en from 'element-plus/dist/locale/en.mjs';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
import { UAParser } from 'ua-parser-js';
import { defineComponent } from 'vue';

import { ReDialog } from '@/components/ReDialog';

export default defineComponent({
    name: 'app',
    components: {
        [ElConfigProvider.name]: ElConfigProvider,
        ReDialog,
    },
    computed: {
        currentLocale() {
            return this.$storage.locale?.locale === 'zh' ? zhCn : en;
        },
    },
    mounted() {
        const { device } = UAParser();
        console.log('当前是否为移动端', device.type === 'mobile');
        // 移动端 / 小屏检测弹框
        if (document.body.clientWidth < 500 || device.type === 'mobile') {
            this.$notify({
                title: '温馨提示',
                message: '为了获得最佳体验，您可选择使用电脑访问我们的网站',
                type: 'info',
            });
        }
    },
});
</script>
