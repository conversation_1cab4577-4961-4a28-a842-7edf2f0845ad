import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';

export interface loginReqVO {
    imgCode: string; // 图形验证码
    password: string; // 用户密码
    randomCode: string; // 获取图形验证码返回的随机码
    tenantId: string; // 1系统用户，2金融机构用户 3 内部系统租户
    userAccount: string; // 用户账号
}

export type UserResult = {
    userInfo: {
        id: string;
        userAccount: string;
        userName: string;
        tenantId: number;
    };
    token: string;
};

// 获取当前用户菜单权限
export const getCurrMemberMenu = () => {
    return requestService(
        '/sysuser/admin/menu/getCurrMemberMenu',
        {},
        {
            method: 'GET',
        },
    );
};

// 获取用户绑定产地主体id
export const getUserTeaSubjectId = (params) => {
    return requestService(
        `${API_PREFIX}/user/getTeaSubjectId?userId=${params.userId}`,
        {},
        {
            method: 'post',
        },
    );
};

// 绑定用户产地主体
export const bindUserTeaSubject = (params) => {
    return requestService(
        `${API_PREFIX}/user/setTeaSubjectId?userId=${params.userId}&teaSubjectId=${params.teaSubjectId}`,
        {},
        {
            method: 'post',
        },
    );
};
