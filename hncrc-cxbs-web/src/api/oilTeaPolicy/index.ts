import type { BasicFetchResult, BasicPageParams } from '@/api/baseModel';
import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';

interface oilTeaPolicyPageParams {
    policyName?: string; // 政策名称
}

// 获取油茶政策信息分页列表
export const getList = (params: oilTeaPolicyPageParams & BasicPageParams) => {
    return requestService<oilTeaPolicyPageParams & BasicPageParams, BasicFetchResult<any>>(
        `${API_PREFIX}/oilteapolicy/page`,
        params,
        {
            method: 'GET',
        },
    );
};

// 获取油茶政策信息详情
export const getDetailById = (id: string) => {
    return requestService(
        `${API_PREFIX}/oilteapolicy/get`,
        {
            id,
        },
        {
            method: 'GET',
        },
    );
};

// 根据ID删除油茶政策信息
export const deleteById = (id: string) => {
    return requestService(
        `${API_PREFIX}/oilteapolicy/delete`,
        {
            id,
        },
        {
            method: 'DELETE',
        },
    );
};

// 更新油茶政策详情信息
export const updateData = (params: any) => {
    return requestService<any, any>(`${API_PREFIX}/oilteapolicy/update`, params, {
        method: 'PUT',
    });
};

// 新增油茶政策信息
export const addData = (params: any) => {
    return requestService<any, any>(`${API_PREFIX}/oilteapolicy/create`, params, {
        method: 'POST',
    });
};
