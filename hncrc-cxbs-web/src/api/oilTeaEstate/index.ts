import type { BasicFetchResult, BasicPageParams } from '@/api/baseModel';
import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';

interface oilTeaEstatePageParams {
    estateName?: string; // 茶企名称
}

// 获取油茶茶园信息分页列表
export const getList = (params: oilTeaEstatePageParams & BasicPageParams) => {
    return requestService<oilTeaEstatePageParams & BasicPageParams, BasicFetchResult<any>>(
        `${API_PREFIX}/oilteaestate/page`,
        params,
        {
            method: 'GET',
        },
    );
};

// 获取油茶茶园信息详情
export const getDetailById = (id: string) => {
    return requestService(
        `${API_PREFIX}/oilteaestate/get`,
        {
            id,
        },
        {
            method: 'GET',
        },
    );
};

// 根据ID删除油茶茶园信息
export const deleteById = (id: string) => {
    return requestService(
        `${API_PREFIX}/oilteaestate/delete`,
        {
            id,
        },
        {
            method: 'DELETE',
        },
    );
};

// 更新油茶茶园详情信息
export const updateData = (params: any) => {
    return requestService<any, any>(`${API_PREFIX}/oilteaestate/update`, params, {
        method: 'PUT',
    });
};

// 新增油茶茶园信息
export const addData = (params: any) => {
    return requestService<any, any>(`${API_PREFIX}/oilteaestate/create`, params, {
        method: 'POST',
    });
};
