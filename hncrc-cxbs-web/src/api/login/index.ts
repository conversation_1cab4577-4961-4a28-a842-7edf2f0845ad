import requestService from '@/services/requestService';

export interface loginReqVO {
    imgCode: string; // 图形验证码
    password: string; // 用户密码
    randomCode: string; // 获取图形验证码返回的随机码
    tenantId: number; // 1系统用户，2金融机构用户 3 内部系统租户
    userAccount: string; // 用户账号
}

export type UserResult = {
    userInfo: {
        id: string;
        userAccount: string;
        userName: string;
        tenantId: number;
    };
    token: string;
};

export type ZoneResult = {
    id: string;
    userId: string;
    subZoneList?: {
        backgroundImage: string;
        cardImage: string;
        id: string;
        status: string;
        zone: string;
    }[];
    zoneId: string;
    zoneName: string;
};

// randomCode image
// 获取图形码
export const getImageCaptcha = () => {
    return requestService('/sysuser/login/getImageCaptcha', undefined, {
        method: 'GET',
    });
};

// 登录接口
export const pwdLogin = (params: loginReqVO) => {
    return requestService<loginReqVO, UserResult>('/sysuser/login/pwdLogin', params);
};
