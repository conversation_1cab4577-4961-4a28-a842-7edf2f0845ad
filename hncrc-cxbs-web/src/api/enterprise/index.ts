import type { BasicFetchResult, BasicPageParams } from '@/api/baseModel';
import requestService from '@/services/requestService';
interface CompanyPageParams {
    zoneId?: string;
    companyName?: string; // 茶企名称
    creditCode?: string; // 统一社会信用代码
    legalPerson?: string; // 法人
    creditLevel?: string; // 信用等级
}

/**获取茶企列表 */
export const getCompanyList = (params: CompanyPageParams & BasicPageParams) => {
    return requestService<CompanyPageParams & BasicPageParams, BasicFetchResult<any>>(
        '/creditwzs/admin-api/company/page',
        params,
        {
            method: 'GET',
        },
    );
};

/**获取茶企信息 */
export const getCompanyAny = (id: string) => {
    return requestService(
        '/creditwzs/admin-api/company/get',
        {
            id,
        },
        {
            method: 'GET',
        },
    );
};
