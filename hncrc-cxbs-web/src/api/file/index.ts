import type { AxiosRequestConfig } from 'axios';

import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';

/**
 * 上传文件
 * 新
 * @param file
 */
export function uploadFile(file: File, options: AxiosRequestConfig = {}) {
    const formData = new FormData();
    formData.append('file', file);
    return requestService<
        any,
        {
            url: string;
            key: string;
        }
    >('/cxbs/admin-api/upload/uploadTest', formData, {
        method: 'POST',
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        ...options,
    });
}

/**
 * 上传文件
 * 新
 * @param file
 */
export function uploadFileNew(file: File, options: AxiosRequestConfig = {}) {
    const formData = new FormData();
    formData.append('file', file);
    return requestService<
        any,
        {
            url: string;
            key: string;
        }
    >(`${API_PREFIX}/upload/uploadFile`, formData, {
        method: 'POST',
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        ...options,
    });
}

// /demo/upload/upload
