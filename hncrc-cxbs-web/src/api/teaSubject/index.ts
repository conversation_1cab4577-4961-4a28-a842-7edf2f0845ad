// import type { AxiosRequestConfig } from 'axios';

import { API_PREFIX } from '@/api/constant';
import type { PageQueryResult } from '@/components/EntityCrud/type';
import requestService from '@/services/requestService';

import type { TeaSubject, TeaSubjectListItem } from './type';

/** 分页获取茶叶经营主体 */
export const getTeaSubjectList = (params) => {
    return requestService<any, PageQueryResult<TeaSubjectListItem>>(
        `${API_PREFIX}/teaSubject/page`,
        params,
        {
            method: 'GET',
        },
    );
};

/** 创建茶叶经营主体 */
export const createTeaSubject = (params) => {
    return requestService<TeaSubject, number>(`${API_PREFIX}/teaSubject/create`, params, {
        method: 'POST',
    });
};

/** 更新茶叶经营主体 */
export const updateTeaSubject = (params) => {
    return requestService<TeaSubject, boolean>(`${API_PREFIX}/teaSubject/update`, params, {
        method: 'PUT',
    });
};

/** 获取茶叶经营主体详情 */
export const getTeaSubjectDetail = (params) => {
    return requestService<any, TeaSubject>(`${API_PREFIX}/teaSubject/get`, params, {
        method: 'GET',
    });
};

/** 删除茶叶经营主体详情 */
export const deleteTeaSubject = (id: number) => {
    return requestService<any, boolean>(
        `${API_PREFIX}/teaSubject/delete`,
        { id },
        {
            method: 'DELETE',
        },
    );
};

/** 下载导入茶叶经营主体模板 */
export const downloadTeaSubjectTemplate = () => {
    return requestService<any, BlobDataRes>(
        `${API_PREFIX}/teaSubject/template`,
        {},
        {
            method: 'GET',
            responseType: 'blob',
        },
    );
};

/** 导入茶叶经营主体 */
export const importTeaSubject = (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return requestService<any, string>(`${API_PREFIX}/teaSubject/importFile`, formData, {
        method: 'POST',
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
};
