import type { BasicFetchResult, BasicPageParams } from '@/api/baseModel';
import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';

interface financialPolicyPageParams {
    policyName?: string; // 政策名称
}

// 获取金融政策信息分页列表
export const getList = (params: financialPolicyPageParams & BasicPageParams) => {
    return requestService<financialPolicyPageParams & BasicPageParams, BasicFetchResult<any>>(
        `${API_PREFIX}/financialPolicy/page`,
        params,
        {
            method: 'GET',
        },
    );
};

// 获取金融政策信息详情
export const getDetailById = (id: string) => {
    return requestService(
        `${API_PREFIX}/financialPolicy/get`,
        {
            id,
        },
        {
            method: 'GET',
        },
    );
};

// 根据ID删除金融政策信息
export const deleteById = (id: string) => {
    return requestService(
        `${API_PREFIX}/financialPolicy/delete`,
        {
            id,
        },
        {
            method: 'DELETE',
        },
    );
};

// 更新金融政策详情信息
export const updateData = (params: any) => {
    return requestService<any, any>(`${API_PREFIX}/financialPolicy/update`, params, {
        method: 'PUT',
    });
};

// 新增金融政策信息
export const addData = (params: any) => {
    return requestService<any, any>(`${API_PREFIX}/financialPolicy/create`, params, {
        method: 'POST',
    });
};
