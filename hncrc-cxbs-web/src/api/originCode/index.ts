import type { BasicFetchResult, BasicPageParams } from '@/api/baseModel';
import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';

interface TeaOriginCode {
    originName?: string; // 产地名称
}
/** 获取产地码分页*/
export const originGetOriginCodePage = (data: TeaOriginCode & BasicPageParams) => {
    return requestService<TeaOriginCode & BasicPageParams, BasicFetchResult<any>>(
        `${API_PREFIX}/teaorigincode/page`,
        data,
        {
            method: 'GET',
        },
    );
};
/**历史记录 */
interface TeaOriginCodeHistory {
    originCodeId?: string; // 产地码id
    startTime?: string; // 开始时间
    endTime?: string; // 结束时间
}
export const originGetOriginCodeHistory = (data: TeaOriginCodeHistory) => {
    return requestService<TeaOriginCodeHistory, BasicFetchResult<any>>(
        `${API_PREFIX}/teaorigincoderecord/recordPage`,
        data,
        {
            method: 'GET',
        },
    );
};
interface TeaOriginCodeHistoryDetail {
    id?: string; // 产地码id
}
/**历史记录详情 */
export const originGetOriginCodeHistoryDetail = (data: TeaOriginCodeHistoryDetail) => {
    return requestService<TeaOriginCodeHistoryDetail, BasicFetchResult<any>>(
        `${API_PREFIX}/teaorigincoderecord/get`,
        data,
        {
            method: 'GET',
        },
    );
};

/**产地码状态修改  */
export const originUpdateCodeStatus = (params: any) => {
    return requestService<any, any>(`${API_PREFIX}/teaorigincode/update`, params, {
        method: 'PUT',
    });
};

/**下载产地码 */
/* export function originCodeGetInfo(params) {
    return requestService<any, any>(`${API_PREFIX}/origin_view_down/addQuarantineView`, params, {
        method: 'POST',
    });
} */
