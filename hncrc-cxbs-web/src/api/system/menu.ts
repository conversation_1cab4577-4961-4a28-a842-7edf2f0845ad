import requestService from '@/services/requestService';

// 获取菜单表格
export function menuSearchBy(data) {
    return requestService(`/sysuser/admin/menu/searchByPage`, data);
}

export function getAllMenu() {
    return requestService(
        `/sysuser/admin/menu/searchList`,
        {},
        {
            method: 'get',
        },
    );
}
// 新增菜单
export function addMenu(data) {
    return requestService(`/sysuser/admin/menu/addMenu`, data);
}
// 修改菜单
export function updateMenu(data) {
    return requestService(`/sysuser/admin/menu/update`, data);
}
// 删除菜单
export function delMenus(data) {
    return requestService(`/sysuser/admin/menu/delMenus`, data);
}
// 隐藏菜单
export function hiddenMenus(data) {
    return requestService(`/sysuser/admin/menu/hiddenMenus`, data);
}
// 获取门户树
export function searchByTree(data) {
    return requestService(`/portal/searchByTree`, data);
}
// 检查门户名
export function checkPortalName(portalName) {
    return requestService(`/portal/checkName/${portalName}`);
}
// 检查菜单名
export function checkMenuName(data) {
    return requestService(`/sysuser/admin/menu/checkName/${data.menuName}`);
}
// 获取父菜单
export function getParentMenu(params) {
    return requestService(`/sysuser/admin/menu/searchMenuList`, params, {
        method: 'GET',
    });
}
