import requestService from '@/services/requestService';

type result = any;

// 创建用户
export const addUser = (data: any) => {
    return requestService<any, result>('/sysuser/admin/user/addUser', data);
};

// 删除用户
export const delUser = (data: any) => {
    return requestService<any, result>('/sysuser/admin/user/delUser', data);
};

// 更新用户
export const updateUser = (data: any) => {
    return requestService<any, result>('/sysuser/admin/user/updateUser', data);
};

// 更新用户角色
export const updateUserRoles = (data: any) => {
    return requestService<any, result>('/sysuser/admin/user/updateUserRoles', data);
};

// 更新用户状态
export const updateUserState = (data: any) => {
    return requestService<any, result>('/sysuser/admin/user/updateUserStatus', data);
};

// 获取用户表格
export const userSearchBy = (params) => {
    return requestService<any, result>('/sysuser/admin/user/searchByPage', params, {
        method: 'GET',
    });
};

// 查询用户能访问的菜单
export function searchUserMenus(userId) {
    return requestService(`/sysuser/admin/menu/searchUserMenus/${userId}`, {}, { method: 'GET' });
}

// 查看用户的权限
export function updateRoleMenus(data) {
    return requestService(`/sysuser/admin/menu/updateRoleMenus`, data);
}

export function getOperateMenu(ticketSNO?) {
    return requestService('/sysuser/admin/menu/getOperateMenu', { ticketSNO }, { method: 'GET' });
}

// 查看用户的权限
export function searchUserRoles(userId) {
    return requestService(
        `/sysuser/admin/role/searchUserRoles/${userId}`,
        {},
        {
            method: 'GET',
        },
    );
}

// 查询全部角色
export function searchByUser(data?) {
    return requestService(`/sysuser/admin/role/searchAllRoles`, data, {
        method: 'GET',
    });
}

// 查询用户所拥有的角色
export const getRoleDtoListByUserId = (userId: string) => {
    return requestService('/sysuser/role/searchUserRoles/' + userId, void 0, {
        method: 'GET',
    });
};
