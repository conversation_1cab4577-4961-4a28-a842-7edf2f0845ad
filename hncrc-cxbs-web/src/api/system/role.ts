import requestService from '@/services/requestService';

// 获取角色表格
export function roleSearchBy(data) {
    return requestService(`/sysuser/admin/role/searchByPage`, data);
}

// 检查角色名是否可用
export function checkRoleName(roleName) {
    return requestService(`/sysuser/admin/role/checkName/${roleName}`, {}, { method: 'GET' });
}

// 新增角色
export function insertRole(data) {
    return requestService(`/sysuser/admin/role/addRole`, data);
}

// 删除角色
export function delRole(roleIds) {
    return requestService(`/sysuser/admin/role/delRoles?roleIds=${roleIds}`);
}

// 更新角色状态
export function updateRoleState(data) {
    return requestService(`/sysuser/admin/role/updateRoleState`, data);
}

// 获取角色树勾选id
export function searchMyMenus(roleId) {
    return requestService(`/sysuser/admin/menu/searchMyMenus/${roleId}`, {}, { method: 'GET' });
}

// 获取所有菜单
export function searchRoleTree() {
    return requestService(
        `/sysuser/admin/menu/searchList`,
        {},
        {
            method: 'get',
        },
    );
}

// 查看用户的权限
export function updateRoleMenus(data) {
    return requestService(`/sysuser/admin/menu/updateRoleMenus`, data);
}
