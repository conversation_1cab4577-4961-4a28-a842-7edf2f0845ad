import type { BasicFetchResult, BasicPageParams } from '@/api/baseModel';
import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';
interface oilTeaSubsidyPublicNotePageParams {
    publicFileName?: string; // 文件名称
}

// 获取油茶补贴信息分页列表
export const getList = (params: oilTeaSubsidyPublicNotePageParams & BasicPageParams) => {
    return requestService<
        oilTeaSubsidyPublicNotePageParams & BasicPageParams,
        BasicFetchResult<any>
    >(`${API_PREFIX}/oilteasubsidypublicnote/page`, params, {
        method: 'GET',
    });
};

// 获取油茶补贴信息详情
export const getDetailById = (id: string) => {
    return requestService(
        `${API_PREFIX}/oilteasubsidypublicnote/getSubjectList`,
        {
            noteId: id,
        },
        {
            method: 'GET',
        },
    );
};

// 根据ID删除油茶补贴信息
export const deleteById = (id: string) => {
    return requestService(
        `${API_PREFIX}/oilteasubsidypublicnote/delete`,
        {
            id,
        },
        {
            method: 'DELETE',
        },
    );
};

// 更新油茶补贴详情信息
export const updateData = (params: any) => {
    return requestService<any, any>(`${API_PREFIX}/oilteasubsidypublicnote/update`, params, {
        method: 'PUT',
    });
};

/**上传文件 */
export const addData = (params: any) => {
    return requestService<any, any>(`${API_PREFIX}/oilteasubsidypublicnote/create`, params, {
        method: 'POST',
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
};

/**下载模板 */
export const downloadTemplate = () => {
    return requestService<any, BlobDataRes>(
        `${API_PREFIX}/oilteasubsidypublicnote/template`,
        {},
        {
            method: 'GET',
            responseType: 'blob',
        },
    );
};
