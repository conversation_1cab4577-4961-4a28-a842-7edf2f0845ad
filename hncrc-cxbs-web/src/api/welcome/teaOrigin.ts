import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';
import type { OliTeaStatisticsVO, TeaOriginListVO, TeaSubjectVO } from './type';

/** 获取茶园简介列表 */
export function getTeaOriginList(params = {}) {
    return requestService<any, BasePaginationFetchRes<TeaOriginListVO>>(
        `${API_PREFIX}/teaOrigin/list`,
        params,
        {
            method: 'GET',
        },
    );
}
/**获取茶叶入驻经营主体分类统计数据 */
export function getTeaStatistics(params = {}) {
    return requestService<any, OliTeaStatisticsVO>(
        `${API_PREFIX}/teaSubject/overview/analysis`,
        params,
        {
            method: 'GET',
        },
    );
}
/**获取茶叶产地主体统计信息 */
export function getTeaSubject(params = {}) {
    return requestService<any, TeaSubjectVO>(
        `${API_PREFIX}/teaSubject/overview/statistics`,
        params,
        {
            method: 'GET',
        },
    );
}
/**获取茶叶经营主体列表 首页 */

export const getHomeTeaSubjectList = (params = {}) => {
    return requestService<any, BasePaginationFetchRes<TeaOriginListVO>>(
        `${API_PREFIX}/home/<USER>
        params,
        {
            method: 'GET',
        },
    );
};
/**获取茶园信息 */
export const getTeaDetail = (params) => {
    return requestService<any, TeaOriginListVO>(`${API_PREFIX}/home/<USER>
        method: 'GET',
    });
};
