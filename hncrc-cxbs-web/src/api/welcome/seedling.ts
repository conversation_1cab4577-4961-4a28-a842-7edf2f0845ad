import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';
import type {
    ALevelSubjectScanStatisticsVO,
    ALevelSubjectStatisticsVO,
    SeedlingCategoryRespVO,
    SeedlingSubjectMerchantProfileVO,
    SubjectStatisticsVO,
} from './type';

/** 获取特色苗木列表 */
export function getSeedlingList(params = {}) {
    return requestService<any, BasePaginationFetchRes<SeedlingCategoryRespVO>>(
        `${API_PREFIX}/home/<USER>
        params,
        {
            method: 'GET',
        },
    );
}

/** 获取苗木主体数量统计 */
export function getSeedlingEntityStatistics(params = {}) {
    return requestService<any, SubjectStatisticsVO>(
        `${API_PREFIX}/home/<USER>
        params,
        {
            method: 'GET',
        },
    );
}

/** 获取A级经营主体扫码统计数据 */
export function getSeedlingLevalAEntityScanStatistics(params = {}) {
    return requestService<any, ALevelSubjectScanStatisticsVO>(
        `${API_PREFIX}/home/<USER>
        params,
        {
            method: 'GET',
        },
    );
}

/** 获取A级经营主体统计数据 */
export function getSeedlingLevalAEntityStatistics(params = {}) {
    return requestService<any, ALevelSubjectStatisticsVO>(
        `${API_PREFIX}/home/<USER>
        params,
        {
            method: 'GET',
        },
    );
}

/** 获取苗木经营主体列表 */
export function getSeedlingEntityList(params) {
    return requestService<any, BasePaginationFetchRes<SeedlingSubjectMerchantProfileVO>>(
        `${API_PREFIX}/home/<USER>
        params,
        {
            method: 'GET',
        },
    );
}
