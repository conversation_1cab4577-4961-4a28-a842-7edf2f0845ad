import type { InformationStatusEnum } from '@/enums/InformationStatusEnum';
import type { SeedlingEntityTypeEnum } from '@/views/welcome/zoneSeedling/config';

/**
 * 金融政策信息 DO
 *
 * FinancialPolicyDO
 */
export interface FinancialPolicyDO {
    /** 创建时间 */
    createTime?: string;
    creator?: string;
    deleted?: boolean;
    /**
     * 主键id
     */
    id?: number;
    /**
     * 政策内容描述(新增,编辑)
     */
    policyContent?: string;
    /**
     * 政策附件key(新增,编辑)
     */
    policyFileKey?: string;
    /**
     * 政策附件url(新增,编辑)
     */
    policyFileUrl?: string;
    /**
     * 政策附件json(新增,编辑)
     */
    policyFileJson?: string;
    /**
     * 政策名称(列表,新增,编辑)
     */
    policyName?: string;
    /**
     * 政策状态(ENUMS:ONLINE-使用中,OFFLINE-已下线)(列表)ONLINE=使用中,OFFLINE=已下线
     */
    policyStatus?: InformationStatusEnum;
    updater?: string;
    updateTime?: string;
}

/** 金融产品 */
export interface FinancialProductDO {
    /** 主键 */
    id?: number;
    /** 文件名称 */
    fileName?: string;
    /** 文件内容 */
    fileContent?: string;
    /** 文件状态 */
    fileStatus?: InformationStatusEnum;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
}

/**
 * 苗木信息
 *
 * SeedlingCategoryRespVO
 */
export interface SeedlingCategoryRespVO {
    /**
     * 主键
     */
    id?: number;
    /**
     * 价格范围
     */
    pieceRange?: string;
    /**
     * 相关政策(条)
     */
    relatedPolicy?: number;
    /**
     * 相关政策列表
     */
    relatedPolicyListJson?: string;
    /**
     * 特点优势(列表,新增,编辑)
     */
    seedlingAdvantages?: string;
    /**
     * 苗木种类(列表,新增,编辑)
     */
    seedlingCategory?: string;
    /**
     * 苗木图片列表
     */
    seedlingPictureJson?: string;
    /**
     * 规格情况(列表,新增,编辑)
     */
    seedlingSpecs?: string;
    /**
     * 苗木状态(列表)(ENUMS:ONLINE-使用中,OFFLINE-已下架)
     */
    seedlingStatus?: InformationStatusEnum;
    /**
     * 苗木品种(列表,新增,编辑)
     */
    seedlingVariety?: string;
}

/**
 * 苗木主体统计信息
 */
export interface SubjectStatisticsVO {
    /**
     * 入驻合作社数
     */
    cooperativeCount?: number;
    /**
     * 入驻企业数
     */
    enterpriseCount?: number;
    /**
     * 入驻农户数
     */
    farmerCount?: number;
    /**
     * 入驻种植户数
     */
    growerCount?: number;
    /**
     * 已入驻经营主体总数
     */
    totalCount?: number;
}

/**
 * A级经营主体扫码统计VO
 */
export interface ALevelSubjectScanStatisticsVO {
    /**
     * 一户一码下载
     */
    familyCodeDownload?: number;
    /**
     * 一户一码查看
     */
    familyCodeView?: number;
    /**
     * 一苗一码下载
     */
    seedlingCodeDownload?: number;
    /**
     * 一苗一码查看
     */
    seedlingCodeView?: number;
    /**
     * A级经营主体总量
     */
    totalCount?: number;
}

/**
 * A级主体数量统计信息
 */
export interface ALevelSubjectStatisticsVO {
    /**
     * A级合作社数量
     */
    cooperativeCount?: number;
    /**
     * A级企业数量
     */
    enterpriseCount?: number;
    /**
     * A级种植户数量
     */
    planterCount?: number;
    /**
     * A级经营主体总量
     */
    totalCount?: number;
}

/**
 * 苗木经营主体信息
 *
 * SeedlingSubjectMerchantProfileVO
 */
export interface SeedlingSubjectMerchantProfileVO {
    /**
     * 联系电话
     */
    contactPhone?: string;
    /**
     * 信用等级(列表)
     */
    creditLevel?: string;
    /**
     * 评价日期(列表)
     */
    evaluationDate?: string;
    /**
     * 评价得分(列表)
     */
    evaluationScore?: string;
    /**
     * 档案内容是否完整及时
     */
    hasArchiveComplete?: number;
    /**
     * 包装标签或使用说明是否规范完整
     */
    hasPackageComplete?: number;
    /**
     * 包装是否有标签或使用说明
     */
    hasPackageStandard?: number;
    /**
     * 种苗是否已检验
     */
    hasSeedInspection?: number;
    /**
     * 种苗是否已检疫
     */
    hasSeedQuarantine?: number;
    /**
     * 是否按规定建立经营档案
     */
    hasStandardArchive?: number;
    /**
     * 主键
     */
    id?: string;
    /**
     * 法定代表人(列表)
     */
    legalName?: string;
    /**
     * 价格范围
     * 价格范围 piece_range
     */
    priceRange?: string;
    /**
     * 苗木种类(列表,新增,编辑)
     */
    seedlingCategory?: string;
    /**
     * 苗木品种(列表,新增,编辑)
     */
    seedlingVariety?: string;
    /**
     * 地址(列表)
     */
    subjectAddress?: string;
    /**
     * 经营主体简介 subject_introduction
     * 经营主体简介
     */
    subjectIntroduction?: string;
    /**
     * 商户名称(列表)
     */
    subjectName?: string;
    /**
     * 经营主体照片
     */
    subjectPictureListJson?: string;
    /**
     * 主体类型(列表)(ENUMS: Cooperative-合作社, Grower-种植户, Enterprise-企业, Others-其它)
     */
    subjectType?: SeedlingEntityTypeEnum;
    /**
     * 统一信用代码(列表)
     */
    uniscid?: string;
    /** 苗木规格 */
    seedlingSpecs?: string;
    /** 特点优势  */
    seedlingAdvantages?: string;
    /**购买链接 */
    saleChannel?: string;
    /**购买二维码 */
    qrCodeUrl?: string;
}

/** 油茶园简介列表*/
export interface TeaGardenIntroductionListVO {
    /**茶园名称 */
    estateName: string;
    /**种植品种 */
    plantType: string;
    /**油茶园面积 */
    estateArea: string | number;
    /**油茶园地址 */
    estateAddress: string;
    /**联系电话 */
    contactPhone: string;
    /**油茶图片 */
    estatePictureListJson: string;
    /**油茶介绍 */
    estateIntroduction: string;
}
/**获取油茶主体分类统计数据*/
export interface OliTeaStatisticsVO {
    /**企业数量 */
    enterpriseCount: number;
    /**合作社数量 */
    cooperativeCount: number;
    /**种植户数量 */
    planterCount: number;
    /**农户数量 */
    farmerCount: number;
}

/**补贴政策 */
export interface OliTeaPolicyListVO {
    id: string;
    /**政策名称 */
    policyName: string;
    /**政策内容描述 */
    policyContent: string;
}

/**补贴落实公示名单 */
export interface OliTeaSubsidySubjectListVO {
    /**补贴公示信息id */
    noteId: string;
    /**乡镇 */
    township: string;
    /**种植户名称 */
    planterName: string;
    /**种植面积 */
    plantArea: string;
    /**种植株树 */
    plantCount: number;
    /**存活率 */
    survivalRate: number;
    /**检查情况 */
    checkStatus: string;
    /**补贴金额 */
    subsidyAmount: string;
    /**备注 */
    remark: string;
}

/**茶园简介列表 */
export interface TeaOriginListVO {
    id: string;
    /**产地名称 */
    originName: string;
    /**茶叶类型 */
    teaType: string;
    /**茶园名称 */
    teaEstate: string;
    /**茶园面积 */
    estateArea: string;
    /**联系电话 */
    telephone: string;
    /**产地地址 */
    originAddress: string;
    /**产地简介 */
    originIntroduction: string;
    /**经营地址 */
    subjectAddress: string;
    /**主体简介 */
    introduction: string;
    /**产地图片 */
    originPic: string;
    /**购买链接 */
    purchaseLink?: string;
    /**购买二维码 */
    purchaseQrcode?: string;
    /**联系电话 */
    contactPhone: string;
}

export interface TeaSubjectVO {
    /**产地码申请主体数 */
    originCodeApplyCount: number;
    /**产地码申领数 */
    originCodeApplyNum: number;
    /**产地码查看量 */
    originCodeViewCount: number;
}
