import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';
import type { OliTeaStatisticsVO, TeaGardenIntroductionListVO } from './type';

/** 获取油茶茶园简介列表 */
export function getOliTeaList(params = {}) {
    return requestService<any, BasePaginationFetchRes<TeaGardenIntroductionListVO>>(
        `${API_PREFIX}/oilteaestate/list`,
        params,
        {
            method: 'GET',
        },
    );
}

/**数据总览茶园简介列表 */
export function getoverViewsOliTeaList(params = {}) {
    return requestService<any, BasePaginationFetchRes<TeaGardenIntroductionListVO>>(
        `${API_PREFIX}/oilteaestate/statisticsList`,
        params,
        {
            method: 'GET',
        },
    );
}
/**获取油茶主体分类统计数据 */
export function getOliTeaStatistics(params = {}) {
    return requestService<any, OliTeaStatisticsVO>(
        `${API_PREFIX}/oilTea/overview/subject-type-stat`,
        params,
        {
            method: 'GET',
        },
    );
}

/**补贴政策列表 */
/* Array<{ content?: string; createTime: number; id: string; name?: string }> */
export function getOliTeaPolicy(params = {}) {
    return requestService<any, any>(`${API_PREFIX}/oilteapolicy/list`, params, {
        method: 'GET',
    });
}

/**补贴落实公示 */
export function getOliTeaSubsidySubject(params = {}) {
    return requestService<any, any>(
        `${API_PREFIX}/oilteasubsidysubject/getSubsidySubjectList`,
        params,
        {
            method: 'GET',
        },
    );
}
