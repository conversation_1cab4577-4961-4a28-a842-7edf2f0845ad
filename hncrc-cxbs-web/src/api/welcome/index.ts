import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';
import type { FinancialPolicyDO } from './type';

interface AnnouncementPageParams {
    companyName?: string;
    promiseSource?: string;
    industryAttr?: string;
    creditCode?: string;
    zoneId?: string;
}

export interface PolicyList {
    id: string;
    // 创建时间
    createTime: string;
    // 文件
    fileUrl: string;
    // 内容
    policyDescribe: string;
    // 信用政策名称
    policyName: string;
}

export interface CompanyList {
    address: string;
    companyName: string;
    creditCode: string;
    creditLevel: string;
    evaluationDate: string;
    evaluationScore: string;
    id: string;
    legalPerson: string;
    subjectType: string;
    zoneId: string;
    zoneZone: string;
}

export interface ZoneData {
    id: string;
    zone: string;
    cardImage: string;
    backgroundImage: string;
    zoneList: {
        id: string;
        // 专区图片
        cardImage: string;
        // 专区
        zone: string;
        backgroundImage: string;
        status: string;
    }[];
}

export type NoticeType = {
    // 创建时间
    createTime: string;
    // 文件
    fileUrl: unknown;
    id: number;
    // 内容
    policyDescribe: string;
    // 信用政策名称
    policyName: string;
};
//获取信用政策公示详情
export const getNoticeDetails = (id: string) => {
    console.log(id);
    return new Promise<NoticeType>((resolve) => {
        return resolve({
            createTime: '',
            fileUrl: '',
            id: 0,
            policyDescribe: '',
            policyName: '',
        });
    });
};

// ====================== 白沙联调 开始 =====================
/** 获取welcome页面金融政策列表 */
export function getWelcomePagePolicyList(params = {}) {
    return requestService<any, FinancialPolicyDO[]>(
        `${API_PREFIX}/home/<USER>
        params,
        {
            method: 'GET',
        },
    );
}

/** 获取金融政策详情 */
export function getFinancialPolicyDetails(params) {
    return requestService<any, any>(`${API_PREFIX}/financialPolicy/get`, params, {
        method: 'GET',
    });
}

/** 获取金融政策详情 */
export function getOliTeaPolicyDetails(params) {
    return requestService<any, any>(`${API_PREFIX}/oilteapolicy/get`, params, {
        method: 'GET',
    });
}

/** 获取金融产品列表 */
export function getFinancingProductList(params = {}) {
    return requestService<any, any[]>(`${API_PREFIX}/financialproductfunding/list`, params, {
        method: 'GET',
    });
}

/** 获取金融产品详情 */
export function getFinancingProductDetails(params) {
    return requestService<any, FinancialPolicyDO>(
        `${API_PREFIX}/financialproductfunding/get`,
        params,
        {
            method: 'GET',
        },
    );
}
// ====================== 白沙联调 结束 =====================
