import type { BasicFetchResult, BasicPageParams } from '@/api/baseModel';
import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';
/** 下载产地承诺书 */
export const downLoadPromiseFile = () => {
    return requestService<any, BlobDataRes>(
        `${API_PREFIX}/teaorigincoderecord/promiseLetter`,
        {},
        {
            method: 'GET',
            responseType: 'blob',
        },
    );
};

/**申领产地码 */
export const originApplyOriginCode = (data: any) => {
    return requestService<any, any>(`${API_PREFIX}/teaorigincoderecord/create`, data, {
        method: 'POST',
    });
};

interface TeaOriginApply {
    originName?: string; // 产地名称
}

/** 获取申领产地码 */
export const originGetOriginCodePage = (data: TeaOriginApply & BasicPageParams) => {
    return requestService<TeaOriginApply & BasicPageParams, BasicFetchResult<any>>(
        `${API_PREFIX}/teaorigincoderecord/page`,
        data,
        {
            method: 'GET',
        },
    );
};

/** 删除申领产地码 */
export const originDeleteOriginCode = (id: string) => {
    return requestService(
        `${API_PREFIX}/teaorigincoderecord/delete`,
        {
            id,
        },
        {
            method: 'DELETE',
        },
    );
};

/** 再次申领产地码 */
export const originUpdateOriginApply = (data: any) => {
    return requestService<any, any>(`${API_PREFIX}/teaorigincoderecord/apply`, data, {
        method: 'POST',
    });
};

/**历史记录 */
interface TeaOriginApplyHistory {
    startTime?: string; // 开始时间
    endTime?: string; // 结束时间
    originCodeId?: string; // 产地id
}
export const originGetOriginApplyHistory = (data: TeaOriginApplyHistory) => {
    return requestService<TeaOriginApplyHistory, BasicFetchResult<any>>(
        `${API_PREFIX}/teaorigincoderecord/recordPage`,
        data,
        {
            method: 'GET',
        },
    );
};

/**历史记录详情 */
interface TeaOriginApplyHistoryDetail {
    id?: string; // 产地码id
}
export const originGetOriginApplyHistoryDetail = (data: TeaOriginApplyHistoryDetail) => {
    return requestService<TeaOriginApplyHistoryDetail, BasicFetchResult<any>>(
        `${API_PREFIX}/teaorigincoderecord/get`,
        data,
        {
            method: 'GET',
        },
    );
};

/**获得茶叶产地下拉列表 */
export const originGetOriginList = () => {
    return requestService<any, any>(
        `${API_PREFIX}/teaOrigin/originList`,
        {},
        {
            method: 'GET',
        },
    );
};
