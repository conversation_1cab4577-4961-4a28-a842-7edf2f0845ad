import type { BasicFetchResult, BasicPageParams } from '@/api/baseModel';
import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';

export const originDeleteSubject = (id: string) => {
    return requestService('/origin/deleteSubject', { id });
};

export const originCreateSubject = (data: any) => {
    return requestService('/origin/createSubject', data);
};

export const originCreateSubjectApi = (data: any) => {
    return requestService('/origin/createSubject', data);
};

export const originGetSubjectApi = (data: any) => {
    return requestService('/origin/getSubject', data);
};
interface TeaOrigin {
    originName?: string; // 产地名称
}
/** 获取产地列表 */
export const originGetOriginPage = (data: TeaOrigin & BasicPageParams) => {
    return requestService<TeaOrigin & BasicPageParams, BasicFetchResult<any>>(
        `${API_PREFIX}/teaOrigin/page`,
        data,
        {
            method: 'GET',
        },
    );
};

/**新增产地信息 */
export const originCreateOrigin = (data: any) => {
    return requestService<any, any>(`${API_PREFIX}/teaOrigin/create`, data, {
        method: 'POST',
    });
};
/** 获取产地信息 */
export const originGetOriginById = (id: string) => {
    return requestService(
        `${API_PREFIX}/teaOrigin/get`,
        {
            id,
        },
        {
            method: 'GET',
        },
    );
};

/** 根据ID删除产地信息 */
export const originDeleteOriginById = (id: string) => {
    return requestService(
        `${API_PREFIX}/teaOrigin/delete`,
        {
            id,
        },
        {
            method: 'DELETE',
        },
    );
};

/** 更新产地信息 */
export const originUpdateOrigin = (data: any) => {
    return requestService<any, any>(`${API_PREFIX}/teaOrigin/update`, data, {
        method: 'PUT',
    });
};

/**被申领次数查询 */
export const originGetApplyCountPage = (data: any) => {
    return requestService<any, any>(`${API_PREFIX}/teaOrigin/getApplyPage`, data, {
        method: 'GET',
    });
};
