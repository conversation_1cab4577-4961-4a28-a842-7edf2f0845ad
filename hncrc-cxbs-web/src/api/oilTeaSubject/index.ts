// import type { AxiosRequestConfig } from 'axios';

import { API_PREFIX } from '@/api/constant';
import type { PageQueryResult } from '@/components/EntityCrud/type';
import requestService from '@/services/requestService';

import type { OilTeaSubject, OilTeaSubjectRespVO } from './type';

/** 分页获取油茶经营主体 */
export const getOilTeaSubjectList = (params) => {
    return requestService<any, PageQueryResult<OilTeaSubjectRespVO>>(
        `${API_PREFIX}/oilTeaSubject/page`,
        params,
        {
            method: 'GET',
        },
    );
};

/** 创建油茶经营主体 */
export const createOilTeaSubject = (params) => {
    return requestService<OilTeaSubject, number>(`${API_PREFIX}/oilTeaSubject/create`, params, {
        method: 'POST',
    });
};

/** 更新油茶经营主体 */
export const updateOilTeaSubject = (params) => {
    return requestService<OilTeaSubject, boolean>(`${API_PREFIX}/oilTeaSubject/update`, params, {
        method: 'PUT',
    });
};

/** 获取经营主体详情 */
export const getOilTeaSubjectDetail = (params) => {
    return requestService<any, OilTeaSubject>(`${API_PREFIX}/oilTeaSubject/get`, params, {
        method: 'GET',
    });
};

/** 删除经营主体详情 */
export const deleteOilTeaSubject = (params) => {
    return requestService<any, boolean>(`${API_PREFIX}/oilTeaSubject/delete`, params, {
        method: 'DELETE',
    });
};

/** 下载导入油茶经营主体模板 */
export const downloadOilTeaSubjectTemplate = () => {
    return requestService<any, BlobDataRes>(
        `${API_PREFIX}/oilTeaSubject/template`,
        {},
        {
            method: 'GET',
            responseType: 'blob',
        },
    );
};

/** 导入油茶经营主体 */
export const importOilTeaSubject = (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return requestService<any, string>(`${API_PREFIX}/oilTeaSubject/importFile`, formData, {
        method: 'POST',
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
};
