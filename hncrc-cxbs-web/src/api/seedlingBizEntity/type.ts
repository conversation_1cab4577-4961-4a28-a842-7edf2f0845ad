import type { SeedlingEntityTypeEnum } from '@/views/welcome/zoneSeedling/config';

/**
 * 苗木经营主体信息
 *
 * SeedlingSubjectRespVO
 */
export interface SeedlingSubjectRespVO {
    /**
     * 联系电话
     */
    contactPhone?: string;
    /**
     * 信用等级(列表)
     */
    creditLevel?: string;
    /**
     * 评价日期(列表)
     */
    evaluationDate?: string;
    /**
     * 评价得分(列表)
     */
    evaluationScore?: string;
    /**
     * 档案内容是否完整及时
     */
    hasArchiveComplete?: number;
    /**
     * 包装标签或使用说明是否规范完整
     */
    hasPackageComplete?: number;
    /**
     * 包装是否有标签或使用说明
     */
    hasPackageStandard?: number;
    /**
     * 种苗是否已检验
     */
    hasSeedInspection?: number;
    /**
     * 种苗是否已检疫
     */
    hasSeedQuarantine?: number;
    /**
     * 是否按规定建立经营档案
     */
    hasStandardArchive?: number;
    /**
     * 主键
     */
    id?: number;
    /**
     * 法定代表人(列表)
     */
    legalName?: string;
    /**
     * 地址(列表)
     */
    subjectAddress?: string;
    /**
     * 经营主体简介
     */
    subjectIntroduction?: string;
    /**
     * 商户名称(列表)
     */
    subjectName?: string;
    /**
     * 经营主体照片
     */
    subjectPictureListJson?: string;
    /**
     * 主体类型(列表)(ENUMS: Cooperative-合作社, Grower-种植户, Enterprise-企业, Others-其它)
     */
    subjectType?: SeedlingEntityTypeEnum;
    /**
     * 统一信用代码(列表)
     */
    uniscid?: string;
}

/**
 * 经营主体基本信息
 */
export interface SeedlingSubjectDetail {
    /**
     * 联系电话
     */
    contactPhone?: string;
    /**
     * 信用等级(列表)
     */
    creditLevel?: string;
    /**
     * 评价日期(列表)
     */
    evaluationDate?: string;
    /**
     * 评价得分(列表)
     */
    evaluationScore?: string;
    /**
     * 档案内容是否完整及时
     */
    hasArchiveComplete?: number;
    /**
     * 包装标签或使用说明是否规范完整
     */
    hasPackageComplete?: number;
    /**
     * 包装是否有标签或使用说明
     */
    hasPackageStandard?: number;
    /**
     * 种苗是否已检验
     */
    hasSeedInspection?: number;
    /**
     * 种苗是否已检疫
     */
    hasSeedQuarantine?: number;
    /**
     * 是否按规定建立经营档案
     */
    hasStandardArchive?: number;
    /**
     * 是否超范围营业（种子生产经营许可）
     */
    hasBeyondScope?: number;
    /**
     * 主键
     */
    id?: number;
    /**
     * 法定代表人(列表)
     */
    legalName?: string;
    /**
     * 地址(列表)
     */
    subjectAddress?: string;
    /**
     * 经营主体简介
     */
    subjectIntroduction?: string;
    /**
     * 商户名称(列表)
     */
    subjectName?: string;
    /**
     * 经营主体照片
     */
    subjectPictureListJson?: string;
    /**
     * 主体类型(列表)(ENUMS: Cooperative-合作社, Grower-种植户, Enterprise-企业, Others-其它)
     */
    subjectType?: SeedlingEntityTypeEnum;
    /**
     * 统一信用代码(列表)
     */
    uniscid?: string;
    /** 额外数据JSON字符串字段 */
    extraInfo?: string;
    /** 额外数据（） */
    extraData?: SeedlingSubjectExtraInfo;
    /**
     * 一户一码(列表)
     */
    oneCode?: string;
    /**
     * 一苗一码(列表)
     */
    oneSeedCode?: string;
    /**
     * 信用查看次数(列表)
     */
    viewCount?: number;
    /**
     * 行政处罚
     */
    xzcfInfoVoList?: [];
    /**
     * 失信被执行人信息
     */
    sxbzxrInfoVoList?: [];
    /**
     * 列入严重违法信息
     */
    yzwfInfoVoList?: SeedlingSubjectExtraInfoCertificate[];
    /**行业资质 */
    patentInfoVoList?: [];
    /**
     * 许可证起始时间
     */
    zzyxqqsrq?: string;
    /**
     * 许可证结束时间
     */
    zzyxqjzrq?: string;
    /**购买链接 */
    saleChannel?: string;
    /**购买二维码 */
    qrCodeUrl?: string;
}

/** 苗木经营主体详情额外数据 */
export interface SeedlingSubjectExtraInfo {
    /** 种子生产经营许可证 */
    ZZSCJYXKZ?: SeedlingSubjectExtraInfoZZSCJYXKZ;
    /** 经营档案 */
    JYDA?: SeedlingSubjectExtraInfoJYDA;
    /** 营业执照/经营档案 */
    basicInfo?: SeedlingSubjectExtraInfoBasicInfo;
    /** 行业资质 */
    certificate?: SeedlingSubjectExtraInfoCertificate;
    /** 行政处罚 */
    administrativeSanction?: SeedlingSubjectExtraInfoPunish;
    /** 列入严重违法信息 */
    dishonestIndividuals?: SeedlingSubjectExtraInfoIllegal;
    /** 失信被执行人信息 */
    SXBZXR?: SeedlingSubjectExtraInfoExecuted;
}

/** 苗木经营主体详情额外数据-数据项-种子生产经营许可证 */
export interface SeedlingSubjectExtraInfoZZSCJYXKZ {
    /** 许可证编号 */
    xkzh?: string;
    /** 生产经营范国 */
    scjyzl?: string;
    /** 生产经营方式 */
    SCJYFS?: string;
    /** 有效区域 */
    YXQY?: string;
    /** 有效期 */
    zzyxqjzrq?: string;
    /**是否超范围营业 */
    hasBeyondScope?: boolean;
}

/** 苗木经营主体详情额外数据-数据项-经营档案 */
export interface SeedlingSubjectExtraInfoJYDA {
    /** 是否超越范围营业 */
    hasBeyondScope?: boolean;
    /** 是否按照规定建立档案 */
    hasStandardArchive?: boolean;
    /** 档案内容是否完整及时 */
    hasArchiveComplete?: boolean;
}

/** 苗木经营主体详情额外数据-数据项-行业资质 */
export interface SeedlingSubjectExtraInfoCertificate {
    /** 专利标题 */
    patName?: string;
    /** 公开日期 */
    sqrq?: string;
}

/** 苗木经营主体详情额外数据-数据项-经营档案 */
export interface SeedlingSubjectExtraInfoBasicInfo {
    /** 统一社会信用代码 */
    uniscid?: string;
    /** 公司类型编码 */
    ENTTYPE?: string;
    /** 公司类型 */
    subjectType?: string;
    /** 企业状态编码 */
    ENTSTATUS?: string;
    /** 企业状态 */
    entStatus?: string;
    /** 注册资金(万元) */
    regCap?: string;
    /** 成立日期 */
    esDate?: string;
    /** 法人/经营者 */
    legalName?: string;
    /** 登记机关编码 */
    REGORG?: string;
    /** 登记机关 */
    regOrg?: string;
    /** 营业期限开始日期 */
    OPFROM?: string;
    /** 营业期限终止日期 */
    OPTO?: string;
    /** 经营范围 */
    opScope?: string;
}

/** 苗木经营主体-行政处罚信息 */
export interface SeedlingSubjectExtraInfoPunish {
    /** 决定书文号 */
    penAuthName?: string;
    /** 处罚类型 */
    penType?: string;
    /** 处罚结果 */
    penResult?: string;
    /** 处罚日期 */
    penDecissDate?: string;
    /** 处罚事由 */
    illegFact?: string;
    /** 处罚机关 */
    penBasis?: string;
}

/** 苗木经营主体-严重违法信息 */
export interface SeedlingSubjectExtraInfoIllegal {
    /** 列入日期 */
    inDate?: string;
    /** 列入原因 */
    inReason?: string;
    /** 列入决定机关 */
    inOrg?: string;
    /** 列出日期 */
    outDate?: string;
    /** 列出原因 */
    outReason?: string;
    /** 列出决定机关 */
    outOrg?: string;
}

/** 苗木经营主体-失信被执行人信息 */
export interface SeedlingSubjectExtraInfoExecuted {
    /** 被执行人姓名/名称 */
    fsxName?: string;
    /** 统一社会信用代码 */
    uniscid?: string;
    /** 执行法院名称 */
    fsxZxfyName?: string;
    /** 执行依据文号 */
    fsxZxyj?: string;
    /** 立案时间 */
    fsxLasj?: string;
    /** 案号 */
    fsxAh?: string;
    /** 做出执行依据单位 */
    fsxZczxdw?: string;
    /** 被执行人的履行情况 */
    fsxLxqk?: string;
    /** 失信被执行人行为具体情形 */
    fsxSxjtqx?: string;
    /** 发布时间 */
    fsxFbDate?: string;
}

/**
 * 苗木经营主体列表项数据
 */
export interface SeedlingSubjectListItem {
    /**
     * 联系电话
     */
    contactPhone?: string;
    /**
     * 信用等级(列表)
     */
    creditLevel?: string;
    /**
     * 评价日期(列表)
     */
    evaluationDate?: string;
    /**
     * 评价得分(列表)
     */
    evaluationScore?: string;
    /**
     * 档案内容是否完整及时
     */
    hasArchiveComplete?: number;
    /**
     * 包装标签或使用说明是否规范完整
     */
    hasPackageComplete?: number;
    /**
     * 包装是否有标签或使用说明
     */
    hasPackageStandard?: number;
    /**
     * 种苗是否已检验
     */
    hasSeedInspection?: number;
    /**
     * 种苗是否已检疫
     */
    hasSeedQuarantine?: number;
    /**
     * 是否按规定建立经营档案
     */
    hasStandardArchive?: number;
    /**
     * 主键
     */
    id?: string;
    /**
     * 法定代表人(列表)
     */
    legalName?: string;
    /**
     * 地址(列表)
     */
    subjectAddress?: string;
    /**
     * 经营主体简介
     */
    subjectIntroduction?: string;
    /**
     * 商户名称(列表)
     */
    subjectName?: string;
    /**
     * 经营主体照片
     */
    subjectPictureListJson?: string;
    /**
     * 主体类型(列表)(ENUMS: Cooperative-合作社, Grower-种植户, Enterprise-企业, Others-其它)
     */
    subjectType?: SeedlingEntityTypeEnum;
    /**
     * 统一信用代码(列表)
     */
    uniscid?: string;
    /** 额外数据JSON字符串字段 */
    extraInfo?: string;
    /** 额外数据（） */
    extraData?: SeedlingSubjectExtraInfo;
    /**
     * 一户一码(列表)
     */
    oneCode?: string;
    /**
     * 一苗一码(列表)
     */
    oneSeedCode?: string;
    /**
     * 信用查看次数(列表)
     */
    viewCount?: number;
    /** 苗木种类 */
    seedlingVarietyIds?: [];
    /**
     * 销售渠道
     */
    saleChannel?: string;
    /**
     * 销售二维码
     */
    qrCodeUrl?: string;
}

/**
 * 苗木经营主体创建表单项
 * SeedlingSubjectCreateFormFields
 */
export interface SeedlingSubjectCreateFormFields {
    /**
     * 联系电话(列表)
     * 联系电话
     */
    contactPhone?: string;
    /**
     * 档案内容是否完整及时
     */
    hasArchiveComplete?: string;
    /**
     * 是否超范围营业（种子生产经营许可）
     */
    hasBeyondScope?: string;
    /**
     * 包装标签或使用说明是否规范完整
     */
    hasPackageComplete?: string;
    /**
     * 包装是否有标签或使用说明
     */
    hasPackageStandard?: string;
    /**
     * 种苗是否已检验
     */
    hasSeedInspection?: string;
    /**
     * 种苗是否已检疫
     */
    hasSeedQuarantine?: string;
    /**
     * 是否按规定建立经营档案
     */
    hasStandardArchive?: string;
    /**
     * 主键
     */
    id?: string;
    /**
     * 法定代表人(列表)
     * 法定代表人
     */
    legalName?: string;
    /**
     * 苗木类型
     */
    list?: SeedlingCategoryAddDTO[];
    /**
     * 地址(列表)
     * 地址
     */
    subjectAddress?: string;
    /**
     * 经营主体简介
     */
    subjectIntroduction: string;
    /**
     * 商户名称(列表)
     * 商户名称
     */
    subjectName: string;
    /**
     * 经营主体照片
     */
    subjectPictureListJson?: string;
    /**
     * 主体类型(列表)(ENUMS: Cooperative-合作社, Grower-种植户, Enterprise-企业, Others-其它)
     * 主体类型(ENUMS: Cooperative-合作社, Grower-种植户, Enterprise-企业, Others-其它)
     */
    subjectType: SeedlingEntityTypeEnum;
    /**
     * 统一信用代码(列表)
     * 统一信用代码
     */
    uniscid: string;
}

/**
 * 苗木品种信息
 *
 * SeedlingCategoryAddDTO
 */
export interface SeedlingCategoryAddDTO {
    /**
     * 苗木种类(列表,新增,编辑)
     */
    seedlingCategory?: string;
    /**
     * 苗木品种(列表,新增,编辑)
     */
    seedlingVariety?: string;
}
