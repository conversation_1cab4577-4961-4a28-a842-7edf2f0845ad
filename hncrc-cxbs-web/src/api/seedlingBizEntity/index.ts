import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';
import type { SeedlingSubjectDetail, SeedlingSubjectListItem, SeedlingSubjectRespVO } from './type';

/** 根据苗木ID获取苗木经营主体列表 */
export function getSeedlingEntitysBySeedlingId(params) {
    return requestService<any, BasePaginationFetchRes<SeedlingSubjectRespVO>>(
        `${API_PREFIX}/seedlingSubject/getSubjectListByCategoryId`,
        params,
        {
            method: 'GET',
        },
    );
}

/** 根据ID获取经营主体详情 */
export function getSeedlingEntityDetail(params) {
    return requestService<any, SeedlingSubjectDetail>(`${API_PREFIX}/seedlingSubject/get`, params, {
        method: 'GET',
    });
}

/** 分页获取苗木经营主体档案列表 */
export function getSeedlingSubjectList(params) {
    return requestService<any, BasePaginationFetchRes<SeedlingSubjectListItem>>(
        `${API_PREFIX}/seedlingSubject/page`,
        params,
        {
            method: 'GET',
        },
    );
}

/** 编辑苗木经营主体档案 */
export function updateSeedlingEntity(params) {
    return requestService<any, string>(`${API_PREFIX}/seedlingSubject/update`, params, {
        method: 'PUT',
    });
}

/** 获取经营主体信息 */
export function getSeedlingEntity(params) {
    return requestService<any, SeedlingSubjectDetail>(`${API_PREFIX}/seedlingSubject/get`, params, {
        method: 'GET',
    });
}

/** 删除苗木经营主体 */
export function deleteSeedlingEntity(id: string) {
    return requestService<any, boolean>(
        `${API_PREFIX}/seedlingSubject/delete`,
        { id },
        {
            method: 'DELETE',
        },
    );
}

/**
 * 获取一苗一码
 * NOTE: 没有改接口，数据直接在列表里面返回了
 */
export function getSeedlingCode(params) {
    return requestService<any, string>(`${API_PREFIX}/seedlingSubject/getSeedlingCode`, params, {
        method: 'GET',
    });
}

/**
 * 获取一户一码
 * NOTE: 没有改接口，数据直接在列表里面返回了
 */
export function getHouseholdCode(params) {
    return requestService<any, string>(`${API_PREFIX}/seedlingSubject/getHouseholdCode`, params, {
        method: 'GET',
    });
}

/** 新增苗木经营主体 */
export function addSeedlingEntity(params) {
    return requestService<any, string>(`${API_PREFIX}/seedlingSubject/create`, params, {
        method: 'POST',
    });
}

/** 下载量统计 */
export const uploadSubjectDataStatistics = (params) => {
    return requestService<any, string>(
        `${API_PREFIX}/origin_view_down/add_origin_view_down`,
        params,
        {
            method: 'POST',
        },
    );
};

/** 获取苗木列表 */
export function getSeedlingCategoryList() {
    return requestService<any, { id: string; seedlingCategory: string; seedlingVariety: string }[]>(
        `${API_PREFIX}/seedlingCategory/selectList`,
        {},
        {
            method: 'GET',
        },
    );
}

/**获取苗木主体信用评价信息 */
export function getScoreAndLevel(params) {
    return requestService<any, any>(
        `${API_PREFIX}/seedlingSubject/getScoreAndLevel?id=${params.id}&scoreList=${params.scoreList}`,
        {},
        {
            method: 'GET',
        },
    );
}
