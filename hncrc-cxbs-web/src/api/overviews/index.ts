import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';

// 诚信苗木数据看板
export const getStatistics = () => {
    return requestService<any, any>(
        `${API_PREFIX}/seedling/overview/statistics`,
        {
            t: new Date().getTime(),
        },
        {
            method: 'GET',
        },
    );
};

// 苗木企业信用评价指标等级数据分析
export const getCreditStats = () => {
    return requestService<any, any>(
        `${API_PREFIX}/seedling/overview/creditStats`,
        {
            t: new Date().getTime(),
        },
        {
            method: 'GET',
        },
    );
};

// 苗木入驻经营主体分析
export const getSubjectTypeStats = () => {
    return requestService<any, any>(
        `${API_PREFIX}/seedling/overview/subjectTypeStats`,
        {
            t: new Date().getTime(),
        },
        {
            method: 'GET',
        },
    );
};

/**茶叶产地主体统计信息 */
export const getTeaDataBoard = () => {
    return requestService<any, any>(
        `${API_PREFIX}/teaSubject/overview/statistics`,
        {},
        {
            method: 'GET',
        },
    );
};

/**茶叶产地主体申领数据分析 */
interface GetTeaApplyTeaSubjectDataParams {
    startTime: string;
    endTime: string;
}
export const getTeaApplyTeaSubjectData = (params: GetTeaApplyTeaSubjectDataParams) => {
    return requestService<any, any>(
        `${API_PREFIX}/teaSubject/overview/listByTeaSubjectData`,
        params,
        {
            method: 'GET',
        },
    );
};
/**茶叶入驻经营主体分析 */
export const getTeaSubjectDataAnalysis = () => {
    return requestService<any, any>(
        `${API_PREFIX}/teaSubject/overview/analysis`,
        {},
        {
            method: 'GET',
        },
    );
};

/**获取诚信油茶数据看板 */
export const getOilTeaDataBoard = () => {
    return requestService<any, any>(
        `${API_PREFIX}/oilTea/overview/statistics`,
        {},
        {
            method: 'GET',
        },
    );
};

/**获取油茶入驻经营主体分类统计 */
export const getOilTeaSubjectTypeStats = () => {
    return requestService<any, any>(
        `${API_PREFIX}/oilTea/overview/subject-type-stat`,
        {},
        {
            method: 'GET',
        },
    );
};
/**金融产品数据看板 */
export const getFinanceProductDataBoard = () => {
    return requestService<any, any>(
        `${API_PREFIX}/businessfunding/overview/statistics`,
        {},
        {
            method: 'GET',
        },
    );
};

/**金融产品申请主体 */
export const getFinanceSubject = () => {
    return requestService<any, any>(
        `${API_PREFIX}/businessfunding/list`,
        {},
        {
            method: 'GET',
        },
    );
};
