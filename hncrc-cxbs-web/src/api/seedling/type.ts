import type { InformationStatusEnum } from '@/enums/InformationStatusEnum';

/**
 * 苗木种类VO
 */
export interface SeedlingCategoryRespVO {
    /**
     * 主键
     */
    id?: number;
    /**
     * 价格范围
     */
    pieceRange?: string;
    /**
     * 相关政策(条)
     */
    relatedPolicy?: number;
    /**
     * 相关政策列表
     */
    relatedPolicyListJson?: string;
    /**
     * 特点优势(列表,新增,编辑)
     */
    seedlingAdvantages?: string;
    /**
     * 苗木种类(列表,新增,编辑)
     */
    seedlingCategory?: string;
    /**
     * 苗木图片列表
     */
    seedlingPictureJson?: string;
    /**
     * 规格情况(列表,新增,编辑)
     */
    seedlingSpecs?: string;
    /**
     * 苗木状态(列表)(ENUMS:ONLINE-使用中,OFFLINE-已下架)
     */
    seedlingStatus?: InformationStatusEnum;
    /**
     * 苗木品种(列表,新增,编辑)
     */
    seedlingVariety?: string;
    /**
     * 附加信息JSON
     */
    extraInfo?: string;
}
