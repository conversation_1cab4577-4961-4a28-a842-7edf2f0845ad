import type { AxiosRequestConfig } from 'axios';

import type { BasicFetchResult } from '@/api/baseModel';
import type { SeedlingCategoryRespVO as SeedlingCategoryHomeRespVO } from '@/api/welcome/type';
import requestService from '@/services/requestService';

import { API_PREFIX } from '../constant';
import type { SeedlingCategoryRespVO } from './type';

/** 统一处理分页数据的函数 */
function fetchListData<T>(url: string, params: any, options: AxiosRequestConfig) {
    return requestService<any, BasicFetchResult<T>>(url, params, options);
}

export interface Seedling {
    id: string;
    /**苗木种类 */
    seedlingCategory: string;
    /**苗木品种 */
    seedlingVariety: string;
    /**规格情况 */
    seedlingSpecs: string;
    /**特点优势 */
    seedlingAdvantages: number;
    /**苗木状态 */
    seedlingStatus: number;
    /**相关政策 */
    relatedPolicy: string;
    /**苗木图片列表 */
    seedlingPictureJson: string;
    /**相关政策列表 */
    relatedPolicyListJson: string;
    /**其他信息 */
    otherInfo: string;
}
/** 获取苗木信息分页 */
export const seedlingPageApi = (params) => {
    return fetchListData<Seedling>(`${API_PREFIX}/seedlingCategory/page`, params, {
        method: 'GET',
    });
};

/** 添加苗木信息 */
export const seedlingAddApi = (data: any) => {
    return requestService(`${API_PREFIX}/seedlingCategory/create`, data, {
        method: 'POST',
    });
};

/**查看苗木详情 */
export const seedlingDetailApi = (id: string) => {
    return requestService(
        `${API_PREFIX}/seedlingCategory/get/${id}`,
        {},
        {
            method: 'GET',
        },
    );
};

/** 修改苗木信息 */
export const seedlingUpdateApi = (data: any) => {
    return requestService(`${API_PREFIX}/seedlingCategory/update`, data, {
        method: 'PUT',
    });
};

/** 删除苗木信息 */
export const seedlingDeleteApi = (id: string) => {
    return requestService(
        `${API_PREFIX}/seedlingCategory/delete`,
        { id },
        {
            method: 'DELETE',
        },
    );
};

/** 根据主体id获取苗木种类 */
export function getSeedlingByEntityId(params) {
    return requestService<any, SeedlingCategoryRespVO>(
        `${API_PREFIX}/seedlingCategory/getBySubjectId`,
        params,
        {
            method: 'GET',
        },
    );
}
/**获取苗木种类列表 */
export const seedlingCategoryListApi = (params) => {
    return requestService(`${API_PREFIX}/seedlingCategory/getDistinctCategoryAndVariety`, params, {
        method: 'GET',
    });
};

/** 获取特色苗木列表 */
export const getSpecialSeedlingListPage = (params) => {
    return requestService<any, BasePaginationFetchRes<SeedlingCategoryRespVO>>(
        `${API_PREFIX}/seedlingCategory/page`,
        params,
        {
            method: 'GET',
        },
    );
};

/** 获取经营主体下的特色苗木列表 */
export const getSubjectsSeedlingListPage = (params) => {
    return requestService<any, BasePaginationFetchRes<SeedlingCategoryHomeRespVO>>(
        `${API_PREFIX}/seedlingCategory/subjectSeedlingPage`,
        params,
        {
            method: 'GET',
        },
    );
};
