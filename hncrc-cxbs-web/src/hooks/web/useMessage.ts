import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';

export const useMessage = () => {
    const t = '系统提示';
    const ok = '确认';
    const cancel = '取消';
    const delMessage = '是否删除所选中数据？';
    const exportMessage = '是否确认导出数据项？';
    return {
        // 消息提示
        info(content: string) {
            ElMessage.info(content);
        },
        // 错误消息
        error(content: string) {
            ElMessage.error(content);
        },
        // 成功消息
        success(content: string) {
            ElMessage.success(content);
        },
        // 警告消息
        warning(content: string) {
            ElMessage.warning(content);
        },
        // 弹出提示
        alert(content: string) {
            return ElMessageBox.alert(content, t);
        },
        // 错误提示
        alertError(content: string) {
            ElMessageBox.alert(content, t, { type: 'error', showClose: false });
        },
        // 成功提示
        alertSuccess(content: string) {
            ElMessageBox.alert(content, t, { type: 'success', showClose: false });
        },
        // 警告提示
        alertWarning(content: string) {
            ElMessageBox.alert(content, t, { type: 'warning' });
        },
        // 通知提示
        notify(content: string) {
            ElNotification.info(content);
        },
        // 错误通知
        notifyError(content: string) {
            ElNotification.error(content);
        },
        // 成功通知
        notifySuccess(content: string) {
            ElNotification.success(content);
        },
        // 警告通知
        notifyWarning(content: string) {
            ElNotification.warning(content);
        },
        // 确认窗体
        confirm(content: string, tip?: string) {
            return ElMessageBox.confirm(content, tip ? tip : t, {
                confirmButtonText: ok,
                cancelButtonText: cancel,
                type: 'warning',
            });
        },
        // 删除窗体
        delConfirm(content?: string, tip?: string) {
            return ElMessageBox.confirm(content ? content : delMessage, tip ? tip : t, {
                confirmButtonText: ok,
                cancelButtonText: cancel,
                type: 'warning',
            });
        },
        // 导出窗体
        exportConfirm(content?: string, tip?: string) {
            return ElMessageBox.confirm(content ? content : exportMessage, tip ? tip : t, {
                confirmButtonText: ok,
                cancelButtonText: cancel,
                type: 'warning',
            });
        },
        // 提交内容
        prompt(content: string, tip: string) {
            return ElMessageBox.prompt(content, tip, {
                confirmButtonText: ok,
                cancelButtonText: cancel,
                type: 'warning',
            });
        },
    };
};
