/** 批量导入hook options配置 */
export type BatchImportOptions<R> = {
    /** 可接受的文件类型 */
    acceptFileType?: Array<string>;
    /** 文件大小限制单位为M */
    maxFileSize?: number;
    /** 上传文件的接口 */
    uploadApi: (file: File) => Promise<R>;
    /** 上传成功的提示语 */
    successMessage?: string;
    /** 上传失败的提示语 */
    errorMessage?: string;
    /** 上传成功的回调 */
    onSuccess?: (res: R) => void;
    /** 上传失败的回调 */
    onError?: (err: Error) => void;
};
