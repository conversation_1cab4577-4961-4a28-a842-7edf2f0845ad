import { isFunction } from '@pureadmin/utils';
import {
    genFileId,
    type UploadInstance,
    type UploadProps,
    type UploadRawFile,
    type UploadRequestOptions,
} from 'element-plus';
import { ref } from 'vue';

import { message } from '@/utils/message';

import type { BatchImportOptions } from './type';

/**
 * 批量导入hook
 */
export const useBatchImportHook = <R = boolean>(options: BatchImportOptions<R>) => {
    /** 支持上传的文件类型 */
    const {
        acceptFileType: opAcceptFileType,
        maxFileSize,
        uploadApi,
        successMessage,
        errorMessage,
        onSuccess,
        onError,
    } = options;

    /** 可接受的文件类型 */
    const acceptFileTypes = ref(opAcceptFileType || ['.xlsx']);

    /** 批量导入成功提示语 */
    const successMsg = successMessage || '批量导入成功';

    /** 批量导入失败提示语 */
    const errorMsg = errorMessage || '批量导入失败';

    /** 上传文件大小限制默认10MB */
    const fileSizeLimit = maxFileSize || 10;

    /** 上传组件ref */
    const uploadRef = ref<UploadInstance>();

    /** 正在上传 */
    const uploading = ref(false);

    /**
     * 文件选择超出上限时触发
     * @description 自动替换掉上一个文件
     * @param files
     */
    const handleExceed: UploadProps['onExceed'] = (files) => {
        uploadRef.value!.clearFiles();
        const file = files[0] as UploadRawFile;
        file.uid = genFileId();
        uploadRef.value!.handleStart(file);
    };

    /** 校验文件 */
    const handleValidateFile = (file: UploadRawFile): Promise<UploadRawFile> => {
        return new Promise((resolve, reject) => {
            if (!acceptFileTypes.value.includes(file.name.slice(file.name.lastIndexOf('.')))) {
                reject(
                    /*   message(`只能上传${acceptFileTypes.value.join('、')}格式的文件`, {
                        type: 'error',
                    }), */
                    message(`只支持上传.xlsx及.xls格式文件`, {
                        type: 'error',
                    }),
                );
            }

            const fileSize = file.size / 1024 / 1024;
            if (fileSize > fileSizeLimit) {
                reject(message(`文件大小不能超过${fileSizeLimit}Mb`, { type: 'error' }));
            } else {
                resolve(file);
            }
            uploading.value = false;
        });
    };

    /** 批量导入上传文件request */
    const batchImportUploadRequest = async (options: UploadRequestOptions) => {
        if (uploading.value) return;
        uploading.value = true;

        return handleValidateFile(options.file).then(async (validatedfile) => {
            try {
                const res = await uploadApi(validatedfile);
                if (res) {
                    message(successMsg, { type: 'success' });
                    onSuccess && isFunction(onSuccess) && onSuccess(res);
                } else {
                    /* message(errorMsg, { type: 'error' });
                    onError && isFunction(onError) && onError(new Error(errorMsg)); */
                    console.error(errorMsg);
                }
            } catch (error) {
                // 有限提示接口返回的错误信息
                /* const { msg } = error as { msg: string };
                message(msg || errorMsg, { type: 'error' });
                onError && isFunction(onError) && onError(new Error(errorMsg)); */
                console.error(error);
            } finally {
                uploading.value = false;
                // 清除选择的文件
                uploadRef.value!.clearFiles();
            }
        });
    };
    return {
        uploadRef,
        uploading,
        acceptFileTypes,
        handleExceed,
        batchImportUploadRequest,
    };
};
