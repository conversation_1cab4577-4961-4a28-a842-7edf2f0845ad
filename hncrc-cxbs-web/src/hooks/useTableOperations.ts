import { ElMessage, ElMessageBox } from 'element-plus';
import { ref } from 'vue';

interface UseTableOperationsOptions {
    // 实体名称，用于显示提示信息，如"新闻"、"地点"等
    entityName: string;
    // 刷新列表的方法
    refresh: () => void;
    // 各种操作的具体实现
    operations?: {
        new?: (form: any) => Promise<void>;
        update?: (form: any) => Promise<void>;
        top?: (row: any) => Promise<void>;
        publish?: (row: any) => Promise<void>;
        unpublish?: (row: any) => Promise<void>;
        delete?: (row: any) => Promise<void>;
        batchDelete?: (rows: any[]) => Promise<void>;
        audit?: (row: any) => Promise<void>;
        batchAudit?: (rows: any[]) => Promise<void>;
    };
}

export function useTableOperations(options: UseTableOperationsOptions) {
    const { entityName, refresh, operations = {} } = options;

    const selectedRows = ref<any[]>([]);

    const handleSelectionChange = (selection: any[]) => {
        selectedRows.value = selection;
    };

    const handleNew = async (form: any) => {
        if (operations.new) {
            await operations.new(form);
            ElMessage.success('添加成功');
            refresh();
        } else {
            ElMessage.warning(`未实现${entityName}的添加操作`);
        }
    };

    const handleUpdate = async (form: any) => {
        if (operations.update) {
            await operations.update(form);
            ElMessage.success('更新成功');
            refresh();
        } else {
            ElMessage.warning(`未实现${entityName}的更新操作`);
        }
    };

    const handleAsyncWithMessage = async (promise: Promise<void>, successMessage?: string) => {
        try {
            await promise;
            ElMessage.success(successMessage ?? '操作成功');
        } catch (error) {
            console.error('操作失败:', error);
        }
    };

    const handleConfirmWithMessage = async (param: {
        message: string;
        promise: Promise<void>;
        successMessage?: string;
        errorMessage?: string;
    }) => {
        try {
            await ElMessageBox.confirm(param.message, '提示', {
                type: 'warning',
            });
            await param.promise;
            ElMessage.success(param.successMessage ?? '操作成功');
        } catch (error) {
            console.error('操作失败:', error);
            ElMessage.error(param.errorMessage ?? '操作失败');
        }
    };

    const handleTop = async (row: any) => {
        const confirm = await ElMessageBox.confirm(
            `确认要${row.isTop ? '取消置顶' : '置顶'}该${entityName}吗？`,
            '提示',
            {
                type: 'warning',
            },
        ).catch(() => Promise.reject('用户取消操作'));
        if (confirm) {
            if (operations.top) {
                await operations.top(row);
                ElMessage.success(`${row.isTop ? '取消置顶' : '置顶'}成功`);
            } else {
                ElMessage.warning(`未实现${entityName}的置顶操作`);
            }
        }
    };

    const handlePublish = async (row: any) => {
        await handleConfirmWithMessage({
            message: `确认要发布该${entityName}吗？`,
            promise: operations.publish
                ? operations.publish(row)
                : Promise.reject(`未实现${entityName}的发布操作`),
            successMessage: '发布成功',
        });
    };

    const handleUnpublish = async (row: any) => {
        await handleConfirmWithMessage({
            message: `确认要下架该${entityName}吗？`,
            promise: operations.unpublish
                ? operations.unpublish(row)
                : Promise.reject(`未实现${entityName}的下架操作`),
            successMessage: '下架成功',
        });
    };

    const handleDelete = async (row: any) => {
        try {
            await ElMessageBox.confirm(`确认要删除该${entityName}吗？`, '提示', {
                type: 'warning',
            });
            if (operations.delete) {
                await operations.delete(row);
                ElMessage.success('删除成功');
            } else {
                ElMessage.warning(`未实现${entityName}的删除操作`);
            }
        } catch (error) {
            // 如果用户点击取消，不会显示错误消息
            if (error !== 'cancel' && error?.type !== 'cancel') {
                console.error('删除失败:', error);
                ElMessage.error('删除失败');
            }
        }
    };

    const handleBatchDelete = async () => {
        if (selectedRows.value.length === 0) {
            ElMessage.warning(`请选择要删除的${entityName}`);
            return;
        }

        try {
            await ElMessageBox.confirm(`确认要删除选中的${entityName}吗？`, '提示', {
                type: 'warning',
            });
            if (operations.batchDelete) {
                await operations.batchDelete(selectedRows.value);
                ElMessage.success('删除成功');
            } else {
                ElMessage.warning(`未实现${entityName}的批量删除操作`);
            }
        } catch (error) {
            // 如果用户点击取消，不会显示错误消息
            if (error !== 'cancel' && error?.type !== 'cancel') {
                console.error('删除失败:', error);
                ElMessage.error('删除失败');
            }
        }
    };

    const handleAudit = async (row: any) => {
        await handleConfirmWithMessage({
            message: `确认要审核该${entityName}吗？`,
            promise: operations.audit
                ? operations.audit(row)
                : Promise.reject(`未实现${entityName}的审核操作`),
            successMessage: '审核成功',
        });
    };

    const handleBatchAudit = async () => {
        if (selectedRows.value.length === 0) {
            ElMessage.warning(`请选择要审核的${entityName}`);
            return;
        }

        await handleConfirmWithMessage({
            message: `确认要审核选中的${entityName}吗？`,
            promise: operations.batchAudit
                ? operations.batchAudit(selectedRows.value)
                : Promise.reject(`未实现${entityName}的批量审核操作`),
            successMessage: '审核成功',
        });
    };

    return {
        selectedRows,
        handleNew,
        handleUpdate,
        handleAsyncWithMessage,
        handleConfirmWithMessage,
        handleSelectionChange,
        handleTop,
        handlePublish,
        handleUnpublish,
        handleDelete,
        handleBatchDelete,
        handleAudit,
        handleBatchAudit,
    };
}
