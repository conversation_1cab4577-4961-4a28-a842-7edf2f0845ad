import { initReport, updateReportUserInfo } from '@/services/reportService';
import { getAuthInfo } from '@/utils/auth';
export function bootstrapReport() {
    console.log('bootstrapReport,加载埋点代码', import.meta.env.VITE_MONITOR_ENV);
    try {
        initReport({
            projectVersion: '1.0.0',
            env: import.meta.env.VITE_MONITOR_ENV || 'sit',
            platform: 'PC',
        });
        updateReportUserInfo(getAuthInfo()?.userId as unknown as string);
    } catch (e) {
        console.log('bootstrapReport error', e);
    }
}
