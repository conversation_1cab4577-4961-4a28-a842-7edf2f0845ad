import { storageLocal } from '@pureadmin/utils';
import { defineStore } from 'pinia';

import { setConfig } from '@/config';
import { store } from '@/store';
import type { ZoneInfo } from '@/utils/auth';
import { setZoneInfo } from '@/utils/auth';

const storeageLocal_CONFIG_KEY = {
    currentSelectZoneInfo: `CURRENT_ZONE_INFO`,
    currentSelectZoneId: `CURRENT_ZONE_ID`,
};

type State = {
    currentSelectZoneId: string;
    storeZoneInfo: ZoneInfo | {};
    currentSelectZoneInfo: Omit<ZoneInfo, 'subZoneList'> | {};
};

function getStoreage<T>(key: string): T {
    return storageLocal().getItem(key);
}
function setStoreage<T>(key: string, value: T) {
    storageLocal().setItem(key, value);
}

function removeStoreage(key: string) {
    storageLocal().removeItem(key);
}

export const useBusinessDistrictZoneStore = defineStore('businessDistrictZone', {
    state() {
        return {
            // 当前选中的专区Id
            currentSelectZoneId: '',
            // 本地存储
            storeZoneInfo: {},
            // 当前选中的专区信息
            currentSelectZoneInfo:
                getStoreage(storeageLocal_CONFIG_KEY.currentSelectZoneInfo) || {},
        } satisfies State;
    },
    getters: {
        /**
         * 当前选中的专区Id
         * @param state
         */
        getCurrentSelectZoneId(state: State): string {
            return state.currentSelectZoneId;
        },
        /**
         * 获取当前选中的专区信息
         * @param state
         */
        getCurrentSelectZoneInfo(state: State): ZoneInfo {
            return state.currentSelectZoneInfo as ZoneInfo;
        },
        /**
         * 获取当前登录账号选中专区名称
         * @param state
         */
        getCurrentSelectZoneName(state: State) {
            return (
                (state.currentSelectZoneInfo as ZoneInfo).zone ||
                (state.currentSelectZoneInfo as ZoneInfo).zoneName
            );
        },
        /**
         * 获取当前登录账号专区Id
         * @param state
         */
        getCurrentZoneId(state: State) {
            return (state.storeZoneInfo as ZoneInfo).zoneId;
        },
        /**
         * 获取当前登录账号专区信息
         * @param state
         */
        getCurrentZoneInfo(state: State) {
            return state.storeZoneInfo as ZoneInfo;
        },
    },
    actions: {
        /**
         * 存储当前当前所在专区Id
         * @param id
         */
        setCurrentSelectZoneId(id: string) {
            this.currentSelectZoneId = id;
            setStoreage(storeageLocal_CONFIG_KEY.currentSelectZoneId, id);
            if (this.storeZoneInfo.zoneId) {
                this.setCurrentSelectZoneInfo();
            }
        },
        /**
         * 根据当前zoneId 设置当前zoneInfo
         * @param info
         */
        setCurrentSelectZoneInfo() {
            if (this.storeZoneInfo?.subZoneList && this.storeZoneInfo?.subZoneList.length > 0) {
                for (const item of this.storeZoneInfo.subZoneList as ZoneInfo['subZoneList']) {
                    if (item.id === this.currentSelectZoneId) {
                        this.currentSelectZoneInfo = item;
                        this.currentSelectZoneInfo.zoneId = item.id;
                        setStoreage(
                            storeageLocal_CONFIG_KEY.currentSelectZoneInfo,
                            this.currentSelectZoneInfo,
                        );
                        return;
                    } else if (this.currentSelectZoneId == '1') {
                        this.currentSelectZoneInfo = this.storeZoneInfo;
                        setStoreage(
                            storeageLocal_CONFIG_KEY.currentSelectZoneInfo,
                            this.currentSelectZoneInfo,
                        );
                        return;
                    }
                }
            } else {
                this.currentSelectZoneInfo = this.storeZoneInfo;
                setStoreage(
                    storeageLocal_CONFIG_KEY.currentSelectZoneInfo,
                    this.currentSelectZoneInfo,
                );
            }
        },
        /**
         * 动态修改标题
         */
        changeConfigTitle() {
            if (this.currentSelectZoneInfo) {
                setConfig({
                    Title: '诚信白沙服务专区',
                });
            }
        },
        /**
         * 设置当前登录账号专区信息本地存储
         * @param info
         */
        setStoreZoneInfo(info: ZoneInfo) {
            this.storeZoneInfo = info;
            setZoneInfo(info);
        },

        $reset() {
            removeStoreage(storeageLocal_CONFIG_KEY.currentSelectZoneInfo);
            Object.assign(this.$state, {
                currentSelectZoneId: this.currentSelectZoneId,
                storeZoneInfo: {},
                currentSelectZoneInfo: {},
            });
        },
    },
});

export const useBusinessDistrictZoneStoreHook = () => {
    return useBusinessDistrictZoneStore(store);
};
