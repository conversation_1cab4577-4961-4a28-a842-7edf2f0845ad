import { storageLocal } from '@pureadmin/utils';
import { defineStore } from 'pinia';

import { pwdLogin, type UserResult } from '@/api/login/index';
import { getConfig } from '@/config';
import { routerArrays } from '@/layout/types';
import { resetRouter, router } from '@/router';
import { updateReportUserInfo } from '@/services/reportService';
import { store } from '@/store';
import { useBusinessDistrictZoneStoreHook } from '@/store/modules/businessDistrictZone';
import { useMultiTagsStoreHook } from '@/store/modules/multiTags';
import { Encrypt2 } from '@/utils/AESUtil';
import { type DataInfo, removeAuthInfo, setAuthInfo, userKey } from '@/utils/auth';

import type { userType } from './types';

export const useUserStore = defineStore({
    id: 'user',
    state: (): userType => ({
        // 用户名
        username: storageLocal().getItem<DataInfo>(userKey)?.userName ?? '',
    }),
    actions: {
        /** 存储用户名 */
        SET_USERNAME(username: string) {
            this.username = username;
        },
        /** 登入 */
        async loginByUsername(data: {
            userAccount: string;
            password: string;
            imgCode: string;
            randomCode: string;
        }) {
            return new Promise<UserResult>(async (resolve, reject) => {
                const password = Encrypt2(data.password);
                try {
                    const res = await pwdLogin({
                        imgCode: data.imgCode, // 图形验证码
                        password: password, // 用户密码
                        randomCode: data.randomCode, // 获取图形验证码返回的随机码
                        tenantId: Number(getConfig('tenantId')), // 1系统用户，2金融机构用户 , 3内部系统常用租户 , 4
                        userAccount: data.userAccount, // 用户账号
                    });
                    updateReportUserInfo(res?.userInfo?.id);
                    setAuthInfo(res);
                    resolve(res);
                } catch (err: any) {
                    reject(err);
                }
            });
        },
        /** 前端登出（不调用接口） */
        logOut() {
            this.username = '';
            removeAuthInfo();
            useMultiTagsStoreHook().handleTags('equal', [...routerArrays]);
            useBusinessDistrictZoneStoreHook().$reset();
            resetRouter();
            router.replace('/login');
        },
    },
});

export function useUserStoreHook() {
    return useUserStore(store);
}
