import { defineStore } from 'pinia';
import { reactive } from 'vue';

import DictAPI from '@/api/dict';
import { store } from '@/store';

type DictMap = Record<string, Record<string, Partial<DictItem>>>;

type DictItem = {
    label: string;
    value: any;
    disabled: boolean;
};

export const useDictStore = defineStore('dict', () => {
    /** 记录请求状态 避免重复请求 */
    const dictKeyStatus: Record<string, boolean> = {};

    /** 字典表 */
    const dict = reactive(
        new Proxy<DictMap>(
            {},
            {
                get(target, key: string) {
                    if (
                        typeof key === 'string' &&
                        !key.startsWith('$') &&
                        !key.startsWith('_') &&
                        !Boolean(Reflect.get(target, key)) &&
                        !Boolean(Reflect.get(dictKeyStatus, key))
                    ) {
                        Reflect.set(dictKeyStatus, key, true);
                        Reflect.set(target, key, {});
                        getDictData(key).then((res) => {
                            setTimeout(() => {
                                Reflect.set(
                                    dict,
                                    key,
                                    Object.fromEntries(res.map((item) => [item.value, item])),
                                );
                            }, 1000);
                        });
                    }

                    return Reflect.get(target, key);
                },
                set(target, key: string, value) {
                    return Reflect.set(target, key, value);
                },
            },
        ),
    );

    /**
     * 获取指定字典类型的所有项目
     * @param key 字典类型的唯一标识符
     * @returns 返回指定字典类型的所有项目对象
     */
    function getDictItem(key: string) {
        return dict[key];
    }

    /**
     * 获取指定字典类型的所有项目列表
     * @param key 字典类型的唯一标识符
     * @returns 返回字典项目的数组
     */
    function getDictItemToList(key: string) {
        return Object.values(dict[key]);
    }

    /**
     * 获取指定字典类型的某一项的label
     * @param key 字典类型的唯一标识符
     * @param value 字典值
     * @returns 返回字典项的label
     */
    function getDictItemLabel(key: string, value: any) {
        return dict[key][String(value)]?.label;
    }

    return { dict, getDictItem, getDictItemToList, getDictItemLabel };
});

function getDictData(type: string) {
    return DictAPI.getDictPage({
        pageNo: 1,
        pageSize: 10,
    }).then((res) => {
        return res.list.map((item) => ({
            label: item.name,
            value: item.value,
            disabled: item.status === 0,
        }));
    });
}

export function useDictStoreHook() {
    return useDictStore(store);
}
