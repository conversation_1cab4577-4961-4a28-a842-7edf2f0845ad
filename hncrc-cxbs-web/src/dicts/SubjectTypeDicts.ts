import { dicts2Map } from '@/utils/dict';

/** 主体类型枚举 */
export enum SubjectTypeEnum {
    /** 合作社 */
    Cooperative = 'Cooperative',
    /** 个体工商户 */
    IndvBusiness = 'IndvBusiness',
    /** 企业 */
    Enterprise = 'Enterprise',
    /** 农户 */
    Farmer = 'Farmer',
}

/** 主体类型字典 */
export const SubjectTypeDicts: Dict<string>[] = [
    {
        id: 1,
        value: SubjectTypeEnum.Cooperative,
        label: '合作社',
        code: SubjectTypeEnum.Cooperative,
        sort: 10,
    },
    {
        id: 2,
        value: SubjectTypeEnum.IndvBusiness,
        label: '个体工商户',
        code: SubjectTypeEnum.IndvBusiness,
        sort: 20,
    },
    {
        id: 3,
        value: SubjectTypeEnum.Enterprise,
        label: '企业',
        code: SubjectTypeEnum.Enterprise,
        sort: 30,
    },
    {
        id: 4,
        value: SubjectTypeEnum.Farmer,
        label: '农户',
        code: SubjectTypeEnum.Farmer,
        sort: 40,
    },
];

/** 油茶经营主体类型字典 */
export const OilTeaSubjectTypeDicts = SubjectTypeDicts.map((item) => {
    return {
        ...item,
        label: item.value === SubjectTypeEnum.IndvBusiness ? '基层组织' : item.label,
    };
});

/** 主体类型字典Map 主要是为了翻译字典时不需要每次翻译都遍历一遍数组，优化性能 */
export const SubjectTypeDictMap = dicts2Map(SubjectTypeDicts);

/** 油茶经营主体类型字典Map 主要是为了翻译字典时不需要每次翻译都遍历一遍数组，优化性能 */
export const OilTeaSubjectTypeDictMap = dicts2Map(OilTeaSubjectTypeDicts);

/** 获取主题类型字典值 */
export function getSubjectTypeLabel(value: string, isOilTea: boolean = false) {
    if (isOilTea) return OilTeaSubjectTypeDictMap.get(value) || value;
    return SubjectTypeDictMap.get(value) || value;
}
