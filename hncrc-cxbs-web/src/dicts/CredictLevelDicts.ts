import { dicts2Map } from '@/utils/dict';

/** 信用等级枚举 */
export enum CredictLevelEnum {
    /** A级 */
    LevelA = 'A',
    /** B级 */
    LevelB = 'B',
    /** C级 */
    LevelC = 'C',
    /** D级 */
    LevelD = 'D',
}

/** 信用等级字典 */
export const CredictLevelDicts: DictWithConfig<string, { color: string; bgColor: string }>[] = [
    {
        id: 1,
        value: CredictLevelEnum.LevelA,
        label: '信用优秀',
        code: CredictLevelEnum.LevelA,
        sort: 10,
        config: {
            color: '#FA5151',
            bgColor: '#FCDBDB',
        },
    },
    {
        id: 2,
        value: CredictLevelEnum.LevelB,
        label: '信用良好',
        code: CredictLevelEnum.LevelB,
        sort: 20,
        config: {
            color: '#FF8F1F',
            bgColor: '#FFE5CC',
        },
    },
    {
        id: 3,
        value: CredictLevelEnum.LevelC,
        label: '信用一般',
        code: CredictLevelEnum.LevelC,
        sort: 30,
        config: {
            color: '#3662EC',
            bgColor: '#CDE1FD',
        },
    },
    {
        id: 4,
        value: CredictLevelEnum.LevelD,
        label: '信用较差',
        code: CredictLevelEnum.LevelD,
        sort: 40,
        config: {
            color: 'rgba(0, 0, 0, 0.85)',
            bgColor: 'rgba(0, 0, 0, 0.3)',
        },
    },
];

/** 信用等级字典Map 主要是为了翻译字典时不需要每次翻译都遍历一遍数组，优化性能 */
export const CredictLevelDictMap = dicts2Map(CredictLevelDicts);

/**
 * 获取信用等级label显示文本
 * @param value 信用等级值
 * @param withVal 是否在label前面带上原始值
 */
export function getCredictLevelLabel(value: string, withValAtPrefix: boolean = false): string {
    const label = CredictLevelDictMap.get(value);
    return label ? `${withValAtPrefix ? `${value}` : ''}${label}` : value;
}

/** 获取字典对应的配置 */
export function getCredictLevelConfig(value: string) {
    const dict = CredictLevelDicts.find((item) => item.value === value);
    if (!dict) return {};
    return {
        color: dict.config.color,
        backgroundColor: dict.config.bgColor,
    };
}
