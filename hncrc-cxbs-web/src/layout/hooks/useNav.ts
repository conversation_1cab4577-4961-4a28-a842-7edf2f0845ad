import { useGlobal } from '@pureadmin/utils';
import { storeToRefs } from 'pinia';
import { computed, type CSSProperties } from 'vue';
import { useRouter } from 'vue-router';

import userAvatar from '@/assets/user.png';
import { getConfig } from '@/config';
import { transformI18n } from '@/plugins/i18n';
import { remainingPaths, router } from '@/router';
import { getTopMenu } from '@/router/utils';
import { useAppStoreHook } from '@/store/modules/app';
import { useEpThemeStoreHook } from '@/store/modules/epTheme';
import { usePermissionStoreHook } from '@/store/modules/permission';
import { useUserStoreHook } from '@/store/modules/user';
import { emitter } from '@/utils/mitt';

import type { routeMetaType } from '../types';

const errorInfo = '当前路由配置不正确，请检查配置';

export function useNav() {
    const appStore = useAppStoreHook();
    const routers = useRouter().options.routes;
    const { wholeMenus } = storeToRefs(usePermissionStoreHook());
    /** 平台`layout`中所有`el-tooltip`的`effect`配置，默认`light` */
    const tooltipEffect = getConfig()?.TooltipEffect ?? 'light';

    const getDivStyle = computed((): CSSProperties => {
        return {
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            overflow: 'hidden',
        };
    });

    /** 用户名 */
    const username = computed(() => {
        return useUserStoreHook()?.username;
    });

    /** 设置国际化选中后的样式 */
    const getDropdownItemStyle = computed(() => {
        return (locale, t) => {
            return {
                background: locale === t ? useEpThemeStoreHook().epThemeColor : '',
                color: locale === t ? '#f4f4f5' : '#000',
            };
        };
    });

    const getDropdownItemClass = computed(() => {
        return (locale, t) => {
            return locale === t ? '' : 'dark:hover:!text-primary';
        };
    });

    const avatarsStyle = computed(() => {
        return username.value ? { marginRight: '10px' } : '';
    });

    const isCollapse = computed(() => {
        return !appStore.getSidebarStatus;
    });

    const device = computed(() => {
        return appStore.getDevice;
    });

    const { $storage, $config } = useGlobal<GlobalPropertiesApi>();
    const layout = computed(() => {
        return $storage?.layout?.layout;
    });

    const title = computed(() => {
        return $config.Title;
    });

    /** 动态title */
    function changeTitle(meta: routeMetaType) {
        const Title = getConfig().Title;
        if (Title) document.title = `${transformI18n(meta.title)} | ${Title}`;
        else document.title = transformI18n(meta.title);
    }

    /** 退出登录 */
    function logout() {
        useUserStoreHook().logOut();
    }

    function backTopMenu() {
        router.push(getTopMenu()?.path);
    }

    function onPanel() {
        emitter.emit('openPanel');
    }

    function toggleSideBar() {
        appStore.toggleSideBar();
    }

    function handleResize(menuRef) {
        menuRef?.handleResize();
    }

    function resolvePath(route) {
        if (!route.children) return console.error(errorInfo);
        const httpReg = /^http(s?):\/\//;
        const routeChildPath = route.children[0]?.path;
        if (httpReg.test(routeChildPath)) {
            return route.path + '/' + routeChildPath;
        } else {
            return routeChildPath;
        }
    }

    function menuSelect(indexPath: string) {
        if (wholeMenus.value.length === 0 || isRemaining(indexPath)) return;
        emitter.emit('changLayoutRoute', indexPath);
    }

    /** 判断路径是否参与菜单 */
    function isRemaining(path: string) {
        return remainingPaths.includes(path);
    }

    /** 获取`logo` */
    function getLogo() {
        return new URL('/logo.png', import.meta.url).href;
    }

    return {
        title,
        device,
        layout,
        logout,
        routers,
        $storage,
        backTopMenu,
        onPanel,
        getDivStyle,
        changeTitle,
        toggleSideBar,
        menuSelect,
        handleResize,
        resolvePath,
        getLogo,
        isCollapse,
        appStore,
        username,
        userAvatar,
        avatarsStyle,
        tooltipEffect,
        getDropdownItemStyle,
        getDropdownItemClass,
    };
}
