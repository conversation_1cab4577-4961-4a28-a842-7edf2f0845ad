import { onBeforeMount, type Ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';

import { useNav } from './useNav';

export function useTranslationLang(ref?: Ref) {
    const { $storage, changeTitle, handleResize } = useNav();
    const { locale, t } = useI18n();
    const route = useRoute();

    function translationCh() {
        $storage.locale = { locale: 'zh' };
        locale.value = 'zh';
        ref && handleResize(ref.value);
    }

    function translationEn() {
        $storage.locale = { locale: 'en' };
        locale.value = 'en';
        ref && handleResize(ref.value);
    }

    watch(
        () => locale.value,
        () => {
            changeTitle(route.meta);
        },
    );

    onBeforeMount(() => {
        locale.value = $storage.locale?.locale ?? 'zh';
    });

    return {
        t,
        route,
        locale,
        translationCh,
        translationEn,
    };
}
