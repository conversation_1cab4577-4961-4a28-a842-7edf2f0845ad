<script lang="ts" setup>
import { getConfig } from '@/config';

const CorpName = getConfig('CorpName');
const CorpUrl = getConfig('CorpUrl') as string;
</script>

<template>
    <footer class="layout-footer text-[rgba(0,0,0,0.6)] dark:text-[rgba(220,220,242,0.8)]">
        Copyright © 2022-present
        <a class="hover:text-primary" :href="CorpUrl" target="_blank"> &nbsp;{{ CorpName }} </a>
    </footer>
</template>

<style lang="scss" scoped>
.layout-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 0 8px;
    font-size: 14px;
}
</style>
