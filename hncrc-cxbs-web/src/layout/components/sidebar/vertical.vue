<script setup lang="ts">
import { isAllEmpty, storageLocal } from '@pureadmin/utils';
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { responsiveStorageNameSpace } from '@/config';
import { useNav } from '@/layout/hooks/useNav';
import { findRouteByPath, getParentPaths } from '@/router/utils';
import { usePermissionStoreHook } from '@/store/modules/permission';
import { emitter } from '@/utils/mitt';

import leftCollapse from './leftCollapse.vue';
import Logo from './logo.vue';
import SidebarItem from './sidebarItem.vue';

const route = useRoute();

const showLogo = ref(
    storageLocal().getItem<StorageConfigs>(`${responsiveStorageNameSpace()}configure`)?.showLogo ??
        true,
);

const { device, appStore, isCollapse, tooltipEffect, menuSelect, toggleSideBar } = useNav();

const subMenuData = ref([]);

// 前端暂时写死 menu列表的数组
const menuData = computed(() => {
    return appStore.layout === 'mix' && device.value !== 'mobile'
        ? subMenuData.value
        : usePermissionStoreHook().wholeMenus;
});

const loading = computed(() => {
    return appStore.layout === 'mix' ? false : menuData.value.length === 0 ? true : false;
});

const defaultActive = computed(() =>
    !isAllEmpty(route.meta?.activePath) ? route.meta.activePath : route.path,
);

function getSubMenuData() {
    let path = '';
    path = defaultActive.value;
    subMenuData.value = [];
    // path的上级路由组成的数组
    const parentPathArr = getParentPaths(path, usePermissionStoreHook().wholeMenus);
    // 当前路由的父级路由信息
    const parenetRoute = findRouteByPath(
        parentPathArr[0] || path,
        usePermissionStoreHook().wholeMenus,
    );
    if (!parenetRoute?.children) return;
    subMenuData.value = parenetRoute?.children;
}

watch(
    () => [route.path, usePermissionStoreHook().wholeMenus],
    () => {
        if (route.path.includes('/redirect')) return;
        getSubMenuData();
        menuSelect(route.path);
    },
);

onMounted(() => {
    getSubMenuData();

    emitter.on('logoChange', (key) => {
        showLogo.value = key;
    });
});

onBeforeUnmount(() => {
    // 解绑`logoChange`公共事件，防止多次触发
    emitter.off('logoChange');
});
</script>

<template>
    <div v-loading="loading" :class="['sidebar-container', showLogo ? 'has-logo' : 'no-logo']">
        <el-scrollbar
            wrap-class="scrollbar-wrapper"
            :class="[device === 'mobile' ? 'mobile' : 'pc']"
        >
            <Logo v-if="showLogo" class="!bg-transparent" :collapse="isCollapse" />

            <el-menu
                router
                unique-opened
                mode="vertical"
                popper-class="custom-scrollbar"
                class="outer-most select-none"
                :collapse="isCollapse"
                :collapse-transition="false"
                :popper-effect="tooltipEffect"
                :default-active="defaultActive"
            >
                <sidebar-item
                    v-for="routes in menuData"
                    :key="routes.path"
                    :item="routes"
                    :base-path="routes.path"
                    class="outer-most select-none"
                />
            </el-menu>
        </el-scrollbar>
        <leftCollapse
            v-if="device !== 'mobile'"
            :is-active="appStore.sidebar.opened"
            @toggleClick="toggleSideBar"
        />
    </div>
</template>

<style scoped lang="scss">
:deep(.el-loading-mask) {
    opacity: 0.45;
}
</style>
