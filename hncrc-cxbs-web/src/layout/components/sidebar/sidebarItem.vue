<script setup lang="ts">
import EpArrowDown from '@iconify-icons/ep/arrow-down-bold';
import ArrowLeft from '@iconify-icons/ep/arrow-left-bold';
import ArrowRight from '@iconify-icons/ep/arrow-right-bold';
import ArrowUp from '@iconify-icons/ep/arrow-up-bold';
import path from 'path';
import { computed, type CSSProperties, type PropType, ref, toRaw } from 'vue';

import { useRenderIcon } from '@/components/ReIcon/src/hooks';
import { ReText } from '@/components/ReText';
import { getConfig } from '@/config';
import { useNav } from '@/layout/hooks/useNav';
import { transformI18n } from '@/plugins/i18n';

import { menuType } from '../../types';
import extraIcon from './extraIcon.vue';

const { layout, isCollapse, tooltipEffect, getDivStyle } = useNav();

const props = withDefaults(
    defineProps<{
        item: menuType;
        parent?: menuType;
        isNest?: boolean;
        basePath?: string;
    }>(),
    {
        basePath: '',
    },
);

// theme.ts 无法处理多层级的菜单，所以这里需要手动处理
const menuStyle = {
    icon: {
        color: '#fff'
    },
    title: {
        color: '#fff !important'
    }
}


const getNoDropdownStyle = computed((): CSSProperties => {
    return {
        display: 'flex',
        alignItems: 'center',
    };
});

const getSubMenuIconStyle = computed((): CSSProperties => {
    return {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        margin:
            layout.value === 'horizontal' ? '0 5px 0 0' : isCollapse.value ? '0 auto' : '0 5px 0 0',
    };
});

const expandCloseIcon = computed(() => {
    if (!getConfig()?.MenuArrowIconNoTransition) return '';
    return {
        'expand-close-icon': useRenderIcon(EpArrowDown),
        'expand-open-icon': useRenderIcon(ArrowUp),
        'collapse-close-icon': useRenderIcon(ArrowRight),
        'collapse-open-icon': useRenderIcon(ArrowLeft),
    };
});

const onlyOneChild: menuType = ref(null);

function hasOneShowingChild(children: menuType[] = [], parent: menuType) {
    const showingChildren = children.filter((item: any) => {
        onlyOneChild.value = item;
        return true;
    });

    if (showingChildren[0]?.meta?.showParent) {
        return false;
    }

    if (showingChildren.length === 1) {
        return true;
    }

    if (showingChildren.length === 0) {
        onlyOneChild.value = { ...parent, path: '', noShowingChildren: true };
        return true;
    }
    return false;
}

function resolvePath(routePath) {
    const httpReg = /^http(s?):\/\//;
    if (httpReg.test(routePath) || httpReg.test(props.basePath)) {
        return routePath || props.basePath;
    } else {
        // 使用path.posix.resolve替代path.resolve 避免windows环境下使用electron出现盘符问题
        return path.posix.resolve(props.basePath, routePath);
    }
}
</script>

<template>
    <el-menu-item
        v-if="
            hasOneShowingChild(props.item.children, props.item) &&
            (!onlyOneChild.children || onlyOneChild.noShowingChildren)
        "
        :index="resolvePath(onlyOneChild.path)"
        :class="{ 'submenu-title-noDropdown': !isNest }"
        :style="getNoDropdownStyle"
        :disabled="Boolean(props.item?.meta?.isDisabled)"
    >
        <div
            v-if="toRaw(props.item?.meta?.icon)"
            class="sub-menu-icon"
            :style="getSubMenuIconStyle"
        >
            <component
                :is="
                    useRenderIcon(
                        toRaw(onlyOneChild?.meta?.icon) ||
                            (props.item?.meta && toRaw(props.item.meta?.icon)),
                            menuStyle.icon,
                    )
                "
            />
        </div>
        <el-text
            v-if="
                (!props.item?.meta?.icon &&
                    isCollapse &&
                    layout === 'vertical' &&
                    props.item?.pathList?.length === 1) ||
                (!onlyOneChild?.meta?.icon &&
                    isCollapse &&
                    layout === 'mix' &&
                    props.item?.pathList?.length === 2)
            "
            truncated
            class="!px-4 !text-inherit"
        >
            {{ transformI18n(onlyOneChild.meta.title) }}
        </el-text>

        <template #title>
            <div :style="getDivStyle">
                <ReText
                    :tippyProps="{
                        offset: [0, -10],
                        theme: tooltipEffect,
                    }"
                    class="text-inherit"
                    :style="[(!isCollapse || parent?.children.length > 1) && menuStyle.title]"
                >
                    {{ transformI18n(onlyOneChild?.meta?.title) }}
                </ReText>
                <extraIcon :extraIcon="onlyOneChild?.meta?.extraIcon" />
            </div>
        </template>
    </el-menu-item>
    <el-sub-menu
        v-else
        ref="subMenu"
        teleported
        :index="resolvePath(props.item.path)"
        v-bind="expandCloseIcon"
    >
        <template #title>
            <div
                v-if="toRaw(props.item.meta.icon)"
                :style="getSubMenuIconStyle"
                class="sub-menu-icon"
            >
                <component :is="useRenderIcon(props.item.meta && toRaw(props.item.meta.icon), menuStyle.icon,
)" />
            </div>
            <ReText
                v-if="
                    !(
                        layout === 'vertical' &&
                        isCollapse &&
                        toRaw(props.item.meta.icon) &&
                        props.item.parentId === null
                    )
                "
                :tippyProps="{
                    offset: [0, -10],
                    theme: tooltipEffect,
                }"
                :class="{
                    '!text-inherit': true,
                    '!px-4':
                        layout !== 'horizontal' &&
                        isCollapse &&
                        !toRaw(props.item?.meta?.icon, ) &&
                        props.item.parentId === null,
                }"
                :style="[menuStyle.title]"
            >
                {{ transformI18n(props.item?.meta?.title) }}
            </ReText>
            <extraIcon v-if="!isCollapse" :extraIcon="props.item?.meta?.extraIcon" />
        </template>

        <sidebar-item
            v-for="child in props.item.children"
            :key="child.path"
            :is-nest="true"
            :item="child"
            :parent="props.item"
            :base-path="resolvePath(child.path)"
            class="nest-menu"
        />
    </el-sub-menu>
</template>
