<script setup lang="ts">
import MenuFold from '@iconify-icons/ri/menu-fold-fill';
import { useGlobal } from '@pureadmin/utils';
import { computed } from 'vue';

import { useNav } from '@/layout/hooks/useNav';

interface Props {
    isActive: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    isActive: false,
});

const { tooltipEffect } = useNav();

const iconClass = computed(() => {
    return [
        'ml-4',
        'mb-1',
        'w-[16px]',
        'h-[16px]',
        'inline-block',
        'align-middle',
        'cursor-pointer',
        'duration-[100ms]',
    ];
});

const { $storage } = useGlobal<GlobalPropertiesApi>();
const themeColor = computed(() => $storage.layout?.themeColor);

const emit = defineEmits<{
    (e: 'toggleClick'): void;
}>();

const toggleClick = () => {
    emit('toggleClick');
};
</script>

<template>
    <div class="collapse-container text-white">
        <IconifyIconOffline
            v-tippy="{
                content: props.isActive ? '点击折叠' : '点击展开',
                theme: tooltipEffect,
                hideOnClick: 'toggle',
                placement: 'right',
            }"
            :icon="MenuFold"
            :class="[iconClass, themeColor === 'light' ? '' : 'text-primary']"
            :style="{ transform: props.isActive ? 'none' : 'rotateY(180deg)' }"
            @click="toggleClick"
        />
    </div>
</template>

<style lang="scss" scoped>
.collapse-container {
    width: 100%;
    height: 40px;
    line-height: 40px;
    box-shadow: 0 0 6px -3px var(--el-color-primary);
}
</style>
