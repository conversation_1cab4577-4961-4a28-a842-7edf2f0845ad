// useWatermarkInterval.ts
import { storageLocal, useWatermark } from '@pureadmin/utils';
import dayjs from 'dayjs';
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

import { getRoleDtoListByUserId } from '@/api/system/user';
import type { DataInfo } from '@/utils/auth';

export function useWatermarkInterval() {
    const { setWatermark, clear } = useWatermark();
    const watermark = ref<string>('');
    const isWatermark = ref<boolean>(false);
    const options = {
        // color: '#000',
        width: 400,
        height: 200,
    };
    let intervalId;
    const user = storageLocal().getItem<DataInfo>('HNCRC-ADMIN-USER-INFO');
    const getRole = async () => {
        const res = await getRoleDtoListByUserId(String(user.userId));
        if (res) {
            // TODO: 这里的判断逻辑可能需要调整, 要确定一下什么情况下需要显示水印
            if (res[0].roleName === '系统管理') {
                /* isWatermark.value = true; */
                isWatermark.value = false;
            } else {
                isWatermark.value = true;
            }
        }
    };
    getRole();

    const updateWatermark = () => {
        clear();
        watermark.value = user.userName + ' ' + dayjs().format('YYYY-MM-DD HH:mm');
        setWatermark(watermark.value, options);
    };

    const startInterval = () => {
        if (!isWatermark.value) return;
        updateWatermark(); // 立即更新一次
        intervalId = setInterval(updateWatermark, 1000); // 然后每秒更新一次
    };

    const stopInterval = () => {
        clearInterval(intervalId);
        clear(); // 清除水印
    };

    onMounted(() => {
        nextTick(() => {
            startInterval();
        });
    });

    onUnmounted(() => {
        stopInterval();
    });

    watch(
        isWatermark,
        (newVal) => {
            if (newVal) {
                startInterval();
            } else {
                stopInterval();
            }
        },
        { immediate: true },
    );
}
