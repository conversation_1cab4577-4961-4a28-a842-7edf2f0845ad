.wave {
    position: fixed;
    height: 100%;
    width: 100%;
    left: 0;
    bottom: 0;
    z-index: -1;
}

.login-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    max-width: 100%;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.img {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.img img {
    width: 500px;
}

.login-box {
    background-color: #fff;
    display: flex;
    position: absolute;
    /* top: 50%; */
    padding: 20px 60px;
    border-radius: 12px;
    /* transform: translateY(-50%); */
    /* right: 15%; */
    justify-content: center;
    align-items: center;
    text-align: center;
}
.login-title {
}

.login-form {
    width: 360px;
}

.avatar {
    width: 350px;
    height: 80px;
}

.login-form h2 {
    text-transform: uppercase;
    margin: 15px 0;
    color: #999;
    font:
        bold 200% Consolas,
        Monaco,
        monospace;
}

@media screen and (max-width: 1180px) {
    .login-container {
        grid-gap: 9rem;
    }

    .login-form {
        width: 290px;
    }

    .login-form h2 {
        font-size: 2.4rem;
        margin: 8px 0;
    }

    .img img {
        width: 360px;
    }

    .avatar {
        width: 280px;
        height: 80px;
    }
}

@media screen and (max-width: 968px) {
    .wave {
        display: none;
    }

    .img {
        display: none;
    }

    .login-container {
        grid-template-columns: 1fr;
    }

    .login-box {
        justify-content: center;
    }
}
