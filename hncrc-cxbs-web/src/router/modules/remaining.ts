import { $t } from '@/plugins/i18n';

const Layout = () => import('@/layout/index.vue');

export default [
    {
        path: '/login',
        name: 'Login',
        component: () => import('@/views/login/index.vue'),
        meta: {
            title: $t('menus.hslogin'),
            showLink: false,
            rank: 101,
        },
    },
    {
        path: '/redirect',
        component: Layout,
        meta: {
            title: $t('status.hsLoad'),
            showLink: false,
            rank: 102,
        },
        children: [
            {
                path: '/redirect/:path(.*)',
                name: 'Redirect',
                component: () => import('@/layout/redirect.vue'),
            },
        ],
    },
    // 下面是一个无layout菜单的例子（一个全屏空白页面），因为这种情况极少发生，所以只需要在前端配置即可（配置路径：src/router/modules/remaining.ts）
    // {
    //     path: '/empty',
    //     name: 'Empty',
    //     component: () => import('@/views/empty/index.vue'),
    //     meta: {
    //         title: $t('menus.hsempty'),
    //         showLink: false,
    //         rank: 103,
    //     },
    // },
    {
        path: '/welcome/seedling',
        meta: {
            icon: 'informationLine',
            showLink: false,
            title: '诚信苗木超市',
        },
        component: () => import('@/views/welcome/zoneSeedling/index.vue'),
    },
    {
        path: '/welcome/oilTea',
        meta: {
            icon: 'informationLine',
            showLink: false,
            title: '诚信油茶',
        },
        component: () => import('@/views/welcome/zoneOilTea/index.vue'),
    },
    {
        path: '/welcome/tea',
        meta: {
            icon: 'informationLine',
            showLink: false,
            title: '诚信茶叶',
        },
        component: () => import('@/views/welcome/zoneTea/index.vue'),
    },
    {
        path: '/welcome/notice',
        meta: {
            icon: 'informationLine',
            showLink: false,
            title: '首页 - 通知详情',
        },
        component: () => import('@/views/welcome/notice/index.vue'),
    },
    {
        path: '/welcome/supportNotice',
        meta: {
            icon: 'informationLine',
            showLink: false,
            title: '首页 - 涉企扶持政策详情',
        },
        component: () => import('@/views/welcome/supportNotice/index.vue'),
    },
    {
        path: '/welcome',
        meta: {
            icon: 'informationLine',
            showLink: false,
            title: '首页',
        },
        component: () => import('@/views/welcome/index.vue'),
    },
] satisfies Array<RouteConfigsTable>;
