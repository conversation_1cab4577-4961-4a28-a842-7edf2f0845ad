import { $t } from '@/plugins/i18n';
const Layout = () => import('@/layout/index.vue');

export default {
    path: '/',
    name: 'Home',
    component: Layout,
    redirect: '/welcome',
    meta: {
        rank: 0,
        icon: 'homeFilled',
        title: $t('menus.hshome'),
    },
    children: [
        {
            path: '/archives',
            name: 'archives',
            component: () => import('@/views/archives/index.vue'),
            meta: {
                title: '企业信用档案-详情',
                hiddenTag: true,
                showLink: false,
            },
        },
        {
            path: '/overviews',
            name: 'overviews',
            component: () => import('@/views/overviews/index.vue'),
            meta: {
                title: '数据总览',
                icon: 'dashboardFilled',
                showLink: false,
            },
        },
        {
            path: '/oilTeaSubject/detail',
            name: 'OilTeaSubjectDetail',
            component: () => import('@/views/oilTeaSubject/oilTeaSubjectDetail.vue'),
            meta: {
                title: '油茶经营主体信息',
                // icon: 'dashboardFilled',
                showLink: false,
                hiddenTag: true,
            },
        },
        {
            path: '/teaSubject/detail',
            name: 'TeaSubjectDetail',
            component: () => import('@/views/teaSubject/teaSubjectDetail.vue'),
            meta: {
                title: '茶叶经营主体信息',
                // icon: 'dashboardFilled',
                showLink: false,
                hiddenTag: true,
            },
        },
        {
            path: '/seedlingSubject/detail',
            name: 'SeedlingSubjectDetail',
            component: () => import('@/views/seedlingSubject/seedlingSubjectDetail.vue'),
            meta: {
                title: '苗木经营主体档案信息',
                showLink: false,
                hiddenTag: true,
            },
        },
    ],
} satisfies RouteConfigsTable;
