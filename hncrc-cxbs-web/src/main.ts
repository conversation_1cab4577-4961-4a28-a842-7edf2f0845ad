// 引入重置样式
import './style/reset.scss';
// 导入公共样式
import './style/index.scss';
// 一定要在main.ts中导入tailwind.css，防止vite每次hmr都会请求src/style/index.scss整体css文件导致热更新慢的问题
import './style/tailwind.css';
import 'element-plus/dist/index.css';
// 导入字体图标
import './assets/iconfont/iconfont.js';
import './assets/iconfont/iconfont.css';

import { MotionPlugin } from '@vueuse/motion';
import { createApp, type Directive } from 'vue';
import WujieVue from 'wujie-vue3';

import CommonTable from '@/components/CommonTable/index.vue';
import ListContainer from '@/components/ListContainer/index.vue';
import { useEcharts } from '@/plugins/echarts';
import { useElementPlus } from '@/plugins/elementPlus';
import { useI18n } from '@/plugins/i18n';
import { setupStore } from '@/store';
import { injectResponsiveStorage } from '@/utils/responsive';

import App from './App.vue';
import { bootstrapReport } from './bootstrap/bootstrapReport';
import { getPlatformConfig } from './config';
import router from './router';

bootstrapReport();
const app = createApp(App);

// 自定义指令
import * as directives from '@/directives';
Object.keys(directives).forEach((key) => {
    app.directive(key, (directives as { [key: string]: Directive })[key]);
});

// 全局注册@iconify/vue图标库
import { FontIcon, IconifyIconOffline, IconifyIconOnline } from './components/ReIcon';
app.component('IconifyIconOffline', IconifyIconOffline);
app.component('IconifyIconOnline', IconifyIconOnline);
app.component('FontIcon', FontIcon);
app.component('ListContainer', ListContainer);
app.component('CommonTable', CommonTable);

// 全局注册vue-tippy
import 'tippy.js/dist/tippy.css';
import 'tippy.js/themes/light.css';

import VueTippy from 'vue-tippy';
app.use(VueTippy);

getPlatformConfig(app).then(async (config) => {
    setupStore(app);
    app.use(router);
    await router.isReady();
    injectResponsiveStorage(app, config);
    app.use(MotionPlugin).use(useI18n).use(useElementPlus).use(useEcharts).use(WujieVue);
    app.mount('#app');
});
