<template>
    <section class="custom-container">
        <ListHeader :title="title" />
        <section class="list-content">
            <section class="select-form">
                <slot name="searchForm" />
            </section>
            <slot />
        </section>
    </section>
</template>
<script>
import ListHeader from './header.vue';

export default {
    components: {
        ListHeader,
    },
    props: {
        title: {
            default: '',
            type: String,
        },
    },
};
</script>
<style scoped lang="less">
.custom-container {
    background-color: #fff;

    .list-content {
        // min-height: 200px;
        width: 100%;
        padding: 20px;
        .select-form {
            display: flex;
            margin-bottom: 12px;
        }
    }
}
</style>
