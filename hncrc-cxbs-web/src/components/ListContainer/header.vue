<template>
    <div class="bg-w">
        <div class="query-main">
            <div class="title">
                {{ title }}
            </div>
            <slot />
        </div>
    </div>
</template>

<script>
export default {
    name: 'ListHeader',
    props: {
        divider: {
            type: Boolean,
            default: true,
        },
        title: {
            type: String,
            default: '',
        },
    },
};
</script>
<style lang="less" scoped>
.bg-w {
    background-color: #fff;
}
.query-main {
    padding: 0 20px;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e9e9e9;
    height: 46px;
    .title {
        font-size: 14px;
        /* font-family: Microsoft YaHei; */
        font-weight: 500;
        color: #000000;
    }
}
</style>
