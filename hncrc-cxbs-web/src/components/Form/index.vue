<script setup lang="ts" generic="T">
import type { FormInstance } from 'element-plus';
import { reactive, useTemplateRef } from 'vue';

import CustomComponent from '@/components/CustomComponent/index.vue';
import { mergePropsAndAttrs } from '@/hooks/components';

import { defaultProps } from './config';
import { useFormatItems } from './hook';
import type { FormProps } from './type';

const props = withDefaults(defineProps<FormProps>(), defaultProps);
const attrs = mergePropsAndAttrs(props);
const emit = defineEmits<{
    change: [AnyObject];
    submit: [AnyObject];
    refresh: [];
}>();
const items = useFormatItems(props);
const ElFormRef = useTemplateRef<FormInstance>('ElFormRef');

/** 表单数据 */
const formData = reactive(
    Object.fromEntries(items.value.map((item) => [item.formItem.prop, item.initialValue])),
);
/** 通过此函数改变 form里的某项数据 */
function assign(key: string, value: any) {
    Object.assign(formData, {
        [key]: value,
    });
}

/** 提交表单 */
function submit() {
    return ElFormRef.value
        ?.validate()
        .then(emitSubmit)
        .catch((err) => {
            // console.log(err)
        });
}
/** 重置表单 */
function reset() {
    ElFormRef.value?.resetFields();
    emitSubmit();
}
/** 发送 submit 事件 */
function emitSubmit() {
    // 处理非日期范围字段
    const normalFields = Object.fromEntries(
        props.items
            .filter((item) => !item.dateRangeFields)
            .map((item) => [
                item.formItem.prop,
                item.formatter
                    ? item.formatter(formData[item.formItem.prop])
                    : formData[item.formItem.prop],
            ]),
    );

    // 处理日期范围字段
    const dateRangeFields = props.items
        .filter((item) => item.is === 'date-picker' && item.props.type === 'daterange')
        .reduce((acc, item) => {
            if (item.dateRangeFields && formData[item.formItem.prop]) {
                const [startField, endField] = item.dateRangeFields;
                const [startValue, endValue] = formData[item.formItem.prop];
                acc[startField] = startValue;
                acc[endField] = endValue;
            }
            return acc;
        }, {});

    const formatData = {
        ...normalFields,
        ...dateRangeFields,
    };

    emit('submit', formatData);
}

defineExpose({
    submit,
    reset,
    assign,
});
</script>

<template>
    <el-form v-if="items.length" ref="ElFormRef" v-bind="attrs" :model="formData">
        <div class="form-box">
            <div
                v-for="item in items"
                :key="item.formItem.prop"
                class="form-box-item"
                :style="{
                    width: item.width,
                }"
            >
                <el-form-item class="w-full" v-bind="item.formItem" labelWidth="90">
                    <slot
                        :name="item.formItem.prop"
                        :formData="formData"
                        :props="item.props"
                        :modelValue="formData[item.formItem.prop]"
                        :set="(value: any) => assign(item.formItem.prop, value)"
                    >
                        <!-- @vue-ignore -->
                        <CustomComponent
                            :is="item.is"
                            v-model="formData[item.formItem.prop]"
                            :props="item.props"
                        />
                    </slot>
                </el-form-item>
            </div>
            <div class="form-box-item footer">
                <el-form-item>
                    <slot name="footer" :submit :reset>
                        <el-button type="primary" @click="submit">搜索</el-button>
                        <el-button @click="reset">重置</el-button>
                    </slot>
                </el-form-item>
            </div>
        </div>
    </el-form>
</template>

<style scoped>
.el-form-item {
    margin-bottom: 0px !important;
    margin-right: 0px !important;
}
.form-box {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;
}
.footer {
    margin-left: auto;
}
</style>
