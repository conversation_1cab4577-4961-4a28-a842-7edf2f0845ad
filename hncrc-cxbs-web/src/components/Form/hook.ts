import { cloneDeep } from 'lodash-es';
import { computed } from 'vue';

import { getCssUnit } from '@/utils/css';

import type { FormProps } from './type';
import { initPlaceholder, initRules } from './util';

export function useFormatItems(props: FormProps) {
    return computed(() => {
        return cloneDeep(props.items || []).map((item) => {
            // 若没有props 则初始化空对象
            item.props || (item.props = {});

            // 初始化提示
            Object.assign(item.props, {
                placeholder: initPlaceholder(item),
            });

            // 初始化校验规则
            item.formItem.rules = initRules(item);

            // 初始化宽度
            item.width = getCssUnit(item.width || props.itemWidth);

            return item;
        });
    });
}
