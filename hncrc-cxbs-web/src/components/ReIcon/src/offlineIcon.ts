import { addIcon } from '@iconify/vue/dist/offline';
import Edit from '@iconify-icons/ep/edit';
import Guide from '@iconify-icons/ep/guide';
import Histogram from '@iconify-icons/ep/histogram';
import HomeFilled from '@iconify-icons/ep/home-filled';
import Lollipop from '@iconify-icons/ep/lollipop';
import Menu from '@iconify-icons/ep/menu';
import Monitor from '@iconify-icons/ep/monitor';
import SetUp from '@iconify-icons/ep/set-up';
import Role from '@iconify-icons/ri/admin-fill';
import FlUser from '@iconify-icons/ri/admin-line';
import Artboard from '@iconify-icons/ri/artboard-line';
import Card from '@iconify-icons/ri/bank-card-line';
import Tag from '@iconify-icons/ri/bookmark-2-line';
import CheckboxCircleLine from '@iconify-icons/ri/checkbox-circle-line';
import Info from '@iconify-icons/ri/file-info-line';
import Ppt from '@iconify-icons/ri/file-ppt-2-line';
import Dept from '@iconify-icons/ri/git-branch-line';
import InformationLine from '@iconify-icons/ri/information-line';
import ListCheck from '@iconify-icons/ri/list-check';
import Search from '@iconify-icons/ri/search-line';
import Setting from '@iconify-icons/ri/settings-3-line';
import Table from '@iconify-icons/ri/table-line';
import TerminalWindowLine from '@iconify-icons/ri/terminal-window-line';
/**
 * 这里存放本地图标，在 src/layout/index.vue 文件中加载，避免在首启动加载
 */
// 本地菜单图标，后端在路由的icon中返回对应的图标字符串并且前端在此处使用addIcon添加即可渲染菜单图标
import UbuntuFill from '@iconify-icons/ri/ubuntu-fill';
addIcon('ubuntuFill', UbuntuFill);
addIcon('ep:menu', Menu);
addIcon('edit', Edit);
addIcon('informationLine', InformationLine);
addIcon('setUp', SetUp);
addIcon('terminalWindowLine', TerminalWindowLine);
addIcon('guide', Guide);
addIcon('homeFilled', HomeFilled);
addIcon('card', Card);
addIcon('listCheck', ListCheck);
addIcon('histogram', Histogram);
addIcon('ppt', Ppt);
addIcon('checkboxCircleLine', CheckboxCircleLine);
addIcon('ri:admin-line', FlUser);
addIcon('ri:admin-fill', Role);
addIcon('ri:settings-3-line', Setting);
addIcon('ri:git-branch-line', Dept);
addIcon('search', Search);
addIcon('ep:lollipop', Lollipop);
addIcon('ep:monitor', Monitor);
addIcon('ri:bookmark-2-line', Tag);
addIcon('table', Table);
addIcon('info', Info);
addIcon('artboard', Artboard);
