<template>
    <div :class="{ hidden: hidden }" class="pagination-container">
        <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="limit"
            :background="background"
            :layout="layout"
            :page-sizes="pageSizes"
            :total="Number(total)"
            v-bind="$attrs"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
    </div>
</template>

<script>
export default {
    name: 'CommonPagination',
    props: {
        total: {
            required: true,
            type: Number,
        },
        pageNum: {
            type: Number,
            default: 1,
        },
        pageSize: {
            type: Number,
            default: 20,
        },
        pageSizes: {
            type: Array,
            default() {
                return [10, 20, 30, 50];
            },
        },
        layout: {
            type: String,
            default: 'total, sizes, prev, pager, next, jumper',
        },
        background: {
            type: Boolean,
            default: true,
        },
        hidden: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        currentPage: {
            get() {
                return this.pageNum;
            },
            set(val) {
                this.$emit('update:pageNum', val);
            },
        },
        limit: {
            get() {
                return this.pageSize;
            },
            set(val) {
                this.$emit('update:pageSize', val);
            },
        },
    },
    methods: {
        handleSizeChange(val) {
            this.currentPage = 1;
            this.$emit('pagination', {
                pageNum: this.currentPage,
                pageNo: this.currentPage,
                pageSize: val,
            });
        },
        handleCurrentChange(val) {
            this.$emit('pagination', { pageNum: val, pageNo: val, pageSize: this.pageSize });
        },
    },
};
</script>

<style scoped>
.pagination-container {
    padding: 15px 16px;
    background: #fff;
}

.pagination-container.hidden {
    display: none;
}
</style>
