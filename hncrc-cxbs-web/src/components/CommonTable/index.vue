<template>
    <div id="screentable" v-loading="loading" class="common-table">
        <div v-if="isShowToolbar" class="toolbar">
            <div class="toolbar-option">
                <div class="slot-option">
                    <slot name="toolbar-option" />
                </div>
            </div>
        </div>
        <div
            v-if="selectedTotal !== 0 && isShowSelectionInfo"
            :class="isShowToolbar ? 'table-alert' : 'table-alert  table-alert-top'"
        >
            <div class="alert-info">
                <div style="marginright: 10px">
                    <i class="el-icon-info them-color" />
                </div>
                <div class="alert-info-content">
                    <div class="alert-info-message">
                        <span class="alert-info-message-item"> 已选{{ selectedTotal }}项 </span>
                        <slot name="alert-message" :scope="multipleSelection" />
                    </div>
                    <div class="alert-info-option">
                        <slot name="alert-option" :scope="multipleSelection" />
                        <el-button type="text" @click="toggleSelection('clear')">清空</el-button>
                    </div>
                </div>
            </div>
        </div>
        <el-table
            ref="table"
            class="table"
            :height="height"
            :data="tableData"
            v-bind="$attrs"
            @selection-change="handleSelectionChange"
            @row-click="rowClick"
        >
            <el-table-column
                v-if="isShowSelection"
                type="selection"
                fixed="left"
                :selectable="selectable"
            />
            <template v-for="(item, index) in cloneTableColumns">
                <el-table-column
                    v-if="item.isShow"
                    :key="index + item.prop"
                    :type="item.type && item.type !== 'selection' ? item.type : void 0"
                    :prop="item.prop"
                    :label="item.label"
                    :width="item.width"
                    :min-width="item.minWidth"
                    :sortable="item.sortable"
                    :show-overflow-tooltip="item.ellipsis"
                    :fixed="item.fixed"
                    :align="item.align"
                    :class-name="item.scroll ? `scroll-x ${item.class}` : item.class"
                    v-bind="item.props"
                >
                    <template #default="scope">
                        <span v-if="item.type === 'index'">{{ scope.$index + 1 }}</span>
                        <span v-else-if="item.type === 'date'">
                            {{
                                formatDate(
                                    Number(scope.row[item.prop]),
                                    item.dateFormat || 'YYYY-MM-DD',
                                )
                            }}
                        </span>
                        <slot v-else-if="item.slot" :name="item.slot" :scope="scope" />
                        <span v-else>{{
                            item.formatter ? item.formatter(scope.row) : scope.row[item.prop]
                        }}</span>
                    </template>
                </el-table-column>
            </template>
        </el-table>
        <Pagination
            v-if="isShowPagination"
            class="pagination"
            v-bind="$attrs"
            @pagination="handlePageChange"
        />
    </div>
</template>

<script>
import { formatDate } from '@/utils/date';

import Pagination from './components/Pagination.vue';

export default {
    name: 'CommonTable',
    components: {
        Pagination,
    },
    props: {
        tableColumns: {
            required: true,
            type: Array,
        },
        columnsMixin: {
            type: Object,
            default: () => {},
        },
        tableData: {
            required: true,
            type: Array,
        },
        height: {
            type: String,
        },
        isShowToolbar: {
            type: Boolean,
            default: false,
        },
        isShowSelection: {
            type: Boolean,
            default: false,
        },
        isShowPagination: {
            type: Boolean,
            default: true,
        },
        isShowSelectionInfo: {
            type: Boolean,
            default: false,
        },
        isShowFilter: {
            type: Boolean,
            default: false,
        },
        loading: {
            type: Boolean,
            default: false,
        },
        defaultSelection: {
            type: Array,
            default: () => [],
        },
        selectable: {
            type: Function,
            default: () => true,
        },
    },
    data() {
        return {
            cloneTableColumns: [],
            multipleSelection: [],
            selectedTotal: 0,
            showSelect: false,
        };
    },
    watch: {
        defaultSelection: {
            handler(val) {
                this.toggleSelection();
                this.$nextTick(() => {
                    if (val.length) {
                        this.toggleSelection(val);
                    }
                });
            },
            immediate: false,
        },
        tableColumns: {
            handler() {
                this.reset();
            },
            immediate: false,
        },
    },
    created() {
        this.reset();
    },
    mounted() {
        this.toggleSelection(this.defaultSelection);
    },
    methods: {
        formatDate: formatDate,
        reset() {
            this.cloneTableColumns = this.tableColumns.map((item) => {
                item = Object.assign({}, this.columnsMixin, item);
                item.fixed = item.fixed ? item.fixed : false;
                item.isShow = item.isShow === false ? item.isShow : true;
                item.class = item.class ? item.class : '';
                return item;
            });
        },
        handlePageChange(page) {
            this.$emit('pageChange', page);
            this.$emit('refresh', page);
        },
        toggleSelection(rows) {
            if (rows && rows !== 'clear') {
                rows.forEach((row) => {
                    this.$refs.table.toggleRowSelection(row);
                });
            } else {
                let selection = [];
                if (rows === 'clear') {
                    selection = this.multipleSelection.filter((row) => !this.selectable(row));
                    this.showSelect = false;
                    this.$emit('filterSelected', selection, false);
                }
                this.$refs.table.clearSelection();
                selection.forEach((item) => {
                    this.$refs.table.toggleRowSelection(item, true);
                });
            }
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
            this.selectedTotal = this.multipleSelection.length;
        },
        rowClick(row) {
            if (this.isShowSelection && this.selectable(row)) {
                this.$refs.table.toggleRowSelection(row);
            }
        },
    },
};
</script>

<style lang="less" scoped>
.common-table {
    :deep(.el-table thead) {
        height: 55px;
    }
    :deep(.el-table tr) {
        height: 50px;
    }
    &:not(:root):-ms-full-screen {
        min-height: 100vh;
        padding: 20px;
        overflow: auto;
        background: #f0f2f5;
    }
    &:not(:root):-moz-full-screen {
        min-height: 100vh;
        padding: 20px;
        overflow: auto;
        background: #f0f2f5;
    }
    &:not(:root):-webkit-full-screen {
        min-height: 100vh;
        padding: 20px;
        overflow: auto;
        background: #f0f2f5;
    }
    &:not(:root):fullscreen {
        min-height: 100vh;
        padding: 20px;
        overflow: auto;
        background: #f0f2f5;
    }
    .toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 24px;
        background: #fff;
        .toolbar-option {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            flex-basis: 100%;
            .default-option {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                .default-option-item {
                    margin: 0 8px;
                    &:last-child {
                        margin-right: 0;
                    }
                    .item {
                        font-size: 14px;
                        color: #333;
                    }
                    :v-deep(.dropdown-link),
                    :v-deep(.svg-icon) {
                        cursor: pointer;
                    }
                }
            }
        }
    }
    .table-alert {
        padding: 0 24px 16px;
        background: #fff;
        border: none;
        .alert-info {
            position: relative;
            display: flex;
            align-items: center;
            box-sizing: border-box;
            padding: 0 10px;
            margin: 0;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
            line-height: 1.5715;
            list-style: none;
            word-wrap: break-word;
            border-radius: 2px;
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            .icon {
                color: #409eff;
            }
            .alert-info-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
                flex: 1 1;
                .alert-info-message {
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    .alert-info-message-item {
                        margin-right: 5px;
                    }
                }
            }
        }
    }
    .table-alert-top {
        padding: 0;
        .alert-info {
            padding: 0 25px;
        }
    }
    .table.el-table {
        :v-deep(td),
        :v-deep(th) {
            margin: 6px 8px;
            &:first-child {
                padding-left: 14px;
            }
            &.el-table-column--selection {
                .cell {
                    text-overflow: unset;
                }
            }
        }
        :v-deep(th) {
            color: rgba(0, 0, 0, 0.85);
            font-weight: 500;
            background: #fafafa;
        }
        :v-deep(.scroll-x .cell) {
            position: relative;
            overflow-x: auto;
            &::-webkit-scrollbar {
                width: 5px;
                height: 5px;
                scrollbar-arrow-color: gray;
            }
            &::-webkit-scrollbar-thumb {
                border-radius: 5px;
                -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
                background: rgba(0, 0, 0, 0.2);
                scrollbar-arrow-color: gray;
            }
            &::-webkit-scrollbar-track {
                -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
                border-radius: 0;
                background: rgba(0, 0, 0, 0.1);
            }
        }
    }
    .pagination {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin: 0;
    }
    .them-color {
        color: #468faf;
    }
}
</style>
