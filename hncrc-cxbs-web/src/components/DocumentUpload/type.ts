import type { UploadProps as ElementUploadProps } from 'element-plus';
export interface DocumentUploadProps extends /* @vue-ignore */ Partial<ElementUploadProps> {
    tip?: string;
    accept?: string;
    modelValue: string;
    acceptType?: string;
    maxSize?: number;
    maxFileCount?: number;
}

export interface DocumentUploadEmits {
    (e: 'update:modelValue' | 'upload-error', value: string): void;
}

export type UploadModelType = {
    key: string;
    url: string;
};

export type UploadModelValue = UploadModelType | UploadModelType[];
