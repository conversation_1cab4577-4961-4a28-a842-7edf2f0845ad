<template>
    <el-upload
        v-model="imgUrl"
        class="single-uploader"
        :show-file-list="false"
        list-type="picture-card"
        :http-request="uploadFile"
        :accept="acceptType"
        :tip="tip"
    >
        <template v-if="imgUrl">
            <video
                v-if="props.fileType === 'video'"
                :src="imgUrl"
                class="single-uploader__video"
                controls
            />
            <img v-else :src="imgUrl" class="single-uploader__image" />
        </template>
        <template v-else>
            <el-button type="primary">
                <el-icon><Plus /></el-icon>
                点击上传{{ props.fileType === 'video' ? '视频' : '图片' }}
            </el-button>
        </template>
    </el-upload>
</template>

<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue';
import { useVModel } from '@vueuse/core';
import { UploadRawFile, UploadRequestOptions } from 'element-plus';
import { ElMessage } from 'element-plus';
import { computed } from 'vue';

import { uploadFileNew } from '@/api/file';

import type { UploadProps } from './type';

const props = defineProps<UploadProps>();

const emit = defineEmits(['update:modelValue', 'upload-error']);
const imgUrl = useVModel(props, 'modelValue', emit);

// 根据文件类型设置accept属性
const acceptType = computed(() => {
    if (props.accept) return props.accept;
    return props.fileType === 'video' ? '.mp4,.webm,.ogg' : '.jpg,.jpeg,.png,.gif';
});

// 根据文件类型设置提示文本
const tip = computed(() => {
    if (props.tip) return props.tip;
    const maxSize = props.maxSize || 2;
    return props.fileType === 'video'
        ? `上传视频不能大于${maxSize}M`
        : `上传图片不能大于${maxSize}M`;
});

/**
 * 自定义文件上传
 */
async function uploadFile(options: UploadRequestOptions): Promise<any> {
    try {
        const data = await uploadFileNew(options.file);
        imgUrl.value = data;
    } catch (error: any) {
        emit('upload-error', error.message);
        throw error;
    }
}

/**
 * 限制用户上传文件的格式和大小
 */
function handleBeforeUpload(file: UploadRawFile) {
    const maxSize = (props.maxSize || 2) * 1024 * 1024;
    if (file.size > maxSize) {
        ElMessage.warning(tip.value);
        return false;
    }
    return true;
}
</script>

<style scoped lang="scss">
.single-uploader {
    overflow: hidden;
    cursor: pointer;
    border: 1px var(--el-border-color) solid;
    border-radius: 6px;

    &:hover {
        border-color: var(--el-color-primary);
    }

    &__image {
        display: block;
        width: 178px;
        height: 178px;
    }

    &__video {
        display: block;
        width: 178px;
        height: 178px;
        object-fit: cover;
    }
}
</style>
