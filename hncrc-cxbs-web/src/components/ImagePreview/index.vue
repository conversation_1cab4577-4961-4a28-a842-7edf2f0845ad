<template>
    <div>
        <el-image
            v-for="(imageUrl, index) in imageUrls"
            :key="index"
            style="width: 100px; height: 100px; margin-right: 10px; border-radius: 4px"
            :src="imageUrl"
            :zoom-rate="1.2"
            :max-scale="3"
            :min-scale="0.5"
            :preview-src-list="imageUrls"
            :initial-index="index"
            fit="cover"
        />
    </div>
</template>

<script setup lang="ts">
import { ImagePreviewProps } from './type';

const props = defineProps<ImagePreviewProps>();
</script>

<style scoped></style>
