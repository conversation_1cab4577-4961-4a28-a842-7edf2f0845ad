## 基本使用

```vue
<script setup lang="ts">
import { ref } from 'vue';
import { defineDialogPage } from '@/components/DialogPage/hook';

const visible = ref(false);

const dialogPageProps = defineDialogPage({
    // 弹窗是否可见
    visible: visible.value,
    // 表单配置
    formItems: [
        {
            is: 'input',
            formItem: {
                prop: 'name',
                label: '名称',
                rules: [{ required: true, message: '请输入名称' }],
            },
        },
    ],
    // 提交接口
    service: (data) => UserAPI.create(data),
    // 获取详情接口（编辑时使用）
    detailService: (id) => UserAPI.getDetail(id),
});

function openDialog(type = 'add', data?: any) {
    dialogPageProps.type = type;
    dialogPageProps.initialData = data;
    visible.value = true;
}
</script>

<template>
    <DialogPage
        v-model:visible="visible"
        v-bind="dialogPageProps"
        @success="handleSuccess"
    />
</template>
```

## 扩展插槽
可以使用Form组件的所有插槽

```vue
<template>
    <DialogPage v-bind="dialogPageProps">
        <template #footer="{ submit }">
            <div>
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="submit">确定</el-button>
            </div>
        </template>
    </DialogPage>
</template>
``` 