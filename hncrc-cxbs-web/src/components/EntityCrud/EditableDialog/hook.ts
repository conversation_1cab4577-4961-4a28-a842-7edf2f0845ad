import { useEditableForm } from '@/components/EditableForm/hook';

import type { EditableItem } from '../type';
import { mapEntityFormItemToEditableFormItem } from '../util';

export interface UseDialogOptions<TData, TFetchedData> {
    /** 标题 */
    title: string;
    /** 表单项配置 */
    formItems: EditableItem<TData | TFetchedData>;
    /** 初始数据服务 */
    initialDataService?: (originalData: TData) => Promise<TFetchedData>;
    /** 新增请求 */
    submitRequest?: (data: TData | TFetchedData) => Promise<any>;
    /** 成功回调 */
    onSuccess?: (data?: TData | TFetchedData) => void;
    /** 表单宽度 */
    width?: string | number;
}

export function useCreatableDialog<T>(options: UseDialogOptions<T, T>) {
    const { formProps, newEditable } = useEditableForm({
        customTitle: options.title,
        addRequest: options.submitRequest,
        onSuccess: options.onSuccess,
        width: options.width,
        formItems: Object.keys(options.formItems)
            .filter((key) => options.formItems[key].type !== 'id')
            .map((key) => mapEntityFormItemToEditableFormItem(key, options.formItems[key])),
    });

    return {
        formProps,
        showDialog: newEditable,
    };
}

export function useEditableDialog<TUpdate, TUpdateableData>(
    options: UseDialogOptions<TUpdate, TUpdateableData>,
) {
    const { formProps, editEditable } = useEditableForm({
        customTitle: options.title,
        initialDataService: options.initialDataService,
        editRequest: options.submitRequest,
        onSuccess: options.onSuccess,
        width: options.width ?? '800px',
        formItems: Object.keys(options.formItems).map((key) =>
            mapEntityFormItemToEditableFormItem(key, options.formItems[key]),
        ),
    });

    return {
        formProps,
        showDialog: editEditable,
    };
}
