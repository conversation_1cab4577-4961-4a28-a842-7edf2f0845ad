import { defineSearchPage } from '@/components/SearchPage/hook';

import { useGlobalConfig } from '../config';
import type { EntityListProps } from '../type';

const globalConfig = useGlobalConfig();
const searchIsMapper = {
    select: 'select',
    date: 'date-picker',
    'date-range': 'date-picker',
    'select-remote': 'select-remote',
};

const hasSelectionColumn = (props: UseSearchableTableOptions<any>) => {
    // 如果没有设定hasSelectionColumn，则根据batchDeleteService是否存在来判断是否显示选择列
    return props.hasSelectionColumn ?? props.hasBatchService ?? false;
};

export type UseSearchableTableOptions<T> = EntityListProps<T> & {
    tableOperations?: any[];
    hasBatchService?: boolean;
    hasExternalRowOperation?: boolean;
};

export function useSearchableTable<T>(props: UseSearchableTableOptions<T>) {
    return defineSearchPage({
        service: props.listFetchService,
        formItems:
            props.filterFormItems == null
                ? []
                : Object.keys(props.filterFormItems).map((key) => ({
                      is: searchIsMapper[props.filterFormItems[key].type] ?? 'input',
                      formItem: {
                          prop: key,
                          label: props.filterFormItems[key].label,
                      },
                      props: {
                          ...(props.filterFormItems[key].type === 'select'
                              ? { options: props.filterFormItems[key].options }
                              : {}),
                          ...(props.filterFormItems[key].type === 'date' ? { type: 'date' } : {}),
                          ...(props.filterFormItems[key].type === 'date-range'
                              ? { type: 'daterange' }
                              : {}),
                          ...(props.filterFormItems[key].type === 'select-remote'
                              ? {
                                    filterable: true,
                                    remote: true,
                                    reserveKeyword: true,
                                    remoteShowSuffix: true,
                                    remoteService: props.filterFormItems[key].remoteFetchService,
                                }
                              : {}),
                          placeholder:
                              props.filterFormItems[key].placeholder ??
                              `请输入${props.filterFormItems[key].label}`,
                      },
                      // 当为日期范围选择器时，宽度为320
                      width: props.filterFormItems[key].type === 'date-range' ? 320 : 250,
                      // 日期选择器配置时两个字段名
                      ...(props.filterFormItems[key].type === 'date-range'
                          ? { dateRangeFields: props.filterFormItems[key].dateRangeFields }
                          : {}),
                  })),
        operations: props.tableOperations,
        tableColumns: {
            ...(hasSelectionColumn(props) ? { selection: {} } : {}),
            ...(props.hasIndexColumn ? { index: {} } : {}),
            ...Object.entries(props.tableColumns).reduce(
                (acc, [key, value]) => ({
                    ...acc,
                    [key]:
                        typeof value === 'string'
                            ? { label: value, emptyText: globalConfig.tableDataEmptyText }
                            : {
                                  ...value,
                                  emptyText: value.emptyText ?? globalConfig.tableDataEmptyText,
                              },
                }),
                {},
            ),
            ...(props.hasExternalRowOperation ||
            props.tableColumns.operations ||
            (props.rowOperations && props.rowOperations.length > 0)
                ? { operations: props.tableColumns.operations ?? {} }
                : {}),
        },
    });
}
