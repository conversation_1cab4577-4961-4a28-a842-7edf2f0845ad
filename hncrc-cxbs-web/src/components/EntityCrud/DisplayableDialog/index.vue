<script setup lang="ts">
import { ElDialog, type FormInstance } from 'element-plus';
import { reactive, useTemplateRef, watch } from 'vue';

import { DisplayableItem } from './type';

type DisplayableDialogProps = {
    title: string;
    width: string;
    visible: boolean;
    displayData: any;
    displayItems: DisplayableItem[];
};

const props = withDefaults(defineProps<DisplayableDialogProps>(), {
    title: '详情',
    width: '600px',
    visible: false,
});

const emit = defineEmits<{
    'update:visible': [visible: boolean];
    refresh: [];
    close: [];
}>();

const ElFormRef = useTemplateRef<FormInstance>('ElFormRef');
const displayDataInternal = reactive<Record<string, any>>({});

// 监听visible变化，获取详情
watch(
    () => props.visible,
    async (visible) => {
        if (visible) {
            displayDataInternal.value = props.displayData;
        }
    },
);

function closeDialog() {
    reset();
    emit('update:visible', false);
    emit('close');
}

function reset() {
    ElFormRef.value?.resetFields();
}
</script>

<template>
    <ElDialog
        :model-value="visible"
        :title="title"
        :width="width"
        @update:model-value="closeDialog"
    >
        <el-descriptions :column="1" border class="descriptions">
            <el-descriptions-item
                v-for="item in displayItems"
                :key="item.label"
                :label="item.label"
                label-align="right"
                :span="1"
            >
                <span v-if="item.type === 'images'" style="display: flex; gap: 10px">
                    <el-image
                        v-for="(image, index) in Array.isArray(displayDataInternal.value[item.prop])
                            ? displayDataInternal.value[item.prop]
                            : [displayDataInternal.value[item.prop]]"
                        :key="image.url"
                        :zoom-rate="1.2"
                        :max-scale="2.5"
                        :min-scale="0.25"
                        :src="image.url"
                        style="height: 100px"
                        :preview-src-list="
                            (Array.isArray(displayDataInternal.value[item.prop])
                                ? displayDataInternal.value[item.prop]
                                : [displayDataInternal.value[item.prop]]
                            ).map((image) => image.url)
                        "
                        :initial-index="index"
                        fit="cover"
                    />
                </span>
                <span v-else-if="item.type === 'documents'">
                    <div
                        v-for="doc in Array.isArray(displayDataInternal.value[item.prop])
                            ? displayDataInternal.value[item.prop]
                            : [displayDataInternal.value[item.prop]]"
                        :key="doc.url"
                    >
                        <!-- TODO -->
                    </div>
                </span>
                <span v-else>
                    <div>
                        <span v-if="displayDataInternal.value[item.prop]">{{
                            displayDataInternal.value[item.prop]
                        }}</span>
                        <span v-else class="placeholder">{{ item.placeholder }}</span>
                    </div>
                </span>
            </el-descriptions-item>
        </el-descriptions>
        <template #footer>
            <el-button @click="closeDialog">关闭</el-button>
        </template>
    </ElDialog>
</template>

<style scoped>
.placeholder {
    color: #eeeeee;
}
:deep(.el-descriptions__label) {
    width: 180px;
}

.el-form-item {
    margin-bottom: 0px !important;
    margin-right: 0px !important;
}
.form-box {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;
}
.footer {
    margin-left: auto;
}
</style>
