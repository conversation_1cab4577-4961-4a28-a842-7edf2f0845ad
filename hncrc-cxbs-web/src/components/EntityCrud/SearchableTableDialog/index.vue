<script setup lang="ts">
import { ElDialog } from 'element-plus';
import { watch } from 'vue';

import SearchPage from '@/components/SearchPage/index.vue';

import { useSearchableTable } from '../SearchableTable/hook';
import type { SearchableTableDialogProps } from './type';

const props = defineProps<SearchableTableDialogProps<any>>();

const emit = defineEmits<{
    'update:visible': [visible: boolean];
    refresh: [];
    cancel: [];
}>();

// 监听visible变化，获取详情
watch(
    () => props.visible,
    async (visible) => {
        if (visible) {
            // TODO anything need to init
        }
    },
);

function closeDialog() {
    emit('update:visible', false);
    emit('cancel');
}

const enturyListProps = useSearchableTable<any>(props);
</script>

<template>
    <ElDialog
        :model-value="props.visible"
        :title="props.title"
        :width="props.width"
        @update:model-value="closeDialog"
    >
        <SearchPage v-bind="enturyListProps" />
    </ElDialog>
</template>

<style scoped></style>
