import { inject } from 'vue';

export const GLOBAL_CONFIG_KEY = Symbol('ENTITY_CRUD_GLOBAL_CONFIG_KEY');

export type GlobalConfig = {
    // 表格数据为空时显示的文本
    tableDataEmptyText?: string;
};

export const defaultGlobalConfig: GlobalConfig = {
    tableDataEmptyText: '-',
};

export const useGlobalConfig = (): GlobalConfig => {
    const config = inject(GLOBAL_CONFIG_KEY);

    if (process.env.NODE_ENV !== 'production' && !config) {
        console.warn('[EntityCrud] No global config provided. Using default config instead.');
    }

    return config || defaultGlobalConfig;
};
