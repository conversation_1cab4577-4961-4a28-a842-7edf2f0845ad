import type { InfoTableColumnItem, InfoTableColumnWithConfig } from './type';

/** 生成带有grid配置的表格列 */
export const generateColumnsWithGridConfig = <T extends Record<string, any>>(
    columnCount: number,
    columns: InfoTableColumnItem<T>[],
): Array<InfoTableColumnWithConfig<T>> => {
    let startIdx = 1;
    let endIdx = 1;
    const resColumns = [] as Array<InfoTableColumnWithConfig<T>>;
    // 所在行索引
    let rowIndex = 1;
    // 所在列索引
    let columnIndex = 1;

    columns.forEach((item) => {
        // 超出最大列数，需要换行
        if (item.span + startIdx > columnCount + 1) {
            startIdx = 1;
            endIdx = startIdx + item.span;
            // 更新行索引
            rowIndex += 1;
            // 更新列索引
            columnIndex = 1;
            // 计算当前 value 所在行末尾线
            const valueRowEnd = rowIndex * 4;

            resColumns.push({
                ...item,
                config: {
                    '--grid-column-start': startIdx,
                    '--grid-column-end': endIdx,
                    '--grid-row-start': valueRowEnd - 3,
                    '--grid-row-end': valueRowEnd - 2,
                    '--value-grid-row-start': valueRowEnd - 1,
                    '--value-grid-row-end': valueRowEnd,
                    rowIndex,
                    columnIndex,
                },
            });
            /** 更新startIdx */
            startIdx = endIdx;
        } else {
            endIdx += item.span;
            // 计算当前 value 所在行末尾线
            const valueRowEnd = rowIndex * 4;
            resColumns.push({
                ...item,
                config: {
                    '--grid-column-start': startIdx,
                    '--grid-column-end': endIdx,
                    '--grid-row-start': valueRowEnd - 3,
                    '--grid-row-end': valueRowEnd - 2,
                    '--value-grid-row-start': valueRowEnd - 1,
                    '--value-grid-row-end': valueRowEnd,
                    rowIndex,
                    columnIndex,
                },
            });
            startIdx = endIdx;
            endIdx = startIdx;
            // 更新列索引
            columnIndex += 1;
        }
    });
    return resColumns;
};

/** 判断是否有值 */
export const hasValue = (value: string | number | null | undefined) => {
    return value !== null && value !== undefined && value !== '';
};

/** 判断是否是函数 */
export const isFunction = (fn: any) => {
    return Object.prototype.toString.call(fn) === '[object Function]';
};
