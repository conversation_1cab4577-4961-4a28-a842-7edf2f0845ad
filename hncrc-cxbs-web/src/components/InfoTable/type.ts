import type { VNode } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

/** 信息表格的Props */
export interface InfoTableProps<T extends Record<string, any>> {
    /** 表格数据，是一个对象 */
    data: PropType<T>;
    /** 表格标题 */
    title?: string;
    /** 列的数量 */
    columnCount?: number;
    /** 表格列配置 */
    columns: Array<InfoTableColumnItem<T>>;
    /** label 位置 */
    labelPosition?: 'left' | 'top' | 'bottom';
    /** label对齐方式 */
    labelAlign?: 'left' | 'center' | 'right';
    /** label宽度 */
    labelWidth?: string;
    /** label的高度 */
    labelHeight?: string;
    /** label背景颜色 */
    labelBgColor?: string;
    /** 值为空时的文案 */
    cellEmptyText?: string;
}

/** 信息表格的表格列配置对象 */
export interface InfoTableColumnItem<T extends Record<string, any>> {
    /** 表格列的label */
    label: string;
    /** 表格列的key */
    key: keyof (T & { slotItem?: string });
    /** 表格列的宽度占比（内部使用el-row+el-col实现，所以span配置同el-col） */
    span?: number;
    /** 表格列的对齐方式 */
    align?: 'left' | 'center' | 'right';
    /**
     * label的对齐方式
     * "top"、"bottom" 仅在 labelPosition 为 "left" 时有效，且此时 labelAlign 无效
     */
    labelAlign?: 'left' | 'center' | 'right' | 'top' | 'bottom' | 'topCenter' | 'bottomCenter';
    /** 表格列的默认值 */
    initialValue?: T[keyof T] & null & string;
    /** 格式化函数，用于格式化表格列的值 */
    formatter?: (value: PropType<T[keyof T]>) => VNode | T[keyof T] | string;
}

/** 带grid配置的表格列 */
export interface InfoTableColumnWithConfig<T extends Record<string, any>>
    extends InfoTableColumnItem<T> {
    isHovered: boolean;
    showTooltip: boolean;
    isExpanded: boolean;
    config: {
        /** grid配置-列起始位置线 */
        '--grid-column-start': number;
        /** grid配置-列终止位置线 */
        '--grid-column-end': number;

        /** grid配置-行起始位置线 */
        '--grid-row-start': number;
        /** grid配置-行终止位置线 */
        '--grid-row-end': number;

        /** grid配置-值行起始位置线 */
        '--value-grid-row-start': number;
        /** grid配置-值行终止位置线 */
        '--value-grid-row-end': number;

        /** 所在行索引 */
        rowIndex: number;
        /** 所在列索引 */
        columnIndex: number;
    };
}

/** 信息表格的实例 */
export type InfoTableInstance = ComponentExposed<typeof import('./index.vue')>;
