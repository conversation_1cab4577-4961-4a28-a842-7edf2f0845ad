<template>
    <div class="info-table-demo">
        <!-- labelPosition=top 默认 -->
        <InfoTable title="这里是设置的标题" :column-count="4" :columns="tableColumn" :data="data" />
        <!-- labelPosition=left 默认 -->
        <InfoTable :column-count="4" label-position="left" :columns="tableColumn" :data="data">
            <template #title>
                <span>这里是自定义标题</span>
            </template>
        </InfoTable>
        <InfoTable
            :column-count="4"
            label-align="center"
            label-position="left"
            :columns="tableColumn"
            :data="data"
        >
            <template #title>
                <span>这里是自定义标题</span>
            </template>
        </InfoTable>
    </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';

import InfoTable from '../index.vue';
import { InfoTableColumnItem } from '../type';

interface DataItem {
    name: string;
    age: number;
    address: string;
    date: string;
    tags: string[];
}

/** 表格列配置  */
const tableColumn = reactive<InfoTableColumnItem<DataItem>[]>([
    {
        key: 'name',
        label: 'Name',
        span: 1,
    },
    {
        key: 'age',
        label: 'Age',
        span: 1,
    },
    {
        key: 'address',
        label: 'Address',
        span: 2,
    },
    {
        key: 'date',
        label: 'Date',
        span: 1,
    },
    {
        key: 'date',
        label: 'Date',
        span: 1,
        formatter: (value) => new Date(Number(value)).toLocaleDateString(),
    },
    {
        key: 'tags',
        label: 'Tags',
        span: 2,
    },
]);

/** 数据 */
const data = reactive<DataItem>({
    name: 'John Brown',
    age: 32,
    address: 'New York No. 1 Lake Park'.repeat(10),
    date: Date.now().toString(),
    tags: ['nice', 'developer'],
});
</script>

<style scoped>
.info-table-demo {
    width: 100%;
}
</style>
