<template>
    <section :style="cssVariables" class="info-table">
        <slot v-if="title || slots.title" name="title">
            <div v-if="title" class="info-table_title">{{ title }}</div>
        </slot>
        <div v-if="labelPosition === 'top'" class="info-table-content">
            <!-- 遍历每一列配置 -->
            <div v-for="item in tableColumn" :key="item.key" class="info-table-column">
                <!-- 渲染标签 -->
                <div class="info-table-item info-table_label_item">
                    <div class="item-label">{{ item.label }}</div>
                </div>
                <!-- 渲染所有对应的值 -->
                <div
                    v-for="(rowData, rowIndex) in normalizedData"
                    :key="rowIndex"
                    class="info-table-value-container"
                >
                    <div
                        class="info-table-item info-table_value_item"
                        :data-row-first="item.config['--grid-row-start'] === 1"
                        :data-cell-first="item.config['--grid-column-start'] === 1"
                        :style="item.config"
                    >
                        <el-tooltip
                            v-if="(getValueText(item, rowData) || '-') !== '-'"
                            :popper-append-to-body="false"
                            :content="getValueText(item, rowData)"
                            placement="bottom"
                            popper-class="box-tooltip"
                        >
                            <div ref="valueElements" class="item-value">
                                {{ getValueText(item, rowData) || '-' }}
                            </div>
                        </el-tooltip>
                        <div
                            v-else
                            ref="valueElements"
                            class="item-value"
                            @mouseenter="updateTooltipStatus"
                        >
                            {{ getValueText(item, rowData) || '-' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-else-if="labelPosition === 'bottom'" class="info-table-content">
            <template v-for="item in tableColumn" :key="item.key">
                <div
                    class="info-table-item info-table_label_item"
                    :data-row-first="item.config['--grid-row-start'] === 1"
                    :data-cell-first="item.config['--grid-column-start'] === 1"
                    :style="item.config"
                >
                    <div class="item-value item-label-top">{{ item.label }}</div>
                </div>
                <div
                    class="info-table-item info-table_value_item"
                    :data-row-first="item.config['--grid-row-start'] === 1"
                    :data-cell-first="item.config['--grid-column-start'] === 1"
                    :style="item.config"
                >
                    <div class="text-[14px]">{{ getValue(item) }}</div>
                </div>
            </template>
        </div>
        <div v-else class="info-table-content">
            <template v-for="item in tableColumn" :key="item.key">
                <div
                    class="info-table-label-left-item"
                    :data-row-first="item.config['--grid-row-start'] === 1"
                    :data-cell-first="item.config['--grid-column-start'] === 1"
                    :style="item.config"
                >
                    <div
                        class="label-left-item-label"
                        :class="isAddLabelAlignClass() ? 'label-align' : ''"
                        :style="getLabelStyle()"
                    >
                        {{ item.label }}
                    </div>
                    <div class="label-left-item-divider" />
                    <div class="label-left-item-value">{{ getValue(item) }}</div>
                </div>
            </template>
        </div>
    </section>
</template>

<script setup lang="ts" generic="T extends Record<string, any>">
import { computed, nextTick, onUnmounted, ref, useSlots, watch } from 'vue';
import { onMounted } from 'vue';

import { defaultProps } from './config';
import { InfoTableColumnWithConfig, InfoTableProps } from './type';
import { generateColumnsWithGridConfig, hasValue, isFunction } from './utils';

/** 组件的Props */
const props = withDefaults(defineProps<InfoTableProps<T>>(), { ...defaultProps });

/** slots */
const slots = useSlots();

/** 所有需要用到的css变量对象 */
const cssVariables = computed(() => {
    return {
        '--column-count': props.columnCount,
        '--label-text-align': props.labelAlign || 'left' || 'bottom',
        '--label-text-align-top': 'top',
        '--label-bg-color': props.labelBgColor,
        '--label-width': props.labelWidth,
    };
});

/** 最终模板中需要用到的表格列 */
const tableColumn = ref<Array<InfoTableColumnWithConfig<T>>>();

watch(
    () => props.columns,
    (newVal) => {
        tableColumn.value = newVal
            ? generateColumnsWithGridConfig<T>(props.columnCount, newVal).map((item) => ({
                  ...item,
                  showTooltip: false, // 是否显示“展开”按钮
                  isExpanded: false, // 是否展开文本
              }))
            : [];
    },
    { immediate: true, deep: true },
);

const valueElements = ref<Array<HTMLElement | null>>([]);

// 检查文本是否溢出
const checkOverflowAndCountChars = (element: HTMLElement) => {
    const isOverflow =
        element.scrollHeight > element.clientHeight || element.scrollWidth > element.clientWidth;
    return { isOverflow };
};

const updateTooltipStatus = () => {
    nextTick(() => {
        tableColumn.value?.forEach((item, index) => {
            const el = valueElements.value[index];
            if (el) {
                const { isOverflow } = checkOverflowAndCountChars(el);
                item.showTooltip = isOverflow;
            }
        });
    });
};

watch(
    () => props.data,
    () => {
        nextTick(() => {
            updateTooltipStatus();
        });
    },
    { deep: true },
);

onMounted(() => {
    window.addEventListener('resize', updateTooltipStatus);
});

onUnmounted(() => {
    window.removeEventListener('resize', updateTooltipStatus); // 组件卸载时移除监听器
});

/** 获取显示的数据值文本 */
const getValueText = (item: InfoTableColumnWithConfig<T>, rowData: T) => {
    const value = rowData[item.key];
    if (item.formatter && isFunction(item.formatter)) {
        return item.formatter(value);
    }
    return hasValue(value) ? value : '-';
};
const getValue = (item: InfoTableColumnWithConfig<T>) => {
    if (item.formatter && isFunction(item.formatter)) {
        return props.data ? item.formatter(props.data[item.key]) : item.formatter(void 0);
    }
    return props.data
        ? hasValue(props.data[item.key])
            ? props.data[item.key]
            : props.cellEmptyText
        : props.cellEmptyText;
};
/** 判断是否添加 label-align 样式类 */
const isAddLabelAlignClass = () => {
    return props.labelPosition === 'left' && ['center', 'top', 'bottom'].includes(props.labelAlign);
};

/** 获取 labelPosition === 'left' 时 对应 label 样式 */
const getLabelStyle = () => {
    if (isAddLabelAlignClass()) {
        const alignMap = {
            center: 'center',
            top: 'flex-start',
            bottom: 'flex-end',
        };
        return { '--label-vetical-align': alignMap[props.labelAlign] || 'center' };
    }
    return {};
};

/** 将传入的数据统一转换为数组形式 */
const normalizeData = (data: T | T[]): T[] => {
    if (Array.isArray(data)) {
        return data.length > 0 ? data : [getDefaultRow()];
    } else if (data) {
        return [data];
    } else {
        return [getDefaultRow()];
    }
};
/** 生成默认的空行数据 */
const getDefaultRow = (): T => {
    const defaultRow: Record<string, string> = {};
    tableColumn.value?.forEach((item) => {
        defaultRow[item.key] = '-';
    });
    return defaultRow as T;
};

/** 使用 normalizeData 处理传入的数据 */
const normalizedData = computed(() => normalizeData(props.data));
</script>
<style lang="scss">
.info-table {
    width: 100%;
    --border-color: #ebeef5;
    --title-color: #606266;
    --font-size: 14px;
}

.info-table * {
    box-sizing: border-box;
}

.info-table .info-table_title {
    color: var(--title-color);
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 10px;
}

.info-table .info-table-content {
    display: grid;
    grid-template-columns: repeat(var(--column-count), 1fr);
}

.info-table .info-table-item {
    padding: 8px 12px;
    color: #606266;
    text-align: var(--label-text-align);
    flex-shrink: 0;
    border: 1px solid var(--border-color);

    display: flex;
    align-items: center;
    flex-shrink: 0;

    grid-column-start: var(--grid-column-start);
    grid-column-end: var(--grid-column-end);
}

.info-table .info-table-item .item-value {
    min-height: 23px;
    font-size: var(--font-size);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    &.show-all-text {
        height: auto;
        -webkit-line-clamp: unset;
        overflow: visible;
        white-space: normal;
    }
}

.info-table .info-table-item .item-label {
    display: flex;
    align-items: center;
    height: 40px;
    font-size: 14px;
}

.info-table .info-table_label_item {
    grid-row-start: var(--grid-row-start);
    grid-row-end: var(--grid-row-end);

    background-color: var(--label-bg-color);
    font-weight: 500;
    text-align: var(--label-text-align);
}

.info-table .info-table_value_item {
    grid-row-start: var(--value-grid-row-start);
    grid-row-end: var(--value-grid-row-end);
    align-items: flex-start;
    font-weight: 400;
}

.info-table .info-table-item[data-row-first='false'] {
    border-top: none;
}

.info-table .info-table-item[data-cell-first='false'] {
    border-left: none;
}
/** */
/* ======== labelPosition=left 开始 ======== */
.info-table .info-table-label-left-item {
    --padding: 8px 12px;
    /* padding: 8px 12px; */
    color: #606266;
    /* text-align: var(--label-text-align); */
    flex-shrink: 0;
    border: 1px solid var(--border-color);

    display: flex;
    align-items: stretch;
    flex-shrink: 0;

    grid-column-start: var(--grid-column-start);
    grid-column-end: var(--grid-column-end);
}

.info-table .info-table-label-left-item .label-left-item-label {
    width: var(--label-width);
    min-width: var(--label-width);
    padding: var(--padding);
    flex-shrink: 0;
    flex-grow: 0;
    font-weight: 500;
    height: 100%;
    text-align: var(--label-text-align);
    background-color: var(--label-bg-color);
    /* border-right: 1px solid var(--border-color); */
}

.info-table .info-table-label-left-item .label-left-item-label.label-align {
    display: flex;
    justify-content: center;
    align-items: var(--label-vetical-align);
}

.info-table .info-table-label-left-item .label-left-item-divider {
    display: inline-block;
    width: 1px;
    height: 100%;
    background-color: var(--border-color);
}

.info-table .info-table-label-left-item .label-left-item-value {
    flex: 1;
    font-weight: 400;
    padding: var(--padding);
}

.info-table .info-table-label-left-item[data-row-first='false'] {
    border-top: none;
}

.info-table .info-table-label-left-item[data-cell-first='false'] {
    border-left: none;
}

.box-tooltip {
    max-width: 300px;
    font-size: 14px;
    background-color: #fff;
}
/* 展开按钮样式 */
.expand-button {
    flex-shrink: 0;
    text-align: center;
    cursor: pointer;
    color: #3775ff;
    font-size: 13px;
}
/* 收起按钮样式 */
.collapse-button {
    display: inline-block;
    margin-left: 8px;
    color: #3775ff;
    font-size: 13px;
    cursor: pointer;
}

/* ======== labelPosition=left 结束 ======== */
</style>
