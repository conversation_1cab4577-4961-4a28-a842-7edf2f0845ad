<script setup lang="ts">
import { camelCase } from 'lodash-es';
import { Component, computed } from 'vue';

import { mergePropsAndAttrs } from '@/hooks/components';
import { upperFirst } from '@/utils/string';

import type { CustomComponentProps } from './type';

const props = defineProps<CustomComponentProps>();
const attrs = mergePropsAndAttrs(props);

/** 获取components下的所有组件映射  */
const componentMap: Record<string, Component> = Object.fromEntries(
    Object.entries(
        import.meta.glob<{
            default: Component;
        }>('@/components/*/index.vue', {
            eager: true,
        }),
    ).map(([key, value]) => {
        const name = key.split('/').at(-2);
        return [name, value.default];
    }),
);
const componentNode = computed(() => componentMap[upperFirst(camelCase(attrs.value.is))]);

const modelValue = defineModel<any>();

defineOptions({
    inheritAttrs: false,
});
</script>

<template>
    <componentNode v-bind="attrs.props" v-model="modelValue" />
</template>

<style scoped></style>
