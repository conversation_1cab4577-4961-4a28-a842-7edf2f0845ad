import type { ButtonProps } from '@/components/Button/type';
import type { DatePickerProps } from '@/components/DatePicker/type';
import type { EditorProps } from '@/components/Editor/type';
import type { InputProps } from '@/components/Input/type';
import type { RadioProps } from '@/components/Radio/type';
import type { SelectProps } from '@/components/Select/type';
import type { SelectRemoteProps } from '@/components/SelectRemote/type';
import type { TableProps } from '@/components/Table/type';
import type { UploadProps } from '@/components/Upload/type';

import type { DocumentUploadProps } from '../DocumentUpload/type';
import type { ImagePreviewProps } from '../ImagePreview/type';
import type { SwitchProps } from '../Switch/type';

export type CustomComponentProps = ComponentUnionType;

export interface ComponentTypeMap {
    radio: RadioProps;
    input: InputProps;
    table: TableProps<any>;
    select: SelectProps;
    'select-remote': SelectRemoteProps;
    switch: SwitchProps;
    button: ButtonProps;
    editor: EditorProps;
    'date-picker': DatePickerProps;
    upload: UploadProps;
    'image-preview': ImagePreviewProps;
    'document-upload': DocumentUploadProps;
}

type ComponentUnionType = /* @vue-ignore */ CreateCustomComponentUnion<ComponentTypeMap>;

export type CreateCustomComponentUnion<T> = {
    [K in keyof T]: {
        is: K;
        props?: T[K];
    };
}[keyof T];
