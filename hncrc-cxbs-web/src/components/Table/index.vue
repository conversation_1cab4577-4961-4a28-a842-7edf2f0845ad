<script setup lang="ts" generic="T extends Object">
import dayjs from 'dayjs';
import { mapValues } from 'lodash-es';
import { computed, useTemplateRef } from 'vue';

import { mergePropsAndAttrs } from '@/hooks/components';
import { getCssUnit } from '@/utils/css';

import { defaultProps, mixinColumns } from './config';
import type { TableColumnUnionType, TableProps } from './type';

const props = withDefaults(defineProps<TableProps<T>>(), defaultProps);
const attrs = mergePropsAndAttrs(props);

const TableColumns = computed(() =>
    mapValues(props.columns || {}, (value, key) => {
        return Object.assign(
            {},
            Reflect.get(mixinColumns, key) as Exclude<TableColumnUnionType, string>,
            typeof value === 'object' ? value : { label: value },
        );
    }),
);
const tableRef = useTemplateRef('tableRef');

defineExpose({
    /** 滚动到 x,y 坐标 */
    scrollTo(options: ScrollToOptions) {
        tableRef.value?.scrollTo(options);
    },
});
defineOptions({
    inheritAttrs: false,
});
</script>

<template>
    <div class="table">
        <el-table
            ref="tableRef"
            v-bind="attrs"
            height="100%"
            :tooltip-options="{
                'popper-class': 'default-max-width-tooltip',
            }"
        >
            <template v-for="(value, key) in TableColumns" :key="key">
                <!-- 多选 -->
                <el-table-column
                    v-if="value?.type === 'selection'"
                    v-bind="value"
                    type="selection"
                />

                <!-- 下标 -->
                <el-table-column v-else-if="value?.type === 'index'" v-bind="value" type="index" />

                <!-- 操作栏 -->
                <el-table-column
                    v-else-if="value?.type === 'operations'"
                    v-bind="value"
                    :type="void 0"
                >
                    <template #default="scope">
                        <slot :name="key" v-bind="scope" />
                    </template>
                </el-table-column>

                <!-- 时间日期 -->
                <el-table-column v-else-if="value?.type === 'date'" v-bind="value">
                    <template #default="scope">
                        {{ dayjs(scope.row[key]).format(value?.template || 'YYYY-MM-DD HH:mm:ss') }}
                    </template>
                </el-table-column>

                <!-- 图片 -->
                <el-table-column v-else-if="value?.type === 'image'" v-bind="value">
                    <template #default="scope">
                        <el-image
                            :style="{
                                width: getCssUnit(value.width),
                                height: getCssUnit(value.height),
                            }"
                            :src="scope.row[key]"
                            :preview-src-list="[scope.row[key]]"
                            preview-teleported
                            :z-index="999"
                            fit="cover"
                        />
                    </template>
                </el-table-column>

                <!-- 字典  todo -->
                <el-table-column v-else-if="value?.type === 'dict'" v-bind="value">
                    <template #default="scope">
                        {{ scope.row[key] }}
                    </template>
                </el-table-column>

                <!-- 其他  todo 完善 scope 的ts类型 -->
                <el-table-column v-else v-bind="value">
                    <template #default="scope">
                        <slot :name="key" v-bind="scope">
                            {{
                                value?.formatter
                                    ? value.formatter(scope.row)
                                    : scope.row[key] || value.emptyText
                            }}
                        </slot>
                    </template>
                </el-table-column>
            </template>
            <template #empty="scope">
                <slot name="empty" :scope="scope" />
            </template>
        </el-table>
    </div>
</template>

<style scoped lang="scss">
.table {
    width: 100%;
    height: 100%;
}

:deep(.default-max-width-tooltip) {
    max-width: 500px;

    .default-max-width-tooltip .el-tooltip__inner {
        max-width: 500px;
    }
}
</style>
