import type { TableColumnUnionType } from './type';

/** props 默认值 */
export const defaultProps = {
    /**	列的宽度是否自撑开 */
    fit: true,
    /**	是否为斑马纹 */
    stripe: true,
    /**	是否带有纵向边框 */
    border: true,
    /**	是否显示表头 */
    showHeader: true,
    /**
     * 在多选表格中，当仅有部分行被选中时，点击表头的多选框时的行为。
     * 若为 true，则选中所有行；若为 false，则取消选择所有行
     */
    selectOnIndeterminate: true,
    /**	当内容过长被隐藏时显示 */
    showOverflowTooltip: true,
    // header-row-style
    headerCellStyle: () => ({
        color: '#333333',
        backgroundColor: '#eff6ff !important',
    }),
};

/** 针对某些字段，统一的配置 会被主动传入的配置覆盖 */
export const mixinColumns = {
    /** 列表的多选框 */
    selection: {
        type: 'selection',
        width: '45',
    },
    /** 列表显示下标 */
    index: {
        type: 'index',
        label: '序号',
        width: '60',
        align: 'center',
    },
    /** 列表显示操作栏 */
    operations: {
        type: 'operations',
        label: '操作',
    },
} as const satisfies Record<string, Exclude<TableColumnUnionType, string>>;
