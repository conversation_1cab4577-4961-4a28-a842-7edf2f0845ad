import type { TableColumnCtx, TableProps as ElTableProps } from 'element-plus';

import type { mixinColumns } from './config';

export interface TableProps<T extends object> extends ElTableProps<T> {
    columns: TableColumnType<T>;
}

export type TableColumnType<T extends object> = TableDefaultColumnType<T> &
    TableMixinColumnsType &
    TableOtherColumnType;

export type TableDefaultColumnType<T extends object> = {
    [key in keyof T]?: TableColumnUnionType;
};

export type TableMixinColumnsType = {
    [key in keyof typeof mixinColumns]?: TableColumnUnionType;
};

export type TableOtherColumnType = {
    [key: string]: TableColumnUnionType;
};

export type TableColumnUnionType =
    | string
    | TableColumnDefaultProps
    | TableColumnIndexProps
    | TableColumnSelectionProps
    | TableColumnOperationsProps
    | TableColumnExpandProps
    | TableColumnDcitProps
    | TableColumnImageProps
    | TableColumnDateProps;

/**
 * 默认的表头
 * 去除掉type 若
 */
interface TableColumnDefaultProps extends Partial<TableColumnCtx<any>> {
    /** 默认类型 */
    type?: 'default';
}

/**
 * 下标类型表头
 */
interface TableColumnIndexProps extends Partial<TableColumnCtx<any>> {
    /** 下标类型 */
    type: 'index';
}

/**
 * 选择类型表头
 */
interface TableColumnSelectionProps extends Partial<TableColumnCtx<any>> {
    /** 选择类型 */
    type: 'selection';
}

/**
 * 展开类型表头
 */
interface TableColumnExpandProps extends Partial<TableColumnCtx<any>> {
    /** 展开类型 */
    type: 'expand';
}

/**
 * 操作类型表头
 */
interface TableColumnOperationsProps extends Partial<TableColumnCtx<any>> {
    /** 操作类型 */
    type: 'operations';
}

/**
 * 字典类型表头
 */
interface TableColumnDcitProps extends Partial<TableColumnCtx<any>> {
    /** 字典类型 */
    type: 'dict';
    /** 字典的类型 用于请求对应数据 */
    dictType: string;
}

/**
 * 图片类型表头
 */
interface TableColumnImageProps extends Partial<TableColumnCtx<any>> {
    /** 图片类型 */
    type: 'image';
    /** 宽度 */
    width: string | number;
    /** 高度 */
    height: string | number;
}

/**
 * 日期类型表头
 */
interface TableColumnDateProps extends Partial<TableColumnCtx<any>> {
    /** 日期类型 */
    type: 'date';
    /**
     * 日期格式化模板
     *
     * 内使用 dayjs 进行格式化
     * ```
     * dayjs(value).format(template)
     * dayjs(value).format()// => ISO8601 中value的当前日期 没有分数秒，例如 '2020-04-02T08:02:17-05:00'
     * dayjs('2019-01-25').format('[YYYYescape] YYYY-MM-DDTHH:mm:ssZ[Z]')// 'YYYYescape 2019-01-25T00:00:00-02:00Z'
     * dayjs('2019-01-25').format('DD/MM/YYYY') // '25/01/2019'
     * dayjs('2019-01-25').format('DD-MM-YYYY HH:mm:ss') // '25-01-2019 00:00:00'
     * ```
     * Docs: https://day.js.org/docs/en/display/format
     */
    template?: string;
}
