<script lang="ts">
export default {
    name: 'CustomTabs',
};
</script>
<script setup lang="ts">
const props = defineProps({
    activeName: {
        type: String,
        default: '',
    },
});
const emit = defineEmits(['tabChange', 'update:activeName']);

const tabChange = (e) => {
    emit('tabChange', e);
    emit('update:activeName', e);
};
</script>

<template>
    <div class="tabs-box">
        <el-tabs :modelValue="activeName" v-bind="$attrs" @tab-change="tabChange">
            <slot />
        </el-tabs>
    </div>
</template>

<style scoped lang="scss">
.tabs-box {
    :deep(.el-tabs) {
        --el-tabs-header-height: auto;
        //height: auto;

        .el-tabs__header {
            display: flex;
            justify-content: center;

            .el-tabs__nav-wrap {
                overflow: visible;

                .el-tabs__nav-scroll {
                    overflow: visible;
                    color: #909399;

                    .el-tabs__nav {
                        position: relative;
                        font-family: Source <PERSON> San<PERSON> SC;
                        font-weight: 500;
                        color: #909399;

                        .el-tabs__active-bar {
                            width: 70px !important;
                            bottom: 2px;
                            left: -35px;

                            &::after {
                                content: '';
                                display: block;
                                width: 70px;
                                height: 4px;
                                background: #95d475;
                            }
                        }

                        .el-tabs__item {
                            width: 5px;
                            font-size: 36px;
                            padding-left: 150px;
                            padding-right: 150px;
                            padding-bottom: 15px;
                            border: none !important;
                            font-weight: bold;
                            color: #909399;

                            &::after {
                                content: '';
                                display: block;
                                width: 70px;
                                height: 2px;
                                position: absolute;
                                bottom: 0;
                            }
                        }

                        .is-active {
                            // color: #222222;
                            color: #000000;
                        }

                        &::after {
                            content: none !important;
                            border: none !important;
                        }
                    }
                }
            }
        }
    }
}
</style>
