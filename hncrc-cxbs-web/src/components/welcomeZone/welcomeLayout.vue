<script setup lang="ts">
import { storageLocal } from '@pureadmin/utils';
import { computed } from 'vue';

import { router } from '@/router';
import { initRouter } from '@/router/utils';
import { usePermissionStoreHook } from '@/store/modules/permission';
import { TokenKey } from '@/utils/auth';
import { getAssetsFileUrl } from '@/utils/file';

const props = withDefaults(
    defineProps<{
        title: string;
        imgSrc: string;
        background?: string;
        /** 距离顶部的距离 */
        top?: number;
        isLeft?: boolean;
    }>(),
    {
        top: 100,
        isLeft: false,
    },
);

// const props = defineProps<{
//     title: string;
//     imgSrc: string;
//     background?: string;
//     /** 距离顶部的距离 */
//     top?: string;
// }>();

const styleVars = computed(() => {
    return {
        '--titleTop': `${props.top}px`,
        '--subTitleTop': `${props.top + 20}px`,
        '--loginBtnTop': `${props.top - 80}px`,
    };
});

const openAdmin = () => {
    // const menus = usePermissionStoreHook().wholeMenus;
    const token = storageLocal().getItem(TokenKey);
    // 判断用户登录态，选择直接进入or登录
    /* if (menus.length) {
        router.push(menus[1].path);
    } else {
        router.push('/login');
    }*/
    if (token) {
        // 初始化路由
        initRouter().then(() => {
            const permissionStore = usePermissionStoreHook();
            // 如果登录了并且路由菜单里面有overviews，则跳转到overviews，否则跳转到 wholeMenus[1].path
            router.push(
                permissionStore.wholeMenus.find((v) => v.path === '/overviews')
                    ? '/overviews'
                    : permissionStore.wholeMenus[1].path,
            );
            // router.push('/overviews');
        });
    } else {
        router.push('/login');
    }
};
</script>

<template>
    <div
        :style="{
            height: '100%',
            backgroundRepeat: 'repeat-y',
            backgroundPosition: 'top center',
            backgroundSize: '100% auto',
            backgroundImage: `url(${getAssetsFileUrl(props.background)})`,
            backgroundPositionY: '995px',
        }"
        class="welcome-layout"
    >
        <div
            :style="`background-image: url(${getAssetsFileUrl(props.imgSrc)})`"
            class="flex items-center justify-center min-h-screen bg-banner w-full"
        >
            <div class="w-min-[1440px] pb-[80px] w-full">
                <div class="w-full min-h-[290px]" :style="styleVars">
                    <slot name="topContent">
                        <div v-if="!isLeft" style="width: 100%; text-align: center">
                            <!-- <div class="!block !relative top-[200px] title w-full"> -->
                            <div class="!block !relative top-[var(--titleTop)] title w-full">
                                <div class="w-full flex justify-between items-center">
                                    <div class="title shrink-0 grow-[1] translate-x-[80px]">
                                        {{ props.title }}
                                    </div>

                                    <div
                                        class="shrink-0 grow-0 float-right translate-x-[-80px] login-btn"
                                        @click.stop="openAdmin"
                                    >
                                        登录诚信白沙服务平台
                                    </div>
                                </div>
                            </div>
                            <!-- <span class="!block !relative top-[200px] title">{{
                                props.title
                            }}</span> -->
                            <span
                                class="!block !relative top-[var(--subTitleTop)] content opacity-85 text-[#ffffff]"
                                >打造特色信用应用场景，营造“诚信有益”价值导向</span
                            >
                            <!-- <span
                                class="!block !relative top-[220px] content opacity-85 text-[#ffffff]"
                                >打造特色信用应用场景，营造“诚信有益”价值导向</span
                            > -->
                        </div>
                        <div v-else style="text-align: left" class="ml-[200px]">
                            <div class="!block !relative top-[var(--titleTop)] title">
                                <div class="flex justify-between items-center">
                                    <div class="title shrink-0 grow-[1] ml-[80px]">
                                        {{ props.title }}
                                    </div>
                                </div>
                            </div>
                            <div
                                class="flex flex-col !block !relative top-[var(--subTitleTop)] ml-[80px] mt-[20px]"
                            >
                                <span class="content opacity-85 text-[#ffffff]"
                                    ><div>打造特色信用应用场景，</div>
                                    <div>营造“诚信有益”价值导向</div></span
                                >
                                <div
                                    class="shrink-0 grow-0 login-btn w-[200px] text-[#fff] mt-[20px]"
                                    @click.stop="openAdmin"
                                >
                                    登录诚信白沙服务平台
                                </div>
                            </div>
                        </div>

                        <el-button
                            v-if="false"
                            type="primary"
                            size="large"
                            class="!block !relative top-[var(--loginBtnTop)] left-[1240px] translate-x-[-20%]"
                            style="
                                border-radius: 20px;
                                border: none !important;
                                background: linear-gradient(
                                    270deg,
                                    rgba(249, 245, 134, 0.9) -13%,
                                    rgba(103, 194, 58, 0.9699) 56%,
                                    #67c23a 100%
                                );
                            "
                            @click="openAdmin"
                        >
                            登录诚信白沙服务平台
                        </el-button>
                    </slot>
                </div>
                <slot name="nav" />
                <div
                    :style="{
                        'margin-top': isLeft ? '120px' : '0px',
                    }"
                    class="welcome-layout-main-container px-[5%] pt-[40px] pb-[100px] rounded-xl mx-[6%]"
                >
                    <slot />
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.welcome-layout {
    &-main-container {
        background-color: rgba(255, 255, 255, 0.92);
    }
    .title {
        font-size: 80px;
        color: #ffffff;
        line-height: 54px;
        text-shadow: 4px 3px 28px rgba(18, 14, 102, 0.03);
        letter-spacing: 20px;
        -webkit-text-stroke: 1px #ffffff;
    }

    .login-btn {
        flex-shrink: 0;
        flex-grow: 0;
        height: 40px;
        // width: 180px;
        line-height: 40px;
        border-radius: 20px;
        border: none;
        background: linear-gradient(
            270deg,
            rgba(249, 245, 134, 0.9) -13%,
            rgba(103, 194, 58, 0.9699) 56%,
            #67c23a 100%
        );
        font-size: 14px;
        letter-spacing: 0.1em;
        padding: 0 20px;
        font-weight: 500;
        -webkit-text-stroke: 0;

        cursor: pointer;

        &:hover {
            opacity: 0.95;
        }

        &:active {
            opacity: 1;
        }
    }

    .content {
        font-size: 30px;
        color: #ffffff;
        line-height: 54px;
        text-shadow: 4px 3px 28px rgba(18, 14, 102, 0.03);
        letter-spacing: 10px;
        -webkit-text-stroke: 1px #ffffff;

        text-shadow:
            1px 0 #6c815e,
            -1px 0 #6c815e,
            0 1px #6c815e,
            0 -1px #6c815e,
            1px 1px #6c815e,
            -1px -1px #6c815e,
            1px -1px #6c815e,
            -1px 1px #6c815e;
    }

    .sub-text {
        font-size: 30px;
        color: #ffffff;
        line-height: 54px;
        letter-spacing: 10px;
        -webkit-text-stroke: 1px #ffffff;
        text-align: center;
        height: 50px;

        .svg {
            width: 976px;
            // transform: translateX(25%);
            // margin: auto;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .text {
            font-size: 34px;
            fill: #ffffff;
            font-weight: 700;
            text-align: center;
        }

        .stroke {
            stroke: #6c815e;
            stroke-width: 1px;
        }
    }
    .bg-banner {
        background-repeat: no-repeat;
        background-position: top center;
        background-size: 100% auto;
    }
}
</style>
