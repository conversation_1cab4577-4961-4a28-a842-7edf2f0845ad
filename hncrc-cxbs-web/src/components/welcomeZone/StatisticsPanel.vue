<script setup lang="ts">
import { defineProps } from 'vue';
interface StatItem {
    value: number;
    unit: string;
    title: string;
}

defineProps<{
    coverImg: string;
    coverImgHeight?: string;
    itemPadding?: string;
    items: StatItem[];
    backgroundColor?: string;
}>();
</script>

<template>
    <div class="bg-[#F0F9EB] h-[249px] relative flex flex-row p-8 mt-[140px] rounded-lg">
        <img
            :src="coverImg"
            :style="{ height: coverImgHeight ?? '300px', backgroundColor: backgroundColor }"
            class="absolute bottom-0 left-0 pr-[-50px] rounded-lg select-none pointer-events-none"
        />
        <div :style="{ width: itemPadding ?? '100px' }" />
        <template v-for="(item, index) in items" :key="index">
            <div
                class="flex-1 text-center pt-[40px] ml-[30px]"
                :class="{ 'border-r-2 border-[#D8D8D8]': index === 0 }"
            >
                <div class="text-[40px] text-[#67C23A]">
                    {{ item.value }}<span class="text-sm ml-2">{{ item.unit }}</span>
                </div>
                <div class="text-[#666666] mt-2">{{ item.title }}</div>
            </div>
        </template>
    </div>
</template>
