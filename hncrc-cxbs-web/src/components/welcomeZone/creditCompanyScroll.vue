<script setup lang="ts">
import { ref } from 'vue';

import { CompanyList } from '@/api/welcome';
const updateTime = ref<string>();
import { emitter } from '@/utils/mitt';
import { CREDIT_RATING } from '@/views/enterprise/enums';

defineProps({
    data: {
        type: Array<CompanyList>,
        default: () => [],
    },
});

const tableColumns = [
    {
        label: '信用等级',
        prop: 'creditLevel',
        slot: 'creditLevel',
        width: '130',
    },
    {
        label: '主体名称',
        prop: 'companyName',
    },
    {
        label: '主体类型',
        width: '130',

        prop: 'subjectType',
    },
    {
        label: '地址',
        prop: 'address',
    },

    {
        label: '操作',
        prop: '$edit',
        width: '80',
        slot: '$edit',
    },
];

const clickDetails = (id) => {
    emitter.emit('commercialZoneIsDetails', {
        id: id,
        isDetails: true,
    });
};
</script>

<template>
    <el-scrollbar v-if="data.length !== 0" height="460px" style="margin-left: 20px">
        <!-- <div class="flex justify-end my-2 mx-5">
            <span class="text-sm text-[#9CA4A8]">更新时间: {{ updateTime }} </span>
        </div> -->
        <CommonTable :table-data="data" :table-columns="tableColumns" :isShowPagination="false">
            <!-- level 插槽 -->
            <template #creditLevel="{ scope }">
                <el-tag
                    :type="CREDIT_RATING[scope?.row?.creditLevel].type"
                    :class="CREDIT_RATING[scope?.row?.creditLevel].class"
                    :color="CREDIT_RATING[scope?.row?.creditLevel].color"
                >
                    {{ CREDIT_RATING[scope?.row?.creditLevel]?.text }}
                </el-tag>
            </template>

            <!-- @click="$router.push('/archives?id=' + scope.row.id)" -->
            <template #$edit="{ scope }">
                <el-text class="cursor-pointer" type="primary" @click="clickDetails(scope.row.id)">
                    查看
                </el-text>
            </template>
        </CommonTable>
    </el-scrollbar>
    <el-empty v-else :image-size="200" />
</template>

<style scoped lang="scss"></style>
