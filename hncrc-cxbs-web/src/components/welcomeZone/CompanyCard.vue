<script setup lang="ts">
import { SeedlingSubjectMerchantProfileVO } from '@/api/welcome/type';
import BorderBox from '@/components/welcomeZone/BorderBox.vue';
import { getCredictLevelConfig, getCredictLevelLabel } from '@/dicts/CredictLevelDicts';
import { getSubjectTypeLabel } from '@/dicts/SubjectTypeDicts';
import { getAssetsFileUrl } from '@/utils/file';

const props = withDefaults(
    defineProps<{
        /** 商户信息 */
        company: SeedlingSubjectMerchantProfileVO;
        /** 无数据占位符 */
        emptyText?: string;
    }>(),
    {
        emptyText: '',
    },
);

/** 默认头像 */
const defaultAvatar = getAssetsFileUrl('welcome/subject_default_avatar.png');

const emit = defineEmits<{
    (e: 'viewDetail', payload: SeedlingSubjectMerchantProfileVO): void;
    (e: 'viewSale', payload: SeedlingSubjectMerchantProfileVO): void;
}>();

/**分数统计 */
const getScore = (data) => {
    if (data) {
        let score = JSON.parse(data);
        let total = score.reduce((acc, curr) => acc + curr, 0);
        return total;
    } else {
        return '';
    }
};
</script>

<template>
    <BorderBox>
        <div class="flex p-4 w-full gap-8">
            <!-- 左侧图片 -->
            <div class="w-[400px] h-[300px]">
                <el-image
                    :src="company.subjectPictureListJson || defaultAvatar"
                    fit="cover"
                    class="w-full h-full rounded-lg select-none"
                />
            </div>

            <!-- 右侧信息 -->
            <div class="flex-1 flex flex-row w-auto gap-8">
                <div class="flex-1">
                    <div class="mb-4 flex flex-row items-center gap-8">
                        <h2 class="text-2xl font-bold text-[black]">{{ company.subjectName }}</h2>
                        <span
                            v-if="company.creditLevel"
                            class="rounded text-[14px] py-1 px-3 shrink-0 group-[0]"
                            :style="getCredictLevelConfig(company.creditLevel)"
                            >{{ getCredictLevelLabel(company.creditLevel, true) }}</span
                        >
                        <span
                            v-if="company.evaluationScore"
                            class="rounded text-[14px] py-1 px-3 shrink-0 group-[0]"
                            :style="getCredictLevelConfig(company.creditLevel)"
                            >信用得分 {{ getScore(company.evaluationScore) }}</span
                        >
                    </div>

                    <div class="text-gray-600 space-y-3">
                        <div>
                            <span class="font-medium text-[#909399]">主体类型：</span>
                            <span>{{
                                company?.subjectType
                                    ? getSubjectTypeLabel(company.subjectType)
                                    : emptyText
                            }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-[#909399]">苗木类型：</span>
                            <span>{{ company.seedlingCategory || emptyText }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-[#909399]">苗木品种：</span>
                            <span>{{ company.seedlingVariety || emptyText }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-[#909399]">苗木规格：</span>
                            <span>{{ company.seedlingSpecs || emptyText }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-[#909399]">特点优势：</span>
                            <span>{{ company.seedlingAdvantages || emptyText }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-[#909399]">联系电话：</span>
                            <span>{{ company.contactPhone || emptyText }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-[#909399]">经营地址：</span>
                            <span>{{ company.subjectAddress || emptyText }}</span>
                        </div>
                    </div>

                    <div class="mt-6">
                        <span class="text-[black] text-lg font-bold">商户简介</span>
                        <div class="flex flex-col w-full overflow-hidden h-[110px]">
                            <el-scrollbar height="100%" class="pr-[10px]">
                                <div class="text-gray-600 space-y-1 pt-4 leading-6">
                                    <!-- <p v-for="(desc, index) in company.description" :key="index">
                                {{ desc }}
                            </p> -->
                                    <p>{{ company.subjectIntroduction || emptyText }}</p>
                                </div>
                            </el-scrollbar>
                        </div>
                    </div>
                </div>
                <div style="display: flex">
                    <el-button
                        type="success"
                        plain
                        size="large"
                        class="p-4 bg-[transparent] text-[#5CA139] border border-[#5CA139]"
                        @click="emit('viewDetail', company)"
                    >
                        查看详情
                    </el-button>
                    <div
                        v-if="
                            company.saleChannel || (company.qrCodeUrl != '[]' && company.qrCodeUrl)
                        "
                        class="flex justify-center cursor-pointer ml-[20px]"
                        @click.stop="emit('viewSale', company)"
                    >
                        <img
                            class="w-[25px] h-[25px] mt-[10px]"
                            :src="getAssetsFileUrl('welcome/shopping.png')"
                        />
                        <span class="mt-[10px] ml-[5px] text-[#8FC31F]">购买渠道</span>
                    </div>
                </div>
            </div>
        </div>
    </BorderBox>
</template>
