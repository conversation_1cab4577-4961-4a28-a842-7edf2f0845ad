<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
    name: 'CreditBorderBox',
});
</script>
<script setup lang="ts">
import { getAssetsFileUrl } from '@/utils/file';

interface Props {
    src?: string;
    coverPosition?: 'left' | 'right';
    isFull?: boolean;
}

withDefaults(defineProps<Props>(), {});
</script>

<template>
    <div class="border-box">
        <div class="flex flex-row credit-module__content overflow-hidden">
            <el-scrollbar v-if="isFull" class="w-full" height="100%">
                <el-image
                    v-if="src && (coverPosition === 'left' || !coverPosition)"
                    class="rounded-[10px] w-full"
                    fit="cover"
                    :src="getAssetsFileUrl(src)"
                />
            </el-scrollbar>
            <el-image
                v-else-if="src && (coverPosition === 'left' || !coverPosition)"
                class="rounded-[10px] h-[420px] overflow-hidden"
                fit="cover"
                :src="getAssetsFileUrl(src)"
            />
            <div class="flex-1 overflow-hidden">
                <slot />
            </div>
            <el-image
                v-if="src && coverPosition === 'right'"
                class="rounded-[10px] h-[420px] mt-[20px] overflow-hidden"
                fit="cover"
                :src="getAssetsFileUrl(src)"
            />
        </div>
    </div>
</template>

<style scoped lang="scss">
.border-box {
    padding: 2px;
    border-radius: 0.75rem;
    background-image: linear-gradient(180deg, rgba(225, 243, 216, 0.2) -9%, #b3e09c 65%);

    .credit-module__content {
        background-color: white;
        padding: 40px;
        border-radius: 10px;
    }
}
</style>
