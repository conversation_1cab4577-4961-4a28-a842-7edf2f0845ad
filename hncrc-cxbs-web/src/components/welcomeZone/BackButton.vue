<template>
    <div
        style="
            position: absolute;
            top: 10px;
            left: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
        "
        class="bg-black"
        @click="handleClick"
    >
        <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            fill="none"
            version="1.1"
            width="10"
            height="36.31792068481445"
            viewBox="0 0 35 36.31792068481445"
        >
            <g>
                <path
                    d="M15.5377,9.10195L15.5377,1.45707C15.5803,1.07388,15.4762,0.681228,15.1829,0.383191C14.6673,-0.12773,13.8394,-0.12773,13.3237,0.383191L0.37568,14.5802C0.101296,14.8498,-0.0169722,15.2141,0.00195117,15.5689C-0.0169715,15.9237,0.101297,16.2832,0.37568,16.5576L13.2528,30.6741C13.7353,31.0857,14.6152,31.3175,15.1829,30.7498C15.4762,30.4565,15.6039,30.2105,15.5614,29.8273L15.5614,22.05C24.1146,22.05,31.8541,28.2094,33.3868,36.3179C34.4228,33.9431,35,31.3223,35,28.5642C35.0047,17.816,26.2907,9.10195,15.5377,9.10195Z"
                    fill="#FFFFFF"
                    fill-opacity="0.8999999761581421"
                    style="mix-blend-mode: passthrough"
                />
            </g>
        </svg>
        <span class="text-white ml-1">返回诚信白沙服务专区</span>
    </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const handleClick = () => {
    router.back();
};
</script>

<style scoped>
.bg-black {
    background: rgba(103, 194, 58, 0.8);
    /* 自动布局 */
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 6px 10px;
    gap: 4px;
    height: 32px;
    border-radius: 4px;
    font-size: 14px;
    opacity: 1;
    line-height: 32px;
    border: 1px solid #ffffff;
}
</style>
