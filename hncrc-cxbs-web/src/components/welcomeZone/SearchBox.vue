<script setup lang="ts">
import { Search } from '@element-plus/icons-vue';

interface Props {
    placeholder?: string;
    modelValue: string;
}

interface Emits {
    (e: 'update:modelValue', value: string): void;
    (e: 'search'): void;
    (e: 'clear'): void;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: '请输入要搜索的特色苗木',
});

const emit = defineEmits<Emits>();

const handleSearch = () => {
    emit('search');
};

const handleClear = () => {
    emit('clear');
};
</script>

<template>
    <div class="search-container">
        <div class="search-box">
            <el-input
                :model-value="modelValue"
                :placeholder="placeholder"
                class="search-input"
                :size="'large'"
                clearable
                @update:modelValue="(val) => emit('update:modelValue', val)"
                @clear="handleClear"
            >
                <template #prefix>
                    <el-icon class="text-[32px]"><Search /></el-icon>
                </template>
                <template #append>
                    <el-button
                        type="success"
                        class="search-btn"
                        :size="'large'"
                        @click="handleSearch"
                    >
                        <span class="text-[24px]">搜索</span>
                    </el-button>
                </template>
            </el-input>
        </div>
    </div>
</template>

<style scoped lang="scss">
.search-container {
    padding: 20px 120px;
    display: flex;
    justify-content: center;
}

.search-box {
    width: 100%;
    max-width: 1200px;
    display: flex;
    gap: 10px;

    .search-input {
        flex: 1;

        :deep(.el-input__wrapper) {
            background-color: #fff;
            height: 80px;
            font-size: 24px;
        }

        :deep(button) {
            border-radius: 0 8px 8px 0;
            color: white;
            background-color: #95d475;
        }

        :deep(.el-input__inner) {
            font-size: 24px;

            & + .el-input__suffix {
                .el-input__clear {
                    font-size: 22px;
                }
            }
        }
    }

    .search-btn {
        width: 160px;
        height: 80px;
    }
}
</style>
