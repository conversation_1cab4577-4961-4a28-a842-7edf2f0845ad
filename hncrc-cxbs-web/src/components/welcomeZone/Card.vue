<script setup lang="ts">
import { getAssetsFileUrl } from '@/utils/file';
defineProps({
    src: {
        type: String,
        default: '',
    },
    status: {
        type: String,
        default: '',
    },
    /*  title: {
        type: String,
        default: '默认标题',
    },*/
    context: {
        type: String,
        default: '默认内容',
    },
    icon: {
        type: String,
        default: '',
    },
});
</script>

<template>
    <div class="w-[420px] flex flex-col rounded">
        <div class="w-full">
            <el-image
                class="h-[340px] !block"
                fit="fill"
                :src="getAssetsFileUrl('image/index/' + src)"
            />
        </div>
        <!--        <span v-if="status == '1'" class="spantype">敬请期待</span>-->
        <div
            class="p-[1px] rounded-t-none rounded-md bg-gradient-to-b from-[#fff] to-[#DBEAEE] flex-grow flex"
        >
            <div
                class="bg-gradient-to-b from-[#D3E9F7] via-[#fff] to-[#fff] text-left rounded-t-none rounded-md flex-grow"
            >
                <div class="px-6 pt-2 pb-5">
                    <p class="text-center mt-2">
                        {{ context }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.spantype {
    position: absolute;
    background: #ff9c00;
    padding: 3px 15px;
    margin: 10px;
    border-radius: 50px;
    color: white;
    font-size: 16px;
}
</style>
