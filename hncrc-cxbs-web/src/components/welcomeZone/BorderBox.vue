<script setup lang="ts">
interface Props {}

defineProps<Props>();
</script>

<template>
    <div class="border-box">
        <div class="flex flex-row module__content">
            <slot />
        </div>
    </div>
</template>

<style scoped lang="scss">
.border-box {
    padding: 1px;
    border-radius: 8px;
    background-image: linear-gradient(180deg, rgba(225, 243, 216, 0.2) -9%, #b3e09c 65%);

    color: #666666;

    h2 {
        color: #529b2e;
    }

    .module__content {
        background: linear-gradient(0deg, #f2f6ef 33%, #ffffff 60%);
        padding: 40px 20px 20px 20px;
        // border-radius: 8px;
        border-radius: inherit;
    }
}
</style>
