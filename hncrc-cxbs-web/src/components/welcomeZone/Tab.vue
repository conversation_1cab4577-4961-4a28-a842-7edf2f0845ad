<script setup lang="ts">
import { getAssetsFileUrl } from '@/utils/file';

defineProps({
    src: {
        type: String,
        default: 'https://www.baidu.com/img/flexible/logo/pc/result.png',
    },
});
</script>

<template>
    <div class="flex justify-center w-full h-[382px] rounded-md mt-5">
        <div class="box flex rounded-md bg-gradient-to-b w-[89%] overflow-hidden">
            <div class="w-[38%]">
                <el-image class="!block" fit="fill" :src="getAssetsFileUrl(src)" />
            </div>
            <div class="w-[62%]">
                <slot name="content" class="" />
            </div>
        </div>
    </div>
</template>

<style scoped>
.box {
    background-image: linear-gradient(to bottom, #f0fbfe, #fff),
        linear-gradient(to bottom, #cdfaf7, #93eacd);
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    border: 2px solid transparent;
}
</style>
