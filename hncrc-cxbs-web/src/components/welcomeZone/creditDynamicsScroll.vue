<script setup lang="ts">
import { Icon } from '@iconify/vue';
import { dayjs } from 'element-plus';
import { onBeforeUnmount, onMounted, ref } from 'vue';

import { contentProcess } from '@/utils/RichTextUtil';

interface DataType {
    id: number;
    name: string;
    createTime: string;
    content: string;
}

const props = defineProps<{
    data: Array<DataType>;
}>();

const emit = defineEmits(['clickItem']);
const currentItem = (item: DataType) => {
    emit('clickItem', item);
};

// 去除html标签
const stripHtmlTags = (str) => {
    if (str === null || str === '') {
        return false;
    } else {
        str = str.toString();
        return str.replace(/<[^>]*>/g, '');
    }
};

// 公告描述处理
const processedAnnouncementDescribe = (str: string) => {
    let decodedString = str;
    let result = stripHtmlTags(contentProcess.decode(decodedString));
    return result;
};

const scrollbarRef = ref();
const scrollInterval = ref();
let scrollTop = 0;

// 开始自动滚动
const startScroll = () => {
    scrollInterval.value = setInterval(() => {
        if (!scrollbarRef.value) return;
        const scrollbar = scrollbarRef.value.$el.querySelector('.el-scrollbar__wrap');
        scrollTop += 1;

        // 如果滚动到底部，立即回到顶部
        if (scrollTop >= scrollbar.scrollHeight - scrollbar.clientHeight) {
            scrollTop = 0;
        }

        scrollbar.scrollTop = scrollTop;
    }, 50);
};

// 停止滚动
const stopScroll = () => {
    if (scrollInterval.value) {
        clearInterval(scrollInterval.value);
    }
};

// 鼠标进入停止滚动
const handleMouseEnter = () => {
    stopScroll();
};

// 鼠标离开继续滚动
const handleMouseLeave = () => {
    startScroll();
};

onMounted(() => {
    startScroll();
});

onBeforeUnmount(() => {
    stopScroll();
});
</script>

<template>
    <div
        class="credit-dynamics-container"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
    >
        <el-scrollbar v-if="data?.length !== 0" ref="scrollbarRef" height="460px">
            <div
                v-for="item in data"
                :key="item.id"
                style="cursor: pointer"
                class="flex justify-center items-center h-[100px] group"
                @click="currentItem(item)"
            >
                <div class="text-center ml-10 pr-7 border-r-[1px] border-r-[#ECF4FF]">
                    <p class="text-[#999999] text-[20px]">
                        {{ dayjs(item.createTime).format('YYYY') }}
                    </p>
                    <el-divider class="header-divider" style="margin: 4px 0" />
                    <p class="text-[#909399] text-[16px] whitespace-nowrap">
                        {{ dayjs(item.createTime).format('MM-DD') }}
                    </p>
                </div>
                <div class="flex-grow ml-6 text-left w-[200px]">
                    <h4 class="text-[#222222] group-hover:text-[#67C23A] text-st">
                        {{ item.name }}
                    </h4>
                    <p class="text-sm text-[#666666] text-st">
                        {{ processedAnnouncementDescribe(item.content) }}
                    </p>
                </div>
                <div
                    class="flex text-[#67C23A] group-hover:text-[#fff] bg-[#F0F9EB]/55 group-hover:bg-[#67C23A] justify-center items-center p-3 rounded-full shadow mx-10"
                >
                    <Icon class="" icon="ep:right" width="1.2em" height="1.2em" />
                </div>
            </div>
        </el-scrollbar>
        <el-empty v-else :image-size="200" />
    </div>
</template>

<style scoped lang="scss">
.text-st {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1 1 auto;
    min-width: 0;
}

.credit-dynamics-container {
    overflow: hidden;
}
</style>
