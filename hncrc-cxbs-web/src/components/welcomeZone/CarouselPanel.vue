<template>
    <div class="carousel-container">
        <!-- 左箭头始终显示，但通过 disabled 类控制状态 -->
        <!--  <div class="arrow left-arrow" :class="{ disabled: currentIndex <= 0 }" @click="prev" /> -->
        <!-- 只有当需要显示箭头时才渲染 -->
        <div
            v-if="showArrows"
            class="arrow left-arrow"
            :class="{ disabled: currentIndex <= 0 }"
            @click="prev"
        >
            <ArrowLeft />
        </div>

        <!-- 轮播内容区 -->
        <div ref="contentRef" class="carousel-content">
            <div
                class="carousel-track"
                :style="{
                    transform: `translateX(-${currentIndex * (100 / props.displayCount)}%)`,
                }"
            >
                <div v-for="(item, index) in items" :key="index" class="carousel-item">
                    <slot name="item" :data="item">
                        {{ item }}
                    </slot>
                </div>
            </div>
        </div>

        <!-- 右箭头始终显示，但通过 disabled 类控制状态 -->

        <!-- 只有当需要显示箭头时才渲染 -->
        <div
            v-if="showArrows"
            class="arrow right-arrow"
            :class="{ disabled: currentIndex >= maxIndex }"
            @click="next"
        >
            <ArrowRight />
        </div>
    </div>
</template>

<script setup lang="ts" generic="T">
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import { computed, ref } from 'vue';

const props = defineProps({
    // 轮播项
    items: {
        type: Array<T>,
        default: () => [] as T[],
    },
    // 同时显示的项目数量
    displayCount: {
        type: Number,
        default: 2,
    },
});

const currentIndex = ref(0);

const maxIndex = computed(() => props.items.length - props.displayCount);

// 添加计算属性：控制箭头显示
const showArrows = computed(() => props.items.length > 3);

// 下一张
const next = () => {
    if (currentIndex.value < maxIndex.value) {
        currentIndex.value++;
    }
};

// 上一张
const prev = () => {
    if (currentIndex.value > 0) {
        currentIndex.value--;
    }
};
</script>

<style scoped lang="scss">
.carousel-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.carousel-content {
    flex: 1;
    overflow: hidden;
    margin-left: 30px;
}

.carousel-track {
    display: flex;
    transition: transform 0.5s ease;
}

.carousel-item {
    flex-shrink: 0;
    width: v-bind('`${100/props.displayCount}%`');
}

.carousel-item {
    padding-right: 30px;
}
.arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: #95d475;
    cursor: pointer;
    z-index: 1;
    transition: all 0.3s;

    &:hover {
        background: rgba(0, 0, 0, 0.5);
    }

    &.disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
            background: rgba(0, 0, 0, 0.3);
        }
    }
}

.left-arrow {
    margin-right: 10px;
}

.right-arrow {
    margin-left: 10px;
}
</style>
