<script lang="ts">
import { defineComponent } from 'vue';

import BorderBox from './BorderBox.vue';

export default defineComponent({
    name: 'ZoneIntro',
});
</script>
<script setup lang="ts">
import router from '@/router';
import { getAssetsFileUrl } from '@/utils/file';

interface Props {
    src?: string;
    title?: string;
    coverPosition?: 'left' | 'right';
    buttonText: string;
    path?: string;
}

const handleClick = () => {
    console.log('handleClick', props);
    if (props.path) {
        router.push(props.path);
    }
};

const props = defineProps<Props>();
</script>

<template>
    <BorderBox>
        <div class="flex flex-row">
            <section
                v-if="src && (coverPosition === 'left' || !coverPosition)"
                class="rounded-[16px] w-[420px] h-[480px] overflow-hidden"
            >
                <el-image fit="cover" :src="getAssetsFileUrl(src)" />
            </section>

            <div class="p-6 flex-1">
                <h2 class="text-2xl font-normal mb-4 text-center title">{{ title }}</h2>
                <div class="leading-loose indent-8 [&>p]:mb-4">
                    <slot />
                </div>
                <div class="text-center mt-10 pt-20">
                    <el-button
                        type="primary"
                        size="large"
                        style="
                            font-size: 22px;
                            padding: 10px 20px;
                            border-radius: 20px;
                            border: none !important;
                            background: linear-gradient(
                                270deg,
                                #67c23a 22%,
                                rgba(103, 194, 58, 0.8071) 51%,
                                rgba(249, 245, 134, 0.502) 108%
                            );
                        "
                        @click="handleClick"
                    >
                        {{ buttonText }}
                    </el-button>
                </div>
            </div>

            <section
                v-if="src && coverPosition === 'right'"
                class="rounded-[16px] w-[420px] h-[480px] overflow-hidden"
            >
                <el-image fit="cover" :src="getAssetsFileUrl(src)" />
            </section>
        </div>
    </BorderBox>
</template>

<style scoped lang="scss">
.border-box {
    padding: 2px;
    border-radius: 0.75rem;
    background-image: linear-gradient(180deg, rgba(225, 243, 216, 0.2) -9%, #b3e09c 65%);

    color: #666666;

    .title {
        font-weight: bold;
    }

    h2 {
        color: #529b2e;
    }

    .intro-module__content {
        background: linear-gradient(0deg, rgba(240, 249, 235, 0.95) 45%, #ffffff 100%);
        padding: 40px;
        border-radius: 10px;
    }
}
</style>
