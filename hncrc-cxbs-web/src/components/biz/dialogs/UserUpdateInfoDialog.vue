<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus';
import { reactive, ref } from 'vue';

import { originCreateSubjectApi, originGetSubjectApi } from '@/api/origin';
import { useMessage } from '@/hooks/web/useMessage';
const message = useMessage();

interface FormData {
    /**产地主体名称 */
    originName: string;
    subjectType: string;
    creditCode: string;
}

const emits = defineEmits<{
    getList: [];
}>();

const target = ref<any>({});
const dialogVisible = ref(false);
const elFormEl = ref<FormInstance>();
const formData = reactive<FormData>({
    originName: '',
    subjectType: '',
    creditCode: '',
});

const rules = reactive<FormRules<FormData>>({
    originName: [{ required: true, message: '请输入产地主体名称', trigger: 'blur' }],
    subjectType: [{ required: true, message: '请输入产地主体类型', trigger: 'blur' }],
    creditCode: [{ required: true, message: '请输入产地主体标识', trigger: 'blur' }],
});

const loading = ref(false);
const loadingDialo = ref(false);

const updateUserInfoFn = () => {
    elFormEl.value?.validate().then(() => {
        loading.value = true;
        originCreateSubjectApi({
            creditCode: formData.creditCode,
            subjectName: formData.originName,
            subjectType: formData.subjectType,
            userId: target.value.id,
        }).then(() => {
            message.success('更新成功');
            dialogVisible.value = false;
            // emits('getList')
        });
    });
};

const close = () => {
    target.value = {};
    loading.value = false;
    loadingDialo.value = false;
    formData.originName = '';
    formData.subjectType = '';
    formData.creditCode = '';
};

defineExpose({
    open: (row: any) => {
        console.log(row);
        target.value = row;
        loadingDialo.value = true;
        dialogVisible.value = true;
        originGetSubjectApi(row.id)
            .then((res) => {
                formData.originName = res.subjectName || '';
                formData.subjectType = res.subjectType || '';
                formData.creditCode = res.creditCode || '';
            })
            .finally(() => {
                loadingDialo.value = false;
            });
    },
});
</script>

<template>
    <div class="f-s-14 o-a">
        <el-dialog
            v-model="dialogVisible"
            title="更新主体信息"
            width="760px"
            height="500px"
            destroy-on-close
            :close-on-click-modal="false"
            @close="close"
        >
            <div v-loading="loadingDialo">
                <el-divider class="header-divider" />
                <el-form ref="elFormEl" :model="formData" :rules="rules" label-width="120px">
                    <el-form-item label="产地主体名称" prop="originName">
                        <el-input
                            v-model="formData.originName"
                            clearable
                            placeholder="请输入产地主体名称"
                        />
                    </el-form-item>
                    <el-form-item label="产地主体类型" prop="subjectType">
                        <el-input
                            v-model="formData.subjectType"
                            clearable
                            placeholder="请输入产地主体类型"
                        />
                    </el-form-item>
                    <el-form-item label="产地主体标识" prop="creditCode">
                        <el-input
                            v-model="formData.creditCode"
                            clearable
                            placeholder="请输入产地主体标识"
                        />
                    </el-form-item>
                </el-form>
            </div>
            <template v-slot:footer>
                <span class="dialog-footer">
                    <el-divider class="m-12-0" />
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" :loading="loading" @click="updateUserInfoFn"
                        >保存</el-button
                    >
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style scope lang="scss"></style>
