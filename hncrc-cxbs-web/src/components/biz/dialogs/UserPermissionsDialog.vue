<template>
    <el-dialog
        v-model="dialogVisible"
        title="分配角色"
        destroy-on-close
        :before-close="close"
        :close-on-click-modal="false"
    >
        <div>
            <el-transfer v-model="value" :data="data" :titles="['角色列表', '已选角色列表']" />
        </div>
        <template v-slot:footer>
            <span class="dialog-footer">
                <el-divider class="m-12-0" />
                <el-button @click="close">取 消</el-button>
                <el-button type="primary" :loading="loading" @click="updateUserRolesFn"
                    >保存</el-button
                >
            </span>
        </template>
    </el-dialog>
</template>
<script setup lang="ts">
import { ref } from 'vue';

import { searchByUser, searchUserRoles, updateUserRoles } from '@/api/system/user';
import { useMessage } from '@/hooks/web/useMessage';
import { el } from 'element-plus/es/locale/index.mjs';
const messages = useMessage();
const data = ref([]);
const value = ref([]);
const beforeValue = ref([]);
const loading = ref(false);
const dialogVisible = ref(false);
const userId = ref('');
import { updateDefaultZone } from '@/api/user';
import { getConfig } from '@/config';

const emit = defineEmits(['success']);

/** 打开弹窗 */
const open = async (row) => {
    dialogVisible.value = true;

    if (row) {
        userId.value = row.id;
        searchUserByRoles();
        searchByUserFn();
    }
};
defineExpose({ open });

const close = () => {
    dialogVisible.value = false;
};

const searchUserByRoles = async () => {
    const res = await searchUserRoles(userId.value);
    value.value = res.map((item) => {
        return item.id;
    });
};

const searchByUserFn = async () => {
    const res = await searchByUser();
    data.value = res.map((item) => {
        return { key: item.id, label: item.roleName };
    });
    beforeValue.value = res.map((item) => {
        return { key: item.id, label: item.roleName };
    });
};

const updateUserRolesFn = async () => {
    loading.value = true;
    await updateUserRoles({ roleIds: value.value, id: userId.value });
    messages.success('保存成功');

    dialogVisible.value = false;
    emit('success');
    loading.value = false;
};
</script>

<style scoped>
.header-divider {
    position: absolute;
    margin-top: -25px;
    margin-left: -20px;
}
</style>
