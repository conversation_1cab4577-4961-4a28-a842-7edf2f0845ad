<template>
    <el-dialog
        v-model="dialogVisible"
        :title="isEdit ? '编辑用户' : '添加用户'"
        class="modal-l auto-padding"
        :close-on-click-modal="false"
        @close="dialogVisible = false"
    >
        <el-form
            ref="formRef"
            v-loading="loading"
            :rules="rules"
            :model="form"
            label-position="right"
            label-width="155px"
        >
            <el-form-item label="用户账号" prop="userAccount">
                <el-input
                    v-model="form.userAccount"
                    :disabled="isEdit"
                    show-word-limit
                    placeholder="请输入"
                    :readonly="readOnlyInput"
                    @focus="cancelReadonlyInput()"
                />
            </el-form-item>
            <el-form-item
                label="账户密码"
                prop="password"
                :rules="{
                    required: !notRequire,
                    pattern:
                        /^(?=.{8,20}$)(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~()-+=]+$)(?![0-9\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\W_!@#$%^&*`~()-+=]/,
                    trigger: 'change',
                    message: '请输入8-20位强密码(数字、字母、特殊字符组合)',
                }"
            >
                <el-input
                    v-model="form.password"
                    style="width: 100%"
                    :disabled="notRequire"
                    show-password
                    maxlength="128"
                    show-word-limit
                    :placeholder="isEdit ? '*******' : '请输入'"
                />
                <el-switch v-if="isEdit" v-model="resetPwd" class="pwdAfter" />
            </el-form-item>
            <el-form-item label="用户姓名" prop="userName">
                <el-input
                    v-model="form.userName"
                    show-word-limit
                    placeholder="请输入"
                    oninput="value=value.replace(/[^\u4E00-\u9FA5a-zA-Z]/g, '')"
                />
            </el-form-item>
            <el-form-item label="性别" prop="sex">
                <el-select v-model="form.sex" style="width: 100%">
                    <el-option label="男" :value="0" />
                    <el-option label="女" :value="1" />
                </el-select>
            </el-form-item>
            <el-form-item label="手机号码" prop="mobile">
                <el-input
                    v-model="form.mobile"
                    maxlength="11"
                    show-word-limit
                    placeholder="请输入"
                />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
                <el-input
                    v-model="form.email"
                    maxlength="50"
                    show-word-limit
                    placeholder="请输入"
                />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
            </el-form-item>
        </el-form>
        <template v-slot:footer>
            <div class="dialog-footer">
                <div class="footer-right">
                    <el-button size="small" :loading="loading" @click="dialogVisible = false"
                        >取 消</el-button
                    >
                    <el-button
                        type="primary"
                        size="small"
                        :loading="loading"
                        @click="confirm(formRef)"
                        >确 定</el-button
                    >
                </div>
            </div>
        </template>
    </el-dialog>
</template>
<script setup lang="ts">
import type { FormInstance } from 'element-plus';
import { assign, cloneDeep } from 'lodash';
import { computed, defineComponent, reactive, ref, unref } from 'vue';

import { addUser, updateUser } from '@/api/system/user';
import { useMessage } from '@/hooks/web/useMessage';
import { Encrypt2 } from '@/utils/AESUtil';
const message = useMessage(); // 消息弹窗
defineComponent({
    name: 'SysAddUser',
});

// 禁止 autocomplete
const readOnlyInput = ref(true);
const cancelReadonlyInput = () => {
    readOnlyInput.value = false;
};

const dialogVisible = ref(false);
const isEdit = ref(false);
const loading = ref(false);
const resetPwd = ref(false);
const formRef = ref();
let password = '';

const form = ref({
    id: undefined,
    userAccount: '',
    password: '',
    userName: '',
    sex: 1,
    mobile: '',
    email: '',
    status: 0,
});

const notRequire = computed(() => {
    return isEdit.value && !resetPwd.value;
});

const rules = reactive({
    userAccount: [
        { required: true, message: '请输入账户名称', trigger: 'blur' },
        {
            pattern: /^[a-zA-Z0-9]+$/,
            message: '仅允许输入字母、数字',
            trigger: 'change',
        },
    ],
    userName: [{ required: true, message: '请输入用户姓名', trigger: 'blur' }],
    sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
    mobile: [
        { required: true, message: '请输入手机号码', trigger: 'blur' },
        {
            pattern:
                /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[189]))\d{8}$/,
            message: '请输入正确格式手机号',
            trigger: 'change',
        },
    ],
    email: [
        {
            pattern:
                /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
            message: '请输入正确格式邮箱',
            trigger: 'change',
        },
    ],
});

const resetForm = () => {
    form.value = {
        id: undefined,
        userAccount: '',
        password: '',
        userName: '',
        sex: 1,
        mobile: '',
        email: '',
        status: 0,
    };
    resetPwd.value = false;
};

/** 打开弹窗 */
const open = (row) => {
    resetForm();
    if (row) {
        isEdit.value = true;
        assign(form.value, row);
        password = form.value.password;
        form.value.password = '';
    } else {
        isEdit.value = false;
    }
    dialogVisible.value = true;
};
defineExpose({ open });

const emit = defineEmits(['success']);
const confirm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            let params = cloneDeep(unref(form));
            // let res: any;
            if (isEdit.value) {
                resetPwd.value
                    ? (params.password = Encrypt2(params.password))
                    : (params.password = password);
            } else {
                params.password = Encrypt2(params.password);
            }
            if (isEdit.value) {
                await updateUser(params);
                emit('success');
                message.success('修改成功');
            } else {
                await addUser(params);
                emit('success');
                message.success('添加成功');
            }
            dialogVisible.value = false;
            // if (res.code === 0) emit('success');
        } else {
            console.log('error submit!', fields);
        }
    });
};
</script>
<style lang="less" scoped>
:deep(.el-dialog__body) {
    padding: 30px 160px 0 120px;
    .el-select,
    .el-input-number {
        width: 100%;
        input {
            text-align: left;
        }
    }
}
:v-deep(.el-dialog__footer) {
    .dialog-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .footer-right {
            margin-left: auto;
        }
    }
}

.center {
    line-height: 0;
    :v-deep(span) {
        display: flex;
        align-items: center;
    }

    img {
        width: 14px;
        height: 14px;
        margin-right: 10px;
    }
}
.pwdAfter::after {
    content: '修改密码';
    position: absolute;
    left: 104%;
    white-space: nowrap;
}
</style>
