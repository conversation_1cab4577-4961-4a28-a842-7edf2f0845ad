<template>
    <div class="search-item f-s-14 o-a">
        <el-dialog
            v-model="dialogVisible"
            center
            title="更改产地主体"
            width="700px"
            destroy-on-close
            :before-close="close"
            :close-on-click-modal="false"
        >
            <el-form :model="formData" label-width="100px">
                <el-form-item label="产地主体" prop="teaSubjectId">
                    <el-select
                        v-model="formData.teaSubjectId"
                        placeholder="请选择产地主体"
                        filterable
                        remote
                        :remote-method="getTeaSubjectOptions"
                    >
                        <el-option
                            v-for="item in teaSubjectList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
            </el-form>

            <template v-slot:footer>
                <span class="dialog-footer">
                    <el-divider class="m-12-0" />
                    <el-button @click="close">取 消</el-button>
                    <el-button type="primary" :loading="loading" @click="handleSubmit"
                        >保存</el-button
                    >
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue';

import { getTeaSubjectDetail, getTeaSubjectList } from '@/api/teaSubject';
import { bindUserTeaSubject, getUserTeaSubjectId } from '@/api/user';
import { useMessage } from '@/hooks/web/useMessage';
const messages = useMessage();

const dialogVisible = ref(false);

const defaultCheckedKeys = ref([]);
const loading = ref(false);
const loadingDialog = ref(false);

const teaSubjectList = ref([]);
const userId = ref('');

var company = reactive<any>({});

// 初始数据
const formData = reactive<{
    teaSubjectId: string;
}>({
    teaSubjectId: null,
});

/** 打开弹窗 */
const open = async (row) => {
    dialogVisible.value = true;

    if (row) {
        userId.value = row.id;
        getUserTeaSubject();
    }
};
defineExpose({ open });

/** 关闭 */
const close = () => {
    dialogVisible.value = false;
    loadingDialog.value = false;
    defaultCheckedKeys.value = [];
    formData.teaSubjectId = null;
    company = reactive<any>({});
};

/** 获取产地主体列表 */
const getTeaSubjectOptions = async (query) => {
    const res = await getTeaSubjectList({
        subjectName: query,
        page: 1,
        pageSize: 100,
    });

    teaSubjectList.value = res.list.map((item) => {
        return {
            label: item.subjectName,
            value: item.id,
        };
    });
};

// 获取用户绑定信息
const getUserTeaSubject = () => {
    loadingDialog.value = true;
    getUserTeaSubjectId({ userId: userId.value }).then(async (res) => {
        console.log('getUserTeaSubjectId', res);
        if (res) {
            await handleInitTeaSubjectList(res);
            formData.teaSubjectId = res;
        }
        loadingDialog.value = false;
    });
};

/** 更新一下产地主体列表初始值 */
const handleInitTeaSubjectList = async (subjectId: string) => {
    const res = await getTeaSubjectDetail({ id: subjectId });
    if (res) {
        // 这里只是做一下临时行的显示，后面会用id覆盖掉
        formData.teaSubjectId = res.subjectName;
        await getTeaSubjectOptions(res.subjectName);
        // teaSubjectList.value = [
        //     {
        //         label: res.subjectName,
        //         value: res.id,
        //     },
        // ];
    }
};

//保存更改商圈
const handleSubmit = async () => {
    if (!formData.teaSubjectId) {
        messages.error('请选择产地主体！');
        return;
    }

    bindUserTeaSubject({ userId: userId.value, teaSubjectId: formData.teaSubjectId }).then(
        (res) => {
            messages.success('保存成功');
            close();
        },
    );
};
</script>
<style scoped lang="less">
.tree-grop {
    width: 100%;
    min-height: 300px;
    max-height: 300px;
    padding: 20px;
    overflow: auto;
}

.top-title {
    margin: 20px;
    font-size: 16px;
    font-weight: 700;
}
</style>
