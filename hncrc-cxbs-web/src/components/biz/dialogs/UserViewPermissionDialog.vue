<template>
    <div class="f-s-14 o-a">
        <el-dialog
            v-model="dialogVisible"
            title="查看权限"
            width="500px"
            height="500px"
            destroy-on-close
            :before-close="close"
            :close-on-click-modal="false"
        >
            <el-divider class="header-divider" />
            <div class="tabs-grop">
                <div v-loading="loading" class="tabs-item">
                    <div class="f-s-14">
                        <div v-if="menusData?.length === 0" class="t-a-c m-t-22 c-909399">
                            <span>暂无数据</span>
                        </div>
                        <div v-else>
                            <el-tree :data="menusData" node-key="id" :props="defaultProps">
                                <template #default="{ node }">
                                    <span>
                                        <span style="margin-right: 4px">{{ node.label }}</span>
                                        <span>
                                            <!-- <el-tag
                                                :type="data.menuType === 1 ? 'primary' : 'success'"
                                                >{{ menuTypeFilter(data.menuType) }}</el-tag
                                            > -->
                                        </span>
                                    </span>
                                </template></el-tree
                            >
                        </div>

                        <!-- <div class="m-10-0">产品中心</div>
            <div class="m-10-0">需求大厅</div>
            <div class="m-10-0">数据资源</div>
            <div class="m-10-0">开发服务</div>
            <div class="m-10-0">第三方服务</div> -->
                    </div>
                </div>
            </div>

            <template v-slot:footer>
                <span class="dialog-footer">
                    <el-divider class="m-12-0" />
                    <el-button type="primary" @click="close">关闭</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import { searchUserMenus } from '@/api/system/user';
import { handleTree } from '@/utils/tree';
const dialogVisible = ref(false);
const loading = ref(false);
const menusData = ref(null);
const defaultProps = ref({
    children: 'children',
    label: 'name',
});

const tabChecked = ref(['2']);
const tab = ref('2');
let userId = '';

/** 打开弹窗 */
const open = async (row) => {
    dialogVisible.value = true;

    if (row) {
        userId = row.id;
        searchUserMenusFn();
    }
};
defineExpose({ open });

/** 关闭 */
const close = () => {
    dialogVisible.value = false;
};

const tabChange = (val) => {
    tab.value = val;
    tabChecked.value = [];
    tabChecked.value.push(val);
};

const searchUserMenusFn = async () => {
    loading.value = true;
    const res = await searchUserMenus(userId);
    menusData.value = handleTree(res);
    loading.value = false;
};
</script>

<style scoped>
.tabs-grop {
    width: 100%;
    border: 1px dashed #dcdfe6;

    /* padding: 10px 30px; */

    /* min-height: 300px;
  max-height: 300px;
  overflow: auto; */
}

.tab-item {
    height: 33px;
    margin: 10px 20px;
    line-height: 33px;
    text-align: center;
}

.checkedTab {
    color: #1890ff;
    border: 1px solid #1890ff;
}

.tabs-item {
    min-height: 300px;
    max-height: 300px;
    padding: 10px 30px;
    overflow: auto;
}
</style>
