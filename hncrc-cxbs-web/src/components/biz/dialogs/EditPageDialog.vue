<template>
    <div class="f-s-14 o-a max-h-500">
        <el-dialog
            v-model="dialogVisible"
            :title="isEdit ? '编辑权限' : '添加权限'"
            :close-on-click-modal="false"
            width="600px"
            append-to-body
        >
            <el-divider class="header-divider" />
            <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="权限类型" prop="menuType">
                    <el-radio-group v-model="form.menuType">
                        <el-radio :label="MenuTypeEnum.menu">菜单权限</el-radio>
                        <el-radio :label="MenuTypeEnum.button">按钮权限</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="权限名称" prop="name">
                    <el-input v-model.trim="form.name" max-length="100" class="w-440" />
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input v-model.trim="form.sort" class="w-440" />
                </el-form-item>
                <el-form-item
                    v-if="form.menuType === MenuTypeEnum.button"
                    label="权限内容"
                    prop="permission"
                >
                    <el-input
                        v-model.trim="form.permission"
                        max-length="100"
                        class="w-440"
                        placeholder="填写权限内容，如system:menus:query"
                    />
                </el-form-item>
                <el-form-item
                    v-if="form.menuType === MenuTypeEnum.menu"
                    label="路由路径"
                    prop="path"
                >
                    <el-input
                        v-model.trim="form.path"
                        type="textarea"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                        class="w-440"
                        max-length="100"
                        placeholder="填写路由路径，如/system/menu"
                    />
                </el-form-item>
                <el-form-item v-if="form.menuType === MenuTypeEnum.menu" label="微前端应用">
                    <el-radio-group v-model="form.isMicroApp">
                        <el-radio :label="true">是</el-radio>
                        <el-radio :label="false">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="form.isMicroApp" label="微前端应用前缀" prop="microAppPath">
                    <el-input v-model.trim="form.microAppPath" max-length="100" class="w-440" />
                </el-form-item>
                <el-form-item v-if="form.menuType === MenuTypeEnum.menu" label="组件路径">
                    <el-input
                        v-model.trim="form.component"
                        type="textarea"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                        class="w-440"
                        max-length="100"
                        placeholder="非必填，填写项目中实际组件路径，如/system/menu/index.vue"
                    />
                </el-form-item>

                <el-form-item v-if="form.menuType === MenuTypeEnum.menu" label="图标" prop="icon">
                    <IconSelect v-model="form.icon" class="w-full" />
                </el-form-item>
                <el-form-item label="父权限" prop="parentId">
                    <el-select
                        v-model="form.parentId"
                        filterable
                        clearable
                        placeholder="请选择"
                        style="width: 100%"
                    >
                        <el-option
                            v-for="item in options"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="Disabled">
                    <el-select v-model="form.isDisabled" class="w-440">
                        <el-option label="不置灰" :value="0" />
                        <el-option label="置灰" :value="1" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template v-slot:footer>
                <span class="dialog-footer">
                    <el-divider class="m-12-0" />
                    <el-button @click="close">取 消</el-button>
                    <el-button type="primary" :loading="loading" @click="handleSubmit(formRef)"
                        >保存</el-button
                    >
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script setup lang="ts">
import type { FormInstance } from 'element-plus';
import { assign } from 'lodash';
import { reactive, ref } from 'vue';

import { addMenu, getParentMenu, updateMenu } from '@/api/system/menu';
import { IconSelect } from '@/components/ReIcon';
import { getConfig } from '@/config';
import { usePermissionManage } from '@/hooks/biz/usePermissionManage';
import { useMessage } from '@/hooks/web/useMessage';

const { MenuTypeEnum } = usePermissionManage();

const messages = useMessage();
const options = ref<any[]>([]);
const loading = ref<boolean>(false);
const dialogVisible = ref<boolean>(false);
const isEdit = ref<boolean>(false);
const formRef = ref();
const form = ref({
    menuType: MenuTypeEnum.menu, // 权限类型
    component: '', // 组件路径 非必填
    isMicroApp: false, // 是否为微前端应用
    microAppPath: '',
    permission: '', // 权限内容，按钮权限才有
    name: '', // 权限名称
    path: '', // 路由路径
    sort: '', // 排序
    parentId: '', // 父级权限ID
    icon: '', // icon
    type: 1, // 废弃字段
    isDisabled: 0, // 是否禁用
    visible: '0', // 废弃字段
    extData: '', // 拓展字段，用于存储前端常用配置 如 组件路径等
});
const rules = reactive({
    microAppPath: [{ required: true, message: '请输入微前端应用前缀' }],
    menuType: [{ required: true, message: '请选择权限类型' }],
    permission: [{ required: true, message: '请输入权限内容' }],
    name: [
        { required: true, message: '请输入权限名称' },
        { max: 30, message: '名称不超过30字符', trigger: 'change' },
    ],
    path: [
        { required: true, message: '请输入Url' },
        { max: 200, message: 'url不超过200字符', trigger: 'change' },
    ],
    sort: [
        { required: true, message: '请输入排序' },
        { pattern: /^[1-9]\d*$/, message: '请输入正整数' },
    ],
});

const emit = defineEmits(['success']);
const handleFormToExtData = () => {
    const result = {
        isMicroApp: form.value.isMicroApp,
        microAppPath: form.value.microAppPath,
        component: form.value.component,
    };
    if (form.value.isMicroApp && !result.component) {
        result.component = '/subApp/' + form.value.microAppPath;
    }
    form.value.extData = JSON.stringify(result);
};
const handleExtDataToForm = () => {
    const target = JSON.parse(form.value.extData || '{}') || {};
    form.value.isMicroApp = target.isMicroApp;
    form.value.component = target.component;
    form.value.microAppPath = target.microAppPath;
};
/** 打开弹窗 */
const open = async (row) => {
    dialogVisible.value = true;
    // formRef.value.resetFields();
    resetForm(formRef.value);
    await getParent();
    if (row) {
        isEdit.value = true;
        assign(form.value, row);
        handleExtDataToForm();
    } else {
        isEdit.value = false;
    }
};
defineExpose({ open });

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
};

const close = () => {
    dialogVisible.value = false;
};

const handleSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            handleFormToExtData();
            if (isEdit.value) {
                updateMenuFn();
            } else {
                addMenuFn();
            }
        } else {
            console.log('error submit!', fields);
        }
    });
};

const updateMenuFn = async () => {
    if (!form.value.parentId) {
        form.value.parentId = '0';
    }
    await updateMenu(form.value);
    emit('success');
    dialogVisible.value = false;
    messages.success('编辑菜单成功');
};

const addMenuFn = async () => {
    if (!form.value.parentId) {
        form.value.parentId = '0';
    }
    await addMenu(form.value);
    emit('success');
    dialogVisible.value = false;
    messages.success('新增菜单成功');
};

const getParent = async () => {
    const data = {
        menuName: '',
        tenantId: getConfig('tenantId'),
    };
    const res = await getParentMenu(data);
    options.value = res;
};
</script>
<style scoped></style>
