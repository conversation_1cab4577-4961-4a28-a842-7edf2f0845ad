<template>
    <div class="f-s-14 o-a max-h-500">
        <el-dialog
            v-model="dialogVisible"
            title="新增角色"
            :before-close="close"
            :close-on-click-modal="false"
            width="500px"
        >
            <el-divider class="header-divider" />
            <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="角色名称" prop="roleName">
                    <el-input v-model.trim="form.roleName" max-length="100" class="w-240" />
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input v-model.trim="form.sort" class="w-240" />
                </el-form-item>
                <el-form-item label="类型">
                    <el-input value="自定义" class="w-240" disabled />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
                </el-form-item>
                <!-- <el-form-item label="门户" prop="portalId">
                    <el-select v-model="form.portalId" placeholder="请选择">
                        <el-option
                            v-for="item in portalOptions"
                            :key="item.portalId"
                            :label="item.portalName"
                            :value="item.portalId"
                            class="w-240"
                            max-length="100"
                        />
                    </el-select>
                </el-form-item> -->
            </el-form>
            <template v-slot:footer>
                <span class="dialog-footer">
                    <el-divider class="m-12-0" />
                    <el-button @click="close">取 消</el-button>
                    <el-button type="primary" :loading="loading" @click="handleSubmit(formRef)"
                        >保存</el-button
                    >
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script setup lang="ts">
import type { FormInstance } from 'element-plus';
import { assign } from 'lodash';
import { reactive, ref, unref } from 'vue';

import { checkRoleName, insertRole } from '@/api/system/role';
import { getConfig } from '@/config';
import { useMessage } from '@/hooks/web/useMessage';

const messages = useMessage();
const dialogVisible = ref<boolean>(false);
const loading = ref<boolean>(false);
// const portalOptions = ref<any[]>([]);
const formRef = ref();
const form = ref({
    roleName: '',
    sort: '',
    roleType: '2',
    status: '',
    tenantId: getConfig('tenantId'),
    // portalId: '',
});

// 检测角色名是否可用
const getCheckRoleName = async (rule, value, callback) => {
    if (value) {
        await checkRoleName(form.value.roleName).catch(() => {
            callback('该角色名已被使用');
        });
    } else {
        callback();
    }
};

const rules = reactive({
    roleName: [
        { required: true, message: '请输入角色名称' },
        { max: 30, message: '名称不超过30字符', trigger: 'change' },
        { validator: getCheckRoleName, trigger: 'blur' },
    ],
    sort: [{ required: true, message: '请输入排序' }],
    // portalId: [{ required: true, message: '请选择门户' }],
});

const emit = defineEmits(['success']);

/** 打开弹窗 */
const open = async (row) => {
    dialogVisible.value = true;
    resetForm(formRef.value);
    if (row) {
        assign(form.value, row);
    }
};
defineExpose({ open });

/** 关闭弹窗 */
const close = () => {
    dialogVisible.value = false;
};
/** 提交 */
const handleSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            try {
                loading.value = true;
                let params = unref(form);
                await insertRole(params);
                dialogVisible.value = false;
                emit('success');
                messages.success('新增角色成功');
            } finally {
                loading.value = false;
            }
        } else {
            console.log('error submit!', fields);
        }
    });
};

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
};
</script>
<style scoped></style>
