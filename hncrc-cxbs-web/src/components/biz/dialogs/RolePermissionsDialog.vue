<template>
    <div class="search-item f-s-14 o-a">
        <el-dialog
            v-model="dialogVisible"
            title="定义权限"
            width="700px"
            destroy-on-close
            :before-close="close"
            :close-on-click-modal="false"
        >
            <el-divider class="header-divider" />
            <div v-loading="loadingDialog" class="tree-grop">
                <el-tree
                    ref="treeRef"
                    :data="data"
                    show-checkbox
                    node-key="id"
                    :props="defaultProps"
                    :default-checked-keys="defaultCheckedKeys"
                >
                    <template #default="{ node, data }">
                        <span>
                            <span style="margin-right: 4px">{{ node.label }}</span>
                            <span>
                                <el-tag :type="data.menuType === 1 ? 'primary' : 'success'">{{
                                    menuTypeFilter(data.menuType)
                                }}</el-tag>
                            </span>
                        </span>
                    </template></el-tree
                >
            </div>
            <template v-slot:footer>
                <span class="dialog-footer">
                    <el-divider class="m-12-0" />
                    <el-button @click="close">取 消</el-button>
                    <el-button type="primary" :loading="loading" @click="handleSubmit"
                        >保存</el-button
                    >
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';

import { searchMyMenus, searchRoleTree, updateRoleMenus } from '@/api/system/role';
import { getConfig } from '@/config';
import { useMessage } from '@/hooks/web/useMessage';
import { menuTypeFilter } from '@/utils/filters/menu.state.filter';
import { handleTree } from '@/utils/tree';
const messages = useMessage();

const dialogVisible = ref(false);
const treeRef = ref();
const data = ref<any[]>([]);
const defaultProps = ref({
    children: 'children',
    label: 'name',
});
const defaultCheckedKeys = ref([]);
const loading = ref(false);
const loadingDialog = ref(false);
let roleId = '';
/** 打开弹窗 */
const open = async (row) => {
    dialogVisible.value = true;

    if (row) {
        loadingDialog.value = true;
        roleId = row.id;
        Promise.all([searchMyMenusFn(), searchRoleTreeFn()]).finally(() => {
            loadingDialog.value = false;
        });
    }
};
defineExpose({ open });

/** 关闭 */
const close = () => {
    dialogVisible.value = false;
    loadingDialog.value = false;
    defaultCheckedKeys.value = [];
};

const handleSubmit = async () => {
    loading.value = true;
    try {
        await updateRoleMenus({
            menuIds: (treeRef.value.getCheckedNodes(false, false) || []).map((item) => item.id),
            roleId,
            tenantId: getConfig('tenantId'), //	1系统用户，2金融机构用户
        });
        messages.success('保存成功');
        dialogVisible.value = false;
    } finally {
        loading.value = false;
    }
};

const searchRoleTreeFn = async () => {
    const res = await searchRoleTree();
    data.value = handleTree(res);
};

const searchMyMenusFn = async () => {
    const res = await searchMyMenus(roleId);
    defaultCheckedKeys.value = res.map((item) => {
        return String(item.id);
    });
};
</script>
<style scoped lang="less">
.tree-grop {
    width: 100%;
    min-height: 300px;
    max-height: 300px;
    padding: 20px;
    overflow: auto;
}
</style>
