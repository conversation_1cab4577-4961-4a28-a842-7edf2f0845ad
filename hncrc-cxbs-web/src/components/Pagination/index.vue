<script setup lang="ts">
import { omit } from 'lodash-es';
import { computed } from 'vue';

import { defaultPager, defaultProps } from './config';
import type { PaginationProps } from './type';
const props = withDefaults(defineProps<PaginationProps>(), defaultProps);
const emits = defineEmits<{
    change: [PageQuery];
}>();
const Props = computed(() => omit(props, ['pageSize', 'currentPage']));

const pageSize = defineModel('pageSize', {
    type: Number,
    default: defaultPager.pageSize,
});
const currentPage = defineModel('currentPage', {
    type: Number,
    default: defaultPager.pageNo,
});

/** 条数改变 */
const handleSizeChange = (nextPageSize: number) => {
    currentPage.value = 1;
    pageSize.value = nextPageSize;
    emitsChange({
        pageNo: 1,
        pageSize: nextPageSize,
    });
};

/** 页数改变 */
const handleCurrentChange = (nextCurrentPage: number) => {
    currentPage.value = nextCurrentPage;
    emitsChange({
        pageNo: nextCurrentPage,
        pageSize: pageSize.value,
    });
};

const emitsChange = (event: PageQuery) => {
    emits('change', event);
};
</script>

<template>
    <el-pagination
        v-bind="Props"
        v-model:page-size="pageSize"
        v-model:current-page="currentPage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    >
        {{ pageSize }}
    </el-pagination>
</template>

<style scoped>
.el-pagination {
    padding: 10px 0;
}
</style>
