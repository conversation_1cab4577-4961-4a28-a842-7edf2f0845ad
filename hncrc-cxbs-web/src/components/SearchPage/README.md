## 基本使用
```vue
<script setup lang="ts">
import { defineSearchPage } from '@/components/SearchPage/hook'

const searchPageProps = defineSearchPage({
  /** 列表请求接口 */
  service: MenuAPI.getRoutes,
  /** 表单配置  参考Form组件 */
  formItems: [
    {
      is: 'input',
      formItem: {
        prop: 'name',
        label: '名字',
      },
    },
  ],
  /** 表头配置  参考Table组件 */
  columns: {
    index: {},
    selection: {},
    dictType: {
      label: '字典类型',
      type: 'dict',
      dictType: 'sys_dict_type', // 字典类型
    },
    image: {
      label: '图片',
    },
  },
})
  
</script>

<template>
  <SearchPage v-bind="searchPageProps" />
</template>
```

## 扩展
### 插槽穿透
使用子组件名字+slot 名字实现多层slot传递

```vue
<template>
  <SearchPage v-bind="searchPageProps" >
    <template #table-header>
      <div>自定义头部</div>
    </template>
    <!-- 或者 -->
    <template #tableFooter>
      <div>自定义底部</div>
    </template>
  </SearchPage>
</template>
```

### props穿透
使用子组件名字+props 名字实现多层props传递

```vue
<template>
  <SearchPage v-bind="searchPageProps" table-height="100" :form-footer="false" @table-selection-change="xxxxx" >
  </SearchPage>
</template>
```
