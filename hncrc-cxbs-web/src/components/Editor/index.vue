<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css';

import { IEditorConfig } from '@wangeditor/editor';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { nextTick, onBeforeUnmount, onMounted, ref, shallowRef, watch } from 'vue';

import { uploadImage } from './richTextConfigKey';
import { customInsert, onBeforeUpload } from './richTextUtils';
import { InsertFnType } from './type';

const editorRef = shallowRef();
const isReady = ref(false);
const mode = 'default';
const toolbarConfig = {
    excludeKeys: ['group-video'],
};
const richTextImageArr = ref([]);

const handleCreated = (editor) => {
    editorRef.value = editor;
    isReady.value = true;

    // 初始化内容
    nextTick(() => {
        if (modelValue.value) {
            editor.setHtml(modelValue.value);
        }
    });
};

const editorConfig: Partial<IEditorConfig> = {
    scroll: true,
    placeholder: '请输入内容...',
    MENU_CONF: {
        uploadImage: Object.assign(uploadImage, {
            customInsert: (res: any, insertFn: InsertFnType) => {
                customInsert(res, insertFn).then((res) => {
                    richTextImageArr.value.push(res.url);
                });
            },
            onBeforeUpload: (file: File) => {
                return onBeforeUpload(file, { editorRef: editorRef.value });
            },
        }),
        insertImage: {
            /*onInsertedImage: async (imageNode: ImageElement | null) => {
                return onInsertedImage(imageNode, (file: File) => {
                    uploadRequset({ file: file } as any).then((res) => {
                        richTextImageArr.value.push(res.url);
                    });
                });
            },*/
        },
    },
};

const modelValue = defineModel<string>();

// 监听值变化
watch(
    () => modelValue.value,
    async (val) => {
        // 确保编辑器已经准备好且内容确实发生变化
        if (!isReady.value || !editorRef.value) return;

        const currentHtml = editorRef.value.getHtml();
        if (currentHtml !== val) {
            await nextTick();
            editorRef.value.setHtml(val || '');
        }
    },
    { immediate: true },
);

// 组件销毁时的清理
onBeforeUnmount(() => {
    isReady.value = false;
    if (editorRef.value) {
        editorRef.value.destroy();
    }
});

const isShowEdit = ref(false);
onMounted(() => {
    isShowEdit.value = true;
});
</script>

<template>
    <div style="border: 1px solid #ccc; width: 100%">
        <Toolbar
            style="border-bottom: 1px solid #ccc"
            :editor="editorRef"
            :defaultConfig="toolbarConfig"
            :mode="mode"
        />
        <Editor
            v-if="isShowEdit"
            v-model="modelValue"
            style="height: 400px; overflow-y: hidden"
            :defaultConfig="editorConfig"
            :mode="mode"
            @onCreated="handleCreated"
        />
    </div>
</template>

<style scoped></style>
