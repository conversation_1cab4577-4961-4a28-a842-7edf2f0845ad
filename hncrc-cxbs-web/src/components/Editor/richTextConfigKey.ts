import { storageLocal } from '@pureadmin/utils';
import { ElMessage } from 'element-plus';

import { TokenKey } from '@/utils/auth';

// 单个文件的最大体积限制
const maxFileSize = 20 * 1024 * 1024;

/**
 * 富文本上传配置
 */
export const uploadImage = {
    // 小于这个值时，将图片转成 base64 格式 不上传服务器 , 插入编辑器中
    // base64LimitSize: 20 * 1024 * 1024, // 20mb,
    // 图片服务端地址
    server: '/api/creditbs/admin-api/upload/uploadFile',

    fieldName: 'file',

    // 单个文件的最大体积限制，默认为 2M
    maxFileSize: maxFileSize,

    // 最多可上传几个文件，默认为 100
    maxNumberOfFiles: 10,

    // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
    allowedFileTypes: ['image/*'],

    // 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
    /* meta: {
        token: 'xxx',
        otherKey: 'yyy',
    },*/

    // 将 meta 拼接到 url 参数中，默认 false
    metaWithUrl: false,

    // 自定义增加 http  header
    headers: {
        Authorization: storageLocal().getItem<string | null>(TokenKey),
    },

    // 跨域是否传递 cookie ，默认为 false
    withCredentials: true,

    // 超时时间，默认为 10 秒
    timeout: 3 * 1000, // 5 秒

    // 上传错误，或者触发 timeout 超时
    onError(file: File, err: any, res: any) {
        const uploadFileSize = file.size;

        if (uploadFileSize > maxFileSize) {
            ElMessage.error({
                message: `上传文件大小不能超过${Math.ceil(maxFileSize / (1024 * 1024))}MB`,
                grouping: true,
            });
        }
        // TS 语法
        // onError(file, err, res) {               // JS 语法
        console.log(`${file.name} 上传出错`, err, res);
    },
};
