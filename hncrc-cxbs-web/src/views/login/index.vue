<script setup lang="ts">
import Check from '@iconify-icons/ep/check';
import Lock from '@iconify-icons/ri/lock-fill';
import User from '@iconify-icons/ri/user-3-fill';
import { debounce } from '@pureadmin/utils';
import { storageLocal } from '@pureadmin/utils';
import { useEventListener } from '@vueuse/core';
import type { FormInstance } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

import * as LoginApi from '@/api/login/index';
import darkIcon from '@/assets/svg/dark.svg?component';
import dayIcon from '@/assets/svg/day.svg?component';
import globalization from '@/assets/svg/globalization.svg?component';
import { useRenderIcon } from '@/components/ReIcon/src/hooks';
import { getConfig } from '@/config';
import { useDataThemeChange } from '@/layout/hooks/useDataThemeChange';
import { useLayout } from '@/layout/hooks/useLayout';
import { useNav } from '@/layout/hooks/useNav';
import { useTranslationLang } from '@/layout/hooks/useTranslationLang';
import { $t, transformI18n } from '@/plugins/i18n';
import { initRouter } from '@/router/utils';
import { useBusinessDistrictZoneStore } from '@/store/modules/businessDistrictZone';
import { usePermissionStoreHook } from '@/store/modules/permission';
import { useUserStoreHook } from '@/store/modules/user';
import { type DataInfo, userKey } from '@/utils/auth';
import { getAssetsFileUrl } from '@/utils/file';
import { message } from '@/utils/message';

import Motion from './utils/motion';
import { loginRules } from './utils/rule';

const CorpName = getConfig('CorpName');
const CorpUrl = getConfig('CorpUrl') as string;
defineOptions({
    name: 'Login',
});
const businessDistrictZoneStore = useBusinessDistrictZoneStore();
const router = useRouter();
const loading = ref(false);
const disabled = ref(false);
const ruleFormRef = ref<FormInstance>();
const imageCodeUrl = ref('');
const { t } = useI18n();
const { initStorage } = useLayout();
initStorage();
const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange();
dataThemeChange(overallStyle.value);
const { title, logout, getDropdownItemStyle, getDropdownItemClass } = useNav();
const { locale, translationCh, translationEn } = useTranslationLang();

const ruleForm = reactive({
    userAccount: '',
    password: '',
    imgCode: '', // 图形验证码
    randomCode: '', // 获取图形验证码返回的随机码
});

const onLogin = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate((valid, fields) => {
        if (valid) {
            loading.value = true;
            useUserStoreHook()
                .loginByUsername({
                    ...ruleForm,
                })
                .then(() => getZoneId())
                .then(() => initRouter())
                .then(() => {
                    disabled.value = true;
                    return router.push(usePermissionStoreHook().wholeMenus[1].path);
                })
                .then(() => {
                    message('登录成功', { type: 'success' });
                })
                .catch(() => {
                    logout();
                    getCode();
                })
                .finally(() => (loading.value = false));
        } else {
            return fields;
        }
    });
};

const getCode = () => {
    LoginApi.getImageCaptcha().then((res) => {
        imageCodeUrl.value = res.image;
        ruleForm.randomCode = res.randomCode;
    });
};

const getZoneId = async () => {
    const UserInfo = storageLocal().getItem<DataInfo>(userKey);
    if (UserInfo && UserInfo.userId) {
        try {
            return Promise.resolve();
        } catch (e) {
            console.log('login-error====>', e);
        }
    }
};

onMounted(() => {
    getCode();
});
const immediateDebounce: any = debounce((formRef) => onLogin(formRef), 1000, true);

useEventListener(document, 'keypress', ({ code }) => {
    if (code === 'Enter' && !disabled.value && !loading.value) immediateDebounce(ruleFormRef.value);
});
</script>

<template>
    <div class="select-none">
        <el-image class="wave" fit="cover" :src="getAssetsFileUrl('login/login_bs.jpg')" />
        <div v-if="false" class="flex-c absolute right-5 top-3">
            <!-- 主题 -->
            <el-switch
                v-model="dataTheme"
                inline-prompt
                :active-icon="dayIcon"
                :inactive-icon="darkIcon"
                @change="dataThemeChange"
            />
            <!-- 国际化 -->
            <el-dropdown trigger="click">
                <globalization
                    class="hover:text-primary hover:!bg-[transparent] w-[20px] h-[20px] ml-1.5 cursor-pointer outline-none duration-300"
                />
                <template #dropdown>
                    <el-dropdown-menu class="translation">
                        <el-dropdown-item
                            :style="getDropdownItemStyle(locale, 'zh')"
                            :class="['dark:!text-white', getDropdownItemClass(locale, 'zh')]"
                            @click="translationCh"
                        >
                            <IconifyIconOffline
                                v-show="locale === 'zh'"
                                class="check-zh"
                                :icon="Check"
                            />
                            简体中文
                        </el-dropdown-item>
                        <el-dropdown-item
                            :style="getDropdownItemStyle(locale, 'en')"
                            :class="['dark:!text-white', getDropdownItemClass(locale, 'en')]"
                            @click="translationEn"
                        >
                            <span v-show="locale === 'en'" class="check-en">
                                <IconifyIconOffline :icon="Check" />
                            </span>
                            English
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>

        <div class="login-container">
            <div class="img">
                <!-- <component :is="toRaw(illustration)" /> -->
            </div>
            <div class="login-box">
                <div class="login-form">
                    <el-image class="mt-[10px]" :src="getAssetsFileUrl('image/logo.png')" />
                    <div
                        class="login-title flex items-center justify-between leading-none mb-[50px] flex-wrap"
                    >
                        <div class="text-[#A8ABB2] text-[22px]">欢迎登录</div>
                        <div class="text-[#303133] text-[28px]">诚信白沙服务专区</div>
                    </div>

                    <!-- <Motion>
                        <h2 class="outline-none">
                            <TypeIt :options="{ strings: [title, '232'], cursor: false, speed: 100 }" />
                        </h2>
                    </Motion> -->

                    <el-form ref="ruleFormRef" :model="ruleForm" :rules="loginRules" size="large">
                        <Motion :delay="100">
                            <el-form-item
                                :rules="[
                                    {
                                        required: true,
                                        message: transformI18n($t('login.usernameReg')),
                                        trigger: 'blur',
                                    },
                                ]"
                                prop="userAccount"
                            >
                                <el-input
                                    v-model="ruleForm.userAccount"
                                    class="!h-[50px]"
                                    clearable
                                    :placeholder="t('login.username')"
                                    :prefix-icon="useRenderIcon(User)"
                                />
                            </el-form-item>
                        </Motion>

                        <Motion :delay="150">
                            <el-form-item prop="password">
                                <el-input
                                    v-model="ruleForm.password"
                                    class="!h-[50px]"
                                    clearable
                                    show-password
                                    :placeholder="t('login.password')"
                                    :prefix-icon="useRenderIcon(Lock)"
                                />
                            </el-form-item>
                        </Motion>

                        <Motion :delay="200">
                            <el-form-item prop="imgCode">
                                <el-input
                                    v-model="ruleForm.imgCode"
                                    class="!h-[50px]"
                                    clearable
                                    :placeholder="t('login.verifyCode')"
                                    :prefix-icon="useRenderIcon('ri:shield-keyhole-line')"
                                >
                                    <template v-slot:append>
                                        <img
                                            style="width: 100px; height: 38px"
                                            :src="imageCodeUrl"
                                            @click="getCode"
                                        />
                                    </template>
                                </el-input>
                            </el-form-item>
                        </Motion>

                        <Motion :delay="250">
                            <el-form-item>
                                <el-button
                                    class="w-full mt-4 !h-[50px] !text-[18px]"
                                    type="primary"
                                    :loading="loading"
                                    :disabled="disabled"
                                    @click="onLogin(ruleFormRef)"
                                >
                                    {{ t('login.login') }}
                                </el-button>
                            </el-form-item>
                        </Motion>
                    </el-form>
                </div>
            </div>
        </div>
        <div
            class="w-full flex-c absolute bottom-3 text-sm text-[rgba(0,0,0,0.6)] dark:text-[rgba(220,220,242,0.8)]"
        >
            Copyright © 2022-present
            <a class="hover:text-primary" :href="CorpUrl" target="_blank"> &nbsp;{{ CorpName }} </a>
        </div>
    </div>
</template>

<style scoped>
@import url('@/style/login.css');
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
    padding: 0;
}

.translation {
    ::v-deep(.el-dropdown-menu__item) {
        padding: 5px 40px;
    }

    .check-zh {
        position: absolute;
        left: 20px;
    }

    .check-en {
        position: absolute;
        left: 20px;
    }
}
</style>
