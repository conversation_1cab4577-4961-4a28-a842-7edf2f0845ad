<template>
    <section class="w-full">
        <InfoTable
            cell-empty-text="-"
            :title="dishonestIndividualsColumns.title"
            :column-count="dishonestIndividualsColumns.columnCount"
            :columns="dishonestIndividualsColumns.columns"
            :data="dishonestIndividualsColumns.data"
        />
    </section>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue';

import {
    SeedlingSubjectDetail,
    SeedlingSubjectExtraInfoIllegal,
} from '@/api/seedlingBizEntity/type';
import InfoTable from '@/components/InfoTable/index.vue';
import { InfoTableColumnItem } from '@/components/InfoTable/type';
import { hasValue } from '@/components/InfoTable/utils';

interface BaseInfoColumnItem<T> {
    key?: string;
    title: string;
    columnCount: number;
    columns: InfoTableColumnItem<T>[];
    data: T;
}

const props = withDefaults(
    defineProps<{
        subjectInfo: SeedlingSubjectDetail;
    }>(),
    {
        subjectInfo: () => ({}) as SeedlingSubjectDetail,
    },
);

/** 列入严重违法信息表格配置 */
const dishonestIndividualsColumns = reactive<BaseInfoColumnItem<SeedlingSubjectExtraInfoIllegal>>({
    title: '列入严重违法信息',
    columnCount: 6,
    columns: [
        {
            label: '列入日期',
            key: 'inDate',
            span: 1,
            formatter: (value) => (hasValue(value) ? value : '-'),
        },
        { label: '列入严重违法失信企业名单原因', key: 'inReason', span: 1 },
        { label: '决出决定机关（列入）', key: 'inOrg', span: 1 },
        {
            label: '移出日期',
            key: 'outDate',
            span: 1,
            formatter: (value) => (hasValue(value) ? value : '-'),
        },
        { label: '移出严重违法失信企业名单原因', key: 'outReason', span: 1 },
        { label: '决出决定机关（移出）', key: 'outOrg', span: 1 },
    ],
    data: {
        inDate: void 0,
        inReason: void 0,
        inOrg: void 0,
        outDate: void 0,
        outReason: void 0,
        outOrg: void 0,
    },
});

watch(
    () => props.subjectInfo,
    () => {
        if (props.subjectInfo && Object.keys(props.subjectInfo).length > 0) {
            // 注入数据
            Object.assign(dishonestIndividualsColumns, {
                data: props.subjectInfo.yzwfInfoVoList,
            });
        }
    },
);
</script>

<style scoped lang="less"></style>
