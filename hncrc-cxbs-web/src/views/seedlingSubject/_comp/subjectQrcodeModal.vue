<template>
    <div v-loading="loading" class="subject-qrcode-modal">
        <el-dialog v-model="modelVal" width="1000">
            <section id="posterCanvasId" class="poster w-[928px] h-[700px]">
                <div
                    v-if="!seedlingQrcodeUrl"
                    class="w-[928px] h-[700px] flex justify-center items-center text-center text-[#666666] text-[16px]"
                >
                    暂无二维码
                </div>
                <el-image
                    v-if="seedlingQrcodeUrl"
                    :src="seedlingQrcodeUrl"
                    fit="cover"
                    class="w-full h-full"
                />
            </section>
            <template #footer>
                <section class="flex justify-center">
                    <el-button @click="modelVal = false">关闭</el-button>
                    <el-button
                        type="primary"
                        :loading="fetching"
                        :disabled="fetching || !seedlingQrcodeUrl"
                        @click="handleDownloadQrCode"
                        >下载</el-button
                    >
                </section>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import QRCode from 'qrcode';
import { nextTick, ref, watch } from 'vue';

import { uploadSubjectDataStatistics } from '@/api/seedlingBizEntity';
import { SeedlingSubjectListItem } from '@/api/seedlingBizEntity/type';
import { getCredictLevelLabel } from '@/dicts/CredictLevelDicts';
import { getSubjectTypeLabel } from '@/dicts/SubjectTypeDicts';
import { getAssetsFileUrl } from '@/utils/file';

import { QrcodeTypeEnum } from '../config';

const props = withDefaults(
    defineProps<{
        entityInfo: SeedlingSubjectListItem;
    }>(),
    {
        entityInfo: () => ({}) as SeedlingSubjectListItem,
    },
);

/** 背景图片 */
const backgroundImage = getAssetsFileUrl('seedling_subject_qrcode_bg.png');
/** 最终生成的二维码画布 */
const qrCodeCanvas = ref<HTMLCanvasElement | null>(null);
/** 页面显示的图片链接 */
const seedlingQrcodeUrl = ref<string>(props.entityInfo.oneCode || '');
/** loading */
const loading = ref(false);

/** 绘制最终的二维码 */
const drawQrCode = async () => {
    // 创建画布
    qrCodeCanvas.value = document.createElement('canvas');

    // 创建图片加载器
    const loadImage: () => Promise<HTMLImageElement> = () => {
        return new Promise((resolve, reject) => {
            const image = new Image();
            image.onload = () => {
                resolve(image);
            };
            image.onerror = reject;
            image.src = backgroundImage;
        });
    };

    // 等待背景图加载
    const bgImage = await loadImage();

    // 设置画布的大小
    qrCodeCanvas.value.height = bgImage.height;
    qrCodeCanvas.value.width = bgImage.width;

    // 获取画布对象
    const canvasCtx = qrCodeCanvas.value.getContext('2d');

    // 将背景图绘制到画布上
    canvasCtx.drawImage(bgImage, 0, 0, qrCodeCanvas.value.width, qrCodeCanvas.value.height);

    // 绘制右侧文本
    drawRightText(canvasCtx, bgImage);

    // 生成并绘制二维码
    const qrCode = await QRCode.toCanvas(seedlingQrcodeUrl.value, {
        width: qrCodeCanvas.value.width * 0.22,
        margin: 0,
    });
    // 将二维码绘制到画布上
    canvasCtx.drawImage(qrCode, qrCodeCanvas.value.width * 0.1, qrCodeCanvas.value.height * 0.42);

    // 将二维码显示为图片
    seedlingQrcodeUrl.value = qrCodeCanvas.value.toDataURL('image/png');

    nextTick(() => {
        loading.value = false;
    });

    // 返回生成的二维码画布
    return qrCodeCanvas.value;
};

/** 将字符串截取成指定长度的数组 */
const splitString = (str: string, len: number) => {
    const arr = [];
    for (let i = 0; i < str.length; i += len) {
        arr.push(str.slice(i, i + len));
    }
    return arr;
};

/** 绘制右侧文本 */
const drawRightText = (canvasCtx: CanvasRenderingContext2D, bgImage: HTMLImageElement) => {
    // 根据背景图的大小计算文本字体大小
    const { titleFontSize, contentFontSize, subTitleFontSize } = calculateFontSize(bgImage.width);
    const positionLeft = qrCodeCanvas.value.width * 0.5;
    const positionTop = qrCodeCanvas.value.height * 0.15;

    const lineHeight = titleFontSize * 1.3; // 设置为字体大小的1.5倍作为行高

    // 将主体名称截取成最多两个部分（每个部分最多包含9个字符）
    const subjectNameArray = splitString(props.entityInfo.subjectName, 10);

    // 确保数组至少有两个元素，如果文本不足两行，则补充空字符串
    while (subjectNameArray.length < 2) {
        subjectNameArray.push('');
    }

    subjectNameArray.forEach((item, index) => {
        // 绘制主体名称
        canvasCtx.font = `bold ${titleFontSize}px Source Han Sans`;
        canvasCtx.fillStyle = '#222222';
        canvasCtx.textAlign = 'left';

        // 使用固定的行高来计算位置
        const yPosition = positionTop + lineHeight * index;

        canvasCtx.fillText(item, positionLeft, yPosition);
    });

    // 绘制统代
    const uniscid = props.entityInfo.uniscid || '';
    // 统代的positionTop
    const uniscidPositionTop = positionTop + titleFontSize * 1.2 * subjectNameArray.length;
    canvasCtx.font = `${subTitleFontSize}px Source Han Sans`;
    canvasCtx.fillStyle = '#666666';
    canvasCtx.textAlign = 'left';
    canvasCtx.fillText(uniscid, positionLeft, uniscidPositionTop);

    //绘制等级
    const creditLevel =
        props.entityInfo.creditLevel + getCredictLevelLabel(props.entityInfo.creditLevel) || '-';
    // 统代的positionTop
    const creditLevelPositionTop = uniscidPositionTop + titleFontSize * 2.1;
    canvasCtx.font = `${subTitleFontSize}px Source Han Sans`;
    canvasCtx.fillStyle = '#ffffff';
    canvasCtx.textAlign = 'left';
    canvasCtx.fillText(creditLevel, qrCodeCanvas.value.width * 0.523, creditLevelPositionTop);

    //绘制分数
    const evaluationScore = props.entityInfo.evaluationScore
        ? props.entityInfo.evaluationScore + '分'
        : '-';
    // 统代的positionTop
    canvasCtx.font = `${subTitleFontSize}px Source Han Sans`;
    canvasCtx.fillStyle = '#ffffff';
    canvasCtx.textAlign = 'left';
    canvasCtx.fillText(evaluationScore, 2250, creditLevelPositionTop);

    // 绘制主体类型
    const subjectTypeLabel = getSubjectTypeLabel(props.entityInfo.subjectType || '') || '';
    const subjectType = `主体类型：${subjectTypeLabel}`;
    // 主体类型的positionTop
    const subjectTypePositionTop = uniscidPositionTop + titleFontSize * 4.1;
    canvasCtx.font = `${contentFontSize}px Source Han Sans`;
    canvasCtx.fillStyle = '#666666';
    canvasCtx.textAlign = 'left';
    canvasCtx.fillText(subjectType, positionLeft, subjectTypePositionTop);

    // 绘制经营苗木种类
    // 将经营苗木种类截取成19个文字一组的数组
    const businessTypeArray = splitString(
        `经营苗木种类：${props.entityInfo['seedlingVariety'] || ''}`,
        17,
    );
    // 苗木经营主体的positionTop
    const businessTypePositionTop = subjectTypePositionTop + contentFontSize * 2;
    businessTypeArray.forEach((item, index) => {
        // 绘制经营苗木种类
        canvasCtx.font = `${contentFontSize}px Source Han Sans`;
        canvasCtx.fillStyle = '#666666';
        canvasCtx.textAlign = 'left';
        canvasCtx.fillText(
            item,
            positionLeft,
            businessTypePositionTop + contentFontSize * 1.5 * index,
        );
    });
};

/** 下载二维码 */
const handleDownloadQrCode = async () => {
    if (loading.value || !qrCodeCanvas.value) return;
    loading.value = true;
    const canvas = qrCodeCanvas.value;
    if (canvas) {
        const link = document.createElement('a');
        link.href = canvas.toDataURL('image/png');
        link.download = `${props.entityInfo.subjectName}一户一码.png`;
        link.click();
        // 只要点击了下载就算是下载一次了，不管保没保存
        handleUpdateDownloadCount();
        loading.value = false;
    }
};

/** 根据背景图的大小计算文本字体大小 */
const calculateFontSize = (bgImageWidth: number) => {
    // 字体大小根据背景图的宽度进行计算
    // title单字占比比例
    const titleRatio = 0.045;
    const contentRatio = titleRatio * 0.6;
    const subTitleRatio = titleRatio * 0.6;

    return {
        titleFontSize: titleRatio * bgImageWidth,
        contentFontSize: contentRatio * bgImageWidth,
        subTitleFontSize: subTitleRatio * bgImageWidth,
    };
};

/** v-model 用与控制显示弹框 */
const modelVal = defineModel<boolean>();

/** 当前tab */

/** 正在获取二维码 */
const fetching = ref(false);

/** 正在提交 */
const submiting = ref(false);
/** 统计下载一户一码下载次数 */
const handleUpdateDownloadCount = async () => {
    try {
        if (submiting.value) return;
        submiting.value = true;
        await uploadSubjectDataStatistics({
            companyId: props.entityInfo.id,
            type: QrcodeTypeEnum.familyCodeDownload,
        });
    } catch (error) {
        console.log(error);
    } finally {
        submiting.value = false;
    }
};

watch(modelVal, (newVal) => {
    if (newVal) {
        nextTick(() => {
            // 获取二维码
            seedlingQrcodeUrl.value = props.entityInfo.oneCode;
            // 绘制二维码
            drawQrCode();
        });
    }
});
</script>

<style scoped lang="less">
@primary-color: #529b2e;
@bg-color: #f0f9eb;

.subject-qrcode-modal {
    .poster {
        border-radius: 5px;
        margin: 20px;
        background: linear-gradient(
            180deg,
            rgba(240, 249, 235, 0.5532) 0%,
            rgba(240, 249, 235, 0.55) 0%,
            #ffffff 99%,
            #ffffff 100%
        );
        overflow: hidden;
        display: flex;
    }
}
</style>
