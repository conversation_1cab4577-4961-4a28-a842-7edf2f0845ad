<template>
    <section class="w-full">
        <InfoTable
            cell-empty-text="-"
            :title="sXBZXRColumns.title"
            :column-count="sXBZXRColumns.columnCount"
            :columns="sXBZXRColumns.columns"
            :data="sXBZXRColumns.data"
        />
    </section>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue';

import {
    SeedlingSubjectDetail,
    SeedlingSubjectExtraInfoExecuted,
} from '@/api/seedlingBizEntity/type';
import InfoTable from '@/components/InfoTable/index.vue';
import { InfoTableColumnItem } from '@/components/InfoTable/type';
import { hasValue } from '@/components/InfoTable/utils';

interface BaseInfoColumnItem<T> {
    key?: string;
    title: string;
    columnCount: number;
    columns: InfoTableColumnItem<T>[];
    data: T;
}

const props = withDefaults(
    defineProps<{
        subjectInfo: SeedlingSubjectDetail;
    }>(),
    {
        subjectInfo: () => ({}) as SeedlingSubjectDetail,
    },
);

/** 失信被执行人信息表格配置 */
const sXBZXRColumns = reactive<BaseInfoColumnItem<SeedlingSubjectExtraInfoExecuted>>({
    title: '失信被执行人信息',
    columnCount: 10,
    columns: [
        { label: '失信被执行人', key: 'fsxName', span: 1 },
        { label: '统一社会信用代码', key: 'uniscid', span: 1 },
        { label: '执行法院', key: 'fsxZxfyName', span: 1 },
        { label: '执行依据文号', key: 'fsxZxyj', span: 1 },
        {
            label: '立案时间',
            key: 'fsxLasj',
            span: 1,
            formatter: (value) => (hasValue(value) ? value : '-'),
        },
        { label: '案号', key: 'fsxAh', span: 1 },
        { label: '做出执行的依据单位', key: 'fsxZczxdw', span: 1 },
        { label: '被执行人履行情况', key: 'fsxLxqk', span: 1 },
        { label: '具体情形', key: 'fsxSxjtqx', span: 1 },
        {
            label: '发布时间',
            key: 'fsxFbDate',
            span: 1,
            formatter: (value) => (hasValue(value) ? value : '-'),
        },
    ],
    data: {
        fsxName: void 0,
        uniscid: void 0,
        fsxZxfyName: void 0,
        fsxZxyj: void 0,
        fsxLasj: void 0,
        fsxAh: void 0,
        fsxZczxdw: void 0,
        fsxLxqk: void 0,
        fsxSxjtqx: void 0,
        fsxFbDate: void 0,
    },
});

watch(
    () => props.subjectInfo,
    () => {
        if (props.subjectInfo && Object.keys(props.subjectInfo).length > 0) {
            // 注入数据
            Object.assign(sXBZXRColumns, {
                data: props.subjectInfo.sxbzxrInfoVoList,
            });
        }
    },
);
</script>

<style scoped lang="less"></style>
