<template>
    <div>
        <section class="header w-full flex gap-[20px]">
            <div class="left">
                <el-image
                    :src="seedlingSubjectInfo?.subjectAvatar || defaultAvatar"
                    fit="cover"
                    class="w-[95px] h-[95px] rounded-lg"
                />
            </div>
            <div class="right">
                <div class="flex gap-[20px] items-center">
                    <div class="text-[20px] text-[#000000] font-[500]">
                        {{ seedlingSubjectInfo?.subjectName }}
                    </div>
                    <ul class="flex gap-[20px]">
                        <li
                            class="text-[#409EFF] h-[28px] leading-[28px] rounded-[14px] px-[12px] border-[1px] border-[#D9ECFF] bg-[#ECF5FF] block"
                        >
                            {{
                                seedlingSubjectInfo?.subjectType
                                    ? getSubjectTypeLabel(seedlingSubjectInfo.subjectType)
                                    : ''
                            }}
                        </li>
                        <li
                            v-if="seedlingSubjectInfo?.creditLevel"
                            class="text-[#F56C6C] h-[28px] leading-[28px] rounded-[14px] px-[12px] border-[1px] block"
                            :style="getCredictLevelConfig(seedlingSubjectInfo?.creditLevel)"
                        >
                            {{
                                seedlingSubjectInfo?.creditLevel +
                                ' ' +
                                getCredictLevelLabel(seedlingSubjectInfo?.creditLevel, false)
                            }}
                        </li>
                        <li
                            v-if="seedlingSubjectInfo?.evaluationScore"
                            class="text-[#F56C6C] h-[28px] leading-[28px] rounded-[14px] px-[12px] border-[1px] block"
                            :style="getCredictLevelConfig(seedlingSubjectInfo?.creditLevel)"
                        >
                            {{ `评价得分${totalScore}` }}
                        </li>
                    </ul>
                </div>
                <div class="leading-[1.8rem] mt-[10px] text-[#606266]">
                    <span class="text-[#000000]">经营地址：</span>
                    <span>{{ seedlingSubjectInfo?.subjectAddress || '—' }}</span>
                </div>
                <div class="flex">
                    <div class="leading-[1.8rem] text-[#606266]">
                        <span class="text-[#000000]">联系电话：</span>
                        <span>{{ seedlingSubjectInfo?.contactPhone || '—' }}</span>
                    </div>
                    <div
                        v-if="
                            seedlingSubjectInfo?.saleChannel ||
                            (seedlingSubjectInfo?.qrCodeUrl != '[]' &&
                                seedlingSubjectInfo?.qrCodeUrl)
                        "
                        class="flex items-center justify-center cursor-pointer ml-[20px]"
                        @click="showSaleChannel"
                    >
                        <img
                            class="w-[25px] h-[25px]"
                            :src="getAssetsFileUrl('welcome/shopping.png')"
                        />
                        <span class="ml-[5px] text-[#8FC31F]">购买渠道</span>
                    </div>
                </div>
            </div>
            <div v-if="!isHome" class="close-icon ml-[auto]">
                <el-icon class="cursor-pointer" :size="20" @click="handleClose"><Close /></el-icon>
            </div>
        </section>
        <el-tabs v-model="currentTabName">
            <el-tab-pane
                v-if="showTabs.includes(SubjectDetailTabEnum.BaseInfo)"
                label="基本信息"
                :name="SubjectDetailTabEnum.BaseInfo"
            >
                <SeedlingSubjectInfoPanel :subjectInfo="seedlingSubjectInfo" />
            </el-tab-pane>
            <el-tab-pane
                v-if="showTabs.includes(SubjectDetailTabEnum.SeedlingInfo)"
                label="苗木信息"
                :name="SubjectDetailTabEnum.SeedlingInfo"
            >
                <SeedlingInfoPanel :subjectInfo="seedlingSubjectInfo" :isHome="isHome" />
            </el-tab-pane>
            <el-tab-pane
                v-if="showTabs.includes(SubjectDetailTabEnum.AdministrativePenalty)"
                label="行政处罚"
                :name="SubjectDetailTabEnum.AdministrativePenalty"
            >
                <AdminPenaltiesPanel :subjectInfo="seedlingSubjectInfo" />
            </el-tab-pane>
            <el-tab-pane
                v-if="showTabs.includes(SubjectDetailTabEnum.ListInSeriousIllegalInfo)"
                label="列入严重违法信息"
                :name="SubjectDetailTabEnum.ListInSeriousIllegalInfo"
            >
                <SeriousIllegalInfoPanel :subjectInfo="seedlingSubjectInfo" />
            </el-tab-pane>
            <el-tab-pane
                v-if="showTabs.includes(SubjectDetailTabEnum.DishonestExecutedPersonInfo)"
                label="失信被执行人信息"
                :name="SubjectDetailTabEnum.DishonestExecutedPersonInfo"
            >
                <JudgementDefaultersPane :subjectInfo="seedlingSubjectInfo" />
            </el-tab-pane>
            <el-tab-pane
                v-if="showTabs.includes(SubjectDetailTabEnum.CreditRatingInfo)"
                label="信用评价信息"
                :name="SubjectDetailTabEnum.CreditRatingInfo"
            >
                <CreditRatingInfoPanel :subjectInfo="seedlingSubjectInfo" @update="handleUpdate" />
            </el-tab-pane>
        </el-tabs>
    </div>
    <!--弹框显示购买渠道-->
    <SaleChannelModal
        v-model="showChannelDialog"
        :saleChannel="seedlingSubjectInfo?.saleChannel"
        :qrCodeUrl="seedlingSubjectInfo?.qrCodeUrl"
    />
</template>

<script setup lang="ts">
import { Close } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { getSeedlingEntityDetail } from '@/api/seedlingBizEntity';
import { SeedlingSubjectDetail } from '@/api/seedlingBizEntity/type';
import { getCredictLevelConfig, getCredictLevelLabel } from '@/dicts/CredictLevelDicts';
import { getSubjectTypeLabel } from '@/dicts/SubjectTypeDicts';
import { getAssetsFileUrl } from '@/utils/file';
import SaleChannelModal from '@/views/welcome/zoneSeedling/_comp/saleChannelModal.vue';

import { SubjectDetailTabEnum } from '../config';
import AdminPenaltiesPanel from './adminPenaltiesPanel.vue';
import CreditRatingInfoPanel from './creditRatingInfoPanel.vue';
import JudgementDefaultersPane from './judgementDefaultersPanel.vue';
import SeedlingInfoPanel from './seedlingInfoPanel.vue';
import SeedlingSubjectInfoPanel from './seedlingSubjectInfoPanel.vue';
import SeriousIllegalInfoPanel from './seriousIllegalInfoPanel.vue';

/** 默认头像 */
const defaultAvatar = getAssetsFileUrl('welcome/subject_default_avatar.png');

/** 苗木经营主体信息 */
const seedlingSubjectInfo = ref<SeedlingSubjectDetail & { subjectAvatar: string }>();

const showChannelDialog = ref(false);

/** 定义 props */
const props = withDefaults(
    defineProps<{
        /** 经营主体 id */
        subjectId: string;
        /** 要显示的 tabs 列表 */
        showTabs?: SubjectDetailTabEnum[];
        /** 是否是在首页显示的，主要是权限问题, 控制具体调用的苗木列表接口, 默认为false */
        isHome?: boolean;
    }>(),
    {
        showTabs: () => [
            SubjectDetailTabEnum.BaseInfo,
            SubjectDetailTabEnum.SeedlingInfo,
            SubjectDetailTabEnum.AdministrativePenalty,
            SubjectDetailTabEnum.ListInSeriousIllegalInfo,
            SubjectDetailTabEnum.DishonestExecutedPersonInfo,
            SubjectDetailTabEnum.CreditRatingInfo,
        ],
        isHome: false,
    },
);

const showSaleChannel = () => {
    showChannelDialog.value = true;
};

/** 当前tab */
const currentTabName = ref<SubjectDetailTabEnum>(SubjectDetailTabEnum.BaseInfo);

/** 正在获取经营主体详情信息 */
const fetchingDetailData = ref(false);

/**分数 */
const totalScore = ref(0);

const handleUpdate = () => {
    handleFetchDetail();
};
/** 获取经营主体详情信息 */
const handleFetchDetail = async () => {
    try {
        if (fetchingDetailData.value) return;
        fetchingDetailData.value = true;
        const res = await getSeedlingEntityDetail({ id: props.subjectId });
        if (res.zzyxqjzrq && res.zzyxqqsrq) {
            res.zzyxqjzrq =
                dayjs(res.zzyxqqsrq).format('YYYY年MM月DD日') +
                '至' +
                dayjs(res.zzyxqjzrq).format('YYYY年MM月DD日');
        }
        Object.assign(res, {
            administrativeSanction: res.xzcfInfoVoList?.length > 0 ? res.xzcfInfoVoList : {},
            dishonestIndividuals: res.yzwfInfoVoList?.length > 0 ? res.yzwfInfoVoList : {},
            SXBZXR: res.sxbzxrInfoVoList?.length > 0 ? res.sxbzxrInfoVoList : {},
        });
        // 处理数据 - 主体头像
        let subjectAvatar = { key: '', url: '' };
        try {
            subjectAvatar = JSON.parse(res?.subjectPictureListJson || '{}');
        } catch (error) {
            subjectAvatar = { key: '', url: '' };
        }

        //处理分数
        let score = '0';
        try {
            score = JSON.parse(res?.evaluationScore || '0');
            let total = 0;
            for (const i of score) {
                total += Number(i);
            }
            totalScore.value = total;
        } catch (error) {
            totalScore.value = 0;
        }
        seedlingSubjectInfo.value = {
            ...res,
            subjectAvatar: subjectAvatar?.url || '',
        };
    } catch (error) {
        console.log(error);
    } finally {
        fetchingDetailData.value = false;
    }
};

watch(
    () => props.subjectId,
    (newVal) => {
        if (newVal) {
            handleFetchDetail();
            currentTabName.value = SubjectDetailTabEnum.BaseInfo;
        }
    },
);

const router = useRouter();

/** 关闭页面-跳转回上一页路由 */
const handleClose = () => {
    router.back();
};

/** 初始化数据 */
const initData = () => {
    handleFetchDetail();
};
initData();
</script>

<style scoped lang="less"></style>
