<template>
    <template v-if="seedlingList.length === 0">
        <div class="flex items-center justify-center h-[300px] text-gray-400">暂无数据</div>
    </template>
    <template v-else>
        <div v-for="data in seedlingList" :key="data.id" class="seedling-info-card">
            <div class="flex p-4 gap-8">
                <!-- 左侧图片 -->
                <div class="w-[500px] h-[300px]">
                    <el-image
                        :src="data.seedlingPicture || defaultAvatar"
                        fit="cover"
                        class="w-full h-full rounded-lg"
                    />
                </div>

                <!-- 右侧信息 -->
                <div class="flex-1 flex flex-col w-auto gap-4">
                    <div class="mb-4">
                        <h2 class="text-2xl font-bold">{{ data.seedlingCategory || '-' }}</h2>
                    </div>

                    <div class="text-gray-600">
                        <div class="mb-[10px]">
                            <span class="font-medium text-[#909399]">苗木品种：</span>
                            <span>{{ data.seedlingVariety || '—' }}</span>
                        </div>

                        <div class="mb-[10px]">
                            <span class="font-medium text-[#909399]">规格情况：</span>
                            <span>{{ data.seedlingSpecs || '—' }}</span>
                        </div>
                        <div class="mb-[10px]">
                            <span class="font-medium text-[#909399]">优势特点：</span>
                            <span>{{ data.seedlingAdvantages || '—' }}</span>
                        </div>
                        <!--  <div>
                            <span class="font-medium text-[#909399]">其他信息：</span>
                            <span>{{ '—' }}</span>
                        </div> -->
                    </div>

                    <div class="mt-6 policy-wrapper">
                        <span class="mb-2 text-[#67C23A] bg-[#E1F3D8] w-auto p-2">相关政策</span>
                        <ul
                            v-if="data.relatedPolicyList.length > 0"
                            class="text-gray-600 space-y-1 pt-4 list-disc list-inside"
                        >
                            <li
                                v-for="policy in data.relatedPolicyList"
                                :key="policy.policyName"
                                class="policy-item"
                                @click.stop="handleClickPolicy(policy)"
                            >
                                {{ policy.policyName }}
                            </li>
                        </ul>
                        <div v-else class="ml-[30px] mt-[10px]">--</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex justify-end">
            <el-pagination
                v-model:current-page="paginationConfig.currentPage"
                v-model:page-size="paginationConfig.pageSize"
                background
                :hide-on-single-page="true"
                :size="paginationConfig.size"
                :page-sizes="paginationConfig.pageSizes"
                layout="total, sizes, prev, pager, next, jumper"
                :total="paginationConfig.total"
                @update:current-page="handleCurrentPageChange"
                @update:page-size="handlePageSizeChange"
            />
        </div>
    </template>

    <!-- 弹框显示政策 -->
    <SeedlingPolicyDetailModal v-model="showPolicyDetailDialog" :policy-info="currentPolicyInfo" />
</template>

<script setup lang="ts">
import { reactive, ref, watch } from 'vue';

import { getSubjectsSeedlingListPage } from '@/api/seedling';
import { SeedlingCategoryRespVO } from '@/api/seedling/type';
import { SeedlingSubjectDetail } from '@/api/seedlingBizEntity/type';
import { getSeedlingList } from '@/api/welcome/seedling';
import { getAssetsFileUrl } from '@/utils/file';
import SeedlingPolicyDetailModal from '@/views/welcome/zoneSeedling/_comp/seedlingPolicyDetailModal.vue';
import { PolicyListItem } from '@/views/welcome/zoneSeedling/type';

type TSeedlingInfo = SeedlingCategoryRespVO & {
    /** 苗木图片 */
    seedlingPicture: string;
    /** 相关政策 */
    // relatedPolicyList: Array<string>;
    relatedPolicyList: Array<PolicyListItem>;
};

const props = withDefaults(
    defineProps<{
        subjectInfo: SeedlingSubjectDetail;
        /** 是否是在首页显示的，主要是权限问题, 控制具体调用的苗木列表接口, 默认为false */
        isHome?: boolean;
    }>(),
    {
        subjectInfo: () => ({}) as SeedlingSubjectDetail,
        isHome: false,
    },
);

/** 默认头像 */
const defaultAvatar = getAssetsFileUrl('welcome/seedling_default_avatar.png');

/** 正在获取数据 */
const fetching = ref(false);

/** 苗木列表 */
const seedlingList = ref<TSeedlingInfo[]>([]);

/** 获取苗木列表 */
const handleFetchList = async () => {
    try {
        if (fetching.value || !props.subjectInfo.id) return;

        fetching.value = true;
        const params = {
            pageNo: paginationConfig.currentPage,
            pageSize: paginationConfig.pageSize,
            subjectId: props.subjectInfo.id,
        };

        const fetchApi = props.isHome ? getSeedlingList : getSubjectsSeedlingListPage;

        if (!props.isHome) {
            const { list = [], total = 0 } = await fetchApi(params);
            seedlingList.value = list.map((item) => {
                let seedlingPictureData = { key: '', url: '' };
                try {
                    seedlingPictureData = JSON.parse(item.seedlingPictureJson || '{}');
                } catch (error) {
                    seedlingPictureData = { key: '', url: '' };
                }

                let relatedPolicyListData = [];
                try {
                    relatedPolicyListData = JSON.parse(item.relatedPolicyListJson || '[]');
                } catch (error) {
                    relatedPolicyListData = [];
                }

                return {
                    ...item,
                    seedlingPicture: seedlingPictureData?.url || '',
                    relatedPolicyList: relatedPolicyListData || [],
                };
            });
            paginationConfig.total = Number(total);
        } else {
            const { list = [], total = 0 } = await fetchApi(params);
            seedlingList.value = list.map((item) => {
                let relatedPolicyListData = [];
                try {
                    relatedPolicyListData = JSON.parse(item.relatedPolicyListJson || '[]');
                } catch (error) {
                    relatedPolicyListData = [];
                }
                return {
                    ...item,
                    seedlingPicture: item.seedlingPictureJson || '',
                    relatedPolicyList: relatedPolicyListData || [],
                };
            });
            paginationConfig.total = Number(total);
        }
    } catch (error) {
        console.log(error);
    } finally {
        fetching.value = false;
    }
};

/** 分页器管理 */
const paginationConfig = reactive({
    /** 当前页码 */
    currentPage: 1,
    /** 每页显示条数 */
    pageSize: 10,
    /** 可配置每页显示条数 */
    pageSizes: [10, 20, 30, 40, 50],
    /** 总条数 */
    total: 0,
    /** 尺寸 */
    size: 'small',
});

/** 翻页 */
const handleCurrentPageChange = (val: number) => {
    paginationConfig.currentPage = val;
    handleFetchList();
};

/** 每页条数设置切换 */
const handlePageSizeChange = (val: number) => {
    paginationConfig.pageSize = val;
    handleFetchList();
};

watch(
    () => props.subjectInfo,
    (newVal) => {
        if (newVal && newVal.id) {
            // 重置分页器
            paginationConfig.currentPage = 1;
            handleFetchList();
        }
    },
    { immediate: true },
);

// =========================== 查看政策详情 开始 ===========================
/** 显示政策详情 */
const showPolicyDetailDialog = ref(false);
/** 政策详情信息 */
const currentPolicyInfo = ref<PolicyListItem>();
/** 点击显示政策详情 */
const handleClickPolicy = (item: PolicyListItem) => {
    currentPolicyInfo.value = { ...item };
    showPolicyDetailDialog.value = true;
};
// =========================== 查看政策详情 结束 ===========================
</script>

<style scoped lang="less">
@primary-color: #95d475;

.seedling-info-card {
    width: 100%;
    padding: 20px;
    border: 1px solid @primary-color;
    border-radius: 10px;
    margin-bottom: 10px;

    .policy-wrapper {
        .info-label {
            display: inline-flex;
            justify-content: center;
            align-content: center;
            width: auto;

            padding: 3px 12px;
            border-radius: 4px;
            border: 0px solid #e1f3d8;
            background: #f0f9eb;
            color: #67c23a;
            text-align: center;
            margin-bottom: 10px;
        }

        .info-value {
            color: #3d3d3d;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }

        .policy-item {
            cursor: pointer;
            color: #3d3d3d;

            &:hover {
                color: #67c23a;
                text-decoration: underline;
            }

            &.active {
                color: #67c23a;
            }
        }
    }
}
</style>
