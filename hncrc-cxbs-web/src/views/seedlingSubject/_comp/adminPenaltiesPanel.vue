<template>
    <section class="w-full">
        <InfoTable
            cell-empty-text="-"
            :title="administrativeSanctionColumns.title"
            :column-count="administrativeSanctionColumns.columnCount"
            :columns="administrativeSanctionColumns.columns"
            :data="administrativeSanctionColumns.data"
        />
    </section>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue';

import {
    SeedlingSubjectDetail,
    SeedlingSubjectExtraInfoPunish,
} from '@/api/seedlingBizEntity/type';
import InfoTable from '@/components/InfoTable/index.vue';
import { InfoTableColumnItem } from '@/components/InfoTable/type';
import { hasValue } from '@/components/InfoTable/utils';

interface BaseInfoColumnItem<T> {
    key?: string;
    title: string;
    columnCount: number;
    columns: InfoTableColumnItem<T>[];
    data: T;
}

const props = withDefaults(
    defineProps<{
        subjectInfo: SeedlingSubjectDetail;
    }>(),
    {
        subjectInfo: () => ({}) as SeedlingSubjectDetail,
    },
);

/** 行政处罚表格配置 */
const administrativeSanctionColumns = reactive<BaseInfoColumnItem<SeedlingSubjectExtraInfoPunish>>({
    title: '行政处罚',
    columnCount: 6,
    columns: [
        { label: '处罚名称', key: 'illegFact', span: 1 },
        { label: '处罚类型', key: 'penType', span: 1 },
        { label: '处罚结果', key: 'penResult', span: 1 },
        { label: '处罚事由', key: 'penBasis', span: 1 },
        {
            label: '处罚日期',
            key: 'penDecissDate',
            span: 1,
            formatter: (value) => (hasValue(value) ? value : '-'),
        },
        { label: '处罚机构', key: 'penAuthName', span: 1 },
    ],
    data: {
        penAuthName: void 0,
        penType: void 0,
        penResult: void 0,
        illegFact: void 0,
        penDecissDate: void 0,
        penBasis: void 0,
    },
});

watch(
    () => props.subjectInfo,
    () => {
        if (props.subjectInfo && Object.keys(props.subjectInfo).length > 0) {
            // 注入数据
            Object.assign(administrativeSanctionColumns, {
                data: props.subjectInfo.xzcfInfoVoList,
            });
        }
    },
);
</script>

<style scoped lang="less"></style>
