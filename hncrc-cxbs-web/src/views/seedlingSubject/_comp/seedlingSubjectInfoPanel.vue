<template>
    <section class="w-full">
        <InfoTable
            v-for="columnConfig in infoTableColumns"
            :key="columnConfig.title"
            class="mb-[20px]"
            cell-empty-text="-"
            :title="columnConfig.title"
            :column-count="columnConfig.columnCount"
            :columns="columnConfig.columns"
            :data="columnConfig.data"
        />
    </section>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { reactive, ref, watch } from 'vue';

import {
    SeedlingSubjectDetail,
    SeedlingSubjectExtraInfoBasicInfo,
    SeedlingSubjectExtraInfoCertificate,
    SeedlingSubjectExtraInfoZZSCJYXKZ,
} from '@/api/seedlingBizEntity/type';
import InfoTable from '@/components/InfoTable/index.vue';
import { InfoTableColumnItem } from '@/components/InfoTable/type';
import { hasValue } from '@/components/InfoTable/utils';
import { getSubjectTypeLabel } from '@/dicts/SubjectTypeDicts';

interface BaseInfoColumnItem<T> {
    key?: string;
    title: string;
    columnCount: number;
    columns: InfoTableColumnItem<T>[];
    data: T;
}

const props = withDefaults(
    defineProps<{
        subjectInfo: SeedlingSubjectDetail;
    }>(),
    {
        subjectInfo: () => ({}) as SeedlingSubjectDetail,
    },
);

/** 营业执照表格配置 */
const basicInfoColumns = reactive<BaseInfoColumnItem<SeedlingSubjectExtraInfoBasicInfo>>({
    title: '营业执照',
    columnCount: 4,
    columns: [
        { label: '统一社会信用代码', key: 'uniscid', span: 1 },
        {
            label: '主体类型',
            key: 'subjectType',
            span: 1,
            formatter: (value) => getSubjectTypeLabel(value),
        },
        { label: '登记状态', key: 'entStatus', span: 1 },
        {
            label: '注册资本(万元)',
            key: 'regCap',
            span: 1,
            formatter: (value) => (value === 0 || !value ? '-' : value),
        },
        {
            label: '成立日期',
            key: 'esDate',
            span: 1,
            formatter: (value) => (hasValue(value) ? dayjs(value).format('YYYY年MM月DD日') : '-'),
        },
        { label: '法人/经营者', key: 'legalName', span: 1 },
        { label: '登记机关', key: 'regOrg', span: 1 },
        { label: '经营范围', key: 'opScope', span: 1 },
    ],
    data: {
        uniscid: void 0,
        subjectType: void 0,
        entStatus: void 0,
        regCap: void 0,
        esDate: void 0,
        legalName: void 0,
        regOrg: void 0,
        opScope: void 0,
    },
});

/** 种子生产经营许可证表格配置 */
const zZSCJYXKZColumns = reactive<
    BaseInfoColumnItem<SeedlingSubjectExtraInfoZZSCJYXKZ & { hasBeyondScope: boolean }>
>({
    title: '种子生产经营许可证',
    columnCount: 4,
    columns: [
        { label: '许可证编号', key: 'xkzh', span: 1 },
        { label: '生产经营范围', key: 'scjyzl', span: 1 },
        {
            label: '有效期',
            key: 'zzyxqjzrq',
            span: 1,
        },
        {
            label: '是否超越范围营业',
            key: 'hasBeyondScope',
            span: 1,
            formatter: (value) => (hasValue(value) && value === 1 ? '是' : '否'),
        },
    ],
    data: {
        xkzh: void 0,
        scjyzl: void 0,
        zzyxqjzrq: void 0,
        hasBeyondScope: void 0,
    },
});

/** 经营档案表格配置 */
const jYDAColumns = reactive<
    BaseInfoColumnItem<{
        /** 是否按照规定建立档案 */
        hasStandardArchive: boolean;
        /** 档案内容是否完整及时 */
        hasArchiveComplete: boolean;
    }>
>({
    title: '经营档案',
    columnCount: 4,
    columns: [
        {
            label: '是否按照规定建立档案',
            key: 'hasStandardArchive',
            span: 1,
            formatter: (value) => (hasValue(value) && value === 1 ? '是' : '否'),
        },
        {
            label: '档案内容是否完整及时',
            key: 'hasArchiveComplete',
            span: 1,
            formatter: (value) => (hasValue(value) && value === 1 ? '是' : '否'),
        },
        { label: '', key: 'slotItem', span: 1, formatter: () => '' },
        { label: '', key: 'slotItem', span: 1, formatter: () => '' },
    ],
    data: {
        hasStandardArchive: void 0,
        hasArchiveComplete: void 0,
    },
});

/** 行业资质表格配置 */
const certificateColumns = reactive<BaseInfoColumnItem<SeedlingSubjectExtraInfoCertificate>>({
    title: '行业资质',
    columnCount: 2,
    columns: [
        { label: '商标或专利名称', key: 'patName', span: 1 },
        {
            label: '获得时间',
            key: 'sqrq',
            span: 1,
            formatter: (value) => (hasValue(value) ? value : '-'),
        },
        /*   { label: '', key: 'slotItem', span: 1, formatter: () => '' },
        { label: '', key: 'slotItem', span: 1, formatter: () => '' }, */
    ],
    data: {
        patName: void 0,
        sqrq: void 0,
    },
});

/** 种苗质量表格配置 */
const seedlingQualityColumns = reactive<
    BaseInfoColumnItem<{
        /** 种苗是否已检验 */
        hasSeedInspection: boolean;
        /** 种苗是否已检疫 */
        hasSeedQuarantine: boolean;
    }>
>({
    title: '种苗质量',
    columnCount: 4,
    columns: [
        {
            label: '种苗是否已检疫',
            key: 'hasSeedQuarantine',
            span: 1,
            formatter: (value) => (hasValue(value) && value === 1 ? '是' : '否'),
        },
        {
            label: '种苗是否已检验',
            key: 'hasSeedInspection',
            span: 1,
            formatter: (value) => (hasValue(value) && value === 1 ? '是' : '否'),
        },
        { label: '', key: 'slotItem', span: 1, formatter: () => '' },
        { label: '', key: 'slotItem', span: 1, formatter: () => '' },
    ],
    data: {
        hasSeedQuarantine: void 0,
        hasSeedInspection: void 0,
    },
});

/** 经营主体简介表格配置 */
const enterpriseInfoColumns = reactive<
    BaseInfoColumnItem<{
        /** 经营主体简介 */
        subjectIntroduction: string;
    }>
>({
    title: '经营主体简介',
    columnCount: 1,
    columns: [{ label: '经营主体简介', key: 'subjectIntroduction', span: 4 }],
    data: {
        subjectIntroduction: void 0,
    },
});

/** 包装质量表格配置 */
const packagingQualityColumns = reactive<
    BaseInfoColumnItem<{
        /** 是否有标签或使用说明 */
        hasPackageStandard: boolean;
        /** 标签或使用说明是否规范完整 */
        hasPackageComplete: boolean;
    }>
>({
    title: '包装质量',
    columnCount: 4,
    columns: [
        {
            label: '是否有标签或使用说明',
            key: 'hasPackageStandard',
            span: 1,
            formatter: (value) => (hasValue(value) && value === 1 ? '是' : '否'),
        },
        {
            label: '标签或使用说明是否规范完整',
            key: 'hasPackageComplete',
            span: 1,
            formatter: (value) => (hasValue(value) && value === 1 ? '是' : '否'),
        },
        { label: '', key: 'slotItem', span: 1, formatter: () => '' },
        { label: '', key: 'slotItem', span: 1, formatter: () => '' },
    ],
    data: {
        hasPackageStandard: void 0,
        hasPackageComplete: void 0,
    },
});

/** 基本信息表格的列配置 */
const infoTableColumns = ref([
    basicInfoColumns,
    zZSCJYXKZColumns,
    jYDAColumns,
    certificateColumns,
    enterpriseInfoColumns,
    seedlingQualityColumns,
    packagingQualityColumns,
]);

watch(
    () => props.subjectInfo,
    () => {
        if (props.subjectInfo && Object.keys(props.subjectInfo).length > 0) {
            // 注入数据
            Object.assign(basicInfoColumns.data, props.subjectInfo);
            Object.assign(zZSCJYXKZColumns.data, props.subjectInfo);
            Object.assign(jYDAColumns.data, props.subjectInfo);
            Object.assign(certificateColumns, {
                data: props.subjectInfo.patentInfoVoList,
            });
            // 注入种苗质量数据
            Object.assign(seedlingQualityColumns.data, props.subjectInfo);
            // 注入包装质量数据
            Object.assign(packagingQualityColumns.data, props.subjectInfo);
            // 注入经营主体简介数据
            Object.assign(enterpriseInfoColumns.data, props.subjectInfo);
            // 刷新页面数据
            infoTableColumns.value = [
                basicInfoColumns,
                zZSCJYXKZColumns,
                jYDAColumns,
                certificateColumns,
                enterpriseInfoColumns,
                seedlingQualityColumns,
                packagingQualityColumns,
            ];
        }
    },
);
</script>

<style scoped lang="less"></style>
