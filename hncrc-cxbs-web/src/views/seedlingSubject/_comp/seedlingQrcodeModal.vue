<template>
    <div>
        <el-dialog v-model="modelVal" width="500">
            <section id="posterCanvasId" class="w-full p-[10px] pb-[10px] flex">
                <canvas v-if="seedlingQrcodeUrl" ref="codeCanvas" class="codeCanvas" />
                <div
                    v-else
                    class="w-[100px] h-[100px] border border-[#E5E5E5] rounded-lg flex justify-center items-center text-center text-[#666666] text-[14px]"
                >
                    暂无二维码
                </div>
                <div class="flex flex-col justify-center gap-[5px] ml-[20px]">
                    <div class="text-[#303133] text-[18px] font-[600]">
                        {{ entityInfo.subjectName }}
                    </div>
                    <div>{{ getSubjectTypeLabel(entityInfo.subjectType) || '-' }}</div>
                </div>
            </section>
            <template #footer>
                <section class="flex justify-center">
                    <el-button @click="modelVal = false">关闭</el-button>
                    <el-button
                        type="primary"
                        :loading="fetching"
                        :disabled="fetching || !seedlingQrcodeUrl"
                        @click="handleDownloadPoster"
                        >下载</el-button
                    >
                </section>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import html2canvas from 'html2canvas';
import QRCode from 'qrcode';
import { nextTick, ref, watch } from 'vue';

import { uploadSubjectDataStatistics } from '@/api/seedlingBizEntity';
import { SeedlingSubjectListItem } from '@/api/seedlingBizEntity/type';
import { getSubjectTypeLabel } from '@/dicts/SubjectTypeDicts';

import { QrcodeTypeEnum } from '../config';

const props = withDefaults(
    defineProps<{
        entityInfo: SeedlingSubjectListItem;
    }>(),
    {
        entityInfo: () => ({}) as SeedlingSubjectListItem,
    },
);

/** v-model 用与控制显示弹框 */
const modelVal = defineModel<boolean>();

/** 当前tab */

/** 正在获取二维码 */
const fetching = ref(false);

/** 二维码链接 */
const seedlingQrcodeUrl = ref('');

/** 获取二维码 */
const handleFetchQrcode = async () => {
    try {
        if (fetching.value || !modelVal.value || !props.entityInfo.id) return;
        fetching.value = true;
        // const res = await getSeedlingCode({ id: props.entityInfo.id });
        seedlingQrcodeUrl.value = props.entityInfo.oneSeedCode;
        nextTick(() => {
            initCanvas();
        });
    } catch (error) {
        console.log(error);
    } finally {
        fetching.value = false;
    }
};

/** 绘制二维码 */
const codeCanvas = ref<HTMLCanvasElement | null>(null);
const initCanvas = async () => {
    const canvas = codeCanvas.value;
    if (!canvas) return;
    canvas.width = 118;
    canvas.height = 118;
    if (!seedlingQrcodeUrl.value) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 生成并绘制二维码
    const qrCanvas = await QRCode.toCanvas(seedlingQrcodeUrl.value, {
        width: 118,
        margin: 2,
        color: {
            dark: '#000000',
            light: '#ffffff',
        },
    });
    ctx.drawImage(qrCanvas, 0, 0, 118, 118);
};

/** 下载二维码 */
const handleDownloadQrcode = () => {
    const canvas = codeCanvas.value;
    if (!canvas) return;

    if (fetching.value) return;

    canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = '一苗一码.png';
        a.click();
        URL.revokeObjectURL(url);
        fetching.value = false;
    });
};

/** 正在提交 */
const submiting = ref(false);

/** 统计下载一苗一码下载次数 */
const handleUpdateDownloadCount = async () => {
    try {
        if (submiting.value) return;
        submiting.value = true;
        await uploadSubjectDataStatistics({
            companyId: props.entityInfo.id,
            type: QrcodeTypeEnum.seedlingCodeDownload,
        });
    } catch (error) {
        console.log(error);
    } finally {
        submiting.value = false;
    }
};

/** 下载整图 */
const handleDownloadPoster = () => {
    // 要下载的整个dom节点
    const downloadDom = document.getElementById('posterCanvasId');
    html2canvas(downloadDom, { scale: 2 }).then((canvas) => {
        const a = document.createElement('a');
        a.href = canvas.toDataURL('image/png');
        a.download = `${props.entityInfo.subjectName}一苗一码.png`;
        a.click();
        // 只要点击了下载就算是下载一次了，不管保没保存
        handleUpdateDownloadCount();
    });
};

watch(modelVal, (newVal) => {
    if (newVal) {
        nextTick(() => {
            handleFetchQrcode();
        });
    }
});
</script>

<style scoped lang="less"></style>
