<script setup lang="ts">
import { ref, watch } from 'vue';

import { getScoreAndLevel } from '@/api/seedlingBizEntity';
import { useMessage } from '@/hooks/web/useMessage';

const emit = defineEmits(['update']);

const props = defineProps<{
    subjectInfo: any;
}>();
const messages = useMessage();
const grade = ref<string>('');
const showInput = ref(false);
const loading = ref(false);

let scores: number[] = [];
let data = ref([
    {
        a: '1',
        b: '生产经营许可',
        c: '许可证延续、变更及时',
        d: '5',
        dd: 'no01',
        e: '0',
        error: false,
    },
    {
        a: '2',
        b: '生产经营许可',
        c: '无超范围生产经营行为',
        d: '10',
        dd: 'no02',
        e: '0',
        error: false,
    },
    {
        a: '3',
        b: '生产经营许可',
        c: '现有生产经营条件符合发证条件',
        d: '10',
        dd: 'no03',
        e: '0',
        error: false,
    },
    {
        a: '4',
        b: '生产经营备案',
        c: '备案及时',
        d: '5',
        dd: 'no04',
        e: '0',
        error: false,
    },
    {
        a: '5',
        b: '生产经营备案',
        c: '备案信息完整真实',
        d: '5',
        dd: 'no05',
        e: '0',
        error: false,
    },
    {
        a: '6',
        b: '生产经营档案',
        c: '档案建立',
        d: '5',
        dd: 'no06',
        e: '0',
        error: false,
    },
    {
        a: '7',
        b: '生产经营档案',
        c: '档案内容齐全、记录及时',
        d: '5',
        dd: 'no07',
        e: '0',
        error: false,
    },
    {
        a: '8',
        b: '生产经营档案',
        c: '按规定保存',
        d: '5',
        dd: 'no08',
        e: '0',
        error: false,
    },
    {
        a: '9',
        b: '包装、标签和使用说明',
        c: '有标签和使用说明',
        d: '10',
        dd: 'no09',
        e: '0',
        error: false,
    },
    {
        a: '10',
        b: '包装、标签和使用说明',
        c: '包装、标签和使用说明真实、规范',
        d: '5',
        dd: 'no10',
        e: '0',
        error: false,
    },
    {
        a: '11',
        b: '种子质量',
        c: '无生产经营假劣种子行为',
        d: '10',
        dd: 'no11',
        e: '0',
        error: false,
    },
    {
        a: '12',
        b: '种子质量',
        c: '开展种子质量自检或委托检验',
        d: '10',
        dd: 'no12',
        e: '0',
        error: false,
    },
    {
        a: '13',
        b: '推广销售',
        c: '无虚假宣传行为',
        d: '5',
        dd: 'no13',
        e: '0',
        error: false,
    },
    {
        a: '14',
        b: '推广销售',
        c: '无未审先推行为',
        d: '5',
        dd: 'no14',
        e: '0',
        error: false,
    },
    {
        a: '15',
        b: '推广销售',
        c: '未发生侵犯品种权行为',
        d: '5',
        dd: 'no15',
        e: '0',
        error: false,
    },
    /* {
        a: '16',
        b: '信用评分结果',
        c: '信用评分结果',
        d: '信用评分结果',
        dd: 'no16',
        e: '-',
    },
    {
        a: '17',
        b: '信用评级结果',
        c: '信用评级结果',
        d: '信用评级结果',
        dd: 'no17',
        e: '-',
    }, */
]);

const scoreCollection = ref<number[]>([]);
const totalPoint = ref(0);
const parseEvaluationScore = () => {
    scoreCollection.value = Array.from({ length: data.value.length }, () => 0);
    if (!props.subjectInfo.evaluationScore) {
        return;
    }
    try {
        scores = JSON.parse(props.subjectInfo.evaluationScore);
        data.value.forEach((item, index) => {
            if (index < scores.length) {
                item.e = scores[index].toString();
            }
        });
        scoreCollection.value = data.value.map((dataItem) => Number(dataItem.e));
        totalPoint.value = scoreCollection.value.reduce((acc, curr) => acc + curr, 0);
        grade.value = props.subjectInfo.creditLevel;
        let total = 0;
        scores.forEach((score) => {
            total += Number(score);
        });
        totalPoint.value = total;
    } catch (error) {
        console.error('Failed to parse evaluationScore:', error);
    }
};

watch(
    () => props.subjectInfo,
    () => {
        parseEvaluationScore();
    },
    { immediate: false },
);
const cancel = () => {
    parseEvaluationScore();
    showInput.value = false;
};
const shouldSkip = (row, index, propName) => {
    if (index == 0) return false;

    return data.value[index - 1][propName] === row[propName];
};

const rowspanCalc = (row, index, propName) => {
    let count = 1;
    for (let i = index + 1; i < data.value.length; i++) {
        if (data.value[i][propName] === data.value[index][propName]) {
            count++;
        } else {
            break;
        }
    }
    return count;
};
const updatePoint = (item, newValue) => {
    item.error = false;
    item.e = newValue.toString();
    const index = data.value.findIndex((dataItem) => dataItem.dd === item.dd);
    if (index !== -1) {
        scoreCollection.value[index] = Number(newValue);
    }
};
const updateStyle = (item, newValue) => {
    item.error = false;
};
const handleSubmit = async () => {
    let isValid = true;
    data.value.forEach((item) => {
        const parsedValue = Number(item.e);
        const maxPoint = Number(item.d);
        if (parsedValue > maxPoint) {
            item.error = true;
            isValid = false;
        } else {
            item.error = false;
        }
    });
    console.log(data.value);
    if (!isValid) {
        messages.warning('存在无效的分数值，请检查并修正');
        return;
    }
    const scoreListString = scoreCollection.value.join(',');
    const params = {
        id: props.subjectInfo.id,
        scoreList: scoreListString,
    };
    try {
        loading.value = true;
        const result = await getScoreAndLevel(params);
        totalPoint.value = result.score;
        grade.value = result.level;
        messages.success('保存成功');
        emit('update');
        showInput.value = false;
    } catch (error) {
        messages.error('保存失败');
    } finally {
        loading.value = false;
    }
};
</script>

<template>
    <el-button v-if="!showInput" type="primary" class="edit-btn" @click="showInput = true"
        >编辑信用评价信息</el-button
    >
    <el-button
        v-if="showInput"
        :loading="loading"
        type="primary"
        class="edit-btn"
        @click="handleSubmit"
        >保存</el-button
    >
    <el-button v-if="showInput" type="default" class="edit-btn" @click="cancel">取消</el-button>
    <table class="table_">
        <thead>
            <tr>
                <th class="w-[180px]">序号</th>
                <th class="w-[180px]">评定内容</th>
                <th class="w-auto">标准</th>
                <th class="w-[100px]">分值</th>
                <th class="w-[140px]">得分/等级</th>
            </tr>
        </thead>
        <tbody>
            <tr v-for="(item, index) in data" :key="item.d">
                <td v-if="!shouldSkip(item, index, 'a')" :rowspan="rowspanCalc(item, index, 'a')">
                    {{ item.a }}
                </td>
                <td v-if="!shouldSkip(item, index, 'b')" :rowspan="rowspanCalc(item, index, 'b')">
                    {{ item.b }}
                </td>
                <td v-if="!shouldSkip(item, index, 'c')" :rowspan="rowspanCalc(item, index, 'c')">
                    {{ item.c }}
                </td>
                <td v-if="!shouldSkip(item, index, 'c')">
                    {{ item.d }}
                </td>
                <td>
                    <span v-if="!showInput">{{ item.e }}</span>
                    <div :class="{ error: item.error }" class="flex">
                        <el-input
                            v-if="showInput"
                            v-model="item.e"
                            type="number"
                            @input="updatePoint(item, $event)"
                            @focus="updateStyle(item, $event)"
                        />
                    </div>
                </td>
            </tr>
            <tr>
                <td>16</td>
                <td :colspan="3">信用评分结果</td>
                <td>{{ totalPoint }}</td>
            </tr>
            <tr>
                <td>17</td>
                <td :colspan="3">信用评级结果</td>
                <td>{{ grade || '-' }}</td>
            </tr>
        </tbody>
    </table>
</template>

<style scope lang="scss">
//给 .table 的单元格加上边框
.table_ {
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;

    th,
    td {
        border: 1px solid #e9e9e9;
    }

    tr {
        height: 58px;
    }

    th {
        background-color: #f2f3f5f5;
    }

    td {
        padding: 10px 20px;
        text-align: center;
        background-color: #fff;

        &:hover {
            background-color: #f2f3f5f5;
        }
    }
}
.edit-btn {
    float: right;
    padding: 0 10px;
    margin: 10px;
}
.demo-form-inline .el-input {
    --el-input-width: 300px;
}

h3 {
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
    margin-bottom: 5px;
}
.error {
    border: 2px solid red;
    border-radius: 5px;
}
</style>
