<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';

import {
    addSeedlingEntity,
    deleteSeedlingEntity,
    getSeedlingCategoryList,
    getSeedlingEntity,
    getSeedlingSubjectList,
    updateSeedlingEntity,
} from '@/api/seedlingBizEntity';
import { SeedlingSubjectListItem } from '@/api/seedlingBizEntity/type';
import EditableDialog from '@/components/EditableForm/index.vue';
import { useCreatableDialog, useEditableDialog } from '@/components/EntityCrud/EditableDialog/hook';
import { defineEntityCrud } from '@/components/EntityCrud/hook';
import EntityCrud from '@/components/EntityCrud/index.vue';
import { EntityCrudProps } from '@/components/EntityCrud/type';
import {
    CredictLevelDicts,
    getCredictLevelConfig,
    getCredictLevelLabel,
} from '@/dicts/CredictLevelDicts';
import { getSubjectTypeLabel, SubjectTypeDicts, SubjectTypeEnum } from '@/dicts/SubjectTypeDicts';

import SeedlingQrcodeModal from './_comp/seedlingQrcodeModal.vue';
import SubjetcQrcodeModal from './_comp/subjectQrcodeModal.vue';

const entityCrudRef = ref();

const CreditLevelEnum = {
    A: 'A 信用优秀',
    B: 'B 信用良好',
    C: 'C 信用中等',
    D: 'D 信用较差',
};

/** 信用等级列表 */
const creditLevelOptions = CredictLevelDicts.map((item) => {
    return {
        value: item.value,
        label: item.value + ' ' + item.label,
    };
});

const router = useRouter();

// ========================== 一苗一码 开始 ==========================
/** 显示一苗一码弹框 */
const showSeedlingCodeModal = ref(false);
/** 当前操作的数据 */
const currentData = ref<SeedlingSubjectListItem | null>(null);
/** 显示一苗一码 */
const handleShowSeedlingCode = (data: SeedlingSubjectListItem) => {
    let oneSeedCode = data.oneSeedCode ? data.oneSeedCode : '';
    if (oneSeedCode) {
        oneSeedCode += '&codeType=seedlingCodeView';
    }
    currentData.value = { ...data, oneSeedCode: oneSeedCode };
    showSeedlingCodeModal.value = true;
};
// ========================== 一苗一码 结束 ==========================

// ========================== 一户一码 开始 ==========================
/** 显示一苗一码弹框 */
const showSubjectCodeModal = ref(false);
/** 当前操作的数据 */
// const currentData = ref<SeedlingSubjectListItem | null>(null);
/** 显示一苗一码 */
const handleShowSubjectCode = (data: SeedlingSubjectListItem) => {
    let oneCode = data.oneCode ? data.oneCode : '';
    if (oneCode) {
        oneCode += '&codeType=familyCodeView';
    }
    currentData.value = { ...data, oneCode: oneCode };
    showSubjectCodeModal.value = true;
};
// ========================== 一户一码 结束 ==========================

/** 是或否 数字版 */
const yesOrNoOptions = [
    { value: 1, label: '是' },
    { value: 0, label: '否' },
];
const config: EntityCrudProps<SeedlingSubjectListItem> = {
    entityName: 'seedlingSubject',
    displayName: '苗木经营主体',
    filterFormItems: {
        subjectName: {
            type: 'input',
            label: '商户名称',
        },
        subjectType: {
            type: 'select',
            label: '主体类型',
            options: SubjectTypeDicts,
        },
        creditLevel: {
            type: 'select',
            label: '信用等级',
            // options: getEnumOptions(CreditLevelEnum),
            options: creditLevelOptions,
        },
    },
    operations: [
        {
            type: 'button',
            label: '新增经营主体',
            colorize: 'primary',
            actionService: async () => {
                await showSeedlingCreateDialog();
            },
        },
    ],
    tableColumns: {
        subjectName: {
            label: '商户名称',
        },
        subjectType: {
            label: '主体类型',
            formatter: (row) => getSubjectTypeLabel(row.subjectType) || '-',
        },
        uniscid: {
            label: '统一社会信用代码',
            formatter: (row) => {
                return row.uniscid || '-';
            },
        },
        legalName: {
            label: '法定代表人',
            formatter: (row) => {
                return row.legalName || '-';
            },
        },
        subjectAddress: {
            label: '地址',
            formatter: (row) => {
                return row.subjectAddress || '-';
            },
        },
        creditLevel: {
            label: '信用等级',
        },
        evaluationScore: {
            label: '评价得分',
            formatter: (row) => {
                return row.evaluationScore ? `${row.evaluationScore}` : '-';
            },
        },
        evaluationDate: {
            label: '评价日期',
            formatter: (row) => {
                return row.evaluationDate ? dayjs(row.evaluationDate).format('YYYY-MM-DD') : '-';
            },
        },
        operations: {
            label: '操作',
            width: 400,
        },
    },
    rowOperations: [
        {
            type: 'link',
            label: '商户详情',
            actionService: async (row) => {
                router.push({
                    path: '/seedlingSubject/detail',
                    query: {
                        id: row.id,
                    },
                });
            },
            displayIndex: -1,
        },
        {
            type: 'link',
            label: '查看一户一码',
            actionService: async (row) => {
                handleShowSubjectCode(row);
            },
            displayIndex: -1,
        },
        {
            type: 'link',
            label: '查看一苗一码',
            actionService: async (row) => {
                handleShowSeedlingCode(row);
            },
            displayIndex: -1,
        },
        {
            type: 'link',
            label: '编辑',
            actionService: async (row) => {
                await showSeedlingCreateDialog(row);
            },
            displayIndex: -1,
        },
    ],
    // 删除
    deleteService: (row: SeedlingSubjectListItem) => {
        return deleteSeedlingEntity(row.id);
    },
    listFetchService: getSeedlingSubjectList,
};

const entityCrudProps = defineEntityCrud(config);

//========================= 新增经营主体 开始 ==========================
const uniscidRequired = ref(true);
const subjectType = ref('');
const createFormRef = ref<any>(null);

/** 获取苗木列表 */
const getSeedlingList = async () => {
    const data = await getSeedlingCategoryList();
    seedlingCategoryList.value = data;
    // 清空已选择的苗木种类
    seedlingVarietyList.value = [];
};

const handleSubjectTypeChange = (value: string) => {
    subjectType.value = value;
    uniscidRequired.value = value !== SubjectTypeEnum.Farmer;
    createFormRef.value?.clearValidate('uniscid');
};

const showSeedlingCreateDialog = async (data?: SeedlingSubjectListItem) => {
    await getSeedlingList();
    if (data) {
        await showUpdateDialog(data);
    } else {
        await showDialog();
    }
};
const seedlingVarietyList = ref([]);
const { formProps: createFormProps, showDialog } = useCreatableDialog({
    title: '新增经营主体',
    formItems: {
        subjectName: {
            type: 'input',
            label: '企业名称',
            required: true,
            width: '50%',
        },
        subjectType: {
            type: 'slot',
            label: '企业类型',
            required: true,
            width: '50%',
        },
        uniscid: {
            type: 'slot',
            width: '50%',
            slotOnFormItem: true,
        },
        contactPhone: {
            type: 'input',
            label: '联系方式',
            required: true,
            width: '50%',
        },
        seedlingType: '苗木种类',
        hasStandardArchive: {
            type: 'select',
            label: '是否按规定建立经营档案',
            options: yesOrNoOptions,
            width: '50%',
        },
        hasArchiveComplete: {
            type: 'select',
            label: '档案内容是否完整及时',
            options: yesOrNoOptions,
            width: '50%',
        },
        hasSeedQuarantine: {
            type: 'select',
            label: '种苗是否已检疫',
            options: yesOrNoOptions,
            width: '50%',
        },
        hasSeedInspection: {
            type: 'select',
            label: '种苗是否已检验',
            options: yesOrNoOptions,
            width: '50%',
        },
        hasPackageStandard: {
            type: 'select',
            label: '包装是否有标签或使用说明',
            options: yesOrNoOptions,
            width: '50%',
        },
        hasPackageComplete: {
            type: 'select',
            label: '包装标签或使用说明是否规范完整',
            options: yesOrNoOptions,
            width: '50%',
        },
        hasBeyondScope: {
            type: 'select',
            label: '是否超范围营业（种子生产经营许可）',
            options: yesOrNoOptions,
            width: '50%',
        },
        xkzh: {
            type: 'input',
            label: '许可证编号（种子生产经营许可）',
            width: '50%',
        },
        scjyzl: {
            type: 'input',
            label: '生产经营范围（种子生产经营许可）',
            width: '50%',
        },
        zzyxqjzrq: {
            type: 'date-picker',
            label: '有效期（种子生产经营许可）',
            width: '50%',
            props: {
                type: 'daterange',
                startPlaceholder: '开始时间',
                endPlaceholder: '结束时间',
            },
        },
        saleChannel: {
            type: 'input',
            label: '购买渠道一',
        },
        subjectPhoto: {
            type: 'single-image',
            label: '经营主体照片',
            width: '50%',
            tip: [
                '单个图片大小不超过2MB，建议上传16:9比例的照片',
                '最多可上传1张图片, 且格式为 jpg、png',
            ],
        },
        qrCodeUrl: {
            type: 'single-image',
            label: '购买渠道二',
            width: '50%',
            tip: [
                '单个图片大小不超过2MB，建议上传16:9比例的照片',
                '最多可上传1张图片, 且格式为 jpg、png',
            ],
        },
        subjectAddress: {
            type: 'input',
            label: '经营地址',
        },
        subjectIntroduction: {
            type: 'textarea',
            label: '经营主体简介',
            required: true,
        },
    },
    // submitRequest: addSeedlingEntity,
    submitRequest: async (data) => {
        // 提交的参数
        const params = {
            ...data,
        };
        // 处理一下参数值
        // 需要转换值的参数key
        const keys = [
            'hasStandardArchive',
            'hasArchiveComplete',
            'hasSeedQuarantine',
            'hasSeedInspection',
            'hasPackageStandard',
            'hasPackageComplete',
            'hasBeyondScope',
        ];
        for (const key of keys) {
            params[key] && (params[key] = Number(params[key]));
        }

        // 苗木种类列表字段对应的key
        params['seedlingVarietyIds'] = seedlingVarietyList.value.map((item) => item.id);

        // 经营主体照片需要转换为JSON字符串
        if (params.subjectPhoto) {
            params['subjectPictureListJson'] = params.subjectPhoto
                ? JSON.stringify(params.subjectPhoto)
                : null;
            delete params.subjectPhoto;
        }
        if (params.qrCodeUrl) {
            params.qrCodeUrl = JSON.stringify(params.qrCodeUrl);
        }

        if (params.zzyxqjzrq) {
            params['zzyxqqsrq'] = params.zzyxqjzrq[0] + 'T00:00:00';
            params['zzyxqjzrq'] = params.zzyxqjzrq[1] + 'T00:00:00';
        }
        return addSeedlingEntity(params).then(() => {
            entityCrudRef.value?.handleRefresh();
        });
    },
    width: '800px',
});

const { formProps: updateFormProps, showDialog: showUpdateDialog } = useEditableDialog({
    title: '编辑经营主体',
    formItems: {
        id: {
            type: 'id',
            label: 'id',
        },
        subjectName: {
            type: 'input',
            label: '企业名称',
            required: true,
            width: '50%',
        },
        subjectType: {
            type: 'slot',
            label: '企业类型',
            required: true,
            width: '50%',
        },
        uniscid: {
            type: 'slot',
            width: '50%',
            slotOnFormItem: true,
        },
        contactPhone: {
            type: 'input',
            label: '联系方式',
            required: true,
            width: '50%',
        },
        seedlingType: '苗木种类',
        hasStandardArchive: {
            type: 'select',
            label: '是否按规定建立经营档案',
            options: yesOrNoOptions,
            width: '50%',
        },
        hasArchiveComplete: {
            type: 'select',
            label: '档案内容是否完整及时',
            options: yesOrNoOptions,
            width: '50%',
        },
        hasSeedQuarantine: {
            type: 'select',
            label: '种苗是否已检疫',
            options: yesOrNoOptions,
            width: '50%',
        },
        hasSeedInspection: {
            type: 'select',
            label: '种苗是否已检验',
            options: yesOrNoOptions,
            width: '50%',
        },
        hasPackageStandard: {
            type: 'select',
            label: '包装是否有标签或使用说明',
            options: yesOrNoOptions,
            width: '50%',
        },
        hasPackageComplete: {
            type: 'select',
            label: '包装标签或使用说明是否规范完整',
            options: yesOrNoOptions,
            width: '50%',
        },
        hasBeyondScope: {
            type: 'select',
            label: '是否超范围营业（种子生产经营许可）',
            options: yesOrNoOptions,
            width: '50%',
        },
        xkzh: {
            type: 'input',
            label: '许可证编号（种子生产经营许可）',
            width: '50%',
        },
        scjyzl: {
            type: 'input',
            label: '生产经营范围（种子生产经营许可）',
            width: '50%',
        },
        zzyxqjzrq: {
            type: 'date-picker',
            label: '有效期（种子生产经营许可）',
            width: '50%',
            props: {
                type: 'daterange',
                startPlaceholder: '开始时间',
                endPlaceholder: '结束时间',
            },
        },
        saleChannel: {
            type: 'input',
            label: '购买渠道一',
        },
        subjectPhoto: {
            type: 'single-image',
            label: '经营主体照片',
            width: '50%',
            tip: [
                '单个图片大小不超过2MB，建议上传16:9比例的照片',
                '最多可上传1张图片, 且格式为 jpg、png',
            ],
        },
        qrCodeUrl: {
            type: 'single-image',
            label: '购买渠道二',
            width: '50%',
            tip: [
                '单个图片大小不超过2MB，建议上传16:9比例的照片',
                '最多可上传1张图片, 且格式为 jpg、png',
            ],
        },
        subjectAddress: {
            type: 'input',
            label: '经营地址',
        },
        subjectIntroduction: {
            type: 'textarea',
            label: '经营主体简介',
            required: true,
        },
    },
    initialDataService: (data: SeedlingSubjectListItem) => {
        return getSeedlingEntity({ id: data.id }).then((res: any) => {
            subjectType.value = res.subjectType;
            seedlingVarietyList.value = res.seedlingVarietyIds.map((id) => {
                const variety = seedlingCategoryList.value.find((item) => item.id === id);
                if (variety) {
                    return variety;
                }
                const category = seedlingCategoryList.value.find((item) => item.id === id);
                if (category) {
                    return category.seedlingVariety;
                }
            });
            res.zzyxqjzrq = [res.zzyxqqsrq, res.zzyxqjzrq];
            return {
                ...res,
                // 处理图片数据
                subjectPhoto: res.subjectPictureListJson
                    ? JSON.parse(res.subjectPictureListJson)
                    : [],
                qrCodeUrl: res.qrCodeUrl ? JSON.parse(res.qrCodeUrl) : [],
            };
        });
    },
    submitRequest: async (data) => {
        // 提交的参数
        const params = {
            ...data,
        };
        // 处理一下参数值
        // 需要转换值的参数key
        const keys = [
            'hasStandardArchive',
            'hasArchiveComplete',
            'hasSeedQuarantine',
            'hasSeedInspection',
            'hasPackageStandard',
            'hasPackageComplete',
            'hasBeyondScope',
        ];
        for (const key of keys) {
            params[key] && (params[key] = Number(params[key]));
        }

        // 苗木种类列表字段对应的key
        params['seedlingVarietyIds'] = seedlingVarietyList.value.map((item) => item.id);

        // 经营主体照片需要转换为JSON字符串
        /*   if (params.subjectPhoto) {
            params['subjectPictureListJson'] =
                params.subjectPhoto.length > 0 ? JSON.stringify(params.subjectPhoto) : null;
            delete params.subjectPhoto;
        } */

        if (params.subjectPhoto) {
            params['subjectPictureListJson'] = params.subjectPhoto
                ? JSON.stringify(params.subjectPhoto)
                : null;
            delete params.subjectPhoto;
        } else {
            params['subjectPictureListJson'] = null;
        }

        if (params.qrCodeUrl) {
            params.qrCodeUrl = JSON.stringify(params.qrCodeUrl);
        }
        if (params.zzyxqjzrq) {
            params['zzyxqqsrq'] = params.zzyxqjzrq[0];
            params['zzyxqjzrq'] = params.zzyxqjzrq[1];
            // 检查并添加时间部分
            if (params['zzyxqqsrq']) {
                [params['zzyxqqsrq'], params['zzyxqjzrq']].forEach((value, index) => {
                    if (typeof value === 'string' && !value.includes('T')) {
                        params[index === 0 ? 'zzyxqqsrq' : 'zzyxqjzrq'] += 'T00:00:00'; // 添加默认的时间部分
                    } else if (value === undefined || value === null) {
                        params[index === 0 ? 'zzyxqqsrq' : 'zzyxqjzrq'] = 'T00:00:00'; // 如果值为undefined或null，则直接设置默认时间
                    }
                });
            } else {
                params['zzyxqqsrq'] = '';
                params['zzyxqjzrq'] = '';
            }
        }

        return updateSeedlingEntity(params).then(() => {
            entityCrudRef.value?.handleRefresh();
        });
    },
    width: '800px',
});

/** 苗木列表 */
const seedlingCategoryList = ref([]);

/** 苗木大类列表 */
const seedlingCategoryOptions = computed(() => {
    return seedlingCategoryList.value.reduce((acc, cur) => {
        if (!acc.find((i) => i.seedlingCategory === cur.seedlingCategory)) {
            acc.push({
                id: cur.seedlingVariety ? cur.seedlingCategory : cur.id,
                seedlingCategory: cur.seedlingCategory,
            });
        }
        console.log(9999, acc);
        return acc;
    }, []);
});

/** 新增苗木种类 */
const handldeCreateSeedlingType = async (context: any) => {
    const data = await showSeedlingTypeDialog();
    // 验证返回的数据是否完整
    if (data) {
        // 如果苗木品种存在则绑定苗木品种信息
        // 如果苗木品种不存在，判断一下是否有该苗木大类数据，如果没有的话则将绑定所有 seedlingCategory 等于 所选择大类的品种
        const selectedId = data.seedlingVariety || data.seedlingCategory;

        // 根据选择的苗木品种ID查找对应的品种信息
        const categoryItem = seedlingCategoryList.value.find((item) => item.id === selectedId);
        if (categoryItem) {
            // 检查是否已经添加过相同的苗木品种，避免重复添加
            const isExist = seedlingVarietyList.value.some(
                (variety) => variety.id === categoryItem.id,
            );
            if (!isExist) {
                seedlingVarietyList.value = [
                    ...seedlingVarietyList.value,
                    {
                        id: categoryItem.id,
                        seedlingCategory: categoryItem.seedlingCategory,
                        seedlingVariety: categoryItem.seedlingVariety,
                    },
                ];
            } else {
                // 如果已存在，给出提示
                ElMessage.warning('该苗木品种已添加，请勿重复添加');
            }
        } else {
            // 找一下所有 所有 seedlingCategory 等于 所选择大类的品种
            const seedlingVarietys = seedlingCategoryList.value.filter(
                (item) => item.seedlingCategory === selectedId,
            );
            if (seedlingVarietys.length === 0) {
                // 如果找不到对应的品种信息，给出错误提示
                ElMessage.error('未找到对应的苗木品种信息');
            }

            seedlingVarietys.forEach((item) => {
                // 检查是否已经添加过相同的苗木品种，避免重复添加
                const isExist = seedlingVarietyList.value.some((variety) => variety.id === item.id);
                if (!isExist) {
                    seedlingVarietyList.value = [
                        ...seedlingVarietyList.value,
                        {
                            id: item.id,
                            seedlingCategory: item.seedlingCategory,
                            seedlingVariety: item.seedlingVariety,
                        },
                    ];
                }
            });
        }
    }
};

/** 获取苗木品种 */
const getSeedlingVariety = (seedlingCategory: string | undefined) => {
    if (!seedlingCategory) return [];
    // 找出所有seedlingCategory的数据
    return seedlingCategoryList.value.filter((item) => item.seedlingCategory === seedlingCategory);

    // const firstSeedlingCategory = seedlingCategoryList.value.find(
    //     (item) => item.id === seedlingCategory,
    // );

    // if (!firstSeedlingCategory) return [];

    // const filtered = seedlingCategoryList.value.filter(
    //     (item) => item.seedlingCategory === firstSeedlingCategory.seedlingCategory,
    // );
    // if (filtered.length == 1 && !filtered[0].seedlingVariety) {
    //     // return [
    //     //     {
    //     //         id: filtered[0].id,
    //     //         seedlingCategory: filtered[0].seedlingCategory,
    //     //         seedlingVariety: filtered[0].seedlingCategory,
    //     //     },
    //     // ];
    //     return [];
    // } else {
    //     return filtered;
    // }
};

/** 新增苗木中弹框 */
const { formProps: seedlingTypeFormProps, showDialog: showSeedlingTypeDialog } = useCreatableDialog(
    {
        title: '添加苗木种类',
        formItems: {
            seedlingCategory: {
                label: '苗木大类',
                type: 'slot',
                required: true, // 添加必填验证
            },
            seedlingVariety: {
                label: '苗木品种',
                type: 'slot',
                // required: true, // 添加必填验证
            },
        },
    },
);

const handleDelete = (data: any, index: number) => {
    seedlingVarietyList.value = seedlingVarietyList.value.filter((_, idx) => idx !== index);
};
//========================= 新增经营主体 结束 ==========================
</script>

<template>
    <div>
        <EditableDialog v-bind="seedlingTypeFormProps">
            <template #seedlingCategory="{ formData, modelValue, set }">
                <el-select
                    :model-value="modelValue"
                    @update:model-value="set"
                    @change="() => (formData.seedlingVariety = undefined)"
                >
                    <el-option
                        v-for="item in seedlingCategoryOptions"
                        :key="item.id"
                        :value="item.id"
                        :label="item.seedlingCategory"
                    />
                </el-select>
            </template>
            <template #seedlingVariety="{ formData, modelValue, set }">
                <el-select
                    :model-value="modelValue"
                    :placeholder="formData.seedlingCategory ? '请选择苗木品种' : '请先选择苗木大类'"
                    :disabled="!formData.seedlingCategory"
                    @update:model-value="set"
                >
                    <el-option
                        v-for="item in getSeedlingVariety(formData.seedlingCategory)"
                        :key="item.id"
                        :value="item.id"
                        :label="item.seedlingVariety"
                        :disabled="
                            seedlingVarietyList.some(
                                (variety) => variety.seedlingVariety === item.seedlingVariety,
                            )
                        "
                    />
                    <!-- 当没有可选品种时显示提示 -->
                    <el-option
                        v-if="
                            formData.seedlingCategory &&
                            getSeedlingVariety(formData.seedlingCategory).length === 0
                        "
                        value=""
                        label="该大类下暂无可选品种"
                        disabled
                    />
                </el-select>
            </template>
        </EditableDialog>

        <EditableDialog v-bind="createFormProps" ref="createFormRef">
            <template #subjectType="{ modelValue, set }">
                <el-select
                    :model-value="modelValue"
                    @update:model-value="set"
                    @change="handleSubjectTypeChange"
                >
                    <el-option
                        v-for="item in SubjectTypeDicts"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                    />
                </el-select>
            </template>
            <template #uniscid="{ modelValue, set }">
                <el-form-item
                    class="w-full"
                    label="统一社会信用代码"
                    prop="uniscid"
                    :rules="
                        subjectType === SubjectTypeEnum.Farmer
                            ? []
                            : [
                                  {
                                      required: true,
                                      message: '请填写统一社会信用代码',
                                      trigger: 'blur',
                                  },
                              ]
                    "
                >
                    <el-input
                        :model-value="modelValue"
                        placeholder="请输入统一社会信用代码"
                        @update:model-value="set"
                    />
                </el-form-item>
            </template>
            <template #seedlingType="data">
                <el-table border stripe :data="seedlingVarietyList">
                    <el-table-column prop="seedlingCategory" label="苗木大类" />
                    <el-table-column prop="seedlingVariety" label="苗木品种" />
                    <el-table-column prop="operations" label="操作">
                        <template #default="{ $index }">
                            <el-button link type="primary" @click="() => handleDelete(data, $index)"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
                <div class="w-full">
                    <el-button
                        type="default"
                        class="w-full"
                        :icon="Plus"
                        @click="handldeCreateSeedlingType(data)"
                        >新增</el-button
                    >
                </div>
            </template>
            <template #saleChannel="{ modelValue, set }">
                <el-input
                    :model-value="modelValue"
                    placeholder="请输入销售链接"
                    @update:model-value="set"
                />
            </template>
        </EditableDialog>

        <EditableDialog v-bind="updateFormProps" ref="updateFormRef">
            <template #subjectType="{ modelValue, set }">
                <el-select
                    :model-value="modelValue"
                    @update:model-value="set"
                    @change="handleSubjectTypeChange"
                >
                    <el-option
                        v-for="item in SubjectTypeDicts"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                    />
                </el-select>
            </template>
            <template #uniscid="{ modelValue, set }">
                <el-form-item
                    class="w-full"
                    label="统一社会信用代码"
                    prop="uniscid"
                    :rules="
                        subjectType === SubjectTypeEnum.Farmer
                            ? []
                            : [
                                  {
                                      required: true,
                                      message: '请填写统一社会信用代码',
                                      trigger: 'blur',
                                  },
                              ]
                    "
                >
                    <el-input :model-value="modelValue" @update:model-value="set" />
                </el-form-item>
            </template>
            <template #seedlingType="data">
                <el-table border stripe :data="seedlingVarietyList">
                    <el-table-column prop="seedlingCategory" label="苗木大类" />
                    <el-table-column prop="seedlingVariety" label="苗木品种" />
                    <el-table-column prop="operations" label="操作">
                        <template #default="{ $index }">
                            <el-button link type="primary" @click="() => handleDelete(data, $index)"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
                <div class="w-full">
                    <el-button
                        type="default"
                        class="w-full"
                        :icon="Plus"
                        @click="handldeCreateSeedlingType(data)"
                        >新增</el-button
                    >
                </div>
            </template>
            <template #saleChannel="{ modelValue, set }">
                <el-input
                    :model-value="modelValue"
                    placeholder="请输入销售链接"
                    @update:model-value="set"
                />
            </template>
        </EditableDialog>

        <EntityCrud ref="entityCrudRef" v-bind="entityCrudProps">
            <template #tableCreditLevel="{ row }: { row: SeedlingSubjectListItem }">
                <div
                    v-if="row.creditLevel"
                    class="rounded text-[14px] py-1 px-3 shrink-0 group-[0] w-[95px]"
                    :style="getCredictLevelConfig(row.creditLevel)"
                >
                    {{ row.creditLevel + ' ' + getCredictLevelLabel(row.creditLevel, false) }}
                </div>
                <div v-else>-</div>
            </template>
        </EntityCrud>

        <!-- 一苗一码 -->
        <SeedlingQrcodeModal v-model="showSeedlingCodeModal" :entityInfo="currentData || {}" />

        <!-- 一户一码 -->
        <SubjetcQrcodeModal v-model="showSubjectCodeModal" :entityInfo="currentData || {}" />
    </div>
</template>
