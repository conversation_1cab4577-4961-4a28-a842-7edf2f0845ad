<script setup lang="ts">
import { dayjs, ElScrollbar } from 'element-plus';
import { onMounted, reactive, ref, shallowRef } from 'vue';
import { useRoute } from 'vue-router';

import { getAssetsFileUrl } from '@/utils/file';
import judicialCredit from '@/views/archives/component/judicialCredit.vue';

import { CREDIT_RATING } from '../enterprise/enums';
import AdministrativeInspection from './component/AdministrativeInspection.vue';
import AdministrativePenalty from './component/AdministrativePenalty.vue';
import BasicInformation from './component/BasicInformation.vue';
import CivilCredit from './component/CivilCredit.vue';
import CourtJudgmentInformation from './component/CourtJudgmentInformation.vue';
import CreditRatingIndices from './component/CreditRatingIndices.vue';
import IllegalInformation from './component/IllegalInformation.vue';
import SocialResponsibility from './component/SocialResponsibility.vue';
// import OtherCreditInformation from './component/OtherCreditInformation.vue';
import TaxViolations from './component/TaxViolations.vue';

const route = useRoute();

/**
 * 公司基本信息
 */
const company = reactive<any>({});
const extraInfo = ref<any>({});

const tableList = reactive([
    {
        title: '基础信息',
        component: shallowRef(BasicInformation),
    },
    {
        title: '行政处罚',
        component: shallowRef(AdministrativePenalty),
    },
    {
        title: '行政检查',
        component: shallowRef(AdministrativeInspection),
    },
    {
        title: '纳税评定信息',
        component: shallowRef(TaxViolations),
    },
    {
        title: '法院判决信息',
        component: shallowRef(CourtJudgmentInformation),
    },
    {
        title: '列入严重违法信息',
        component: shallowRef(IllegalInformation),
    },
    // {
    //     title: '其他信用信息',
    //     component: shallowRef(OtherCreditInformation),
    // },
    {
        title: '司法信用',
        component: shallowRef(judicialCredit),
    },
    {
        title: '民用信用信息',
        component: shallowRef(CivilCredit),
    },
    {
        title: '社会责任信息',
        component: shallowRef(SocialResponsibility),
    },
    {
        title: '信用评价指标',
        component: shallowRef(CreditRatingIndices),
    },
]);

const editableTabsValue = ref(0);

const scrollbarRef = ref<InstanceType<typeof ElScrollbar>>();
const scrollHeight = ref(0);
onMounted(() => {
    scrollHeight.value = scrollbarRef.value?.$el.scrollHeight;
});

const OrgTypeEnum = Object.freeze({
    1: '企业',
    2: '个体户',
    3: '政府机构',
});

const EntStatusEnum = Object.freeze({
    '1': '在营（开业）',
    '2': '吊销',
    '21': '吊销，未注销',
    '22': '吊销，已注销',
    '3': '注销',
    '4': '迁出',
    '5': '撤销',
    '6': '临时(个体工商户使用)',
    '8': '停业',
    '9': '其他',
    '9_01': '撤销',
    '9_02': '待迁入',
    '9_03': '经营期限届满',
    '9_04': '清算中',
    '9_05': '停业',
    '9_06': '拟注销',
    '9_07': '非正常户',
    '30': '正在注销',
    '!': '-',
});

const setExtraInfo = () => {
    if (
        extraInfo.value?.archive?.dbInfo?.table_1_col1 == '否' ||
        extraInfo.value?.archive?.dbInfo?.table_1_col2 == '是' ||
        extraInfo.value?.archive?.dbInfo?.table_1_col3 == '否'
    ) {
        extraInfo.value.table_1_status = '否';
    }
    if (
        extraInfo.value?.archive?.dbInfo?.table_10_col1 == '是' ||
        extraInfo.value?.archive?.dbInfo?.table_10_col2 == '否'
    ) {
        extraInfo.value.table_10_status = '否';
    }

    try {
        const _qyzbList = extraInfo.value.archive.qyzb.list;
        _qyzbList[0].org_type = OrgTypeEnum[_qyzbList[0]?.org_type];
        _qyzbList[0].ENTSTATUS = EntStatusEnum[_qyzbList[0].ENTSTATUS];
    } catch (e) {}

    if (!extraInfo.value?.archive?.certificate?.items) {
        extraInfo.value.archive.certificate = null;
    }
};
</script>

<template>
    <div class="flex-1 flex flex-col box-border !overflow-hidden">
        <div class="flex items-center h-[80px]">
            <div
                class="w-[70px] h-[70px] rounded-[50%] border-slate-300 border-solid border flex items-center justify-center"
            >
                <el-image class="w-[40px] h-[40px]" :src="getAssetsFileUrl('image/aa.png')" />
            </div>
            <div class="text-[#1D3A67] text-[16px] ml-[10px]">
                <div class="text-[#1D3967] text-[22px]">{{ company.companyName }}</div>
                <div class="flex space-x-3 text-[15px] mt-[4px]">
                    <div class="bg-[#F1F5FF] text-[#5F84F6] px-[10px] py-[3px] rounded-[20px]">
                        {{ company.subjectType }}
                    </div>

                    <div
                        class="px-[10px] py-[3px] rounded-[20px]"
                        :class="[levelConfig[company.creditLevel]?.class]"
                        :style="{
                            color: levelConfig[company.creditLevel]?.color,
                        }"
                    >
                        {{ CREDIT_RATING[company?.creditLevel]?.text }}
                    </div>
                    <div class="bg-[#FFF6EF] text-[#FDA544] px-[10px] py-[3px] rounded-[20px]">
                        评价得分 {{ company.evaluationScore }}
                    </div>
                </div>
            </div>
        </div>

        <div
            class="flex h-[50px] items-center space-x-[30px] mb-[10px] border-b-[1px] border-slate-300"
        >
            <div
                v-for="(item, index) in tableList"
                :key="item.title"
                class="cursor-pointer"
                :class="[
                    editableTabsValue === index &&
                        'text-[#3775FF] underline decoration-solid decoration-2 underline-offset-[18px]',
                ]"
                @click="editableTabsValue = index"
            >
                {{ item.title }}
            </div>
            <div class="flex-grow text-right pr-5 text-sm">
                更新时间: {{ dayjs(company.evaluationDate).format('YYYY-MM-DD') }}
            </div>
        </div>

        <el-scrollbar ref="scrollbarRef" class="flex-1" :height="scrollHeight" :noresize="true">
            <template v-if="scrollHeight">
                <component :is="tableList[editableTabsValue].component" :info="extraInfo" />
            </template>
        </el-scrollbar>
    </div>
</template>

<style scope lang="scss"></style>
