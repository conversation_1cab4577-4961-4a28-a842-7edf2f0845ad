<script setup lang="ts">
import { computed } from 'vue';
const props = defineProps<{
    info: any;
}>();

const data = computed(() => props.info?.cpws?.list || []);

const tableColumns = [
    {
        label: '序号',
        prop: '$index',
        width: '70px',
    },
    {
        label: '案件名称',
        prop: 'causename',
    },
    {
        label: '案件身份',
        prop: '',
    },
    {
        label: '案号',
        prop: 'CASENO',
    },
    {
        label: '涉事理由',
        prop: '',
    },
    {
        label: '发布日期',
        prop: 'sdate',
    },
    {
        label: '审理机关',
        prop: 'courtname',
    },
    {
        label: '裁决时间',
        prop: 'pdate',
    },
];
const handleDetail = (row) => {
    console.log(row);
};
</script>

<template>
    <CommonTable
        class="mt-[20px]"
        :isShowPagination="false"
        :table-data="data"
        :table-columns="tableColumns"
        :total="50"
        :pageNum="1"
        :pageSize="10"
        :columnsMixin="{
            align: 'center',
        }"
        :headerCellStyle="{
            backgroundColor: '#F8F8F9',
        }"
    >
        <template #edit="{ scope }">
            <div class="space-x-3">
                <el-text class="cursor-pointer" type="primary" @click="handleDetail(scope.row)"
                    >详情</el-text
                >
            </div>
        </template>
    </CommonTable>
</template>

<style scope lang="scss"></style>
