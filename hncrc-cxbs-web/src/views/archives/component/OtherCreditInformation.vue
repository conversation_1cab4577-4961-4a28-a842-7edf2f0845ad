<script setup lang="ts">
import { computed } from 'vue';

import { getColumnTableAttrs, getRowTableAttrs } from '@/utils/index';



const props = defineProps<{
    info: any;
}>();

const tableList = computed(() => {
    return [
        {
            title: '规模属性',
            data: getRowTableAttrs([
                ['名称', '经营年限'],
                ['年限', props.info?.businessYear],
            ]),
        },
        {
            title: '表彰信息',
            data: getRowTableAttrs([
                ['名称', '有行业荣誉及管理方表彰'],
                ['是/否', props.info?.reportInfo?.honor, { prop: 'switch', slot: 'switch' }],
            ]),
        },
        {
            title: '信用承诺',
            data: getRowTableAttrs([
                ['名称', '近三年，在行政事项办理过程中做出信用承诺并履行。'],
                ['是/否', props.info?.creditPromise, { prop: 'switch', slot: 'switch' }],
            ]),
        },
        {
            title: '消费者权益',
            data: getColumnTableAttrs(
                ['名称', { label: '是/否', prop: 'switch', slot: 'switch' }],
                [
                    [
                        '建立商品或服务退换机制，提供保障服务质量的承诺',
                        props.info?.returnPolicy,
                    ],
                    ['明示消费投诉电话', props.info?.complaintPhone],
                    ['健全消费纠纷处理机制，能主动自行协商和解消费争议。', props.info?.disputeResolution],
                    ['在用计量器具均按规定通过强制检定，确保计量准确，无短斤缺两行为，重点核查计量器具年检，铅封完好度。', props.info?.measurement],
                ],
            ),
        },
        {
            title: '投诉及舆情信息',
            data: getColumnTableAttrs(
                ['名称', { label: '是/否', prop: 'switch', slot: 'switch' }],
                [
                    [
                        '近一年，市级以上媒体、平台对企业有正面报道信息，如有品牌影响力、知名度、存续时间、社会荣誉度等',
                        props.info?.reportInfo?.positiveReport,
                    ],
                    [
                        '近一年内，权威媒体对企业有负面评价信息',
                        props.info?.reportInfo?.negativeReport,
                    ],
                    ['近一年内，不配合处理消费者投诉', props.info?.reportInfo?.complaint],
                ],
            ),
        },
        {
            title: '党建活动',
            data: getRowTableAttrs([
                ['名称', '有党员'],
                ['是/否', props.info?.partyMember, { prop: 'switch', slot: 'switch' }],
            ]),
        },
        {
            title: '履约情况',
            data: getColumnTableAttrs(
                ['名称', { label: '是/否', prop: 'switch', slot: 'switch' }],
                [
                    ['近一年内，房租费、物业费、水电燃气费或其他合同未常履行等', props.info?.contractPerformance]
                ],
            ),
        },
        {
            title: '经营行为',
            data: getColumnTableAttrs(
                ['名称', { label: '是/否', prop: 'switch', slot: 'switch' }],
                [
                    ['规范明码标价', props.info?.priceTag],
                    ['正常提供发票', props.info?.invoice],
                    [
                        '经营者向消费者提供的服务信息和广告宣传、网络营销与实际提供的服务不符合',
                        props.info?.serviceInfo,
                    ],

                    ['污染环境、乱搭乱建', props.info?.environment],
                ],
            ),
        },
        {
            title: '资产负债率',
            data: getRowTableAttrs([
                ['名称', '资产负债率'],
                [
                    '负债率',
                    props.info?.assetLiabilityRate,
                    { prop: 'percentage', slot: 'percentage' },
                ],
            ]),
        },
    ];
});
</script>

<template>
    <div>
        <div v-for="item in tableList" :key="item.title">
            <div class="text-[#1D3967] text-[16px] font-semibold mt-[10px]">{{ item.title }}</div>
            <CommonTable
                class=""
                :tableData="item.data.data"
                :tableColumns="item.data.columns"
                :isShowPagination="false"
                :headerCellStyle="{
                    color: '#929EB5',
                    backgroundColor: '#F4F8FE',
                }"
            >
                <template #switch="{ scope }">
                    <el-checkbox :model-value="Boolean(scope.row['switch'])" :value="true"></el-checkbox>
                    {{ Boolean(scope.row['switch'])  ? '是' : '否' }}
                </template>
                <template #percentage="{ scope }">
                    <div>{{ scope.row['percentage'] }}%</div>
                </template>
            </CommonTable>
        </div>
    </div>
</template>

<style scope lang="scss">
:deep(.el-checkbox__inner){
    border-radius: 100% !important;
}
</style>
