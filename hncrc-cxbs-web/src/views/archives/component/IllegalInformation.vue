<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
    info: any;
}>();

const tableData = computed(() => props.info?.archive?.yzwfsx?.list || []);
const tableColumns = [
    {
        label: '序号',
        prop: '$index',
        type: 'index',
        width: '70px',
    },
    {
        label: '列入日期',
        prop: 'in_date',
    },
    {
        label: '列入严重违法失信企业名单(黑名单)原因',
        prop: 'in_reason',
    },
    {
        label: '做出决定机关(列入)',
        prop: 'in_department',
    },
    {
        label: '移出日期',
        prop: 'out_date',
    },
    {
        label: '移出严重违法失信企业名单(黑名单)原因',
        prop: 'out_reason',
    },
    {
        label: '做出决定机关(移出)',
        prop: 'out_department',
    },
];
</script>

<template>
    <CommonTable
        class="mt-[20px]"
        :table-data="tableData"
        :table-columns="tableColumns"
        :isShowPagination="false"
        :columnsMixin="{
            align: 'center',
        }"
        :headerCellStyle="{
            backgroundColor: '#F8F8F9',
        }"
    />
</template>

<style scope lang="scss"></style>
