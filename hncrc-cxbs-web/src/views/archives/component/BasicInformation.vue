<script setup lang="ts">
import { computed, ref } from 'vue';

import { getAssetsFileUrl } from '@/utils/file';
import { getColumnTableAttrs, getRowTableAttrs } from '@/utils/index';

import EnterpriseDetailDialog from './EnterpriseDetailDialog.vue';
const EnterpriseDetailDialogRef = ref<InstanceType<typeof EnterpriseDetailDialog>>();

const props = defineProps<{
    info: any;
}>();

console.log('基础信息===>', props.info);

const isAbnormalInfo = () => {
    for (let i = 0; i < props.info?.archive?.abnormalInfo?.items?.length; i++) {
        if (props.info?.archive?.abnormalInfo?.items[i]?.disabled === '0') {
            return true;
        }
    }
    return false;
};

const tableList = computed(() => {
    return [
        {
            title: '营业执照',
            data: getRowTableAttrs([
                ['统一社会信用代码', props.info?.archive?.qyzb?.list[0]?.UNISCID],
                ['企业类型', props.info?.archive?.qyzb?.list[0]?.org_type],
                ['登记状态', props.info?.archive?.qyzb?.list[0]?.ENTSTATUS],
                [
                    '注册资本',
                    props.info?.archive?.qyzb?.list[0]?.REGCAP
                        ? `${props.info?.archive?.qyzb?.list[0]?.REGCAP}万元`
                        : '0' + '万元',
                ],
                ['成立日期', props.info?.archive?.qyzb?.list[0]?.ESDATE],
                ['法定代表人', props.info?.archive?.qyzb?.list[0]?.NAME],
                ['登记机关', props.info?.archive?.qyzb?.list[0]?.REGORG],
                ['在堂醒目是否悬挂公示', props.info?.archive?.dbInfo?.table_1_col3],
                ['操作', '', { prop: 'preview', slot: 'preview', width: '120px' }],
            ]),
        },
        {
            title: '经营许可证',
            data: getRowTableAttrs([
                ['统一社会信用代码(身份证号码)', props.info?.archive?.qyzb?.list[0]?.UNISCID],
                ['有效期至', props.info?.archive?.qyzb?.list[0]?.OPTO],
                ['日常监管管理机构', props.info?.archive?.qyzb?.list[0]?.REGORG],
                ['法定代表人', props.info?.archive?.qyzb?.list[0]?.NAME],
                ['登记机关', props.info?.archive?.qyzb?.list[0]?.REGORG],
                ['在堂醒目是否悬挂公示', props.info?.archive?.dbInfo?.table_1_col3],
            ]),
        },
        {
            title: '经营质量',
            data: getColumnTableAttrs(
                ['名称', { label: '是/否', prop: 'switch', slot: 'switch' }],
                [
                    [
                        '检查各类证照是否齐全、合法有效以及按照规定亮照经营',
                        props.info?.table_1_status ? props.info?.table_1_status : '是',
                    ],
                    [
                        '检查是否按照核准的名称、经营范围、经营场所从事经营活动',
                        props.info?.archive?.dbInfo?.table_1_col4,
                    ],
                    [
                        '检查是否遵守所属管理主体要求的商户经营管理规范，主动配合市场管理人员工作',
                        props.info?.archive?.dbInfo?.table_1_col5,
                    ],
                    ['检查是否按照规定明码标价', props.info?.archive?.dbInfo?.table_1_col6],
                    [
                        '检查是否严格履行市容环卫责任制，做到门前“三包”门店无店外占道经营、乱张贴、乱涂写，环境整洁干净',
                        props.info?.table_10_status ? props.info?.table_10_status : '是',
                    ],
                ],
            ),
        },
        {
            title: '行业资质',
            data: getRowTableAttrs([
                // ['证书类型', props.info?.archive?.certificate?.items[0]?.type],
                ['证书名称', props.info?.archive?.certificate?.items[0]?.name],
                ['证书编号', props.info?.archive?.certificate?.items[0]?.num],
                // ['证书状态', props.info?.archive?.certificate?.items[0]?.status],
                ['证书类型', props.info?.archive?.certificate?.items[0]?.type],
                ['操作', '', { prop: 'certificate', slot: 'certificate', width: '120px' }],
            ]),
        },
        {
            title: '认证信息',
            data: getColumnTableAttrs(
                ['认证名称', { label: '是/否', prop: 'switch', slot: 'switch' }],
                [
                    [
                        '老字号品牌',
                        props.info?.archive?.dbInfo?.table_4_col1 === '是' ? '是' : '否',
                    ],
                    [
                        '“名特优新”个体工商号',
                        props.info?.archive?.dbInfo?.table_1_co7 === '是' ? '是' : '否',
                    ],
                ],
            ),
        },
        {
            title: '年报信息',
            data: getRowTableAttrs([
                ['名称', '按照有关法律法规规定的年度报告信息公示情况，进行年报报告公示'],
                [
                    '是/否',
                    props.info?.archive?.entyearexaminfo?.total > 0 ? '是' : '否',
                    { prop: 'switch', slot: 'switch' },
                ],
                ['操作', '', { prop: 'entyearexaminfo', slot: 'entyearexaminfo', width: '120px' }],
            ]),
        },
        {
            title: '其他信息',
            data: getColumnTableAttrs(
                ['名称', { label: '是/否', prop: 'switch', slot: 'switch' }],
                [['被纳入经营异常名录', isAbnormalInfo() ? '是' : '否']],
            ),
        },
        {
            title: '社会责任',
            data: getRowTableAttrs([
                ['类别', null],
                ['活动名称', null],
                ['活动时间', null],
            ]),
        },
        {
            title: '荣誉奖项',
            data: getRowTableAttrs([
                ['名称', null],
                ['获奖级别', null],
            ]),
        },
    ];
});

// 查看详情
const handleDetail = (row: any) => {
    EnterpriseDetailDialogRef.value?.open(
        props.info?.archive.qyzb?.list[0],
        props.info?.archive?.dbInfo?.table_1_col3,
        '营业执照',
    );
};

// 查看行业资质详情
const handleCertificateDetail = (row: any) => {
    EnterpriseDetailDialogRef.value?.open(props.info?.archive.certificate, null, '行业资质');
};
// 查看年报信息详情
const handleEntyearexaminfoDetail = (row: any) => {
    EnterpriseDetailDialogRef.value?.open(props.info?.archive.entyearexaminfo, null, '年报信息');
};
</script>

<template>
    <div>
        <EnterpriseDetailDialog ref="EnterpriseDetailDialogRef" />
        <div v-for="item in tableList" :key="item.title">
            <div class="flex items-center">
                <el-image
                    class="w-[17px] h-[17px] top-[6px]"
                    :src="getAssetsFileUrl('image/details-icon.png')"
                />
                <div class="ml-[6px]">
                    <div class="text-[#222222] text-[18px] font-semibold mt-[16px]">
                        {{ item.title }}
                    </div>
                </div>
            </div>
            <CommonTable
                :tableData="item.data.data"
                :tableColumns="item.data.columns"
                :isShowPagination="false"
                :headerCellStyle="{
                    color: '#999999',
                    backgroundColor: '#F3F9FF',
                }"
            >
                <template #switch="{ scope }">
                    <el-checkbox :model-value="scope.row['switch'] == '是'" :value="true" />
                    {{ scope.row['switch'] }}
                </template>

                <template #percentage="{ scope }">
                    <div>{{ scope.row['percentage'] }}%</div>
                </template>
                <template #preview="{ scope }">
                    <el-text class="cursor-pointer" type="primary" @click="handleDetail(scope.row)"
                        >详情</el-text
                    >
                    <!-- <el-text class="cursor-pointer" type="primary" style="margin-left: 10px"
                        >查看图片</el-text
                    > -->
                </template>
                <template #certificate="{ scope }">
                    <el-text
                        class="cursor-pointer"
                        type="primary"
                        @click="handleCertificateDetail(scope.row)"
                        >详情</el-text
                    >
                </template>
                <template #entyearexaminfo="{ scope }">
                    <el-text
                        class="cursor-pointer"
                        type="primary"
                        @click="handleEntyearexaminfoDetail(scope.row)"
                        >详情</el-text
                    >
                </template>
            </CommonTable>
        </div>
    </div>
</template>

<style scope lang="scss">
:deep(.el-checkbox__inner) {
    border-radius: 100% !important;
}
</style>
