<script setup lang="ts">
import { computed } from 'vue';

import { getAssetsFileUrl } from '@/utils/file';
import { getRowTableAttrs, isBoolean } from '@/utils/index';

const props = defineProps<{
    info: any;
}>();

const tableList = computed(() => {
    return [
        {
            title: '获奖情况',
            data: getRowTableAttrs([
                ['证书名称', props.info?.archive.dbInfo.table_2_3_col1],
                ['证书编号', props.info?.archive.dbInfo.table_2_3_col2],
                ['证书等级', props.info?.archive.dbInfo.table_2_3_col3],
            ]),
        },
        {
            title: '公益服务',
            data: getRowTableAttrs([
                ['公益服务名称', props.info?.reportInfo?.name],
                ['公益服务描述', props.info?.reportInfo?.social_security_status],
            ]),
        },
        {
            title: '社会活动',
            data: getRowTableAttrs([
                ['社会活动名称', props.info?.reportInfo?.name],
                ['社会活动描述', props.info?.reportInfo?.social_security_status],
            ]),
        },
    ];
});
</script>

<template>
    <div>
        <div v-for="item in tableList" :key="item.title">
            <div class="flex items-center">
                <el-image
                    class="w-[17px] h-[17px] top-[6px]"
                    :src="getAssetsFileUrl('image/details-icon.png')"
                />
                <div class="ml-[6px]">
                    <div class="text-[#222222] text-[18px] font-semibold mt-[16px]">
                        {{ item.title }}
                    </div>
                </div>
            </div>
            <CommonTable
                :tableData="item.data.data"
                :tableColumns="item.data.columns"
                :isShowPagination="false"
                :headerCellStyle="{
                    color: '#999999',
                    backgroundColor: '#F3F9FF',
                }"
            >
                <template #switch="{ scope }">
                    <template v-if="isBoolean(scope.row['switch'])">
                        <el-checkbox :model-value="scope.row['switch']" :value="true" />
                        {{ scope.row['switch'] ? '是' : '否' }}
                    </template>
                    <template v-else>
                        {{ scope.row['switch'] }}
                    </template>
                </template>
                <template #percentage="{ scope }">
                    <div>{{ scope.row['percentage'] }}%</div>
                </template>
                <!-- <template #preview="{ scope }">
          <el-text class="cursor-pointer" type="primary">查看图片</el-text>
        </template> -->
            </CommonTable>
        </div>
    </div>
</template>

<style scope lang="scss">
:deep(.el-checkbox__inner) {
    border-radius: 100% !important;
}
</style>
