<script setup lang="ts">
import { computed, reactive } from 'vue';

import type { BasicPageParams } from '@/api/baseModel';
import { getAssetsFileUrl } from '@/utils/file';

const props = defineProps<{
    info: any;
}>();

const list = [
    {
        title: '日常检查不合格次数',
        key: '',
        bg: '#F1F5FF',
        color: '#3775FF',
        icon: 'image/bga_icon.png',
    },
    {
        title: '轻微行政处罚次数',
        key: '',
        bg: '#E6F7FF',
        color: '#1890FF',
        icon: 'image/bgb_icon.png',
    },
    {
        title: '一般行政处罚次数',
        key: '',
        bg: '#E6FFFB',
        color: '#14C2C3',
        icon: 'image/bgc_icon.png',
    },
    {
        title: '较重行政处罚次数',
        key: '',
        bg: '#FFF6EF',
        color: '#FD960C',
        icon: 'image/bgd_icon.png',
    },
    {
        title: '严重行政处罚次数',
        key: '',
        bg: '#FFF5F6',
        color: '#FF6464',
        icon: 'image/bge_icon.png',
    },
]; 

const tableColumns = [
    {
        label: '序号',
        prop: '$index',
        type: 'index',
        width: '70px',
    },
    {
        label: '处罚名称',
        prop: '',
    },
    {
        label: '处罚类型',
        prop: 'CF_CFLB',
    },
    {
        label: '处罚结果',
        prop: 'CF_NR',
    },
    {
        label: '处罚事由',
        prop: 'CF_SY',
    },
    {
        label: '处罚判定日期',
        prop: 'CF_JDRQ',
    },
    {
        label: '处罚机构',
        prop: 'CF_CFJG',
    },
];

const pager = reactive({
    pageNo: 1,
    pageSize: 10,
    total: 0,
    loading: false,
});
const tableData = computed(() => props.info?.archive?.xzcf?.list || []);

const ajax = (pageNo: number = 1) => {
    pager.total = props.info?.archive?.xzcf?.list?.length || 0;
};
ajax();

const pagination = (event: BasicPageParams) => {
    pager.pageSize = event.pageSize;
    ajax(event.pageNo);
}; 
</script>

<template>
    <div>
        <CommonTable
            class="mt-[20px]"
            :table-data="tableData"
            :table-columns="tableColumns"
            :total="pager.total"
            :pageNum="pager.pageNo"
            :pageSize="pager.pageSize"
            :columnsMixin="{
                align: 'center',
            }"
            :headerCellStyle="{
                backgroundColor: '#F8F8F9',
            }"
            :isShowPagination="false"
        >
        </CommonTable>
    </div>
</template>

<style scope lang="scss"></style>
