<script setup lang="ts">
import dayjs from 'dayjs';
import { computed } from 'vue';

import { getRowTableAttrs } from '@/utils';
import { getAssetsFileUrl } from '@/utils/file';

const props = defineProps<{
    info: any;
}>();

const sxInfoData = computed(() => props.info?.archive?.sxInfo?.items?.filter(s=>s.disabled === '0') || []);
const sxInfoColumns = [
    {
        label: '序号',
        prop: '$index',
        type: 'index',
        width: '70px',
    },
    {
        label: '执行依据文号',
        prop: 'doc_number',
    },
    {
        label: '案号',
        prop: 'case_number',
    },
    {
        label: '执行标的',
        prop: 'amount',
    },
    {
        label: '执行法院',
        prop: 'court',
    },
    {
        label: '法定代表人',
        prop: 'oper_name',
    },
    {
        label: '组织机构号',
        prop: 'number',
    },
    {
        label: '立案时间',
        prop: 'date',
    }
];


const bzxrData = computed(() => props.info?.archive?.bzxr?.list || []);
const bzxrColumns = [
    {
        label: '序号',
        prop: '$index',
        type: 'index',
        width: '70px',
    },
    {
        label: '统一社会信用代码',
        prop: 'UNISCID',
    },
    {
        label: '注册号',
        prop: 'REGNO',
    },
    {
        label: '企业名称',
        prop: 'ENTNAME',
    },
    {
        label: '被执行人姓名/名称',
        prop: 'FSS_NAME',
    },
    {
        label: '立案时间',
        prop: 'FSS_LASJ',
    }
];

</script>

<template>


    <div class="flex items-center">
        <el-image
            class="w-[17px] h-[17px] top-[6px]"
            :src="getAssetsFileUrl('image/details-icon.png')"
        />
        <div class="ml-[6px]">
            <div class="text-[#222222] text-[18px] font-semibold mt-[16px]">
                司法被执行人信息
            </div>
        </div>
    </div>

    <CommonTable
        class="mt-[20px]"
        :table-data="bzxrData"
        :table-columns="bzxrColumns"
        :isShowPagination="false"
        :columnsMixin="{
            align: 'center',
        }"
        :headerCellStyle="{
            backgroundColor: '#F8F8F9',
        }"
    />

    <div class="flex items-center">
        <el-image
            class="w-[17px] h-[17px] top-[6px]"
            :src="getAssetsFileUrl('image/details-icon.png')"
        />
        <div class="ml-[6px]">
            <div class="text-[#222222] text-[18px] font-semibold mt-[16px]">
                司法失信被执行人信息
            </div>
        </div>
    </div>

    <CommonTable
        class="mt-[20px]"
        :table-data="sxInfoData"
        :table-columns="sxInfoColumns"
        :isShowPagination="false"
        :columnsMixin="{
            align: 'center',
        }"
        :headerCellStyle="{
            backgroundColor: '#F8F8F9',
        }"
    />
</template>

<style scoped lang="scss"></style>
