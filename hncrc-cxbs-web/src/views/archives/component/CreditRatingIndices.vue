<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps<{
    info: any;
}>();

const point = ref([]);
const totalPoint = ref<number>(0);
point.value = props.info?.points?.pointModel?.points;
totalPoint.value = props.info?.points?.pointModel?.totalPoint;
 
let data = [
    {
        a: '基本信息',
        b: '成立时间',
        c: '成立年限',
        d: '1',
        dd: 'no01',
        e: '大于等于5年的，得3分；小于5年的，按（成立年限/5）*3计算得分。',
        f: '3',
        g: '-',
        h: true, //是否加分项
    },
    {
        a: '基本信息',
        b: '年报信息',
        c: '按照有关法律法规规定的年度报告信息公示情况',
        d: '2',
        dd: 'no02',
        e: '未按照有关法律法规规定进行年度报告公示的，扣5分',
        f: '5',
        g: '-'
    },
    {
        a: '基本信息',
        b: '非正常户信息',
        c: '非正常户信息记录',
        d: '3',
        dd: 'no03',
        e: '存在被税务主管部门认定为非正常户记录的，扣5分。',
        f: '5',
        g: '-',
    },
    {
        a: '基本信息',
        b: '经营异常名录',
        c: '经营异常名录记录',
        d: '4',
        dd: 'no04',
        e: '被纳入经营异常名录的，扣5分。',
        f: '5',
        g: '-',
    },
    {
        a: '基本信息',
        b: '行业资质',
        c: '资质等级情况',
        d: '5',
        dd: 'no05',
        e: '存在国家、省市资质许可信息的，得3分；具有特定认证情况，如老字号品牌信息、驰著名商标信息、软著、专利证书以及“名、特、优、新”信息，得3分。',
        f: '6',
        g: '-',
        h: true, //是否加分项
    },
    {
        a: '经营质量',
        b: '经营规范',
        c: '检查各类证照是否齐全、合法有效以及按照规定亮照经营',
        d: '6',
        dd: 'no06',
        e: '证照不齐全，扣5分；证照过期、无效、伪造等，扣10分；未依照规定将营业执照置于经营场所，扣5分。',
        f: '10',
        g: '-',
    },
    {
        a: '经营质量',
        b: '经营规范',
        c: '检查是否按照核准的名称、经营范围、经营场所从事经营活动',
        d: '7',
        dd: 'no07',
        e: '营业执照上的名称、经营范围、经营场所与实际经营业务不符，扣2分/项，最高扣6分。',
        f: '6',
        g: '-',
    },
    {
        a: '经营质量',
        b: '经营行为',
        c: '检查是否遵守所属管理主体要求的商户经营管理规范，主动配合市场管理人员工作',
        d: '8',
        dd: 'no08',
        e: '不服从管理、不遵守相关经营管理规范的，扣5分。',
        f: '5',
        g: '-',
    },
    {
        a: '经营质量',
        b: '经营行为',
        c: '检查是否按照规定明码标价',
        d: '9',
        dd: 'no09',
        e: '不明码标价的，扣5分。',
        f: '5',
        g: '-',
    },
    {
        a: '经营质量',
        b: '经营行为',
        c: '检查是否严格履行市容环卫责任制，做到门前“三包”门店无店外占道经营、乱张贴、乱涂写，环境整洁干净',
        d: '10',
        dd: 'no10',
        e: '占道经营，扣1.5分；不维护门前卫生，扣1.5分。',
        f: '3',
        g: '-',
    },
    {
        a: '监管信用',
        b: '行政监管',
        c: '政府部门行政处罚',
        d: '11',
        dd: 'no11',
        e: '根据近三年国家企业信息公示平台、信用中国等发生未修复的行政处罚记录进行扣分。有一般行政处罚记录的，每项扣4分；有被予以暂扣或者吊销许可证、暂扣或吊销营业执照、罚款金额达2万元以上的行政处罚记录的，每项扣4分；',
        f: '20',
        g: '-',
    },
    {
        a: '监管信用',
        b: '行政监管',
        c: '政府部门行政处罚',
        d: '11',
        dd: 'no11',
        e: '有被纳入严重失信主体名单的，信用等级评定为最低级。',
        f: '/',
        g: '-',
    },
    {
        a: '监管信用',
        b: '行政监管',
        c: '行政检查抽查和“双随机、一公开”',
        d: '12',
        dd: 'no12',
        e: '根据近三年国家企业信息公示平台等公示记录进行扣分：结果不合格，每次扣2分。',
        f: '8',
        g: '-',
    },
    {
        a: '监管信用',
        b: '行政监管',
        c: '纳税信用等级评定情况',
        d: '13',
        dd: 'no13',
        e: '根据纳税信用级别扣分： 最近年度纳税信用级别为C级的，扣5分；未按要求进行税务登记的，扣5分。',
        f: '5',
        g: '-',
    },
    {
        a: '监管信用',
        b: '行政监管',
        c: '纳税信用等级评定情况',
        d: '13',
        dd: 'no13',
        e: '最近年度纳税信用级别为D级的，信用等级直接评定为最低级。',
        f: '/',
        g: '-',
    },
    {
        a: '监管信用',
        b: '司法信用',
        c: '经营主体涉案纠纷',
        d: '14',
        dd: 'no14',
        e: '根据近三年中国执行信息公开网、中国裁判文书网等记录扣分：存在被执行记录，每条扣5分；',
        f: '5',
        g: '-',
    },
    {
        a: '监管信用',
        b: '司法信用',
        c: '经营主体涉案纠纷',
        d: '14',
        dd: 'no14',
        e: '有列为失信被执行人的，信用等级评定为最低级。',
        f: '/',
        g: '-',
    },
    {
        a: '监管信用',
        b: '司法信用',
        c: '法人/经营者涉案纠纷',
        d: '15',
        dd: 'no15',
        e: '根据近三年中国执行信息公开网、中国裁判文书网等记录扣分：存在被执行记录，每条扣1分；',
        f: '3',
        g: '-',
    },
    {
        a: '监管信用',
        b: '司法信用',
        c: '法人/经营者涉案纠纷',
        d: '15',
        dd: 'no15',
        e: '存在失信被执行人记录或属于限制高消费人员，信用等级评定为最低级。',
        f: '/',
        g: '-',
    },
    {
        a: '监管信用',
        b: '民事信用',
        c: '公共事业欠费情况',
        d: '16',
        dd: 'no16',
        e: '近三年有公共事业费用（例如水、电、租金等）欠缴信息，每条扣1分。',
        f: '4',
        g: '-',
    },
    {
        a: '监管信用',
        b: '民事信用',
        c: '投诉举报信息',
        d: '17',
        dd: 'no17',
        e: '近三年因预付消费、产品和服务质量、违规经营等被投诉举报且被核实认定的，每次扣2分',
        f: '6',
        g: '-',
    },
    {
        a: '监管信用',
        b: '民事信用',
        c: '信用承诺情况',
        d: '18',
        dd: 'no18',
        e: '近三年有签署信用承诺书的，不扣分；未签署的，扣5分。',
        f: '5',
        g: '-',
    },
    {
        a: '社会责任',
        b: '获奖情况',
        c: '获得的国家级、省级、市级等奖项情况',
        d: '19',
        dd: 'no19',
        e: '近三年省人民政府或者国家有关部门正式发文表彰的，得2分；近三年市人民政府或省级部门正式发文表彰的，得1分；近三年县级人民政府或市级部门正式发文表彰的，得0.5分。同一事迹以最高奖项计分。',
        f: '4',
        g: '-',
        h: true, //是否加分项
    },
    {
        a: '社会责任',
        b: '公益服务',
        c: '参与志愿服务、参与公益服务情况',
        d: '20',
        dd: 'no20',
        e: '近三年以经营主体名义参与志愿服务、参与公益服务情况，每次得1分。',
        f: '3',
        g: '-',
        h: true, //是否加分项
    },
    {
        a: '社会责任',
        b: '社会活动',
        c: '参与各类文明创建、诚信评比活动。',
        d: '21',
        dd: 'no21',
        e: '近三年以经营主体名义积极参加各类文明创建、诚信评比类似活动的，每次得2分。',
        f: '4',
        g: '-',
        h: true, //是否加分项
    },
];
// 数据处理

if (point?.value) {
    for (const p of Object.keys(point.value)) {
        for (const d of data) {
            if (d.dd === p && d.f != '列入黑名单') {
                d.g = point.value[p];
                break;
            }
        }
    }
}

const shouldSkip = (row, index, propName) => {
    if (index == 0) return false;

    return data[index - 1][propName] === row[propName];
};

const rowspanCalc = (row, index, propName) => {
    let count = 1;
    for (let i = index + 1; i < data.length; i++) {
        if (data[i][propName] === data[index][propName]) {
            count++;
        } else {
            break;
        }
    }
    return count;
};

const showPoint = (item) =>{
    let point = 0;
    if(item.h && item.h === true){
        point = parseFloat(item.g);
    }else{
        point = parseFloat(item.f) + parseFloat(item.g);
    }
    return point;
}
</script>

<template>
    <table class="table_">
        <thead>
            <tr>
                <th class="w-[180px]">一级指标</th>
                <th class="w-[180px]">二级指标</th>
                <th class="w-[180px]">三级指标</th>
                <th class="w-[100px]">序号</th>
                <th class="w-auto">评价细则</th>
                <th class="w-[140px]">得分/扣分标准</th>
                <th class="w-[140px]">最终得分</th>
            </tr>
        </thead>
        <tbody>
            <tr v-for="(item, index) in data" :key="item.d">
                <td v-if="!shouldSkip(item, index, 'a')" :rowspan="rowspanCalc(item, index, 'a')">
                    {{ item.a }}
                </td>
                <td v-if="!shouldSkip(item, index, 'b')" :rowspan="rowspanCalc(item, index, 'b')">
                    {{ item.b }}
                </td>
                <td v-if="!shouldSkip(item, index, 'c')" :rowspan="rowspanCalc(item, index, 'c')">
                    {{ item.c }}
                </td>
                <td v-if="!shouldSkip(item, index, 'd')" :rowspan="rowspanCalc(item, index, 'd')">
                    {{ item.d }}
                </td>
                <td>{{ item.e }}</td>
                <td v-if="!shouldSkip(item, index, 'd')" :rowspan="rowspanCalc(item, index, 'd')">
                    {{ item.f }}
                </td>
                <td v-if="!shouldSkip(item, index, 'd')" :rowspan="rowspanCalc(item, index, 'd')">
                    {{  showPoint(item) }}
                </td>
            </tr>
        </tbody>
    </table>
</template>

<style scope lang="scss">
//给 .table 的单元格加上边框
.table_ {
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;

    th,
    td {
        border: 1px solid #e9e9e9;
    }

    tr {
        height: 58px;
    }

    th {
        background-color: #f2f3f5f5;
    }

    td {
        padding: 10px 20px;
        text-align: center;
        background-color: #fff;

        &:hover {
            background-color: #f2f3f5f5;
        }
    }
}
</style>
