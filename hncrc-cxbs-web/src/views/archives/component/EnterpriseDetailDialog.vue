<script setup lang="ts">
import { computed, reactive, ref } from 'vue';

import { getColumnTableAttrs } from '@/utils/index';
const visible = ref(false);

const infos = reactive<any>({});

// 打开对话框
const open = (row: any, shows: any, showType: any) => {
    visible.value = true;
    Object.assign(infos, row);
    infos.table_1_col3 = shows;
    infos.showType = showType;
};

const tableList = computed(() => {
    return [
        {
            title: '营业执照',
            data: getColumnTableAttrs(
                ['名称', { label: '详情' }],
                [
                    ['统一社会信用代码', infos.UNISCID],
                    ['企业类型', infos?.org_type],
                    ['登记状态', infos?.ENTSTATUS],
                    ['注册资本', infos?.REGCAP],
                    ['成立日期', infos?.ESDATE],
                    ['法定代表人', infos?.NAME],
                    ['登记机关', infos?.REGORG],
                    ['注册地址', infos?.DOM],
                    ['经营范围', infos?.OPSCOPE],
                    ['在堂醒目是否悬挂公示', infos.table_1_col3],
                ],
            ),
        },
    ];
});

const tableColumns = [
    {
        label: '序号',
        prop: '$index',
        type: 'index',
        width: '70px',
    },
    {
        label: '报告年度',
        prop: 'ancheYear',
    },
    {
        label: '企业名称',
        prop: 'entName',
    },
    {
        label: '统一社会信用代码',
        prop: 'uniscId',
    },
    {
        label: '企业通讯地址',
        prop: 'dom',
    },
    {
        label: '企业主营业务活动',
        prop: 'opScope',
        width: '370px',
    },
    {
        label: '企业经营状态',
        prop: 'state',
    },
];

const certificateColumns = [
    {
        label: '序号',
        prop: '$index',
        type: 'index',
        width: '70px',
    },
    {
        label: '证书类型',
        prop: 'type',
    },
    {
        label: '证书编号',
        prop: 'num',
    },
    {
        label: '发证时间',
        prop: 'issue_date',
    },
    {
        label: '截止日期',
        prop: 'validity_end',
    },
    {
        label: '状态',
        prop: 'status',
    },
    {
        label: '备注',
        prop: 'remarks',
    },
];

// 关闭对话框
const close = () => {
    visible.value = false;
};

defineExpose({
    open,
    close,
});

const emits = defineEmits<{
    getList: [];
}>();

// 预览承诺书
const handleShowFile = (row: any) => {
    let a = document.createElement('a');
    a.target = '_blank';
    a.href = row;
    a.click();
};
</script>

<template>
    <el-dialog
        v-model="visible"
        center
        title="查看详情"
        class="!rounded-lg p-0"
        width="1200px"
        @close="close"
    >
        <div v-for="item in tableList" v-show="infos.showType == item.title">
            <CommonTable
                :tableData="item.data.data || []"
                :tableColumns="item.data.columns"
                :isShowPagination="false"
                :headerCellStyle="{
                    color: '#999999',
                    backgroundColor: '#F3F9FF',
                }"
            />
        </div>

        <div v-show="infos.showType == '年报信息'">
            <CommonTable
                class="mt-[20px]"
                :table-data="infos.list || []"
                :table-columns="tableColumns"
                :columnsMixin="{
                    align: 'center',
                }"
                :headerCellStyle="{
                    backgroundColor: '#F8F8F9',
                }"
                :isShowPagination="false"
            />
        </div>

        <div v-show="infos.showType == '行业资质'">
            <CommonTable
                class="mt-[20px]"
                :table-data="infos.items || []"
                :table-columns="certificateColumns"
                :columnsMixin="{
                    align: 'center',
                }"
                :headerCellStyle="{
                    backgroundColor: '#F8F8F9',
                }"
                :isShowPagination="false"
            />
        </div>

        <template #footer>
            <div class="mb-10">
                <el-button class="" size="large" @click="close()">
                    <span class="px-3">关闭</span>
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped></style>
