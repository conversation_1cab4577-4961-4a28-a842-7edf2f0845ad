<script setup lang="ts">
import { computed } from 'vue';

import { getAssetsFileUrl } from '@/utils/file';
import { getRowTableAttrs, isBoolean } from '@/utils/index';

const props = defineProps<{
    info: any;
}>();

const extraListInfo = () => {
    const extraList = [];
    if(props.info.archive.dbInfo?.table_9_col1 !== '0'){
        extraList.push({
                    'c1': props.info.archive.dbInfo?.table_9_col1 ,
                    'c2': '12345',
                    'c3': props.info.archive.dbInfo?.etl_date});
    };
    if(props.info.archive.dbInfo?.table_1_col9 !== '否'){
        extraList.push({
                    'c1': props.info.archive.dbInfo?.table_1_col9,
                    'c2': '12315',
                    'c3': props.info.archive.dbInfo?.etl_date});
    }
    return extraList;
};

const tableData = computed(() => extraListInfo() || []);
const tableColumns = [
    {
        label: '序号',
        prop: '$index',
        type: 'index',
        width: '70px',
    },
    {
        label: '认定内容',
        prop: 'c1',
    },
    {
        label: '认定渠道',
        prop: 'c2',
    },
    {
        label: '录入时间',
        prop: 'c3',
    }
];

const tableList = computed(() => {
    return [
        {
            title: '公共事业欠费情况',
            data: getRowTableAttrs([
                ['欠费金额（元）', null],
                ['欠缴开始时间', null],
                ['欠缴结束时间', null],
                ['录入时间', props.info.archive?.dbInfo?.etl_date],
            ]),
        },
        {
            title: '信用承诺信息',
            data: getRowTableAttrs([
                ['信用承诺信息', props.info?.archive?.promiseTypeList?.join('、')],
                [
                    '录入日期',
                    props.info?.archive?.promiseTypeList
                        ? props.info.archive?.dbInfo?.etl_date
                        : '',
                ],
            ]),
        },
    ];
});
</script>

<template>
    <div>
        <div v-for="item in tableList" :key="item.title">
            <div class="flex items-center">
                <el-image
                    class="w-[17px] h-[17px] top-[6px]"
                    :src="getAssetsFileUrl('image/details-icon.png')"
                />
                <div class="ml-[6px]">
                    <div class="text-[#222222] text-[18px] font-semibold mt-[16px]">
                        {{ item.title }}
                    </div>
                </div>
            </div>
            <CommonTable
                :tableData="item.data.data"
                :tableColumns="item.data.columns"
                :isShowPagination="false"
                :headerCellStyle="{
                    color: '#999999',
                    backgroundColor: '#F3F9FF',
                }"
            >
                <template #switch="{ scope }">
                    <template v-if="isBoolean(scope.row['switch'])">
                        <el-checkbox :model-value="scope.row['switch']" :value="true" />
                        {{ scope.row['switch'] ? '是' : '否' }}
                    </template>
                    <template v-else>
                        {{ scope.row['switch'] }}
                    </template>
                </template>
                <template #percentage="{ scope }">
                    <div>{{ scope.row['percentage'] }}%</div>
                </template>
                <!-- <template #preview="{ scope }">
          <el-text class="cursor-pointer" type="primary">查看图片</el-text>
        </template> -->
            </CommonTable>
        </div>
        <div class="flex items-center">
                <el-image
                    class="w-[17px] h-[17px] top-[6px]"
                    :src="getAssetsFileUrl('image/details-icon.png')"
                />
                <div class="ml-[6px]">
                    <div class="text-[#222222] text-[18px] font-semibold mt-[16px]">
                        投诉举报信息
                    </div>
                </div>
            </div>
        <CommonTable
            class="mt-[20px]"
            :table-data="tableData"
            :table-columns="tableColumns"
            :isShowPagination="false"
            :columnsMixin="{
                align: 'center',
            }"
            :headerCellStyle="{
                    color: '#999999',
                    backgroundColor: '#F3F9FF',
            }"
    />
    </div>
</template>

<style scope lang="scss">
:deep(.el-checkbox__inner) {
    border-radius: 100% !important;
}
</style>
