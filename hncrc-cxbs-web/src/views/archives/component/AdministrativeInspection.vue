<script setup lang="ts">
/**
 * 行政检查页面
 **/
import { computed, reactive } from 'vue';

import type { BasicPageParams } from '@/api/baseModel';

const props = defineProps<{
    info: any;
}>();

const list = [
    {
        title: '日常检查不合格次数',
        key: '',
        bg: '#F1F5FF',
        color: '#3775FF',
        icon: 'image/bga_icon.png',
    },
    {
        title: '轻微行政处罚次数',
        key: '',
        bg: '#E6F7FF',
        color: '#1890FF',
        icon: 'image/bgb_icon.png',
    },
    {
        title: '一般行政处罚次数',
        key: '',
        bg: '#E6FFFB',
        color: '#14C2C3',
        icon: 'image/bgc_icon.png',
    },
    {
        title: '较重行政处罚次数',
        key: '',
        bg: '#FFF6EF',
        color: '#FD960C',
        icon: 'image/bgd_icon.png',
    },
    {
        title: '严重行政处罚次数',
        key: '',
        bg: '#FFF5F6',
        color: '#FF6464',
        icon: 'image/bge_icon.png',
    },
];

const tableColumns = [
    {
        label: '序号',
        prop: '$index',
        type: 'index',
        width: '70px',
    },
    {
        label: '任务名称',
        prop: 'raninsplanName',
    },
    {
        label: '检查项目',
        prop: '',
    },
    {
        label: '检查及反馈开始时间',
        prop: 'jcsj',
    },
    {
        label: '检查及反馈结束时间',
        prop: '',
    },
    {
        label: '参与部门',
        prop: '',
    },
];

const pager = reactive({
    pageNo: 1,
    pageSize: 10,
    total: 0,
    loading: false,
});
const tableData = computed(() => {
    try {
        return props.info.archive?.doubleRandomSamplingInfo.list || [];
    } catch (e) {
        return [];
    }
});

const ajax = (pageNo: number = 1) => {
    pager.total = props.info?.archive?.doubleRandomSamplingInfo?.length || 0;
};
ajax();

const pagination = (event: BasicPageParams) => {
    pager.pageSize = event.pageSize;
    ajax(event.pageNo);
};

const handleDetail = (row) => {
    console.log(row);
};
</script>

<template>
    <div>
        <CommonTable
            class="mt-[20px]"
            :table-data="tableData"
            :table-columns="tableColumns"
            :total="pager.total"
            :pageNum="pager.pageNo"
            :pageSize="pager.pageSize"
            :columnsMixin="{
                align: 'center',
            }"
            :headerCellStyle="{
                backgroundColor: '#F8F8F9',
            }"
            :isShowPagination="false"
        />
    </div>
</template>

<style scope lang="scss"></style>
