<script setup lang="ts">
import { computed, reactive } from 'vue';
import { getAssetsFileUrl } from '@/utils/file';

const props = defineProps<{
    info: any;
}>();

const tableData = computed(() => [
    {
        rating: props.info.archive.dbInfo.table_5_col1,
        evaluationUnit: props.info.archive.dbInfo.table_5_col2,
        etl_date: props.info.archive.dbInfo.table_5_col3,
        mark: props.info.archive.dbInfo.table_5_col5,
    },
]);

const tableData2 = computed(() => props.info?.archive?.abnormalEnterpriseInfo?.items || []);
const tableColumns2 = [
    {
        label: '序号',
        prop: '$index',
        type: 'index',
        width: '70px',
    },
    {
        label: '纳税人状态',
        prop: 'state',
    },
    {
        label: '认定日期',
        prop: 'judge_date',
    },
    {
        label: '认定单位',
        prop: 'judge_department',
    },
    {
        label: '公告日期',
        prop: 'pub_date',
    }

];

const tableColumns = [
    {
        label: '序号',
        prop: '$index',
        type: 'index',
        width: '70px',
    },
    {
        label: '评价结果',
        prop: 'rating',
    },
    {
        label: '评级名称税务机关',
        prop: 'evaluationUnit',
    },
    {
        label: '录入时间',
        prop: 'etl_date',
    },
    {
        label: '备注',
        prop: 'mark',
    },
];


const handleDetail = (row) => {
    console.log(row);
};

const pager = reactive({
    pageNo: 1,
    pageSize: 10,
    total: 0,
});
/**
 * 格式化字符串,并返回特定值
 * @param string 待格式化字符串
 */
const helpStringResult = (string: string) => {
    return string.at(-2);
};

// ajax();
</script>

<template>

    <div class="flex items-center">
        <el-image
            class="w-[17px] h-[17px] top-[6px]"
            :src="getAssetsFileUrl('image/details-icon.png')"
        />
        <div class="ml-[6px]">
            <div class="text-[#222222] text-[18px] font-semibold mt-[16px]">纳税非正常户信息</div>
        </div>
    </div>
    <CommonTable
        class="mt-[20px]"
        :isShowPagination="false"
        :table-data="tableData2"
        :table-columns="tableColumns2"
        :total="pager.total"
        :pageNum="pager.pageNo"
        :pageSize="pager.pageSize"
        :columnsMixin="{
            align: 'center',
        }"
        :headerCellStyle="{
            backgroundColor: '#F8F8F9',
        }"
    >
        
    </CommonTable>

    <div class="flex items-center">
        <el-image
            class="w-[17px] h-[17px] top-[6px]"
            :src="getAssetsFileUrl('image/details-icon.png')"
        />
        <div class="ml-[6px]">
            <div class="text-[#222222] text-[18px] font-semibold mt-[16px]">纳税评定信息</div>
        </div>
    </div>
    <CommonTable
        class="mt-[20px]"
        :isShowPagination="false"
        :table-data="tableData"
        :table-columns="tableColumns"
        :total="pager.total"
        :pageNum="pager.pageNo"
        :pageSize="pager.pageSize"
        :columnsMixin="{
            align: 'center',
        }"
        :headerCellStyle="{
            backgroundColor: '#F8F8F9',
        }"
    >
        <!--        <template #edit="{ scope }">
            <div class="space-x-3">
                <el-text class="cursor-pointer" type="primary" @click="handleDetail(scope.row)"
                    >详情</el-text
                >
            </div>
        </template>-->
    </CommonTable>

</template>

<style scope lang="scss"></style>
