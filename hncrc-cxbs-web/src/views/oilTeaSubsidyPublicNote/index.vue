<script setup lang="ts">
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';
import { ref } from 'vue';

import {
    addData,
    deleteById,
    downloadTemplate,
    getDetailById,
    getList,
    updateData,
} from '@/api/oilTeaSubsidyPublicNote';
import { defineEntityCrud } from '@/components/EntityCrud/hook';
import EntityCrud from '@/components/EntityCrud/index.vue';
import { EntityCrudProps } from '@/components/EntityCrud/type';
import { getEnumOptions } from '@/components/EntityCrud/util';
import { downloadBlobFile } from '@/utils/file';

const FileStatusEnum = {
    ONLINE: '公示中',
    OFFLINE: '已下线',
};

type OilTeaSubsidySubject = {
    id?: string;
    township: string;
    planterName: string;
    plantArea: number;
    plantCount: number;
    survivalRate: number;
    checkStatus: string;
    subsidyAmount: number;
    remark: string;
};

type OilTeaSubsidyPublicNote = {
    id?: string;
    publicFileName: string;
    publicFileUrl: string;
    publicFileKey: string;
    fileStatus: keyof typeof FileStatusEnum;
    creator: string;
    updater: string;
    createTime: Date;
    updateTime: Date;
};

const dialogVisible = ref(false);
const oilteasubsidysubjectList = ref<OilTeaSubsidySubject[]>([]);
const entityCrudRef = ref();

const config: EntityCrudProps<OilTeaSubsidyPublicNote> = {
    entityName: 'oilTeaSubsidyPublicNote',
    displayName: '油茶补贴公示信息',
    filterFormItems: {
        publicFileName: {
            type: 'input',
            label: '文件名称',
        },
    },
    operations: [
        {
            type: 'button',
            label: '上传',
            colorize: 'primary',
            actionService: async () => {
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = '.xls, .xlsx';
                fileInput.onchange = async (e) => {
                    const file = (e.target as HTMLInputElement).files[0];
                    if (file) {
                        const formData = new FormData();
                        formData.append('file', file);
                        await addData(formData);
                        ElMessage.success('上传成功');
                        entityCrudRef.value?.handleRefresh();
                    }
                };
                fileInput.click();
                Promise.resolve();
            },
        },
        {
            type: 'button',
            label: '下载模板',
            colorize: 'primary',
            actionService: async () => {
                const {
                    data,
                    fileName = '油茶补贴主体模板',
                    fileType = 'application/vnd.ms-excel',
                } = await downloadTemplate();
                if (data) {
                    downloadBlobFile(data, fileName, fileType);
                }
            },
        },
    ],
    tableColumns: {
        publicFileName: {
            label: '文件名称',
        },
        createTime: {
            label: '上传时间',
            formatter: (row) => dayjs(row.createTime).format('YYYY-MM-DD'),
        },
        fileStatus: {
            label: '状态',
            formatter: (row) => FileStatusEnum[row.fileStatus],
        },
        operations: {
            label: '操作',
            width: '230px',
        },
    },
    createFormItems: {
        publicFileName: {
            type: 'input',
            label: '文件名称',
            required: true,
        },
        fileStatus: {
            type: 'select',
            label: '文件状态',
            options: getEnumOptions(FileStatusEnum),
            required: true,
        },
        publicFileUrl: {
            type: 'single-document',
            label: '补贴公示文件',
            required: true,
        },
    },
    rowOperations: [
        {
            type: 'link',
            label: '详情',
            colorize: () => 'primary',
            displayIndex: -1,
            actionService: async (row) => {
                const result: any = await getDetailById(row.id);
                result.forEach((item) => {
                    if (item.planterName) {
                        if (item.planterName.length > 2) {
                            item.planterName =
                                item.planterName[0] + '*' + item.planterName.slice(2);
                        }
                    }
                    item.survivalRate = item.survivalRate + '%';
                    item.subsidyAmount = item.subsidyAmount.toLocaleString();
                });
                oilteasubsidysubjectList.value = result;
                dialogVisible.value = true;
                return Promise.resolve();
            },
        },
    ],
    listFetchService: async (params) => {
        const result: any = await getList(params);
        return Promise.resolve(result);
    },

    publishButtonLabel: '上线',
    canPublish: (row) => row.fileStatus === 'OFFLINE',
    publishService: (record: OilTeaSubsidyPublicNote) => {
        record.fileStatus = 'ONLINE';
        return updateData(record);
    },

    unpublishButtonLabel: '下线',
    canUnpublish: (row) => row.fileStatus === 'ONLINE',
    unpublishService: (record: OilTeaSubsidyPublicNote) => {
        record.fileStatus = 'OFFLINE';
        return updateData(record);
    },

    deleteService: (record: OilTeaSubsidyPublicNote) => deleteById(record?.id),
};

const entityCrudProps = defineEntityCrud(config);
</script>

<template>
    <div>
        <EntityCrud ref="entityCrudRef" v-bind="entityCrudProps">
            <template #tableFileStatus="{ row }: { row: OilTeaSubsidyPublicNote }">
                <el-tag :type="row.fileStatus === 'ONLINE' ? 'success' : 'warning'">
                    {{ FileStatusEnum[row.fileStatus] }}
                </el-tag>
            </template>
        </EntityCrud>
        <el-dialog v-model="dialogVisible" title="补贴主体名单" :close-on-click-modal="false">
            <el-table :data="oilteasubsidysubjectList" border height="450" :max-height="450">
                <el-table-column prop="township" label="乡镇" />
                <el-table-column prop="planterName" label="种植户名称" width="110px" />
                <el-table-column prop="plantArea" label="种植面积" />
                <el-table-column prop="plantCount" label="种植株树" />
                <el-table-column prop="survivalRate" label="存活率" />
                <el-table-column prop="checkStatus" label="检查情况" />
                <el-table-column prop="subsidyAmount" label="补贴金额（元）" width="130px" />
                <el-table-column prop="remark" label="备注" />
            </el-table>
        </el-dialog>
    </div>
</template>
