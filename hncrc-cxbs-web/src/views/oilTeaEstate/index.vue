<script setup lang="ts">
import { addData, deleteById, getDetailById, getList, updateData } from '@/api/oilTeaEstate';
import { defineEntityCrud } from '@/components/EntityCrud/hook';
import EntityCrud from '@/components/EntityCrud/index.vue';
import { EntityCrudProps } from '@/components/EntityCrud/type';

const EstateStatusEnum = {
    ONLINE: '使用中',
    OFFLINE: '已下线',
};

type OilTeaEstate = {
    id?: string;
    estateName: string;
    plantType: string;
    estateArea: number;
    contactPhone: string;
    estateAddress: string;
    estateIntroduction: string;
    estateStatus: keyof typeof EstateStatusEnum;
    estatePictureListJson: string;
    createTime?: string;
    updateTime?: string;
};

const config: EntityCrudProps<OilTeaEstate> = {
    entityName: 'oilTeaEstate',
    displayName: '油茶茶园',
    filterFormItems: {
        estateName: {
            type: 'input',
            label: '油茶园名称',
        },
    },
    operations: [
        {
            overwriteInternalType: 'create',
            type: 'button',
            label: '新增油茶园',
            colorize: 'primary',
            actionService: (params, internalService) => {
                internalService(params);
                return Promise.resolve();
            },
        },
    ],
    tableColumns: {
        estateName: {
            label: '油茶园名称',
            emptyText: '-',
        },
        plantType: {
            label: '种植品种',
            emptyText: '-',
        },
        estateArea: {
            label: '油茶园面积(亩)',
            emptyText: '-',
        },
        contactPhone: {
            label: '联系电话',
            emptyText: '-',
        },
        estateAddress: {
            label: '油茶园地址',
            emptyText: '-',
        },
        updateTime: {
            label: '更新时间',
            emptyText: '-',
        },
        estateStatus: {
            label: '状态',
            formatter: (row) => EstateStatusEnum[row.estateStatus],
        },
        operations: {
            label: '操作',
            width: '200px',
        },
    },
    createFormItems: {
        id: {
            type: 'id',
        },
        estateName: {
            type: 'input',
            label: '茶园名称',
            required: true,
        },
        plantType: {
            type: 'input',
            label: '种植类型',
        },
        estateArea: {
            type: 'number',
            label: '油茶园面积(亩)',
        },
        contactPhone: {
            type: 'input',
            label: '联系电话',
        },
        estateAddress: {
            type: 'input',
            label: '油茶园地址',
            required: true,
        },
        estateIntroduction: {
            type: 'textarea',
            label: '油茶茶园简介',
            required: true,
        },
        estatePictureListJson: {
            type: 'single-image',
            label: '油茶园图片',
            required: true,
            tip: '单个图片大小不超过2MB，建议上传16:9比例,且格式为 jpg、png',
        },
    },
    listFetchService: async (params) => {
        const result: any = await getList(params);
        result.list.forEach((item: any) => {
            item.estatePictureListJson = JSON.parse(item.estatePictureListJson);
        });
        return Promise.resolve(result);
    },
    createService: (newRecord: OilTeaEstate) => {
        let record = { ...newRecord };
        record.estatePictureListJson = JSON.stringify(record.estatePictureListJson);
        return addData(record);
    },

    useCreateFormItemsAsDetail: true,
    detailFetchService: async (record: OilTeaEstate) => {
        const result = await getDetailById(record?.id);
        result.estatePictureListJson = JSON.parse(result.estatePictureListJson);
        return Promise.resolve(result);
    },

    useCreateFormItemsAsUpdate: true,
    updateService: (updateRecord: OilTeaEstate) => {
        let record = { ...updateRecord };
        record.estatePictureListJson = JSON.stringify(record.estatePictureListJson);
        console.log('record', record);
        return updateData(record);
    },

    publishButtonLabel: '上线',
    canPublish: (row) => row.estateStatus !== 'ONLINE',
    publishService: (record: OilTeaEstate) => {
        record.estatePictureListJson = JSON.stringify(record.estatePictureListJson);
        record.estateStatus = 'ONLINE';
        return updateData(record);
    },

    unpublishButtonLabel: '下线',
    canUnpublish: (row) => row.estateStatus === 'ONLINE',
    unpublishService: (record: OilTeaEstate) => {
        record.estatePictureListJson = JSON.stringify(record.estatePictureListJson);
        record.estateStatus = 'OFFLINE';
        return updateData(record);
    },

    deleteService: (record: OilTeaEstate) => deleteById(record?.id),
};

const entityCrudProps = defineEntityCrud(config);
</script>

<template>
    <div>
        <EntityCrud v-bind="entityCrudProps">
            <template #tableEstateStatus="{ row }: { row: OilTeaEstate }">
                <el-tag v-if="row.estateStatus === 'ONLINE'" type="success">使用中</el-tag>
                <el-tag v-else type="warning">已下线</el-tag>
            </template>
        </EntityCrud>
    </div>
</template>
