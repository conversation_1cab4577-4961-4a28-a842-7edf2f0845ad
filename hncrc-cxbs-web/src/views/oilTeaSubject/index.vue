<script setup lang="ts">
import dayjs from 'dayjs';
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';

import {
    deleteOilTeaSubject,
    downloadOilTeaSubjectTemplate,
    getOilTeaSubjectList,
    importOilTeaSubject,
    updateOilTeaSubject,
} from '@/api/oilTeaSubject';
import { OilTeaSubject } from '@/api/oilTeaSubject/type';
import EditableDialog from '@/components/EditableForm/index.vue';
import { useEditableDialog } from '@/components/EntityCrud/EditableDialog/hook';
import { defineEntityCrud } from '@/components/EntityCrud/hook';
import EntityCrud from '@/components/EntityCrud/index.vue';
import { EntityCrudInstance, EntityCrudProps } from '@/components/EntityCrud/type';
import { getEnumOptions } from '@/components/EntityCrud/util';
import {
    getSubjectTypeLabel,
    OilTeaSubjectTypeDicts,
    SubjectTypeDicts,
    SubjectTypeEnum,
} from '@/dicts/SubjectTypeDicts';
import { useBatchImportHook } from '@/hooks/useBatchImportHook';
import { downloadBlobFile } from '@/utils/file';

import { PlantModeEnum, PlantTypeEnum } from './config';

const router = useRouter();

/** EntityCrud ref */
const entityCrudRef = ref<EntityCrudInstance>();

/** 正在下载文件 */
const isDownloading = ref(false);

/** 是否正在加载 */
const isLoading = computed(() => {
    return isDownloading.value;
});

/** 经营主体类型列表 */

/** 导入经营主体 */
/** 批量导入 */
const { uploadRef, batchImportUploadRequest, handleExceed, acceptFileTypes } = useBatchImportHook({
    uploadApi: importOilTeaSubject,
    onSuccess: () => {
        entityCrudRef.value?.handleRefresh();
    },
});

/** 下载模板 */
const hanldeDownloadTemplate = async () => {
    try {
        if (isDownloading.value) return;
        isDownloading.value = true;
        const {
            data,
            fileName = '油茶经营主体模板',
            fileType = 'application/vnd.ms-excel',
        } = await downloadOilTeaSubjectTemplate();
        if (data) {
            downloadBlobFile(data, fileName, fileType);
        }
    } catch (error) {
        console.log(error);
    } finally {
        isDownloading.value = false;
    }
};

const config: EntityCrudProps<OilTeaSubject> = {
    entityName: 'oilTeaSubject',
    displayName: '油茶经营主体',
    filterFormItems: {
        subjectName: {
            type: 'input',
            label: '商户名称',
        },
        subjectType: {
            type: 'select',
            label: '主体类型',
            options: OilTeaSubjectTypeDicts,
        },
    },
    operations: [
        {
            type: 'button',
            label: '导入经营主体',
            colorize: 'primary',
            actionService: async () => {
                console.log('导入经营主体');
            },
        },
        {
            type: 'button',
            label: '下载模板',
            colorize: 'info',
            actionService: async () => {
                try {
                    if (isDownloading.value) return;
                    isDownloading.value = true;
                    const {
                        data,
                        fileName = '油茶经营主体模板',
                        fileType = 'application/vnd.ms-excel',
                    } = await downloadOilTeaSubjectTemplate();
                    if (data) {
                        downloadBlobFile(data, fileName, fileType);
                    }
                } catch (error) {
                    console.log(error);
                } finally {
                    isDownloading.value = false;
                }
            },
        },
    ],
    tableColumns: {
        subjectName: '商户名称',
        subjectType: '主体类型',
        uniscid: {
            label: '统一社会信用代码',
            formatter: (row) => {
                return row.uniscid ? row.uniscid : '-';
            },
        },
        legal: {
            label: '法定代表人',
            formatter: (row) => {
                return row.legal ? row.legal : '-';
            },
        },
        opAddress: {
            label: '地址',
            formatter: (row) => {
                return row.opAddress ? row.opAddress : '-';
            },
        },
        updateTime: {
            label: '更新时间',
            formatter: (row) => {
                return row.updateTime ? dayjs(row.updateTime).format('YYYY-MM-DD') : '-';
            },
        },
        operations: {
            label: '操作',
            width: '180px',
        },
    },
    updateFormItems: {
        id: {
            type: 'id',
        },
        uniscid: {
            type: 'slot',
            slotOnFormItem: true,
        },
        subjectName: {
            type: 'input',
            label: '商户名称',
            required: true,
        },
        subjectType: {
            type: 'select',
            label: '主体类型',
            options: OilTeaSubjectTypeDicts,
            required: true,
        },
        legal: {
            type: 'input',
            label: '法定代表人',
        },
        belongTownship: {
            type: 'input',
            label: '所属乡镇',
            required: true,
        },
        contactPhone: {
            type: 'input',
            label: '联系电话',
        },
        opAddress: {
            type: 'input',
            label: '经营地址',
            required: true,
        },
        plantType: {
            type: 'select',
            label: '种植类型',
            options: getEnumOptions(PlantTypeEnum),
        },
        plantMode: {
            type: 'select',
            label: '种植模式',
            options: getEnumOptions(PlantModeEnum),
        },
        plantTotalArea: {
            type: 'non-negative-number',
            label: '种植总面积(亩)',
        },
        plantCount: {
            type: 'non-negative-number',
            label: '种植株数',
        },
        isEliteSeed: {
            type: 'switch',
            label: '是否良种树苗',
            activeValue: 1,
            inactiveValue: 0,
        },
        hasReceivedSubsidy: {
            type: 'switch',
            label: '是否领取补贴',
            activeValue: 1,
            inactiveValue: 0,
        },
        receivedSubsidy: {
            type: 'non-negative-number',
            label: '领取政策补贴',
        },
        subsidyTime: {
            type: 'date-picker',
            label: '补贴时间',
            placeholder: '请选择补贴时间',
        },
    },

    // 这里需要替换为实际的API服务
    listFetchService: getOilTeaSubjectList,

    // // detailFetchService: getOilTeaSubjectDetail,
    // NOTE: 即便配置了rowOperations对应的覆盖行为，改配置还是不能少，不然还是会有默认行为。也可能时配置的不对？？？

    /* updateService: updateOilTeaSubject, */
    deleteService: deleteOilTeaSubject,

    /** 自定义操作列按钮 */
    rowOperations: [
        {
            /** 操作类型 */
            type: 'link',
            /** 操作按钮文本 */
            label: '详情',
            /** 按钮样式计算函数 */
            colorize: () => 'primary',
            /** 操作执行函数 */
            actionService: async (params) => {
                router.push({
                    path: '/oilTeaSubject/detail',
                    query: {
                        id: params.id,
                    },
                });
            },
            /** 是否显示 */
            canDisplay: () => true,
            displayIndex: -1,
        },
        {
            type: 'link',
            label: '编辑',
            actionService: async (params) => {
                subjectType.value = params.subjectType;
                await showUpdateDialog(params);
            },
            /** 是否显示 */
            canDisplay: () => true,
            displayIndex: -1,
        },
    ],
};

const { formProps: updateFormProps, showDialog: showUpdateDialog } = useEditableDialog({
    title: '编辑经营主体',
    formItems: {
        id: {
            type: 'id',
            label: 'id',
        },
        uniscid: {
            type: 'slot',
            slotOnFormItem: true,
        },
        subjectName: {
            type: 'input',
            label: '商户名称',
            required: true,
        },
        subjectType: {
            type: 'slot',
            label: '主体类型',
            required: true,
        },
        legal: {
            type: 'input',
            label: '法定代表人',
            required: true,
        },
        belongTownship: {
            type: 'input',
            label: '所属乡镇',
            required: true,
        },
        contactPhone: {
            type: 'input',
            label: '联系电话',
        },
        opAddress: {
            type: 'input',
            label: '经营地址',
            required: true,
        },
        plantType: {
            type: 'input',
            label: '种植品种',
            /*  options: getEnumOptions(PlantTypeEnum), */
        },
        plantMode: {
            type: 'select',
            label: '种植模式',
            options: getEnumOptions(PlantModeEnum),
        },
        plantTotalArea: {
            type: 'non-negative-number',
            label: '种植总面积(亩)',
        },
        plantCount: {
            type: 'non-negative-number',
            label: '种植株数',
        },
        isEliteSeed: {
            type: 'switch',
            label: '是否良种树苗',
            activeValue: 1,
            inactiveValue: 0,
        },
        hasReceivedSubsidy: {
            type: 'switch',
            label: '是否领取补贴',
            activeValue: 1,
            inactiveValue: 0,
        },
        receivedSubsidy: {
            type: 'non-negative-number',
            label: '领取政策补贴',
        },
        subsidyTime: {
            type: 'date-picker',
            label: '补贴时间',
            placeholder: '请选择补贴时间',
        },
    },
    submitRequest: async (data) => {
        // 提交的参数
        const params = {
            ...data,
        };
        return updateOilTeaSubject(params).then(() => {
            entityCrudRef.value?.handleRefresh();
        });
    },
    width: '800px',
});
const subjectType = ref('');
const uniscidRequired = ref(false);
const updateFormRef = ref<any>(null);
const handleSubjectTypeChange = (value: string) => {
    subjectType.value = value;
    uniscidRequired.value = value !== SubjectTypeEnum.Farmer;
    updateFormRef.value?.clearValidate('uniscid');
};

const entityCrudProps = defineEntityCrud(config);
</script>

<template>
    <div v-loading="isLoading">
        <EditableDialog v-bind="updateFormProps" ref="updateFormRef">
            <template #subjectType="{ modelValue, set }">
                <el-select
                    :model-value="modelValue"
                    @update:model-value="set"
                    @change="handleSubjectTypeChange"
                >
                    <el-option
                        v-for="item in SubjectTypeDicts"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                    />
                </el-select>
            </template>
            <template #uniscid="{ modelValue, set }">
                <el-form-item
                    class="w-full"
                    style="margin-bottom: 0px"
                    label="统一社会信用代码"
                    prop="uniscid"
                    :rules="
                        subjectType === SubjectTypeEnum.Farmer
                            ? []
                            : [
                                  {
                                      required: true,
                                      message: '请填写统一社会信用代码',
                                      trigger: 'blur',
                                  },
                              ]
                    "
                >
                    <el-input
                        :model-value="modelValue"
                        placeholder="请填写统一社会信用代码"
                        @update:model-value="set"
                    />
                </el-form-item>
            </template>
        </EditableDialog>
        <EntityCrud ref="entityCrudRef" v-bind="entityCrudProps">
            <!-- 操作栏插槽 -->
            <template #operations>
                <section class="flex gap-[20px] pb-[15px]">
                    <el-upload
                        ref="uploadRef"
                        :limit="1"
                        :on-exceed="handleExceed"
                        :auto-upload="true"
                        :show-file-list="false"
                        :accept="acceptFileTypes.join(',')"
                        :http-request="batchImportUploadRequest"
                    >
                        <el-button type="primary">导入经营主体</el-button>
                    </el-upload>

                    <el-button type="primary" plain @click="hanldeDownloadTemplate"
                        >下载模板</el-button
                    >
                </section>
            </template>

            <template #tableSubjectType="{ row }: { row: OilTeaSubject }">
                {{ getSubjectTypeLabel(row.subjectType, true) || '-' }}
                <!-- <el-tag>{{ OilTeaSubjectTypeDicts[row.subjectType] }}</el-tag> -->
            </template>
        </EntityCrud>
    </div>
</template>
