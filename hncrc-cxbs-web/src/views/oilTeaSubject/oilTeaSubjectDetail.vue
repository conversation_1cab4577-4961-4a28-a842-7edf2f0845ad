<template>
    <section class="w-full">
        <section class="w-full flex gap-[20px] mb-[20px]">
            <el-image
                :src="subjectAvatar"
                fit="cover"
                style="width: 70px; height: 70px; border-radius: 5px; overflow: hidden"
            />
            <div>
                <div class="text-[20px] font-[500] text-[#000000]">
                    {{ detailData?.subjectName || '' }}
                </div>
                <div
                    class="mt-[10px] px-[12px] inline-block h-[28px] rounded-[14px] leading-[28px] bg-[#ECF5FF] text-[14px] text-[#409EFF]"
                >
                    {{ detailData && getSubjectTypeLabel(detailData.subjectType) }}
                </div>
            </div>
        </section>

        <InfoTable
            v-for="columnConfig in infoTableColumns"
            :key="columnConfig.title"
            class="mb-[20px]"
            cell-empty-text="-"
            :title="columnConfig.title"
            :column-count="columnConfig.columnCount"
            :columns="columnConfig.columns"
            :data="columnConfig.data"
            :labelPosition="'bottom'"
        />
    </section>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { getOilTeaSubjectDetail } from '@/api/oilTeaSubject';
import { OilTeaSubject } from '@/api/oilTeaSubject/type';
import subjectAvatar from '@/assets/image/subjectAvatar.png';
import InfoTable from '@/components/InfoTable/index.vue';
import { InfoTableColumnItem } from '@/components/InfoTable/type';
import { getSubjectTypeLabel } from '@/dicts/SubjectTypeDicts';
import { notNull } from '@/utils';

import { PlantModeEnum, PlantTypeEnum } from './config';

/** 基本的 InfoTable props 配置 */
interface BaseInfoColumnItem<T> {
    key?: string;
    title: string;
    columnCount: number;
    columns: InfoTableColumnItem<T>[];
    data: T;
}

/** 基本信息表格配置 */
const basicInfoColumns = reactive<BaseInfoColumnItem<OilTeaSubject>>({
    title: '基本信息',
    columnCount: 4,
    columns: [
        { label: '统一社会信用代码', key: 'uniscid', span: 1 },
        {
            label: '企业类型',
            key: 'subjectType',
            span: 1,
            formatter: (value) => (notNull(value) ? getSubjectTypeLabel(value) : '-'),
        },
        { label: '法人/经营者', key: 'legal', span: 1 },
        { label: '所属乡镇', key: 'belongTownship', span: 1 },
        { label: '联系电话', key: 'contactPhone', span: 2 },
        { label: '经营地址', key: 'opAddress', span: 2 },
    ],
    data: {
        uniscid: void 0,
        subjectType: void 0,
        legal: void 0,
        belongTownship: void 0,
        contactPhone: void 0,
        opAddress: void 0,
    },
});

/** 种植信息表格配置 */
const plantInfoColumns = reactive<BaseInfoColumnItem<OilTeaSubject>>({
    title: '种植信息',
    columnCount: 4,
    columns: [
        {
            label: '种植苗木品种',
            key: 'plantType',
            span: 1,
            formatter: (value) => (notNull(value) ? enumTranslate(value, PlantTypeEnum) : '-'),
        },
        {
            label: '种植模式',
            key: 'plantMode',
            span: 1,
            formatter: (value) => (notNull(value) ? enumTranslate(value, PlantModeEnum) : '-'),
        },

        { label: '种植总面积（亩）', key: 'plantTotalArea', span: 1 },
        { label: '种植株数（株）', key: 'plantCount', span: 1 },
        {
            key: 'isEliteSeed',
            label: '种植苗木是否良种苗木',

            span: 1,
            formatter: (value) => (notNull(value) ? (String(value) === '1' ? '是' : '否') : '-'),
        },
        {
            key: 'hasReceivedSubsidy',
            label: '是否领取补贴',

            span: 1,
            formatter: (value) => (notNull(value) ? (String(value) === '1' ? '是' : '否') : '-'),
        },
        { key: 'receivedSubsidy', label: '领取政策补贴（万元）', span: 1 },
        {
            key: 'subsidyTime',
            label: '补贴时间',

            span: 1,
            formatter: (value) => (notNull(value) ? dayjs(value).format('YYYY年MM月') : '-'),
        },
    ],
    data: {
        uniscid: void 0,
        subjectType: void 0,
        legal: void 0,
        belongTownship: void 0,
        contactPhone: void 0,
        opAddress: void 0,
    },
});

/** 基本信息表格的列配置 */
const infoTableColumns = ref([basicInfoColumns, plantInfoColumns]);

/** 基本信息数据项配置 */
interface BaseInfoConfig {
    /** label */
    label: string;
    /** 值 */
    value: string;
    /** 列宽 */
    span: number;
    /** 对应的key值 */
    key: string;
    /** 格式化函数 */
    formatter?: (value: string) => string;
}

/** 枚举值翻译 */
const enumTranslate = (value: string, enumObj: typeof PlantModeEnum | typeof PlantTypeEnum) => {
    return enumObj[value] || value;
};

const route = useRoute();
const { id } = route.query;
/** 正在获取详情数据 */
const fetchingDetail = ref(false);
/** 详情数据 */
const detailData = ref<OilTeaSubject>();
/** 获取详情数据 */
const handleFetchDetail = async () => {
    try {
        if (fetchingDetail.value || !id) return;
        fetchingDetail.value = true;
        const res = await getOilTeaSubjectDetail({ id });
        detailData.value = res;
        // 注入数据
        Object.assign(basicInfoColumns.data, res);
        Object.assign(plantInfoColumns.data, res);

        // 刷新页面
        infoTableColumns.value = [basicInfoColumns, plantInfoColumns];
    } catch (error) {
        console.log(error);
    } finally {
        fetchingDetail.value = false;
    }
};

/** 初始化数据 */
const initData = () => {
    if (id) {
        handleFetchDetail();
    }
};
initData();
</script>

<style scoped lang="less"></style>
