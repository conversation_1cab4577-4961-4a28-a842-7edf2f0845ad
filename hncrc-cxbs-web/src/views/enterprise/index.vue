<script setup lang="ts">
import type { FormInstance } from 'element-plus';
import { ElForm } from 'element-plus';
import { computed, reactive, ref } from 'vue';

import type { BasicPageParams } from '@/api/baseModel';
import { useBusinessDistrictZoneStore } from '@/store/modules/businessDistrictZone';
import { CREDIT_RATING } from '@/views/enterprise/enums';

import tableColumns from './tableColumns';

defineOptions({
    name: '企业信用档案',
});

const elFormEl = ref<FormInstance>();
const ZoneId = useBusinessDistrictZoneStore().getCurrentZoneId;
const originOptions = ref([]);

const isAdmin = computed<boolean>(() => {
    return ZoneId === '1';
});

// form 表单数据
const formData = ref({
    companyName: '',
    creditCode: '',
    legalPerson: '',
    creditLevel: '',
    zoneName: '', //商圈
});
const mapperTableColumns = computed(() => {
    const _tb = [];
    for (let tableColumn of tableColumns) {
        if (tableColumn.prop !== 'zoneName') {
            _tb.push(tableColumn);
        } else {
            if (isAdmin.value) _tb.push(tableColumn);
        }
    }
    return _tb;
});

const pager = reactive({
    pageNo: 1,
    pageSize: 10,
    total: 0,
    loading: false,
});
const tableData = ref([]);

const ajax = (pageNo: number = 1) => {
    pager.loading = true;
    const zoneId = useBusinessDistrictZoneStore().getCurrentZoneId;
    return Promise.resolve();
};
ajax();
const pagination = (event: BasicPageParams) => {
    pager.pageSize = event.pageSize;
    ajax(event.pageNo);
};

// 提交表单
const submitForm = () => {
    elFormEl.value?.validate().then(() => {
        ajax();
    });
};

// elfrom 重置表单
const resetForm = () => {
    formData.value = (() => {
        const _a: any = {};
        for (let key of Object.keys(formData.value)) {
            _a[key] = '';
        }
        return _a;
    })();
    ajax();
};
</script>

<template>
    <div>
        <el-form ref="elFormEl" :model="formData" inline>
            <el-form-item label="经营主体名称:" prop="name">
                <el-input v-model="formData.companyName" class="!w-[200px]" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="统一社会信用代码:" prop="code">
                <el-input v-model="formData.creditCode" class="!w-[200px]" placeholder="请输入" />
            </el-form-item>
            <!--            <el-form-item label="法定代表人:" prop="name">
                <el-input v-model="formData.legalPerson" placeholder="请输入" />
            </el-form-item>-->
            <el-form-item v-if="isAdmin" label="商圈归属：">
                <el-select
                    v-model="formData.zoneName"
                    clearable
                    placeholder="请选择商圈归属"
                    class="!w-[170px]"
                >
                    <el-option
                        v-for="i in originOptions"
                        :key="i.value"
                        :label="i.label"
                        :value="i.value"
                    />
                </el-select>
            </el-form-item>

            <el-form-item label="信用等级:" prop="level">
                <!-- el下拉框 -->
                <el-select
                    v-model="formData.creditLevel"
                    clearable
                    style="width: 180px"
                    placeholder="请选择"
                >
                    <el-option label="A" value="A" />
                    <el-option label="B" value="B" />
                    <el-option label="C" value="C" />
                    <el-option label="D" value="D" />
                </el-select>
            </el-form-item>
            <!-- 查询和重置按钮 -->
            <el-form-item>
                <el-button type="primary" @click="submitForm">查询</el-button>
                <el-button @click="resetForm">重置</el-button>
            </el-form-item>
        </el-form>
        <CommonTable
            :loading="pager.loading"
            :table-data="tableData"
            :table-columns="mapperTableColumns"
            :total="pager.total"
            :pageNum="pager.pageNo"
            :pageSize="pager.pageSize"
            @pagination="pagination"
        >
            <!-- level 插槽 -->
            <template #creditLevel="{ scope }">
                <el-tag
                    :type="CREDIT_RATING[scope.row.creditLevel].type"
                    :class="CREDIT_RATING[scope.row.creditLevel].class"
                    :color="CREDIT_RATING[scope.row.creditLevel].color"
                >
                    {{ CREDIT_RATING[scope.row.creditLevel].text }}
                </el-tag>
            </template>

            <template #$edit="{ scope }">
                <el-text
                    class="cursor-pointer"
                    type="primary"
                    @click="$router.push('/archives?id=' + scope.row.id)"
                >
                    查看信用档案
                </el-text>
            </template>
        </CommonTable>
    </div>
</template>

<style scope lang="scss"></style>
