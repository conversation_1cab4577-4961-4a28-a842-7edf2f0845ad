export type CreditRatingTYPE = {
    text: string;
    class?: string;
    type?: 'primary' | 'success' | 'info' | 'warning' | 'danger';
    color?: string;
};
// 信用等级
export const CREDIT_RATING: Record<string, CreditRatingTYPE> = {
    A: {
        text: 'A 优秀',
        class: '!text-[#FF6464]',
        type: 'danger',
    },
    B: {
        text: 'B 良好',
        class: '!text-[#FD960C]',
        type: 'warning',
    },
    C: {
        text: 'C 一般',
        color: '#FFFBEF',
        class: '!text-[#FABE00]',
    },
    D: {
        text: 'D 风险',
        class: '!text-[#3C6DF5]',
    },
};
