<script setup lang="ts">
import dayjs from 'dayjs';
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';

import {
    deleteTeaSubject,
    downloadTeaSubjectTemplate,
    getTeaSubjectList,
    importTeaSubject,
    updateTeaSubject,
} from '@/api/teaSubject';
import { TeaSubject } from '@/api/teaSubject/type';
import EditableDialog from '@/components/EditableForm/index.vue';
import { useEditableDialog } from '@/components/EntityCrud/EditableDialog/hook';
import { defineEntityCrud } from '@/components/EntityCrud/hook';
import EntityCrud from '@/components/EntityCrud/index.vue';
import { EntityCrudInstance, EntityCrudProps } from '@/components/EntityCrud/type';
import { getSubjectTypeLabel, SubjectTypeDicts, SubjectTypeEnum } from '@/dicts/SubjectTypeDicts';
import { useBatchImportHook } from '@/hooks/useBatchImportHook';
import { downloadBlobFile } from '@/utils/file';

const router = useRouter();

/** EntityCrud ref */
const entityCrudRef = ref<EntityCrudInstance>();

/** 正在下载文件 */
const isDownloading = ref(false);

/** 是否正在加载 */
const isLoading = computed(() => {
    return isDownloading.value;
});

/** 导入经营主体 */
/** 批量导入 */
const { uploadRef, batchImportUploadRequest, handleExceed, acceptFileTypes } = useBatchImportHook({
    uploadApi: importTeaSubject,
    onSuccess: () => {
        entityCrudRef.value?.handleRefresh();
    },
});

/** 下载模板 */
const hanldeDownloadTemplate = async () => {
    try {
        if (isDownloading.value) return;
        isDownloading.value = true;
        const {
            data,
            fileName = '茶叶经营主体模板',
            fileType = 'application/vnd.ms-excel',
        } = await downloadTeaSubjectTemplate();
        if (data) {
            downloadBlobFile(data, fileName, fileType);
        }
    } catch (error) {
        console.log(error);
    } finally {
        isDownloading.value = false;
    }
};

/** EntityCrud 配置 */
const config: EntityCrudProps<TeaSubject> = {
    entityName: 'teaSubject',
    displayName: '茶叶经营主体',
    hasIndexColumn: true,
    filterFormItems: {
        subjectName: {
            type: 'input',
            label: '商户名称',
        },
        subjectType: {
            type: 'select',
            label: '主体类型',
            options: SubjectTypeDicts,
        },
    },
    operations: [
        {
            type: 'button',
            label: '导入经营主体',
            colorize: 'primary',
            actionService: () => Promise.resolve(),
        },
        {
            type: 'button',
            label: '下载模板',
            colorize: 'info',
            actionService: () => Promise.resolve(),
        },
    ],
    tableColumns: {
        subjectName: {
            label: '商户名称',
            formatter: (row) => {
                return row.subjectName ? row.subjectName : '-';
            },
        },
        subjectType: {
            label: '主体类型',
            formatter: (row) => getSubjectTypeLabel(row.subjectType) || '-',
        },
        uniscid: {
            label: '统一社会信用代码',
            formatter: (row) => {
                return row.uniscid ? row.uniscid : '-';
            },
        },
        legalName: {
            label: '法人/经营者',
            formatter: (row) => {
                return row.legalName ? row.legalName : '-';
            },
        },
        subjectAddress: {
            label: '地址',
            formatter: (row) => {
                return row.subjectAddress ? row.subjectAddress : '-';
            },
        },
        updateTime: {
            label: '更新时间',
            formatter: (row) => {
                return row.updateTime ? dayjs(row.updateTime).format('YYYY-MM-DD') : '-';
            },
        },
    },
    rowOperations: [
        {
            type: 'link',
            label: '详情',
            displayIndex: -1,
            colorize: () => 'primary',
            actionService: async (row) => {
                router.push({
                    path: '/teaSubject/detail',
                    query: {
                        id: row.id,
                    },
                });
            },
        },
        {
            type: 'link',
            label: '编辑',
            actionService: async (params) => {
                subjectType.value = params.subjectType;
                await showUpdateDialog(params);
            },
            /** 是否显示 */
            canDisplay: () => true,
            displayIndex: -1,
        },
    ],
    updateFormItems: {
        id: {
            type: 'id',
        },
        subjectName: {
            type: 'input',
            label: '商户名称',
            required: true,
        },
        subjectType: {
            type: 'select',
            label: '主体类型',
            options: SubjectTypeDicts,
            required: true,
        },
        uniscid: {
            type: 'input',
            label: '统一社会信用代码',
            required: true,
        },
        legalName: {
            type: 'input',
            label: '法定代表人',
            required: true,
        },
        subjectAddress: {
            type: 'input',
            label: '地址',
        },
        belongTownship: {
            type: 'input',
            label: '所属乡镇',
            required: true,
        },
        telephone: {
            type: 'input',
            label: '联系电话',
        },
        plantingArea: {
            type: 'non-negative-number',
            label: '种植面积(亩)',
        },
        teaType: {
            type: 'input',
            label: '茶叶品种',
        },
        teaYield: {
            type: 'non-negative-number',
            label: '茶青产量(吨)',
        },
    },

    // useCreateFormItemsAsUpdate: true,

    // 这里需要替换为实际的API服务
    /*  listFetchService: getTeaSubjectList, */
    listFetchService: async (params) => {
        const result: any = await getTeaSubjectList(params);
        result.list.forEach((item) => {
            item.avatar = JSON.parse(item.avatar);
        });
        result.list.forEach((item) => {
            item.purchaseQrcode = JSON.parse(item.purchaseQrcode);
        });
        return Promise.resolve(result);
    },

    /*  updateService: updateTeaSubject, */
    deleteService: (row: TeaSubject) => {
        return deleteTeaSubject(row.id);
    },
    /* deleteService: deleteTeaSubject, */
};

const { formProps: updateFormProps, showDialog: showUpdateDialog } = useEditableDialog({
    title: '编辑经营主体',
    formItems: {
        id: {
            type: 'id',
            label: 'id',
        },
        subjectName: {
            type: 'input',
            label: '商户名称',
            required: true,
        },
        subjectType: {
            type: 'slot',
            label: '主体类型',
            required: true,
        },
        uniscid: {
            type: 'slot',
            slotOnFormItem: true,
        },
        legalName: {
            type: 'input',
            label: '法人/经营者',
            required: true,
        },
        telephone: {
            type: 'input',
            label: '联系电话',
        },
        subjectAddress: {
            type: 'input',
            label: '经营地址',
        },
        teaType: {
            type: 'input',
            label: '经营茶叶品种',
        },
        introduction: {
            type: 'textarea',
            label: '主体简介',
            required: true,
        },
        avatar: {
            type: 'single-image',
            label: '企业头像',
            tip: '上传图片，图片大小不超过2MB',
            required: true,
        },
        purchaseLink: {
            type: 'input',
            label: '茶叶购买链接',
        },

        purchaseQrcode: {
            type: 'single-image',
            label: '茶叶购买二维码',
            tip: '上传图片，图片大小不超过2MB',
        },
        /* belongTownship: {
            type: 'input',
            label: '所属乡镇',
            required: true,
        },
        plantingArea: {
            type: 'non-negative-number',
            label: '种植面积(亩)',
        }, */
        /* teaType: {
            type: 'input',
            label: '经营茶叶品种',
        },
        teaYield: {
            type: 'non-negative-number',
            label: '茶青产量(吨)',
        }, */
    },
    submitRequest: async (data) => {
        data.avatar = data.avatar ? JSON.stringify(data.avatar) : '[]';
        data.purchaseQrcode = data.purchaseQrcode ? JSON.stringify(data.purchaseQrcode) : '[]';
        // 提交的参数
        const params = {
            ...data,
        };
        return updateTeaSubject(params).then(() => {
            entityCrudRef.value?.handleRefresh();
        });
    },
    width: '800px',
});
const subjectType = ref('');
const uniscidRequired = ref(false);
const updateFormRef = ref<any>(null);
const handleSubjectTypeChange = (value: string) => {
    subjectType.value = value;
    uniscidRequired.value = value !== SubjectTypeEnum.Farmer;
    updateFormRef.value?.clearValidate('uniscid');
};

const entityCrudProps = defineEntityCrud(config);
</script>

<template>
    <div v-loading="isLoading">
        <EditableDialog v-bind="updateFormProps" ref="updateFormRef">
            <template #subjectType="{ modelValue, set }">
                <el-select
                    :model-value="modelValue"
                    @update:model-value="set"
                    @change="handleSubjectTypeChange"
                >
                    <el-option
                        v-for="item in SubjectTypeDicts"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                    />
                </el-select>
            </template>
            <template #uniscid="{ modelValue, set }">
                <el-form-item
                    class="w-full"
                    style="margin-bottom: 0px"
                    label="统一社会信用代码"
                    prop="uniscid"
                    :rules="
                        subjectType === SubjectTypeEnum.Farmer
                            ? []
                            : [
                                  {
                                      required: true,
                                      message: '请填写统一社会信用代码',
                                      trigger: 'blur',
                                  },
                              ]
                    "
                >
                    <el-input
                        :model-value="modelValue"
                        placeholder="请填写统一社会信用代码"
                        @update:model-value="set"
                    />
                </el-form-item>
            </template>
        </EditableDialog>
        <EntityCrud ref="entityCrudRef" v-bind="entityCrudProps">
            <!-- 操作栏插槽 -->
            <template #operations>
                <section class="flex gap-[20px] pb-[15px]">
                    <el-upload
                        ref="uploadRef"
                        :limit="1"
                        :on-exceed="handleExceed"
                        :auto-upload="true"
                        :show-file-list="false"
                        :accept="acceptFileTypes.join(',')"
                        :http-request="batchImportUploadRequest"
                    >
                        <el-button type="primary">导入经营主体</el-button>
                    </el-upload>

                    <el-button type="primary" plain @click="hanldeDownloadTemplate"
                        >下载模板</el-button
                    >
                </section>
            </template>
        </EntityCrud>
    </div>
</template>
