<template>
    <section class="w-full">
        <section class="w-full flex gap-[20px] mb-[20px]">
            <el-image
                :src="detailData?.avatar || subjectAvatar"
                fit="cover"
                style="width: 70px; height: 70px; border-radius: 5px; overflow: hidden"
            />
            <div>
                <div class="text-[20px] font-[500] text-[#000000]">
                    {{ detailData?.subjectName || '' }}
                </div>
                <div class="flex">
                    <div
                        v-if="detailData?.subjectType"
                        class="mt-[10px] px-[12px] h-[28px] inline-block rounded-[14px] leading-[28px] bg-[#ECF5FF] text-[14px] text-[#409EFF]"
                    >
                        {{ detailData && getSubjectTypeLabel(detailData.subjectType) }}
                    </div>
                    <div
                        v-if="
                            detailData?.purchaseLink ||
                            (detailData?.purchaseQrcode != '[]' && detailData?.purchaseQrcode)
                        "
                        class="flex items-center justify-center cursor-pointer ml-[20px] mt-[10px]"
                        @click="showSaleChannel"
                    >
                        <img
                            class="w-[25px] h-[25px]"
                            :src="getAssetsFileUrl('welcome/shopping.png')"
                        />
                        <span class="ml-[5px] text-[#8FC31F] mt-[2px]">购买渠道</span>
                    </div>
                </div>
            </div>
        </section>

        <el-tabs>
            <el-tab-pane label="基本信息">
                <InfoTable
                    title="基础信息"
                    :column-count="4"
                    :columns="tableColumn"
                    :data="detailData"
                    cell-empty-text="-"
                    :labelPosition="'bottom'"
                />
            </el-tab-pane>
        </el-tabs>

        <SaleChannelModal
            v-model="showChannelDialog"
            :saleChannel="detailData?.purchaseLink"
            :qrCodeUrl="detailData?.purchaseQrcode"
        />
    </section>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { getTeaSubjectDetail } from '@/api/teaSubject';
import { TeaSubject } from '@/api/teaSubject/type';
import subjectAvatar from '@/assets/image/subjectAvatar.png';
import InfoTable from '@/components/InfoTable/index.vue';
import { InfoTableColumnItem } from '@/components/InfoTable/type';
import { getSubjectTypeLabel } from '@/dicts/SubjectTypeDicts';
import { getAssetsFileUrl } from '@/utils/file';
import SaleChannelModal from '@/views/welcome/zoneSeedling/_comp/saleChannelModal.vue';

/** 表格列配置  */
const tableColumn = reactive<InfoTableColumnItem<TeaSubject>[]>([
    { key: 'uniscid', label: '统一社会信用代码', span: 1 },
    {
        key: 'subjectType',
        label: '主体类型',
        span: 1,
        formatter: (value: string) => getSubjectTypeLabel(value) || '-',
    },
    { key: 'legalName', label: '法人/经营者', span: 1 },
    { key: 'telephone', label: '联系电话', span: 1 },
    { key: 'teaType', label: '经营茶叶品种', span: 2 },
    { key: 'subjectAddress', label: '经营地址', span: 2 },
    { key: 'introduction', label: '主体简介', span: 4 },
]);

const route = useRoute();
const { id } = route.query;
/** 正在获取详情数据 */
const fetchingDetail = ref(false);
/** 详情数据 */
const detailData = ref<TeaSubject>({});
/** 获取详情数据 */
const handleFetchDetail = async () => {
    try {
        if (fetchingDetail.value || !id) return;
        fetchingDetail.value = true;
        const res = await getTeaSubjectDetail({ id });
        let pic = res.avatar ? JSON.parse(res.avatar) : '';
        res.avatar = pic.url || '';
        detailData.value = res;
    } catch (error) {
        console.log(error);
    } finally {
        fetchingDetail.value = false;
    }
};
const showChannelDialog = ref(false);
const showSaleChannel = () => {
    showChannelDialog.value = true;
};

/** 初始化数据 */
const initData = () => {
    if (id) {
        handleFetchDetail();
    }
};
initData();
</script>

<style scoped lang="less"></style>
