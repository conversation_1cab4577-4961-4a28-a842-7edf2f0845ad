import type { TableColumnCtx } from 'element-plus';

export function teaLevelConfig() {
    return [
        {
            src: 'image/index/grade_a.png',
        },
        {
            src: 'image/index/grade_b.png',
        },
        {
            src: 'image/index/grade_c.png',
        },
        {
            src: 'image/index/grade_d.png',
        },
    ];
}

export function getData() {
    return [
        {
            src: 'image/index/a.jpg',
            title: '企业信用档案',
            context:
                '企业信用档案是对企业信用信息采集、整理、保存、加工而提供的信用记录和信用报告，是企业整体信用状况的真实体现，是企业获得商业信任的绿色通行证，是大众消费指南和交易决策的重要参考，是安全消费、公平交易（信贷、借贷、 赊销、劳务等商务活动）的重要社会保障体系。',
            icon: 'image/index/aa.png',
        },
        {
            src: 'image/index/b.jpg',
            title: '企业信用码',
            context:
                '为直观的反映经营主体的信用状况，方便消费者和监管部门获取信用信息，将抽象、无形的信用概念转化为看得见的二维码（“信用码”），每个编码与唯一信用主体对应，打造可视化“一户一码”的信用身份证，扫码后展示信用评价结果信息、信用颜色标识信息及其他基本信息（统一社会信用代码、联系方式、具体地址、双公示情况等）。',
            icon: 'image/index/bb.png',
        },
        {
            src: 'image/index/c.jpg',
            title: '五指山热带雨林大叶茶产地码',
            context:
                '茶叶产品赋码，通过对茶叶产地信息录入、传递、 追溯，消费者只需扫一扫茶叶产品上的二维码，就能查看茶叶生产地，确认产品信息的真实性，并根据产品信用评价标准展示信用等级。',
            icon: 'image/index/cc.png',
        },
    ];
}

interface Data {
    one: String;
    two: String;
    three: String;
    number: String;
    evaluation: String;
    score: String;
    source: String;
}

// interface DataTwo {
//     one: String;
//     two: String;
//     three: String;
// }

interface SpanProps {
    row: Data;
    column: TableColumnCtx<Data>;
    rowIndex: number;
    columnIndex: number;
}

// interface SpanPropsTwo {
//     row: DataTwo;
//     column: TableColumnCtx<DataTwo>;
//     rowIndex: number;
//     columnIndex: number;
// }

// export function getSpanAll({ row, column, rowIndex, columnIndex }: SpanProps) {
//     if (columnIndex === 0) {
//         // 第一列
//         if (rowIndex === 0) {
//             return [12, 1];
//         } else if (rowIndex < 12) {
//             return [0, 0];
//         }
//         if (rowIndex === 12) {
//             return [10, 1];
//         } else if (rowIndex < 12) {
//             return [0, 0];
//         }
//         if (rowIndex === 22) {
//             return [20, 1];
//         } else if (rowIndex < 42) {
//             return [0, 0];
//         }
//     } else if (columnIndex === 1) {
//         // 第二列
//         if (rowIndex === 0) {
//             return [3, 1]; // 合并 1-3 行
//         } else if (rowIndex >= 0 && rowIndex < 3) {
//             return [0, 0]; // 这些单元格不显示
//         } else if (rowIndex === 3) {
//             return [6, 1]; // 合并 4-9 行
//         } else if (rowIndex > 3 && rowIndex < 9) {
//             return [0, 0]; // 这些单元格不显示
//         } else if (rowIndex === 10) {
//             return [2, 1]; // 合并 11-12 行
//         } else if (rowIndex === 11) {
//             return [0, 0]; // 这个单元格不显示
//         } else if (rowIndex === 15) {
//             return [5, 1]; // 合并 16-20 行
//         } else if (rowIndex > 15 && rowIndex < 20) {
//             return [0, 0]; // 这些单元格不显示
//         } else if (rowIndex === 23) {
//             return [5, 1]; // 合并 24-28 行
//         } else if (rowIndex > 23 && rowIndex < 28) {
//             return [0, 0]; // 这些单元格不显示
//         } else if (rowIndex === 28) {
//             return [2, 1]; // 合并 29-30 行
//         } else if (rowIndex === 29) {
//             return [0, 0]; // 这个单元格不显示
//         } else if (rowIndex === 30) {
//             return [13, 1]; // 合并 31-43 行
//         } else if (rowIndex > 30 && rowIndex < 43) {
//             return [0, 0]; // 这些单元格不显示
//         }
//     } else if (columnIndex === 2) {
//         // 第三列
//         if (rowIndex === 1) {
//             return [2, 1]; // 合并 2-3 行
//         } else if (rowIndex === 2) {
//             return [0, 0]; // 这个单元格不显示
//         } else if (rowIndex === 32) {
//             return [3, 1]; // 合并 33-35 行
//         } else if (rowIndex > 32 && rowIndex < 35) {
//             return [0, 0]; // 这些单元格不显示
//         } else if (rowIndex === 35) {
//             return [8, 1]; // 合并 36-43 行
//         } else if (rowIndex > 35 && rowIndex < 43) {
//             return [0, 0]; // 这些单元格不显示
//         }
//     } else if (columnIndex === 6) {
//         if (rowIndex === 32) {
//             return [3, 1]; // 合并 33-35 行
//         } else if (rowIndex > 32 && rowIndex < 35) {
//             return [0, 0]; // 这些单元格不显示
//         } else if (rowIndex === 35) {
//             return [8, 1]; // 合并 36-43 行
//         } else if (rowIndex > 35 && rowIndex < 43) {
//             return [0, 0]; // 这些单元格不显示
//         }
//     }
// }

export function getSpan({ rowIndex, columnIndex }: SpanProps) {
    if (columnIndex === 0) {
        if (rowIndex < 12) {
            return rowIndex === 0 ? [12, 1] : [0, 0];
        } else if (rowIndex < 22) {
            return rowIndex === 12 ? [10, 1] : [0, 0];
        } else if (rowIndex < 43) {
            return rowIndex === 22 ? [21, 1] : [0, 0];
        }
    } else if (columnIndex === 1) {
        if (rowIndex < 3) {
            return rowIndex === 0 ? [3, 1] : [0, 0];
        } else if (rowIndex < 9) {
            return rowIndex === 3 ? [6, 1] : [0, 0];
        } else if (rowIndex === 9) {
            return [1, 1];
        } else if (rowIndex < 12) {
            return rowIndex === 10 ? [2, 1] : [0, 0];
        } else if (rowIndex < 15) {
            return [1, 1];
        } else if (rowIndex < 20) {
            return rowIndex === 15 ? [5, 1] : [0, 0];
        } else if (rowIndex < 23) {
            return [1, 1];
        } else if (rowIndex < 28) {
            return rowIndex === 23 ? [5, 1] : [0, 0];
        } else if (rowIndex < 30) {
            return rowIndex === 28 ? [2, 1] : [0, 0];
        } else if (rowIndex < 43) {
            return rowIndex === 30 ? [13, 1] : [0, 0];
        }
    } else if (columnIndex === 2) {
        // 第三列
        if (rowIndex === 1) {
            return [2, 1]; // 合并 2-3 行
        } else if (rowIndex === 2) {
            return [0, 0]; // 这个单元格不显示
        } else if (rowIndex === 32) {
            return [3, 1]; // 合并 33-35 行
        } else if (rowIndex > 32 && rowIndex < 35) {
            return [0, 0]; // 这些单元格不显示
        } else if (rowIndex === 35) {
            return [8, 1]; // 合并 36-43 行
        } else if (rowIndex > 35 && rowIndex < 43) {
            return [0, 0]; // 这些单元格不显示
        }
    }
}
