<script setup lang="ts">
import { reactive, ref } from 'vue';

import { getSeedlingList } from '@/api/welcome/seedling';
import BorderBox from '@/components/welcomeZone/BorderBox.vue';
import CustomTabs from '@/components/welcomeZone/customTabs.vue';
import SearchBox from '@/components/welcomeZone/SearchBox.vue';
import { getAssetsFileUrl } from '@/utils/file';

import SeedlingDetailModal from './_comp/seedlingDetailModal.vue';
import SeedlingPolicyDetailModal from './_comp/seedlingPolicyDetailModal.vue';
import { PolicyListItem, TSeedlingInfo } from './type';

/** 默认头像 */
const defaultAvatar = getAssetsFileUrl('welcome/seedling_default_avatar.png');

/** 搜索关键字 */
const searchKeyword = ref('');

/** 分页器管理 */
const paginationConfig = reactive({
    /** 当前页码 */
    currentPage: 1,
    /** 每页显示条数 */
    pageSize: 10,
    /** 可配置每页显示条数 */
    pageSizes: [10, 30, 50, 100],
    /** 总条数 */
    total: 0,
    /** 尺寸 */
    size: 'large',
});

/** 正在获取数据 */
const fetching = ref(false);

/** 苗木列表 */
const seedlingList = ref<TSeedlingInfo[]>([]);

/** 获取苗木列表 */
const handleFetchList = async () => {
    try {
        if (fetching.value) return;
        fetching.value = true;
        const params = {
            pageNo: paginationConfig.currentPage,
            pageSize: paginationConfig.pageSize,
        };
        if (searchKeyword.value) {
            params['seedlingCategory'] = searchKeyword.value;
        }
        const { list = [], total = 0 } = await getSeedlingList(params);
        seedlingList.value = list.map((item) => {
            const seedlingPictureData = item.seedlingPictureJson ? [item.seedlingPictureJson] : '';

            let relatedPolicyListData = [];
            try {
                relatedPolicyListData = JSON.parse(item.relatedPolicyListJson || '[]');
            } catch (error) {
                relatedPolicyListData = [];
            }
            return {
                ...item,
                seedlingPicture: seedlingPictureData.length > 0 ? seedlingPictureData[0] : '',
                relatedPolicyList: relatedPolicyListData || [],
            };
        });
        paginationConfig.total = Number(total);
    } catch (error) {
        console.log(error);
    } finally {
        fetching.value = false;
    }
};

/** 翻页 */
const handleCurrentPageChange = (val: number) => {
    paginationConfig.currentPage = val;
    handleFetchList();
};

/** 每页条数设置切换 */
const handlePageSizeChange = (val: number) => {
    paginationConfig.pageSize = val;
    handleFetchList();
};

/** 关键词搜索 */
const handleSearch = () => {
    // 重置页码
    paginationConfig.currentPage = 1;
    handleFetchList();
};

// =========================== 查看详情 开始 ===========================
/** 显示详情弹框 */
const showSeedlingDetailDialog = ref(false);

/** 当前查看的苗木信息 */
const currentSeedlingInfo = ref<TSeedlingInfo>();

/** 点击查看苗木详情 */
const handleShowSeedlingDetail = (item: TSeedlingInfo) => {
    currentSeedlingInfo.value = { ...item };
    showSeedlingDetailDialog.value = true;
};
// =========================== 查看详情 结束 ===========================

// =========================== 查看政策详情 开始 ===========================
/** 显示政策详情 */
const showPolicyDetailDialog = ref(false);
/** 政策详情信息 */
const currentPolicyInfo = ref<PolicyListItem>();
/** 点击显示政策详情 */
const handleClickPolicy = (item: PolicyListItem) => {
    currentPolicyInfo.value = { ...item };
    showPolicyDetailDialog.value = true;
};
// =========================== 查看政策详情 结束 ===========================

/** 初始化数据 */
const initData = () => {
    handleFetchList();
};
initData();
</script>

<template>
    <custom-tabs active-name="特色苗木" class="mt-[0px]">
        <template #default>
            <el-tab-pane label="特色苗木" name="特色苗木" />
        </template>
    </custom-tabs>

    <SearchBox v-model="searchKeyword" @search="handleSearch" />

    <div class="text-[#999999] border-b border-[#999999] p-2 mb-12">
        共有 <span class="text-[#67C23A]">{{ paginationConfig.total }}</span> 类特色苗木
    </div>

    <template v-if="seedlingList.length">
        <div v-loading="fetching">
            <div v-for="item in seedlingList" :key="item.id" class="pb-6">
                <BorderBox>
                    <div class="flex p-4 w-full gap-8">
                        <!-- 左侧图片 -->
                        <div class="w-[500px] h-[300px]">
                            <el-image
                                :src="item.seedlingPicture || defaultAvatar"
                                fit="cover"
                                class="w-full h-full rounded-lg"
                            />
                        </div>

                        <!-- 右侧信息 -->
                        <div class="flex-1 flex flex-row gap-8">
                            <div class="flex-1">
                                <div class="mb-4">
                                    <h2 class="text-2xl font-bold">{{ item.seedlingCategory }}</h2>
                                </div>

                                <div class="text-gray-600 space-y-3">
                                    <div>
                                        <span class="font-medium text-[#909399]">苗木品种：</span>
                                        <span>{{ item.seedlingVariety || '—' }}</span>
                                    </div>

                                    <div>
                                        <span class="font-medium text-[#909399]">规格情况：</span>
                                        <span>{{ item.seedlingSpecs || '—' }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-[#909399]">优势特点：</span>
                                        <span>{{ item.seedlingAdvantages || '—' }}</span>
                                    </div>
                                </div>

                                <div class="mt-6 policy-wrapper">
                                    <span class="mb-2 text-[#67C23A] bg-[#E1F3D8] w-auto p-2"
                                        >相关政策</span
                                    >

                                    <section
                                        v-if="
                                            item.relatedPolicyList && item.relatedPolicyList.length
                                        "
                                        class="text-gray-600 space-y-1 pt-4"
                                    >
                                        <div
                                            v-for="policy in item.relatedPolicyList"
                                            :key="policy.policyName"
                                            :underline="false"
                                            class="policy-item"
                                            @click.stop="handleClickPolicy(policy)"
                                        >
                                            {{ policy.policyName }}
                                        </div>
                                    </section>
                                    <template v-else>
                                        <p class="text-gray-600 info-value pt-4">—</p>
                                    </template>
                                </div>
                            </div>
                            <div>
                                <el-button
                                    type="success"
                                    plain
                                    size="large"
                                    class="p-4 bg-[transparent] text-[#5CA139] border border-[#5CA139]"
                                    @click.stop="handleShowSeedlingDetail(item)"
                                >
                                    查看详情
                                </el-button>
                            </div>
                        </div>
                    </div>
                </BorderBox>
            </div>

            <div class="pagination-container flex justify-end">
                <el-pagination
                    v-model:current-page="paginationConfig.currentPage"
                    v-model:page-size="paginationConfig.pageSize"
                    background
                    :hide-on-single-page="true"
                    :size="paginationConfig.size"
                    :page-sizes="paginationConfig.pageSizes"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="paginationConfig.total"
                    @update:current-page="handleCurrentPageChange"
                    @update:page-size="handlePageSizeChange"
                />
            </div>
        </div>
    </template>

    <template v-else> <el-empty description="暂无数据" :image-size="200" /></template>

    <!-- 弹框显示苗木详情 -->
    <SeedlingDetailModal v-model="showSeedlingDetailDialog" :seedling-info="currentSeedlingInfo" />

    <!-- 弹框显示政策 -->
    <SeedlingPolicyDetailModal v-model="showPolicyDetailDialog" :policy-info="currentPolicyInfo" />
</template>

<style scoped lang="scss">
.policy-wrapper {
    .info-label {
        display: inline-flex;
        justify-content: center;
        align-content: center;
        width: auto;

        padding: 3px 12px;
        border-radius: 4px;
        border: 0px solid #e1f3d8;
        background: #f0f9eb;
        color: #67c23a;
        text-align: center;
        margin-bottom: 10px;
    }

    .info-value {
        color: #3d3d3d;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
    }

    .policy-item {
        cursor: pointer;
        color: #3d3d3d;

        &:hover {
            color: #67c23a;
            text-decoration: underline;
        }

        &.active {
            color: #67c23a;
        }
    }
}

.pagination-container {
    :deep(.el-pagination.is-background .el-pager li.is-active) {
        background-color: #67c23a;
    }
}

.search-container {
    padding: 60px 120px;
    display: flex;
    justify-content: center;
}

.search-box {
    width: 100%;
    max-width: 1200px;
    display: flex;
    gap: 10px;

    .search-input {
        flex: 1;

        :deep(.el-input__wrapper) {
            background-color: #fff;
            height: 80px;
            font-size: 24px;
        }

        :deep(button) {
            border-radius: 0 8px 8px 0;
            color: white;
            background-color: #95d475;
        }

        :deep(.el-input__inner) {
            font-size: 24px;
        }
    }

    .search-btn {
        width: 160px;
        height: 80px;
    }
}
</style>
