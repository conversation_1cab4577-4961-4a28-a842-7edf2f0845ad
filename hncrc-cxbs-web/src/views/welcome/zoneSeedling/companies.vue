<script setup lang="ts">
import { reactive, ref } from 'vue';

import { getSeedlingEntityList, getSeedlingEntityStatistics } from '@/api/welcome/seedling';
import { SeedlingSubjectMerchantProfileVO } from '@/api/welcome/type';
import CompanyCard from '@/components/welcomeZone/CompanyCard.vue';
import CustomTabs from '@/components/welcomeZone/customTabs.vue';
import SearchBox from '@/components/welcomeZone/SearchBox.vue';
import StatisticsPanel from '@/components/welcomeZone/StatisticsPanel.vue';
import { getAssetsFileUrl } from '@/utils/file';

import EntityDetailModal from './_comp/entityDetailModal.vue';
import SaleChannelModal from './_comp/saleChannelModal.vue';

/** 搜索关键字 */
const searchKeyword = ref('');

/** 分页器管理 */
const paginationConfig = reactive({
    /** 当前页码 */
    currentPage: 1,
    /** 每页显示条数 */
    pageSize: 10,
    /** 可配置每页显示条数 */
    pageSizes: [10, 30, 50, 100],
    /** 总条数 */
    total: 0,
    /** 尺寸 */
    size: 'large',
});

// ================================== 商户简介列表 开始 ==================================
/** 正在获取列表数据 */
const fetchingList = ref(false);

/** 商户简介列表 */
const seedlingEntityList = ref<SeedlingSubjectMerchantProfileVO[]>([]);

/** 获取商户简介列表数据 */
const handleEntityFetchList = async () => {
    try {
        fetchingList.value = true;
        const { list = [], total = 0 } = await getSeedlingEntityList({
            pageNo: paginationConfig.currentPage,
            pageSize: paginationConfig.pageSize,
            subjectName: searchKeyword.value,
        });
        seedlingEntityList.value = list;
        paginationConfig.total = Number(total);
    } catch (error) {
        console.log(error);
    } finally {
        fetchingList.value = false;
    }
};
// ================================== 商户简介列表 结束 ==================================

// ================================== 苗木主体统计 开始 ==================================
/** 苗木主体统计数据 */
const entityStatisticsData = ref([
    { value: 0, unit: '家', title: '已入驻经营主体', key: 'totalCount' },
    { value: 0, unit: '家', title: '入驻企业', key: 'enterpriseCount' },
    { value: 0, unit: '家', title: '入驻合作社', key: 'cooperativeCount' },
    { value: 0, unit: '家', title: '入驻个体工商户', key: 'growerCount' },
    { value: 0, unit: '家', title: '入驻农户', key: 'farmerCount' },
]);

/** 正在获取统计数据 */
const fetchingStatistics = ref(false);

/** 获取苗木商户统计数据 */
const handleFetchEntityStatistics = async () => {
    try {
        fetchingStatistics.value = true;
        const res = await getSeedlingEntityStatistics();
        entityStatisticsData.value.forEach((item) => {
            item.value = res[item.key] || item.value;
        });
    } catch (error) {
        console.log(error);
    } finally {
        fetchingStatistics.value = false;
    }
};
// ================================== 苗木主体统计 结束 ==================================

// ================================== 查看商户 开始 ==================================
/** 显示商户详情弹框 */
const showEntityDetailDialog = ref(false);

/** 当前查看的商户信息 */
const currentEntityInfo = ref<SeedlingSubjectMerchantProfileVO>();

/** 点击查看商户详情 */
const handleShowEntityDetail = (item: SeedlingSubjectMerchantProfileVO) => {
    currentEntityInfo.value = { ...item };
    showEntityDetailDialog.value = true;
};
// ================================== 查看商户 结束 ==================================

/** 翻页 */
const handleCurrentPageChange = (val: number) => {
    paginationConfig.currentPage = val;
    handleEntityFetchList();
};

/** 每页条数设置切换 */
const handlePageSizeChange = (val: number) => {
    paginationConfig.pageSize = val;
    handleEntityFetchList();
};

/** 关键词搜索 */
const handleSearch = () => {
    // 重置分页器到第一页然后查询数据
    paginationConfig.currentPage = 1;
    handleEntityFetchList();
};

/** 点击查看详情 */
const handleViewDetail = (entity: SeedlingSubjectMerchantProfileVO) => {
    // 处理查看详情的逻辑
    console.log('查看详情', entity);
};
const showChannelDialog = ref(false);
/**点击查看销售渠道 */
const showSaleChannelModal = (item: SeedlingSubjectMerchantProfileVO) => {
    currentEntityInfo.value = { ...item };
    showChannelDialog.value = true;
};

/** 初始化数据 */
const initData = () => {
    handleEntityFetchList();
    handleFetchEntityStatistics();
};
initData();
</script>

<template>
    <custom-tabs active-name="苗木商户" class="mt-[0px]">
        <template #default>
            <el-tab-pane label="苗木商户" name="苗木商户" />
        </template>
    </custom-tabs>
    <div class="relative top-[-60px] left-0">
        <StatisticsPanel
            :cover-img="getAssetsFileUrl('welcome/image04.png')"
            :cover-img-height="'400px'"
            :item-padding="'340px'"
            :items="entityStatisticsData"
        />
    </div>

    <SearchBox
        v-model="searchKeyword"
        placeholder="请输入要搜索的商户名称"
        @search="handleSearch"
    />

    <template v-if="seedlingEntityList.length">
        <div v-loading="fetchingList">
            <div v-for="item in seedlingEntityList" :key="item.id" class="py-6">
                <CompanyCard
                    :company="item"
                    emptyText="-"
                    @view-detail="handleShowEntityDetail"
                    @view-sale="showSaleChannelModal"
                />
            </div>

            <!--   <div v-for="item in seedlingEntityList" :key="item.id" class="py-6">
                <CompanyCard :company="item" emptyText="-" @view-detail="handleShowEntityDetail" />
            </div>
 -->
            <div class="pagination-container flex justify-end">
                <el-pagination
                    v-model:current-page="paginationConfig.currentPage"
                    v-model:page-size="paginationConfig.pageSize"
                    background
                    :hide-on-single-page="true"
                    :size="paginationConfig.size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="paginationConfig.total"
                    @update:current-page="handleCurrentPageChange"
                    @update:page-size="handlePageSizeChange"
                />
            </div>
        </div>
    </template>

    <template v-else> <el-empty description="暂无数据" :image-size="200" /></template>

    <!-- 弹框显示苗木详情 -->
    <EntityDetailModal v-model="showEntityDetailDialog" :entity-info="currentEntityInfo" />

    <!--弹框显示购买渠道-->
    <SaleChannelModal
        v-model="showChannelDialog"
        :saleChannel="currentEntityInfo?.saleChannel"
        :qrCodeUrl="currentEntityInfo?.qrCodeUrl"
    />
</template>

<style scoped lang="scss">
.pagination-container {
    :deep(.el-pagination.is-background .el-pager li.is-active) {
        background-color: #67c23a;
    }
}

.search-container {
    padding: 60px 120px;
    display: flex;
    justify-content: center;
}

.search-box {
    width: 100%;
    max-width: 1200px;
    display: flex;
    gap: 10px;

    .search-input {
        flex: 1;

        :deep(.el-input__wrapper) {
            background-color: #fff;
            height: 80px;
            font-size: 24px;
        }

        :deep(button) {
            border-radius: 0 8px 8px 0;
            color: white;
            background-color: #95d475;
        }

        :deep(.el-input__inner) {
            font-size: 24px;
        }
    }

    .search-btn {
        width: 160px;
        height: 80px;
    }
}
</style>
