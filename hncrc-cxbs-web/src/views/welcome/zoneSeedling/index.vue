<script lang="ts">
export default {
    name: 'welcomeIndex',
};
</script>
<script setup lang="ts">
import { nextTick, ref, watch } from 'vue';

import BackButton from '@/components/welcomeZone/BackButton.vue';
import WelcomeLayout from '@/components/welcomeZone/welcomeLayout.vue';

import Companies from './companies.vue';
import { ZoneSeedlingTabEnum } from './config';
import Home from './home.vue';
import LevelA from './levelA.vue';
import List from './list.vue';

const initData = async () => {
    nextTick(() => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth',
        });
    });
};
initData();

/** 当前子页面 */
const currentSubpageIndex = ref<ZoneSeedlingTabEnum>(ZoneSeedlingTabEnum.Home);

/** 子页面title列表 */
const subpageTitleList = ref([
    { text: '首页', tabKey: ZoneSeedlingTabEnum.Home },
    { text: '特色苗木', tabKey: ZoneSeedlingTabEnum.SpecialSeedling },
    { text: '苗木商户', tabKey: ZoneSeedlingTabEnum.SeedlingMerchant },
    { text: 'A级经营主体', tabKey: ZoneSeedlingTabEnum.ALevelBusinessEntity },
]);

/** 子页面切换 */
const handleSubpageClick = (item: { text: string; tabKey: ZoneSeedlingTabEnum }) => {
    currentSubpageIndex.value = item.tabKey;
};

/** 查看更多 */
const handleViewMore = (tabKey: ZoneSeedlingTabEnum) => {
    currentSubpageIndex.value = tabKey;
};

/** 切换tab后需要滚动到顶部 */
const handleScrollTop = () => {
    window.scrollTo({
        top: 0,
        behavior: 'smooth',
    });
};

watch(
    () => currentSubpageIndex.value,
    () => {
        nextTick(() => {
            handleScrollTop();
        });
    },
);
</script>

<template>
    <div class="zoon-seedling">
        <BackButton />
        <WelcomeLayout title="信用+苗木超市" imgSrc="welcome/home_seedling_bg.jpg">
            <template #nav>
                <div
                    class="flex flex-row text-[#ffffff] text-[24px] gap-[80px] mt-[-20px] pb-[20px] mx-[6%] font-bold"
                >
                    <span
                        v-for="(item, index) in subpageTitleList"
                        :key="index"
                        :style="{
                            color: currentSubpageIndex === item.tabKey ? '#95D475' : '#fff',
                        }"
                        class="hover:text-[#95D475] cursor-pointer"
                        @click="handleSubpageClick(item)"
                    >
                        {{ item.text }}
                    </span>
                </div>
            </template>
            <Home
                v-if="currentSubpageIndex === ZoneSeedlingTabEnum.Home"
                @viewMore="handleViewMore"
            />
            <List v-if="currentSubpageIndex === ZoneSeedlingTabEnum.SpecialSeedling" />
            <Companies v-if="currentSubpageIndex === ZoneSeedlingTabEnum.SeedlingMerchant" />
            <LevelA v-if="currentSubpageIndex === ZoneSeedlingTabEnum.ALevelBusinessEntity" />
        </WelcomeLayout>
    </div>
</template>
<style scoped lang="scss">
:deep(.financial-zone) {
    .credit-module__content {
        padding: 0;
        overflow: hidden;
    }
}

.zoon-seedling {
    width: 100%;
    overflow: auto;
}

.sports-section {
    padding: 60px 0;
    background-color: #f0f9eb;
    padding: 40px;
}

.sports-title {
    text-align: center;
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 10px;
}

.sports-title-line {
    width: 100px;
    height: 4px;
    background-color: #67c23a;
    margin: 0 auto;
    margin-top: 20px;
    margin-bottom: 40px;
}

.sports-subtitle {
    text-align: left;
    font-weight: bold;
    color: #529b2e;
    font-size: 24px;
    margin-bottom: 40px;
}

.sports-content {
    display: flex;
    margin: 40px auto;
}

.sports-text {
    flex: 1;
    color: #666666;
    p {
        margin-bottom: 20px;
        line-height: 1.8;
        text-indent: 2em;
    }
}

.sports-card {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    padding: 20px;
    gap: 30px;
    display: flex;
    flex-direction: row;
    width: 100%;
}

.credit-level {
    .level-item {
        margin-bottom: 30px;

        &:last-child {
            border-bottom: none;
        }
    }

    .level-title {
        font-weight: bold;
        color: #303133;
        margin-bottom: 10px;
    }

    .level-benefit {
        color: #67c23a;
        margin-bottom: 10px;
        margin-left: 20px;
    }

    .level-desc {
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 10px;
    }

    .level-time {
        color: #909399;
        font-size: 12px;
        margin-bottom: 5px;
    }

    .level-expire {
        color: #529b2e;
        font-size: 12px;
    }
}
</style>
