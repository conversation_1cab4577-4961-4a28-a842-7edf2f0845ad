/** 苗木超市 tab 列表 枚举 */
export enum ZoneSeedlingTabEnum {
    /** 首页 */
    Home = 'home',
    /** 特色苗木 */
    SpecialSeedling = 'SpecialSeedling',
    /** 苗木商户 */
    SeedlingMerchant = 'SeedlingMerchant',
    /** A级经营主体 */
    ALevelBusinessEntity = 'ALevelBusinessEntity',
}

/**
 * 主体类型(列表)(ENUMS: Cooperative-合作社, Grower-种植户, Enterprise-企业, Others-其它)
 */
export enum SeedlingEntityTypeEnum {
    /** 合作社 */
    Cooperative = 'Cooperative',
    /** 企业 */
    Enterprise = 'Enterprise',
    /** 种植户 */
    Grower = 'Grower',
    /** 其它 */
    Other = 'Other',
}

/** 主体类型字典 */
export const SeedlingEntityTypeDictMap = {
    [SeedlingEntityTypeEnum.Cooperative]: '合作社',
    [SeedlingEntityTypeEnum.Enterprise]: '企业',
    [SeedlingEntityTypeEnum.Grower]: '种植户',
    [SeedlingEntityTypeEnum.Other]: '其它',
};

/** 苗木主体详情 tab 列表 枚举 */
export enum EntityDetailDialogTabEnum {
    /** 基本信息 */
    BasicInformation = 'BasicInformation',
    /** 苗木信息 */
    SeedlingInfo = 'SeedlingInfo',
}
