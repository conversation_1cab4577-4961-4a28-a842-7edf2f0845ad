<template>
    <el-dialog
        v-model="modelVal"
        title="购买链接"
        width="500"
        class="dialog-bar module__content"
        :style="{ borderRadius: '5px' }"
    >
        <div v-if="saleChannel" class="ml-[20px] text-[16px]">
            {{ saleChannelLabel }}
        </div>
        <div v-if="saleChannel" class="mt-[10px] mb-[10px] ml-[20px]">
            <el-button
                class="text-[#409EFF] text-[16px] flex"
                link
                type="primary"
                @click="goToRoute"
            >
                <div class="point" />
                {{ saleChannel }}
            </el-button>
        </div>
        <div v-if="qrCodeUrl" class="ml-[20px] text-[16px] mt-[20px]">
            {{ qrCodeLabel }}
        </div>
        <div v-if="qrCodeUrl" class="qr-code-container">
            <div class="qr-code-image">
                <el-image :src="qrCodeUrl" style="width: 100%; height: 100%" />
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="modelVal = false">关闭</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
    saleChannel: string;
    qrCodeUrl: string;
}>();

const modelVal = defineModel<boolean>();

const getQrCodeUrl = (data: string) => {
    if (!data) return '';
    try {
        const pic = JSON.parse(data);
        return pic.url;
    } catch (error) {
        console.error('Failed to parse QR code URL:', error);
        return '';
    }
};

const qrCodeUrl = computed(() => getQrCodeUrl(props.qrCodeUrl));

/* const dialogTitle = computed(() => (props.saleChannel && qrCodeUrl.value ? '购买渠道' : '')); */

const saleChannelLabel = computed(() =>
    props.saleChannel && qrCodeUrl.value ? '购买渠道一：' : '购买渠道：',
);

const qrCodeLabel = computed(() =>
    props.saleChannel && qrCodeUrl.value ? '购买渠道二：' : '购买渠道：',
);

const goToRoute = () => {
    window.open(props.saleChannel, '_blank');
};
</script>

<style lang="scss">
.qr-code-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 16px;
    margin-bottom: 20px;
}

.qr-code-image {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 300px;
    height: 300px;
    margin-top: 10px;
}

.dialog-footer {
    text-align: center;
}

.point {
    width: 10px;
    background-color: #409eff;
    height: 10px;
    border-radius: 50px;
    margin-right: 5px;
}
.dialog-bar {
    border: 2px solid #b3e09c;
    background-image: linear-gradient(180deg, #e4f8d7 -9%, rgba(225, 243, 216, 0.2) 65%);
    .el-dialog__body {
        padding: 0px !important;
    }
    .el-dialog__header .show-close {
        display: flex;
        border-bottom: #f2f6ef !important;
        margin-left: 50px;
    }
}
.module__content {
    background: linear-gradient(0deg, #ffffff 33%, #f7f8f6 60%);
    border-radius: inherit;
}
</style>
