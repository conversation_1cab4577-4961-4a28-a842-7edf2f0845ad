<template>
    <div class="home-seedling-info-card">
        <div v-if="data.seedlingCategory" class="overflow-hidden">
            <img
                :src="data.seedlingPicture || defaultAvatar"
                class="w-full h-[200px] object-cover cursor-pointer select-none rounded-[5px]"
                @click.stop="emit('viewDetail', data)"
            />
            <div class="p-4">
                <h3
                    class="text-xl font-[bold] mb-4 text-[black] cursor-pointer"
                    @click.stop="emit('viewDetail', data)"
                >
                    {{ data.seedlingCategory }}
                </h3>

                <div class="mb-3">
                    <div class="info-label">苗木品种</div>
                    <template v-if="data.seedlingVariety">
                        <el-tooltip
                            popper-class="home-seedling-info-card-box-tooltip"
                            class="box-tooltip"
                            :content="data.seedlingVariety"
                            placement="top"
                            :disabled="!isShowTooltip.seedlingVariety"
                        >
                            <p ref="seedlingVarietyRef" class="text-gray-600 info-value">
                                {{ data.seedlingVariety }}
                            </p>
                        </el-tooltip>
                    </template>

                    <template v-else>
                        <p class="text-gray-600 info-value">—</p>
                    </template>
                </div>

                <div class="mb-3">
                    <div class="info-label">规格情况</div>
                    <template v-if="data.seedlingSpecs">
                        <el-tooltip
                            popper-class="home-seedling-info-card-box-tooltip"
                            class="box-tooltip"
                            :content="data.seedlingSpecs"
                            placement="top"
                            :disabled="!isShowTooltip.seedlingSpecs"
                        >
                            <p ref="seedlingSpecsRef" class="text-gray-600 info-value">
                                {{ data.seedlingSpecs }}
                            </p>
                        </el-tooltip>
                    </template>
                    <template v-else>
                        <p class="text-gray-600 info-value">—</p>
                    </template>
                </div>

                <div class="mb-3">
                    <div class="info-label">特点优势</div>
                    <template v-if="data.seedlingAdvantages">
                        <el-tooltip
                            popper-class="home-seedling-info-card-box-tooltip"
                            class="box-tooltip"
                            :content="data.seedlingAdvantages"
                            placement="top"
                            :disabled="!isShowTooltip.seedlingAdvantages"
                        >
                            <p ref="seedlingAdvantagesRef" class="text-gray-600 info-value">
                                {{ data.seedlingAdvantages }}
                            </p>
                        </el-tooltip>
                    </template>

                    <template v-else>
                        <p class="text-gray-600 info-value">—</p>
                    </template>
                </div>

                <div>
                    <div class="info-label">相关政策</div>

                    <template v-if="data.relatedPolicyList && data.relatedPolicyList.length">
                        <div
                            v-for="policy in data.relatedPolicyList"
                            :key="policy.policyName"
                            :underline="false"
                            class="policy-item"
                            @click.stop="emit('viewPolicy', policy)"
                        >
                            {{ policy.policyName }}
                        </div>
                    </template>
                    <template v-else>
                        <p class="text-gray-600 info-value">—</p>
                    </template>
                </div>
            </div>
        </div>
        <div
            v-else
            class="border border-[#B3E09C] rounded-[5px] h-[630px] flex flex-col justify-center items-center"
            @click.stop="emit('viewMore', ZoneSeedlingTabEnum.SpecialSeedling)"
        >
            <div class="flex flex-col justify-center items-center">
                <div class="w-[200px] h-[90px]">
                    <img
                        :src="getAssetsFileUrl('welcome/box_null.png')"
                        class="w-[100%] h-[100%]"
                    />
                </div>
                <div
                    class="text-[#67C23A] text-lg cursor-pointer hover:underline hover:opacity-80 active:opacity-100 select-none"
                >
                    查看更多
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, reactive, ref, watch } from 'vue';

import { getAssetsFileUrl } from '@/utils/file';

import { ZoneSeedlingTabEnum } from '../config';
import { PolicyListItem, TSeedlingInfo } from '../type';

const props = defineProps<{
    data: TSeedlingInfo;
}>();

/** 默认头像 */
const defaultAvatar = getAssetsFileUrl('welcome/seedling_default_avatar.png');

const emit = defineEmits<{
    (e: 'viewDetail', payload: TSeedlingInfo): void;
    (e: 'viewMore', payload: ZoneSeedlingTabEnum): void;
    (e: 'viewPolicy', payload: PolicyListItem): void;
}>();

/** 是否显示tooltip */
const isShowTooltip = reactive({
    seedlingVariety: false,
    seedlingSpecs: false,
    seedlingAdvantages: false,
});

/** ref引用 */
const seedlingVarietyRef = ref<HTMLElement | null>(null);
const seedlingSpecsRef = ref<HTMLElement | null>(null);
const seedlingAdvantagesRef = ref<HTMLElement | null>(null);

/** 更新是否显示tooltip */
const updateTextOverflowStatus = () => {
    if (seedlingVarietyRef.value) {
        isShowTooltip.seedlingVariety =
            seedlingVarietyRef.value.scrollHeight > seedlingVarietyRef.value.clientHeight;
    }

    if (seedlingSpecsRef.value) {
        isShowTooltip.seedlingSpecs =
            seedlingSpecsRef.value.scrollHeight > seedlingSpecsRef.value.clientHeight;
    }

    if (seedlingAdvantagesRef.value) {
        isShowTooltip.seedlingAdvantages =
            seedlingAdvantagesRef.value.scrollHeight > seedlingAdvantagesRef.value.clientHeight;
    }
};

watch(
    () => props.data,
    () => {
        nextTick(() => {
            updateTextOverflowStatus();
        });
    },
    {
        deep: true,
    },
);

onMounted(() => {
    nextTick(() => {
        updateTextOverflowStatus();
    });
});
</script>

<style>
.home-seedling-info-card-box-tooltip {
    max-width: 300px;
}
</style>

<style scoped lang="scss">
.home-seedling-info-card {
    .introduction-wrapper {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 5;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    .info-label {
        display: inline-flex;
        justify-content: center;
        align-content: center;
        width: auto;

        padding: 3px 12px;
        border-radius: 4px;
        border: 0px solid #e1f3d8;
        background: #e1f3d8;
        color: #67c23a;
        text-align: center;
        margin-bottom: 10px;
    }

    .info-value {
        height: 50px;
        color: #3d3d3d;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
    }

    .policy-item {
        cursor: pointer;
        color: #3d3d3d;

        &:hover {
            color: #67c23a;
            text-decoration: underline;
        }

        &.active {
            color: #67c23a;
        }
    }
}
</style>
