<template>
    <section class="w-full h-full flex gap-[10px] overflow-hidden">
        <div class="shrink-0 max-w-[35%] mr-[20px]">
            <el-image :src="imgUrl" class="w-full h-[100%]" />
        </div>
        <div class="grow-[1] h-full overflow-y-auto">
            <div class="text-[#666666] text-[14px] mb-[10px]">
                按照《海南省林草种子行业信用分级分类监管办法（试行）》（琼林规〔2024〕2号）开展苗木经营主体信用评价
            </div>
            <el-table
                :data="tableData"
                border
                :span-method="mergeRowFn"
                :header-row-style="{ backgroundColor: '#eff7e9' }"
                :header-cell-style="{ backgroundColor: 'rgb(240,249,235)' }"
            >
                <el-table-column type="index" width="60" label="序号" align="center" />
                <el-table-column label="评定内容" prop="label" align="center" />
                <el-table-column label="标准" prop="value" align="left" />
            </el-table>
        </div>
    </section>
</template>

<script setup lang="ts">
import { TableColumnCtx } from 'element-plus';
import { ref } from 'vue';

import { getAssetsFileUrl } from '@/utils/file';

interface DataItem {
    label: string;
    value: string;
    sort: number;
    rowSpan?: number;
}

interface SpanMethodProps {
    row: DataItem;
    column: TableColumnCtx<DataItem>;
    rowIndex: number;
    columnIndex: number;
}

const imgUrl = getAssetsFileUrl('welcome/teamwork.png');

const tableData = ref<DataItem[]>([
    { sort: 1, label: '生产经营许可', value: '许可证延续，变更及时' },
    { sort: 2, label: '生产经营许可', value: '无超范围生产经营行为' },
    { sort: 3, label: '生产经营许可', value: '现有生产经营条件符合发证条件' },
    { sort: 4, label: '生产经营备案', value: '备案及时' },
    { sort: 5, label: '生产经营备案', value: '备案信息完整真实' },
    { sort: 6, label: '生产经营档案', value: '档案建立' },
    { sort: 7, label: '生产经营档案', value: '档案内容齐全、记录及时' },
    { sort: 8, label: '生产经营档案', value: '按规定保存' },
    { sort: 9, label: '包装、标签和使用说明', value: '有标签和使用说明' },
    { sort: 10, label: '包装、标签和使用说明', value: '包装、标签和使用说明真实、规范' },
    { sort: 11, label: '种子质量', value: '无生产经营假劣种子行为' },
    { sort: 12, label: '种子质量', value: '开展种子质量自检或委托检验' },
    { sort: 13, label: '推广销售', value: '无虚假宣传行为' },
    { sort: 14, label: '推广销售', value: '无未审先推行为' },
    { sort: 15, label: '推广销售', value: '未发生侵权品种权行为' },
]);

/** 计算合并单元格并重新排序数据 */
const genDataWithRowSpanCopnfig = (data: DataItem[]) => {
    let rowSpanConfig = {};
    const retData = data.map((item) => {
        if (rowSpanConfig[item.label] === undefined || rowSpanConfig[item.label] === null) {
            rowSpanConfig[item.label] = 1;
        } else {
            rowSpanConfig[item.label] += 1;
        }
        return {
            ...item,
            rowSpan: rowSpanConfig[item.label] || 1,
        };
    });
    // 更新设置rowspan
    const hasSettingRowSpanProps = [];
    retData.forEach((item) => {
        if (hasSettingRowSpanProps.includes(item.label)) {
            item.rowSpan = 0;
        } else {
            item.rowSpan = rowSpanConfig[item.label];
            hasSettingRowSpanProps.push(item.label);
        }
    });
    return retData;
};

/** 立即计算一次 */
// const rowSpanConfig = genDataWithRowSpanCopnfig(tableData.value);
tableData.value = genDataWithRowSpanCopnfig(tableData.value);

/** 表格行合并函数 */
const mergeRowFn = ({ row, column, rowIndex, columnIndex }: SpanMethodProps) => {
    // const data = genDataWithRowSpan(tableData.value);
    if (columnIndex === 1) {
        return {
            // rowspan: row.rowSpan || 1,
            rowspan: row.rowSpan,
            colspan: row.rowSpan > 0 ? 1 : 0,
        };
    }
};
</script>

<style scoped lang="scss"></style>
