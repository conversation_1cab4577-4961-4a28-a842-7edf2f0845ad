<template>
    <div v-for="data in seedlingList" :key="data.id" class="seedling-info-card">
        <div class="flex p-4 gap-8">
            <!-- 左侧图片 -->
            <div class="w-[500px] h-[300px]">
                <el-image
                    :src="data.seedlingPicture"
                    fit="cover"
                    class="w-full h-full rounded-lg"
                />
            </div>

            <!-- 右侧信息 -->
            <div class="flex-1 flex flex-col w-auto gap-4">
                <div class="mb-4">
                    <h2 class="text-2xl font-bold">{{ data.seedlingCategory || '-' }}</h2>
                </div>

                <div class="text-gray-600">
                    <div>
                        <span class="font-medium text-[#909399]">苗木品种：</span>
                        <span>{{ data.seedlingVariety || '—' }}</span>
                    </div>

                    <div>
                        <span class="font-medium text-[#909399]">规格情况：</span>
                        <span>{{ data.seedlingSpecs || '—' }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-[#909399]">优势特点：</span>
                        <span>{{ data.seedlingAdvantages || '—' }}</span>
                    </div>
                    <!-- <div>
                        <span class="font-medium text-[#909399]">其他信息：</span>
                        <span>{{ '—' }}</span>
                    </div> -->
                </div>

                <div class="mt-6">
                    <span class="mb-2 text-[#67C23A] bg-[#E1F3D8] w-auto p-2">相关政策</span>
                    <ul class="text-gray-600 space-y-1 pt-4 list-disc list-inside">
                        <li v-for="policy in data.relatedPolicyList" :key="policy.policyName">
                            {{ policy.policyName }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="flex justify-end">
        <el-pagination
            v-model:current-page="paginationConfig.currentPage"
            v-model:page-size="paginationConfig.pageSize"
            background
            :hide-on-single-page="true"
            :size="paginationConfig.size"
            :page-sizes="paginationConfig.pageSizes"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationConfig.total"
            @update:current-page="handleCurrentPageChange"
            @update:page-size="handlePageSizeChange"
        />
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from 'vue';

import { getSeedlingList } from '@/api/welcome/seedling';
import { SeedlingSubjectMerchantProfileVO } from '@/api/welcome/type';

import { TSeedlingInfo } from '../type';

const props = withDefaults(
    defineProps<{
        // data: TSeedlingInfo;
        entityInfo: SeedlingSubjectMerchantProfileVO;
    }>(),
    {
        // data: () => ({}) as TSeedlingInfo,
        entityInfo: () => ({}) as SeedlingSubjectMerchantProfileVO,
    },
);

/** 正在获取数据 */
const fetching = ref(false);

/** 苗木列表 */
const seedlingList = ref<TSeedlingInfo[]>([]);

/** 获取苗木列表 */
const handleFetchList = async () => {
    try {
        if (fetching.value || !props.entityInfo.id) return;

        fetching.value = true;
        const params = {
            pageNo: paginationConfig.currentPage,
            pageSize: paginationConfig.pageSize,
            subjectId: props.entityInfo.id,
        };

        const { list = [], total = 0 } = await getSeedlingList(params);
        seedlingList.value = list.map((item) => {
            const seedlingPictureData = item.seedlingPictureJson ? [item.seedlingPictureJson] : '';
            const relatedPolicyListData = JSON.parse(item.relatedPolicyListJson || '[]');

            return {
                ...item,
                seedlingPicture: seedlingPictureData.length > 0 ? seedlingPictureData[0] : '',
                relatedPolicyList: relatedPolicyListData,
            };
        });
        paginationConfig.total = Number(total);
    } catch (error) {
        console.log(error);
    } finally {
        fetching.value = false;
    }
};

/** 分页器管理 */
const paginationConfig = reactive({
    /** 当前页码 */
    currentPage: 1,
    /** 每页显示条数 */
    pageSize: 2,
    /** 可配置每页显示条数 */
    pageSizes: [1, 2, 3, 4, 5],
    /** 总条数 */
    total: 0,
    /** 尺寸 */
    size: 'small',
});

/** 翻页 */
const handleCurrentPageChange = (val: number) => {
    paginationConfig.currentPage = val;
    handleFetchList();
};

/** 每页条数设置切换 */
const handlePageSizeChange = (val: number) => {
    paginationConfig.pageSize = val;
    handleFetchList();
};

watch(
    () => props.entityInfo,
    (newVal) => {
        if (newVal && newVal.id) {
            // 重置分页器
            paginationConfig.currentPage = 1;
            handleFetchList();
        }
    },
    { immediate: true },
);
</script>

<style scoped lang="less">
@primary-color: #95d475;

.seedling-info-card {
    width: 100%;
    padding: 20px;
    border: 1px solid @primary-color;
    border-radius: 10px;
    margin-bottom: 10px;
}
</style>
