<template>
    <div>
        <el-dialog v-model="modelVal" width="1200">
            <SubjectDetail :subjectId="subjectId" :showTabs="showTabs" isHome />
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

import { SeedlingSubjectMerchantProfileVO } from '@/api/welcome/type';
import SubjectDetail from '@/views/seedlingSubject/_comp/subjectDetail.vue';
import { SubjectDetailTabEnum } from '@/views/seedlingSubject/config';

const props = withDefaults(
    defineProps<{
        entityInfo: SeedlingSubjectMerchantProfileVO;
        // subjectId: string;
    }>(),
    {
        // subjectId: '',
        entityInfo: () => ({}) as SeedlingSubjectMerchantProfileVO,
    },
);

/** 经营主体 id */
const subjectId = ref<string>(props.entityInfo.id || '');

/** 要显示的 tabs 列表 */
const showTabs = ref<SubjectDetailTabEnum[]>([
    SubjectDetailTabEnum.BaseInfo,
    SubjectDetailTabEnum.SeedlingInfo,
]);

/** v-model 用与控制显示弹框 */
const modelVal = defineModel<boolean>();

watch(modelVal, (newVal) => {
    if (newVal) {
        const { entityInfo } = props;
        subjectId.value = entityInfo.id || '';
    }
});
</script>

<style scoped></style>
