<template>
    <section class="w-full">
        <el-descriptions
            v-for="item in columns"
            :key="item.title"
            :title="item.title"
            :border="true"
            :column="item.columnCount"
            direction="vertical"
            size="default"
            class="mb-[20px]"
        >
            <el-descriptions-item
                v-for="dataItem in item.data"
                :key="dataItem.key"
                :label="dataItem.label"
                :span="dataItem.span"
                :rwoSpan="dataItem.span"
                :width="dataItem.width"
                :min-width="dataItem.width"
                >{{
                    dataItem.formatter ? dataItem.formatter(dataItem.value) : dataItem.value
                }}</el-descriptions-item
            >
        </el-descriptions>
    </section>
</template>

<script setup lang="ts">
import { SeedlingEntityBaseInfoPanelDataItem } from '../type';

withDefaults(
    defineProps<{
        columns: SeedlingEntityBaseInfoPanelDataItem[];
    }>(),
    {
        columns: () => [],
    },
);
</script>

<style scoped lang="less"></style>
