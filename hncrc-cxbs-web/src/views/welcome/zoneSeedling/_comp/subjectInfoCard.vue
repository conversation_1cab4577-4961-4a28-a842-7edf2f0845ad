<template>
    <div>
        <BorderBox class="subject-info-card">
            <div v-if="data.subjectName" class="custom-item w-full overflow-hidden h-[530px]">
                <div class="flex flex-col w-full h-full overflow-hidden">
                    <el-image
                        :src="data.subjectPictureListJson || defaultAvatar"
                        fit="cover"
                        class="w-full h-[200px] object-cover shrink-0 rounded-[5px] grow-0 select-none"
                        @click.stop="emit('viewDetail', data)"
                    />
                    <div
                        class="pt-4 text-sm color-[#666666] text-ellipsis overflow-hidden"
                        @click.stop="emit('viewDetail', data)"
                    >
                        <h3 class="text-xl font-bold mb-4 text-[black] pl-[5px]">
                            {{ data.subjectName }}
                        </h3>
                    </div>
                    <div class="flex-1 overflow-hidden" @click.stop="emit('viewDetail', data)">
                        <el-scrollbar height="100%" class="pr-[10px] pl-[5px]">
                            <div class="mb-2 flex items-center gap-[3px] mr-[5px]">
                                <el-icon><TakeawayBox /></el-icon>
                                <span class="flex">
                                    主体类型:
                                    <text class="info-value">
                                        {{
                                            data?.subjectType
                                                ? getSubjectTypeLabel(data.subjectType)
                                                : ''
                                        }}
                                    </text>
                                </span>
                            </div>
                            <div class="mb-2 flex items-center gap-[3px] mr-[5px]">
                                <el-icon><TakeawayBox /></el-icon>
                                <span class="flex"
                                    >苗木类型:
                                    <text class="info-value">{{
                                        data.seedlingCategory || '-'
                                    }}</text></span
                                >
                            </div>
                            <div
                                class="mb-2 flex items-start gap-[3px] mr-[5px] seedling-type-wrapper"
                            >
                                <el-icon class="mt-[0.25em]"><TakeawayBox /></el-icon>
                                <el-tooltip
                                    popper-class="home-subject-info-card-box-tooltip"
                                    :content="data.seedlingVariety"
                                    placement="top"
                                    :disabled="!isShowTooltip.seedlingVariety"
                                >
                                    <div
                                        ref="seedlingVarietyRef"
                                        class="leading-[1.5em] seedling-type"
                                    >
                                        苗木品种:
                                        {{ data.seedlingVariety || '-' }}
                                    </div>
                                </el-tooltip>
                            </div>
                            <div
                                class="mb-2 flex items-start gap-[3px] mr-[5px] seedling-type-wrapper"
                            >
                                <el-icon class="mt-[0.25em]"><Phone /></el-icon>
                                <el-tooltip
                                    popper-class="home-subject-info-card-box-tooltip"
                                    :content="data.contactPhone"
                                    placement="top"
                                    :disabled="!isShowTooltip.contactPhone"
                                >
                                    <div
                                        ref="contactPhoneRef"
                                        class="leading-[1.5em] seedling-type"
                                    >
                                        联系电话: {{ data.contactPhone || '-' }}
                                    </div>
                                </el-tooltip>
                            </div>
                            <div
                                class="mb-2 flex items-start gap-[3px] mr-[5px] seedling-type-wrapper"
                            >
                                <el-icon class="mt-[3px] !text-[16px]"><AddLocation /></el-icon>
                                <el-tooltip
                                    popper-class="home-subject-info-card-box-tooltip"
                                    :content="data.subjectAddress"
                                    placement="top"
                                    :disabled="!isShowTooltip.subjectAddress"
                                >
                                    <div
                                        ref="subjectAddressRef"
                                        class="address leading-[1.5em] seedling-type"
                                    >
                                        经营地址: {{ data.subjectAddress }}
                                    </div>
                                </el-tooltip>
                            </div>
                            <div
                                class="mb-2 mt-4 font-bold text-lg text-[black] mr-[5px] flex items-center"
                            >
                                <img
                                    class="mr-1"
                                    :src="getAssetsFileUrl('welcome/tea_introduction.png')"
                                />
                                商户简介
                            </div>
                            <el-tooltip
                                popper-class="home-subject-info-card-box-tooltip"
                                :content="data.subjectIntroduction"
                                placement="top"
                                :disabled="!isShowTooltip.subjectIntroduction"
                            >
                                <div
                                    ref="subjectIntroductionRef"
                                    class="text-gray-600 introduction-wrapper"
                                >
                                    {{ data.subjectIntroduction }}
                                </div>
                            </el-tooltip>
                        </el-scrollbar>
                    </div>
                    <div
                        v-if="data.saleChannel || (data.qrCodeUrl != '[]' && data.qrCodeUrl)"
                        class="m-auto flex items-center justify-center cursor-pointer"
                        @click.stop="emit('viewSale', data)"
                    >
                        <img
                            class="w-[30px] h-[30px] mt-[10px]"
                            :src="getAssetsFileUrl('welcome/shopping.png')"
                        />
                        <span class="mt-[12px] ml-[5px] text-[#8FC31F]">购买渠道</span>
                    </div>
                </div>
            </div>
            <div
                v-else
                class="h-[530px] w-[500px] flex flex-col flex items-center justify-center"
                @click.stop="emit('viewMore', ZoneSeedlingTabEnum.SeedlingMerchant)"
            >
                <div class="flex flex-col justify-center items-center">
                    <div class="w-[200px] h-[90px]">
                        <img
                            :src="getAssetsFileUrl('welcome/box_null.png')"
                            class="w-[100%] h-[100%]"
                        />
                    </div>
                    <div
                        class="text-[#67C23A] text-lg cursor-pointer"
                        @click.stop="emit('viewMore', ZoneSeedlingTabEnum.SeedlingMerchant)"
                    >
                        查看更多
                    </div>
                </div>
            </div>
        </BorderBox>
    </div>
</template>

<script setup lang="ts">
import { AddLocation, Phone, TakeawayBox } from '@element-plus/icons-vue';
import { nextTick, onMounted, reactive, ref, watch } from 'vue';

import { SeedlingSubjectMerchantProfileVO } from '@/api/welcome/type';
import BorderBox from '@/components/welcomeZone/BorderBox.vue';
import { getSubjectTypeLabel } from '@/dicts/SubjectTypeDicts';
import { getAssetsFileUrl } from '@/utils/file';

import { ZoneSeedlingTabEnum } from '../config';

/** 默认头像 */
const defaultAvatar = getAssetsFileUrl('welcome/subject_default_avatar.png');

const props = defineProps<{
    data: SeedlingSubjectMerchantProfileVO;
}>();

const showDialog = ref(false);

const emit = defineEmits<{
    (e: 'viewDetail', payload: SeedlingSubjectMerchantProfileVO): void;
    (e: 'viewMore', payload: ZoneSeedlingTabEnum): void;
    (e: 'viewSale', payload: SeedlingSubjectMerchantProfileVO): void;
}>();

/** 是否显示tooltoop */
const isShowTooltip = reactive({
    /** 苗木品种 */
    seedlingVariety: true,
    /* 联系电话 */
    contactPhone: true,
    /* 经营地址 */
    subjectAddress: true,
    /* 商户简介 */
    subjectIntroduction: true,
});
/** 苗木品种容器 */
const seedlingVarietyRef = ref<HTMLElement | null>(null);
/** 联系电话容器 */
const contactPhoneRef = ref<HTMLElement | null>(null);
/** 经营地址容器 */
const subjectAddressRef = ref<HTMLElement | null>(null);
/** 商户简介容器 */
const subjectIntroductionRef = ref<HTMLElement | null>(null);

/** 更新是否显示tooltoop */
const updateTextOverflowStatus = () => {
    if (seedlingVarietyRef.value) {
        isShowTooltip.seedlingVariety =
            seedlingVarietyRef.value.scrollHeight > seedlingVarietyRef.value.clientHeight;
    }

    if (contactPhoneRef.value) {
        isShowTooltip.contactPhone =
            contactPhoneRef.value.scrollHeight > contactPhoneRef.value.clientHeight;
    }

    if (subjectAddressRef.value) {
        isShowTooltip.subjectAddress =
            subjectAddressRef.value.scrollHeight > subjectAddressRef.value.clientHeight;
    }

    if (subjectIntroductionRef.value) {
        isShowTooltip.subjectIntroduction =
            subjectIntroductionRef.value.scrollHeight > subjectIntroductionRef.value.clientHeight;
    }
};

watch(
    () => props.data,
    () => {
        nextTick(() => {
            updateTextOverflowStatus();
        });
    },
    {
        deep: true,
    },
);

const openSaleModal = () => {
    showDialog.value = true;
};

onMounted(() => {
    nextTick(() => {
        updateTextOverflowStatus();
    });
});
</script>

<style scoped lang="scss">
.subject-info-card {
    // .seedling-type-wrapper {
    .seedling-type {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        text-overflow: ellipsis;
        overflow: hidden;
        word-wrap: break-word;
        word-break: break-all;
    }

    .introduction-wrapper {
        word-wrap: break-word;
        word-break: break-all;
    }

    .address {
        word-wrap: break-word;
        word-break: break-all;
    }
}
</style>

<style>
.home-subject-info-card-box-tooltip {
    max-width: 600px;
}
.info-value {
    color: #3d3d3d;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
}
</style>
