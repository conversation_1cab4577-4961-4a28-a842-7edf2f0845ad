<template>
    <div class="seedling-info-card">
        <div class="flex p-4 gap-8">
            <!-- 左侧图片 -->
            <div class="w-[500px] h-[300px]">
                <el-image
                    :src="data.seedlingPicture"
                    fit="contain"
                    class="w-full h-full rounded-lg"
                />
            </div>

            <!-- 右侧信息 -->
            <div class="flex-1 flex flex-col w-auto gap-4">
                <div class="mb-2">
                    <h2 class="text-2xl font-bold">{{ data.seedlingCategory || '-' }}</h2>
                </div>

                <div class="text-gray-600">
                    <div class="info-item">
                        <span class="info-item-label">苗木品种：</span>
                        <span class="info-item-value">{{ data.seedlingVariety || '—' }}</span>
                    </div>

                    <div class="mt-[5px] info-item">
                        <span class="info-item-label">规格情况：</span>
                        <span class="info-item-value">{{ data.seedlingSpecs || '—' }}</span>
                    </div>
                    <div class="mt-[5px] info-item">
                        <span class="info-item-label">优势特点：</span>
                        <span class="info-item-value">{{ data.seedlingAdvantages || '—' }}</span>
                    </div>
                </div>

                <div class="mt-4 policy-wrapper">
                    <span class="mb-2 text-[#67C23A] bg-[#E1F3D8] w-auto p-2">相关政策</span>
                    <ul
                        v-if="data.relatedPolicyList.length > 0"
                        class="text-gray-600 space-y-1 pt-4 list-disc list-inside"
                    >
                        <li
                            v-for="policy in data.relatedPolicyList"
                            :key="policy.policyName"
                            class="policy-item"
                            @click.stop="handleClickPolicy(policy)"
                        >
                            {{ policy.policyName }}
                        </li>
                    </ul>
                    <div v-else class="mt-[10px]">—</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 弹框显示政策 -->
    <SeedlingPolicyDetailModal v-model="showPolicyDetailDialog" :policy-info="currentPolicyInfo" />
</template>

<script setup lang="ts">
import { ref } from 'vue';

import { PolicyListItem, TSeedlingInfo } from '../type';
import SeedlingPolicyDetailModal from './seedlingPolicyDetailModal.vue';

const props = withDefaults(
    defineProps<{
        data: TSeedlingInfo;
    }>(),
    {
        data: () => ({}) as TSeedlingInfo,
    },
);

// =========================== 查看政策详情 开始 ===========================
/** 显示政策详情 */
const showPolicyDetailDialog = ref(false);
/** 政策详情信息 */
const currentPolicyInfo = ref<PolicyListItem>();
/** 点击显示政策详情 */
const handleClickPolicy = (item: PolicyListItem) => {
    currentPolicyInfo.value = { ...item };
    showPolicyDetailDialog.value = true;
};
// =========================== 查看政策详情 结束 ===========================
</script>

<style scoped lang="less">
@primary-color: #95d475;

.seedling-info-card {
    width: 100%;
    padding: 20px;
    border: 1px solid @primary-color;
    border-radius: 10px;

    .policy-wrapper {
        .info-label {
            display: inline-flex;
            justify-content: center;
            align-content: center;
            width: auto;

            padding: 3px 12px;
            border-radius: 4px;
            border: 0px solid #e1f3d8;
            background: #f0f9eb;
            color: #67c23a;
            text-align: center;
            margin-bottom: 10px;
        }

        .info-value {
            color: #3d3d3d;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }

        .policy-item {
            cursor: pointer;
            color: #3d3d3d;

            &:hover {
                color: #67c23a;
                text-decoration: underline;
            }

            &.active {
                color: #67c23a;
            }
        }
    }

    .info-item {
        display: flex;
        font-size: 14px;
        line-height: 1.5;

        .info-item-label {
            flex-shrink: 0;
            flex-grow: 0;
            color: #909399;
        }

        .info-item-value {
            flex-grow: 1;
            color: #3d3d3d;
        }
    }
}
</style>
