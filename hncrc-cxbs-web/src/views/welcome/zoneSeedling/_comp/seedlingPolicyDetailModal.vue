<template>
    <div>
        <el-dialog v-model="modelVal" :title="policyInfo.policyName" center width="900">
            <div class="w-full max-h-[65vh] overflow-y-auto bg-white">
                <div class="text-center pb-2">
                    <!-- <p class="text-xl pt-6 pb-4">
                        {{ policyInfo.policyName }}
                    </p> -->
                    <!-- <span class="text-sm text-[#999999]"
                        >发布时间:
                        {{ dayjs(policyInfo?.createTime).format('YYYY-MM-DD  HH:mm:ss') }}</span
                    > -->
                </div>
                <div class="w-full px-5">
                    <div />
                    <div
                        class="w-full h-auto main-page"
                        v-html="contentProcess.decode(unescapeHtml(policyInfo?.policyDetail))"
                    />

                    <!--这里是文件列表-->
                    <template
                        v-if="
                            (policyInfo?.policyAttachments as Record<string, any>) &&
                            policyInfo?.policyAttachments?.length
                        "
                    >
                        <div class="flex items-center mt-[20px]">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    fill="#888888"
                                    d="M3 1h12.414L21 6.586V23H3zm2 2v18h14V9h-6V3zm10 .414V7h3.586z"
                                />
                            </svg>
                            附件:
                        </div>
                        <div
                            v-for="(item, index) in (policyInfo?.policyAttachments as Record<
                                string,
                                any
                            >) || []"
                            :key="item.url"
                            class="mt-[8px] flex items-center gap-[3px]"
                        >
                            <span>{{ index + 1 }}.</span>
                            <el-link type="primary" :href="item.url" target="_blank">
                                {{ item.url?.split('/').at(-1) }}
                            </el-link>
                        </div>
                    </template>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { watch } from 'vue';

import { unescapeHtml } from '@/utils/RichTextUtil';
import { contentProcess } from '@/utils/RichTextUtil';

import { PolicyListItem } from '../type';

const props = withDefaults(
    defineProps<{
        policyInfo: PolicyListItem;
    }>(),
    {
        policyInfo: () => ({}) as PolicyListItem,
    },
);

/** v-model 用与控制显示弹框 */
const modelVal = defineModel<boolean>();

watch(modelVal, (newVal) => {
    if (newVal) {
        const { policyInfo } = props;
        console.log(policyInfo);
    }
});
</script>

<style lang="scss" scoped>
.active {
    color: #1790ff;
    background-color: #f1f6ff;
}

:deep(ol) {
    margin: 0 0 0 20px;
    list-style: decimal;
}

:deep(p) {
    display: block;
    color: #000000;
    line-height: 1.5;
    display: block;
    margin-top: 1em;
    margin-bottom: 1em;
    margin-left: 0;
    margin-right: 0;
    font-size: 16px;
}
:deep(.main-page) {
    img {
        width: 100% !important;
    }
}
</style>
