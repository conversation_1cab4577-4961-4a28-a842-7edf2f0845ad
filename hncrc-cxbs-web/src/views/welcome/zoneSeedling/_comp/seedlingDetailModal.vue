<template>
    <div style="--title-text-color: #333333; --title-font-size: 16px">
        <el-dialog v-model="modelVal" title="特色苗木详情" width="1200">
            <div class="mb-[10px] text-[var(--title-text-color)] font-[500]">苗木信息</div>
            <SeedlingInfoCard :data="seedlingInfo" />

            <section class="w-full overflow-hidden mt-[20px]">
                <div
                    class="w-full flex justify-between items-center text-[var(--title-text-color)]"
                >
                    <div class="text-[var(--title-text-color)] font-[500]">
                        经营该苗木的经营主体
                    </div>
                    <div>{{ `共 ${paginationConfig.total} 家` }}</div>
                </div>
                <section v-loading="fetchingList" class="w-full overflow-x-auto mt-[10px]">
                    <Table
                        ref="tableRef"
                        height="100%"
                        width="100%"
                        :data="entityList"
                        :columns="tableColumns"
                    >
                        <template #legalName="{ row }">
                            <span>{{ row.legalName || '-' }}</span>
                        </template>
                        <template #subjectAddress="{ row }">
                            <span>{{ row.subjectAddress || '-' }}</span>
                        </template>
                        <template #creditLevel="{ row }">
                            <span
                                v-if="row.creditLevel"
                                class="rounded text-[14px] py-1 px-3 shrink-0 group-[0]"
                                :style="getCredictLevelConfig(row.creditLevel)"
                                >{{
                                    row.creditLevel +
                                    ' ' +
                                    getCredictLevelLabel(row.creditLevel, false)
                                }}</span
                            >
                            <div v-else>-</div>
                        </template>
                        <template #evaluationScore="{ row }">
                            <span>{{ getScore(row.evaluationScore) || '-' }}</span>
                        </template>
                        <template #operations="{ row }">
                            <el-link
                                :underline="false"
                                type="primary"
                                @click.stop="handleShowEntityDetail(row)"
                                >查看</el-link
                            >
                        </template>
                    </Table>
                    <el-pagination
                        v-model:current-page="paginationConfig.currentPage"
                        v-model:page-size="paginationConfig.pageSize"
                        background
                        :hide-on-single-page="true"
                        :size="paginationConfig.size"
                        :page-sizes="paginationConfig.pageSizes"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="paginationConfig.total"
                        @update:current-page="handleCurrentPageChange"
                        @update:page-size="handlePageSizeChange"
                    />
                </section>
            </section>
        </el-dialog>

        <!-- 弹框显示苗木详情 -->
        <EntityDetailModal v-model="showEntityDetailDialog" :entity-info="currentEntityInfo" />
    </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { nextTick, reactive, ref, watch } from 'vue';

import { getSeedlingEntitysBySeedlingId } from '@/api/seedlingBizEntity';
import { SeedlingSubjectRespVO } from '@/api/seedlingBizEntity/type';
import { SeedlingSubjectMerchantProfileVO } from '@/api/welcome/type';
import Table from '@/components/Table/index.vue';
import { TableColumnType } from '@/components/Table/type';

import { TSeedlingInfo } from '../type';
import EntityDetailModal from './entityDetailModal.vue';
import SeedlingInfoCard from './seedlingInfoCard.vue';

const props = withDefaults(
    defineProps<{
        seedlingInfo: TSeedlingInfo;
    }>(),
    {
        seedlingInfo: () => ({}) as TSeedlingInfo,
    },
);

import { getCredictLevelConfig, getCredictLevelLabel } from '@/dicts/CredictLevelDicts';

/** v-model 用与控制显示弹框 */
const modelVal = defineModel<boolean>();

/** 分页器管理 */
const paginationConfig = reactive({
    /** 当前页码 */
    currentPage: 1,
    /** 每页显示条数 */
    pageSize: 10,
    /** 可配置每页显示条数 */
    pageSizes: [10, 20, 30, 50, 100],
    /** 总条数 */
    total: 0,
    /** 尺寸 */
    size: 'large',
});

/** 经营改苗木的经营主体列表 */
const tableColumns = reactive<TableColumnType<SeedlingSubjectRespVO>>({
    index: '序号',
    subjectName: {
        label: '商户名称',
        width: 200,
    },
    /*  subjectType: {
        label: '主体类型',
        formatter: (row) => {
            return getSubjectTypeLabel(row.subjectType) || row.subjectType || '-';
        },
    },
    uniscid: {
        label: '统一社会信用代码',
        minWidth: 120,
    },
    legalName: '法定代表人', */
    contactPhone: {
        label: '联系电话',
        width: 120,
    },
    subjectAddress: {
        label: '地址',
        width: 350,
    },
    creditLevel: {
        label: '信用等级',
        // formatter: (row) => row.creditLevel,
    },
    // evaluationScore: '评价得分',
    evaluationScore: {
        label: '评价得分',
    },
    evaluationDate: {
        label: '评价日期',
        formatter: (row) => {
            return row.evaluationDate ? dayjs(row.evaluationDate).format('YYYY-MM-DD') : '-';
        },
    },
    // 如果需要指定操作列的表头名或宽度，可以显式声明 operations 列
    operations: {
        label: '操作',
        fixed: 'right',
        width: '80',
    },
});

/** 经营改苗木的经营主体 */
const entityList = ref<SeedlingSubjectRespVO[]>([]);

/** 正在请求列表数据 */
const fetchingList = ref(false);

/** 获取列表数据 */
const handleFetchList = async () => {
    try {
        if (!props.seedlingInfo.id) return;
        if (fetchingList.value) return;
        fetchingList.value = true;
        const { list = [], total = 0 } = await getSeedlingEntitysBySeedlingId({
            pageNo: paginationConfig.currentPage,
            pageSize: paginationConfig.pageSize,
            CategoryId: props.seedlingInfo.id,
        });
        entityList.value = list;
        paginationConfig.total = Number(total);
    } catch (error) {
        console.log(error);
    } finally {
        fetchingList.value = false;
    }
};

const getScore = (scores) => {
    let score = '0';
    try {
        score = JSON.parse(scores || '0');
        let total = 0;
        for (const i of score) {
            total += Number(i);
        }
        return total;
    } catch (error) {
        return 0;
    }
};

/** 查看经营主体详情 */
const handleViewEntity = (row: SeedlingSubjectRespVO) => {
    console.log(9999, row);
};

watch(
    modelVal,
    () => {
        if (modelVal.value) {
            nextTick(() => {
                handleFetchList();
            });
        }
    },
    {
        immediate: true,
    },
);

/** 初始化数据 */
const initData = () => {
    handleFetchList();
};
initData();

/** 翻页 */
const handleCurrentPageChange = (val: number) => {
    paginationConfig.currentPage = val;
    handleFetchList();
};

/** 每页条数设置切换 */
const handlePageSizeChange = (val: number) => {
    paginationConfig.pageSize = val;
    handleFetchList();
};

// ================================== 查看商户 开始 ==================================
/** 显示商户详情弹框 */
const showEntityDetailDialog = ref(false);

/** 当前查看的商户信息 */
const currentEntityInfo = ref<SeedlingSubjectMerchantProfileVO>();

/** 点击查看商户详情 */
const handleShowEntityDetail = (item: SeedlingSubjectMerchantProfileVO) => {
    currentEntityInfo.value = { ...item };
    showEntityDetailDialog.value = true;
};
// ================================== 查看商户 结束 ==================================
</script>

<style scoped lang="less"></style>
