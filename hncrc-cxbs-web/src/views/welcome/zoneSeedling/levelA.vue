<script setup lang="ts">
import { reactive, ref } from 'vue';

import { getSeedlingEntityList, getSeedlingLevalAEntityStatistics } from '@/api/welcome/seedling';
import { SeedlingSubjectMerchantProfileVO } from '@/api/welcome/type';
import CompanyCard from '@/components/welcomeZone/CompanyCard.vue';
import CustomTabs from '@/components/welcomeZone/customTabs.vue';
import SearchBox from '@/components/welcomeZone/SearchBox.vue';
import StatisticsPanel from '@/components/welcomeZone/StatisticsPanel.vue';
import { getAssetsFileUrl } from '@/utils/file';

import EntityDetailModal from './_comp/entityDetailModal.vue';

/** 搜索关键字 */
const searchKeyword = ref('');

/** 分页器管理 */
const paginationConfig = reactive({
    /** 当前页码 */
    currentPage: 1,
    /** 每页显示条数 */
    pageSize: 10,
    /** 可配置每页显示条数 */
    pageSizes: [10, 30, 50, 100],
    /** 总条数 */
    total: 0,
    /** 尺寸 */
    size: 'large',
});

// ================================== A级商户简介列表 开始 ==================================
/** 正在获取列表数据 */
const fetchingList = ref(false);

/** A级商户简介列表 */
const seedlingEntityList = ref<SeedlingSubjectMerchantProfileVO[]>([]);

/** 获取A级商户简介列表数据 */
const handleEntityFetchList = async () => {
    try {
        fetchingList.value = true;
        const { list = [], total = 0 } = await getSeedlingEntityList({
            pageNo: paginationConfig.currentPage,
            pageSize: paginationConfig.pageSize,
            subjectName: searchKeyword.value,
            creditLevel: 'A',
        });
        seedlingEntityList.value = list;
        paginationConfig.total = Number(total);
    } catch (error) {
        console.log(error);
    } finally {
        fetchingList.value = false;
    }
};
// ================================== A级商户简介列表 结束 ==================================

// ================================== A级经营主体统计 结束 ==================================
/** 苗木主体统计数据 */
const levelAEntityStatisticsData = ref([
    { value: 0, unit: '家', title: 'A级经营主体量', key: 'totalCount' },
    { value: 0, unit: '家', title: 'A级企业', key: 'enterpriseCount' },
    { value: 0, unit: '家', title: 'A级合作社', key: 'cooperativeCount' },
    { value: 0, unit: '家', title: 'A级个体工商户', key: 'planterCount' },
]);
/** 正在获取苗木商户统计数据  */
const fetchingLevelAEntityStatistics = ref(false);
/** 获取苗木商户统计数据 */
const handleFetchLevelAEntityStatistics = async () => {
    try {
        fetchingLevelAEntityStatistics.value = true;
        const res = await getSeedlingLevalAEntityStatistics();
        levelAEntityStatisticsData.value.forEach((item) => {
            item.value = res[item.key] || item.value;
        });
    } catch (error) {
        console.log(error);
    } finally {
        fetchingLevelAEntityStatistics.value = false;
    }
};
// ================================== A级经营主体统计 结束 ==================================

// ================================== 查看商户 开始 ==================================
/** 显示商户详情弹框 */
const showEntityDetailDialog = ref(false);

/** 当前查看的商户信息 */
const currentEntityInfo = ref<SeedlingSubjectMerchantProfileVO>();

/** 点击查看商户详情 */
const handleShowEntityDetail = (item: SeedlingSubjectMerchantProfileVO) => {
    currentEntityInfo.value = { ...item };
    showEntityDetailDialog.value = true;
};
// ================================== 查看商户 结束 ==================================

/** 翻页 */
const handleCurrentPageChange = (val: number) => {
    paginationConfig.currentPage = val;
    handleEntityFetchList();
};

/** 每页条数设置切换 */
const handlePageSizeChange = (val: number) => {
    paginationConfig.pageSize = val;
    handleEntityFetchList();
};

/** 关键词搜索 */
const handleSearch = () => {
    // 重置分页器到第一页然后查询数据
    paginationConfig.currentPage = 1;
    handleEntityFetchList();
};

/** 初始化数据 */
const initData = () => {
    handleEntityFetchList();
    handleFetchLevelAEntityStatistics();
};
initData();
</script>

<template>
    <custom-tabs active-name="A级经营主体" class="mt-[0px]">
        <template #default>
            <el-tab-pane label="A级经营主体" name="A级经营主体" />
        </template>
    </custom-tabs>
    <div class="relative top-[-60px] left-0">
        <StatisticsPanel
            :cover-img="getAssetsFileUrl('welcome/image02.png')"
            :cover-img-height="'350px'"
            :item-padding="'340px'"
            :items="levelAEntityStatisticsData"
        />
    </div>

    <SearchBox
        v-model="searchKeyword"
        placeholder="请输入要搜索的商户名称"
        @search="handleSearch"
    />

    <template v-if="seedlingEntityList.length">
        <div v-loading="fetchingList">
            <div v-for="item in seedlingEntityList" :key="item.id" class="py-6">
                <CompanyCard :company="item" emptyText="-" @view-detail="handleShowEntityDetail" />
            </div>

            <div class="pagination-container flex justify-end">
                <el-pagination
                    v-model:current-page="paginationConfig.currentPage"
                    v-model:page-size="paginationConfig.pageSize"
                    background
                    :hide-on-single-page="true"
                    :size="paginationConfig.size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="paginationConfig.total"
                    @update:current-page="handleCurrentPageChange"
                    @update:page-size="handlePageSizeChange"
                />
            </div>
        </div>
    </template>

    <template v-else> <el-empty description="暂无数据" :image-size="200" /></template>

    <!-- 弹框显示苗木详情 -->
    <EntityDetailModal v-model="showEntityDetailDialog" :entity-info="currentEntityInfo" />
</template>

<style scoped lang="scss">
.pagination-container {
    :deep(.el-pagination.is-background .el-pager li.is-active) {
        background-color: #67c23a;
    }
}

.search-container {
    padding: 60px 120px;
    display: flex;
    justify-content: center;
}

.search-box {
    width: 100%;
    max-width: 1200px;
    display: flex;
    gap: 10px;

    .search-input {
        flex: 1;

        :deep(.el-input__wrapper) {
            background-color: #fff;
            height: 80px;
            font-size: 24px;
        }

        :deep(button) {
            border-radius: 0 8px 8px 0;
            color: white;
            background-color: #95d475;
        }

        :deep(.el-input__inner) {
            font-size: 24px;
        }
    }

    .search-btn {
        width: 160px;
        height: 80px;
    }
}
</style>
