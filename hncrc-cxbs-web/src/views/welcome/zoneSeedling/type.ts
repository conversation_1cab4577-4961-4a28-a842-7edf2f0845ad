import type { SeedlingCategoryRespVO } from '@/api/welcome/type';

/** 苗木政策 */
export interface PolicyListItem {
    /** 政策附件 */
    policyAttachments: { key: string; url: string }[];
    /** 政策名称 */
    policyName: string;
    /** 政策详情 */
    policyDetail: string;
}

/** 苗木列表数展示数据格式 */
export type TSeedlingInfo = SeedlingCategoryRespVO & {
    /** 苗木图片 */
    seedlingPicture: string;
    /** 相关政策 */
    // relatedPolicyList: Array<string>;
    relatedPolicyList: Array<PolicyListItem>;
};

/** 苗木商户基本信息数据项配置 */
export interface SeedlingEntityBaseInfoConfig {
    /** label */
    label: string;
    /** 值 */
    value: string;
    /** 列宽 */
    span: number;
    /** 每列的宽度 */
    width: string;
    /** 对应的key值 */
    key: string;
    /** 格式化函数 */
    formatter?: (value: string) => string;
}

/** 苗木商户基本信息panel数据配置 */
export interface SeedlingEntityBaseInfoPanelDataItem {
    /** 标题 */
    title: string;
    /** 总列数 */
    columnCount: number;
    /** 数据 */
    data: SeedlingEntityBaseInfoConfig[];
}
