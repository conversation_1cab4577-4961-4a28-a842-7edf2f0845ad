<script setup lang="ts">
import { reactive, ref } from 'vue';

import {
    getSeedlingEntityList,
    getSeedlingEntityStatistics,
    getSeedlingLevalAEntityScanStatistics,
    getSeedlingList,
} from '@/api/welcome/seedling';
import { SeedlingSubjectMerchantProfileVO } from '@/api/welcome/type';
import BorderBox from '@/components/welcomeZone/BorderBox.vue';
import CarouselPanel from '@/components/welcomeZone/CarouselPanel.vue';
import CreditBorderBox from '@/components/welcomeZone/CreditBorderBox.vue';
import CustomTabs from '@/components/welcomeZone/customTabs.vue';
import StatisticsPanel from '@/components/welcomeZone/StatisticsPanel.vue';
import { getCredictLevelConfig, getCredictLevelLabel } from '@/dicts/CredictLevelDicts';
import { getSubjectTypeLabel } from '@/dicts/SubjectTypeDicts';
import { getAssetsFileUrl } from '@/utils/file';
import AutoTable from '@/views/overviews/components/AutoTable/index.vue';

import CreditRatingIndices from './_comp/creditRatingIndices.vue';
import EntityDetailModal from './_comp/entityDetailModal.vue';
import HomeSeedlingInfoCard from './_comp/homeSeedlingInfoCard.vue';
import SaleChannelModal from './_comp/saleChannelModal.vue';
import SeedlingDetailModal from './_comp/seedlingDetailModal.vue';
import SeedlingPolicyDetailModal from './_comp/seedlingPolicyDetailModal.vue';
import SubjectInfoCard from './_comp/subjectInfoCard.vue';
import { ZoneSeedlingTabEnum } from './config';
import { PolicyListItem, TSeedlingInfo } from './type';

// ================================== 特色苗木 开始 ==================================
const showChannelDialog = ref(false);
/** 特色苗木列表 */
const featureSeedlingList = ref<TSeedlingInfo[]>([]);
/** 正在获取苗木列表数据 */
const fetchingSeedlingList = ref(false);
/** 获取特色苗木列表 */
const fetchFeatureSeedlingList = async () => {
    fetchingSeedlingList.value = true;
    try {
        const { list = [] } = await getSeedlingList({ pageNo: 1, pageSize: 6 });
        featureSeedlingList.value = list.map((item) => {
            const seedlingPictureData = item.seedlingPictureJson ? [item.seedlingPictureJson] : '';
            // const seedlingPictureData = JSON.parse(item.seedlingPictureJson || '[]');
            // const relatedPolicyListData = JSON.parse(item.relatedPolicyListJson || '[]');
            let relatedPolicyListData = [];
            try {
                relatedPolicyListData = JSON.parse(item.relatedPolicyListJson || '[]');
            } catch (error) {
                relatedPolicyListData = [];
            }
            return {
                ...item,
                seedlingPicture: seedlingPictureData.length > 0 ? seedlingPictureData[0] : '',
                relatedPolicyList: relatedPolicyListData || [],
            };
        });
    } catch (error) {
        console.log(error);
    } finally {
        fetchingSeedlingList.value = false;
    }
};

/** 显示详情弹框 */
const showSeedlingDetailDialog = ref(false);

/** 当前查看的苗木信息 */
const currentSeedlingInfo = ref<TSeedlingInfo>();

/** 点击查看苗木详情 */
const handleShowSeedlingDetail = (item: TSeedlingInfo) => {
    currentSeedlingInfo.value = { ...item };
    showSeedlingDetailDialog.value = true;
};

/** 显示政策详情 */
const showPolicyDetailDialog = ref(false);
/** 政策详情信息 */
const currentPolicyInfo = ref<PolicyListItem>();
/** 点击显示政策详情 */
const handleShowPolicyDetail = (item: PolicyListItem) => {
    currentPolicyInfo.value = { ...item };
    showPolicyDetailDialog.value = true;
};
// ================================== 特色苗木 结束 ==================================

// ================================== 查看商户 开始 ==================================
/** 显示商户详情弹框 */
const showEntityDetailDialog = ref(false);

/** 当前查看的商户信息 */
const currentEntityInfo = ref<SeedlingSubjectMerchantProfileVO>();

/** 点击查看商户详情 */
const handleShowEntityDetail = (item: SeedlingSubjectMerchantProfileVO) => {
    currentEntityInfo.value = { ...item };
    showEntityDetailDialog.value = true;
};

/**点击查看销售渠道 */
const showSaleChannelModal = (item: SeedlingSubjectMerchantProfileVO) => {
    currentEntityInfo.value = { ...item };
    showChannelDialog.value = true;
};
// ================================== 查看商户 结束 ==================================

// ================================== 商户简介列表 开始 ==================================
/** 商户简介列表 */
const seedlingEntityList = ref<SeedlingSubjectMerchantProfileVO[]>([]);
/** 正在请求商户简介列表数据 */
const fetchingSeedlingEntityList = ref(false);
/** 获取商户简介列表数据 */
const fetchSeedlingEntityList = async () => {
    try {
        fetchingSeedlingEntityList.value = true;
        const { list = [] } = await getSeedlingEntityList({ pageNo: 1, pageSize: 5 });
        seedlingEntityList.value = list;
    } catch (error) {
        console.log(error);
    } finally {
        fetchingSeedlingEntityList.value = false;
    }
};
// ================================== 商户简介列表 结束 ==================================

// ================================== 苗木主体统计 开始 ==================================
/** 苗木主体统计数据 */
const entityStatisticsData = ref([
    { value: 0, unit: '家', title: '已入驻经营主体', key: 'totalCount' },
    { value: 0, unit: '家', title: '入驻企业', key: 'enterpriseCount' },
    { value: 0, unit: '家', title: '入驻合作社', key: 'cooperativeCount' },
    { value: 0, unit: '家', title: '入驻个体工商户', key: 'growerCount' },
    { value: 0, unit: '家', title: '入驻农户', key: 'farmerCount' },
]);
/** 正在获取苗木商户统计数据  */
const fetchingEntityStatisticsData = ref(false);
/** 获取苗木商户统计数据 */
const fetchEntityStatisticsList = async () => {
    try {
        fetchingEntityStatisticsData.value = true;
        const res = await getSeedlingEntityStatistics();
        entityStatisticsData.value.forEach((item) => {
            item.value = res[item.key] || item.value;
        });
    } catch (error) {
        console.log(error);
    } finally {
        fetchingEntityStatisticsData.value = false;
    }
};
// ================================== 苗木主体统计 结束 ==================================

// ================================== A级经营主体列表 开始 ==================================
/** 列表表格配置 */
const levelAEntityTableColumns = reactive([
    { label: '信用等级', prop: 'creditLevel' },
    { label: '主体名称', prop: 'subjectName' },
    { label: '主体类型', prop: 'subjectType' },
    { label: '地址', prop: 'subjectAddress' },
    { label: '操作', prop: 'operations' },
]);
/** A级经营主体列表 */
const levelAEntityList = ref<SeedlingSubjectMerchantProfileVO[]>([]);
/** 正在请求A级经营主体列表数据 */
const fetchingLevelAEntityList = ref(false);
/** 获取A级经营主体列表数据 */
const fetchLevelAEntityList = async () => {
    try {
        fetchingLevelAEntityList.value = true;
        const { list = [] } = await getSeedlingEntityList({
            pageNo: 1,
            pageSize: 20,
            creditLevel: 'A',
        });
        levelAEntityList.value = list;
    } catch (error) {
        console.log(error);
    } finally {
        fetchingLevelAEntityList.value = false;
    }
};
// ================================== A级经营主体列表 开始 ==================================

// ================================== A级经营主体统计 结束 ==================================
/** 苗木主体统计数据 */
const levelAEntityStatisticsData = ref([
    { value: 0, unit: '家', title: 'A级经营主体', key: 'totalCount' },
    { value: 0, unit: '次', title: '一户一码下载', key: 'familyCodeDownload' },
    { value: 0, unit: '次', title: '一户一码查看', key: 'familyCodeView' },
    { value: 0, unit: '次', title: '一苗一码下载', key: 'seedlingCodeDownload' },
    { value: 0, unit: '次', title: '一苗一码查看', key: 'seedlingCodeView' },
]);
/** 正在获取苗木商户统计数据  */
const fetchingLevelAEntityStatisticsData = ref(false);
/** 获取苗木商户统计数据 */
const fetchLevelAEntityStatisticsList = async () => {
    try {
        fetchingLevelAEntityStatisticsData.value = true;
        const res = await getSeedlingLevalAEntityScanStatistics();
        levelAEntityStatisticsData.value.forEach((item) => {
            item.value = res[item.key] || item.value;
        });
    } catch (error) {
        console.log(error);
    } finally {
        fetchingLevelAEntityStatisticsData.value = false;
    }
};
// ================================== A级经营主体统计 结束 ==================================

/** 初始化数据 */
const initData = () => {
    fetchFeatureSeedlingList();
    fetchSeedlingEntityList();
    fetchEntityStatisticsList();
    fetchLevelAEntityList();
    fetchLevelAEntityStatisticsList();
};
initData();

/** 事件管理 */
const emit = defineEmits<{
    (e: 'viewMore', payload: ZoneSeedlingTabEnum): void;
}>();

/** 切换页签 */
const handleChangeTab = (nextTab: ZoneSeedlingTabEnum) => {
    emit('viewMore', nextTab);
};
</script>

<template>
    <section class="zoom-seedling-home">
        <custom-tabs active-name="苗木介绍">
            <template #default>
                <el-tab-pane label="苗木介绍" name="苗木介绍">
                    <BorderBox>
                        <div v-if="featureSeedlingList.length > 0">
                            <CarouselPanel
                                generic="TSeedlingInfo"
                                :items="[
                                    ...featureSeedlingList,
                                    {} as TSeedlingInfo, //预留一个查看更多的位置
                                ]"
                                :display-count="4"
                            >
                                <template #item="{ data }">
                                    <HomeSeedlingInfoCard
                                        :data="data"
                                        @view-more="handleChangeTab"
                                        @view-detail="handleShowSeedlingDetail"
                                        @view-policy="handleShowPolicyDetail"
                                    />
                                </template>
                            </CarouselPanel>
                        </div>
                        <div
                            v-else
                            class="h-[630px] w-[100%] flex flex-col flex items-center justify-center"
                        >
                            <div>
                                <img :src="getAssetsFileUrl('welcome/box_null.png')" />
                            </div>
                            暂无数据
                        </div>
                    </BorderBox>
                </el-tab-pane>
            </template>
        </custom-tabs>

        <custom-tabs active-name="商户简介" class="mt-[60px]">
            <template #default>
                <el-tab-pane label="商户简介" name="商户简介">
                    <div v-if="seedlingEntityList.length > 0">
                        <CarouselPanel
                            generic="SeedlingSubjectMerchantProfileVO"
                            :items="[
                                ...seedlingEntityList,
                                {} as SeedlingSubjectMerchantProfileVO, //预留一个查看更多的位置
                            ]"
                            :display-count="3"
                            class="mt-[30px]"
                        >
                            <template #item="{ data }">
                                <SubjectInfoCard
                                    :data="data"
                                    @view-more="handleChangeTab"
                                    @view-detail="handleShowEntityDetail"
                                    @view-sale="showSaleChannelModal"
                                />
                            </template>
                        </CarouselPanel>
                    </div>
                </el-tab-pane>
            </template>
        </custom-tabs>

        <StatisticsPanel
            :cover-img="getAssetsFileUrl('welcome/welcome_farmer.png')"
            :cover-img-height="'350px'"
            :item-padding="'340px'"
            :items="entityStatisticsData"
        />

        <custom-tabs active-name="A级经营主体" class="mt-[60px]">
            <template #default>
                <el-tab-pane label="A级经营主体" name="A级经营主体">
                    <CreditBorderBox src="welcome/subject_hands.png">
                        <div class="text-right text-[#999999]">更新时间：-</div>
                        <div class="py-4 px-4">
                            <AutoTable
                                :data="levelAEntityList"
                                :columns="levelAEntityTableColumns"
                                :show-overflow-tooltip="false"
                                :headerCellStyle="{ backgroundColor: 'transparent' }"
                                height="380px"
                            >
                                <template #creditLevel="{ row }">
                                    <span
                                        v-if="row.creditLevel"
                                        class="rounded text-[14px] py-1 px-3 shrink-0 group-[0]"
                                        :style="getCredictLevelConfig(row.creditLevel)"
                                        >{{
                                            getCredictLevelLabel(row.creditLevel, true).replace(
                                                '信用',
                                                '',
                                            )
                                        }}</span
                                    >
                                </template>

                                <template #subjectType="{ row }">
                                    <span>
                                        {{
                                            row?.subjectType
                                                ? getSubjectTypeLabel(row.subjectType)
                                                : ''
                                        }}
                                    </span>
                                </template>

                                <template #operations="{ row }">
                                    <span
                                        class="text-[#67C23A] cursor-pointer"
                                        @click.stop="handleShowEntityDetail(row)"
                                        >查看</span
                                    >
                                </template>
                            </AutoTable>
                        </div>
                        <div class="text-center">
                            <div
                                v-if="levelAEntityList.length > 0"
                                class="text-[#67C23A] cursor-pointer"
                                @click.stop="
                                    handleChangeTab(ZoneSeedlingTabEnum.ALevelBusinessEntity)
                                "
                            >
                                查看更多
                            </div>
                        </div>
                    </CreditBorderBox>
                </el-tab-pane>
                <el-tab-pane label="信用评价指标" name="信用评价指标">
                    <BorderBox>
                        <div class="h-[430px] w-full">
                            <!-- <el-empty description="暂无数据" :image-size="230" /> -->
                            <CreditRatingIndices />
                        </div>
                    </BorderBox>
                </el-tab-pane>
            </template>
        </custom-tabs>

        <StatisticsPanel
            :cover-img="getAssetsFileUrl('welcome/welcome_qrcode.png')"
            :cover-img-height="'352px'"
            :item-padding="'340px'"
            :backgroundColor="'#fff'"
            :items="levelAEntityStatisticsData"
        />

        <!-- 弹框显示苗木详情 -->
        <SeedlingDetailModal
            v-model="showSeedlingDetailDialog"
            :seedling-info="currentSeedlingInfo"
        />

        <!-- 弹框显示苗木经营主体详情 -->
        <EntityDetailModal v-model="showEntityDetailDialog" :entity-info="currentEntityInfo" />

        <!-- 弹框显示政策 -->
        <SeedlingPolicyDetailModal
            v-model="showPolicyDetailDialog"
            :policy-info="currentPolicyInfo"
        />
        <!--弹框显示购买渠道-->
        <SaleChannelModal
            v-model="showChannelDialog"
            :saleChannel="currentEntityInfo?.saleChannel"
            :qrCodeUrl="currentEntityInfo?.qrCodeUrl"
        />
    </section>
</template>

<style scoped lang="scss">
/* 将原 index.vue 的所有样式复制到这里 */
:deep(.financial-zone) {
    .credit-module__content {
        padding: 0;
        overflow: hidden;
    }
}

.zoom-seedling-home {
    width: 100%;
    overflow-x: hidden;

    * {
        box-sizing: border-box;
    }

    .info-label {
        display: inline-flex;
        justify-content: center;
        align-content: center;
        width: auto;

        padding: 3px 12px;
        border-radius: 4px;
        border: 0px solid #e1f3d8;
        background: #f0f9eb;
        color: #67c23a;
        text-align: center;
        margin-bottom: 10px;
    }

    .info-value {
        color: #3d3d3d;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
    }

    .policy-item {
        cursor: pointer;
        color: #3d3d3d;

        &:hover {
            color: #67c23a;
            text-decoration: underline;
        }

        &.active {
            color: #67c23a;
        }
    }

    .introduction-wrapper {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 5;
        text-overflow: ellipsis;
        overflow: hidden;
    }
}

.custom-item {
    cursor: pointer;
}

.icon-class {
    line-height: 1.1em;
}

.sports-section {
    padding: 60px 0;
    background-color: #f0f9eb;
    padding: 40px;
}

.sports-title {
    text-align: center;
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 10px;
}

.sports-title-line {
    width: 100px;
    height: 4px;
    background-color: #67c23a;
    margin: 0 auto;
    margin-top: 20px;
    margin-bottom: 40px;
}

.sports-subtitle {
    text-align: left;
    font-weight: bold;
    color: #529b2e;
    font-size: 24px;
    margin-bottom: 40px;
}

.sports-content {
    display: flex;
    margin: 40px auto;
}

.sports-text {
    flex: 1;
    color: #666666;
    p {
        margin-bottom: 20px;
        line-height: 1.8;
        text-indent: 2em;
    }
}

.sports-card {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    padding: 20px;
    gap: 30px;
    display: flex;
    flex-direction: row;
    width: 100%;
}

.credit-level {
    .level-item {
        margin-bottom: 30px;

        &:last-child {
            border-bottom: none;
        }
    }

    .level-title {
        font-weight: bold;
        color: #303133;
        margin-bottom: 10px;
    }

    .level-benefit {
        color: #67c23a;
        margin-bottom: 10px;
        margin-left: 20px;
    }

    .level-desc {
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 10px;
    }

    .level-time {
        color: #909399;
        font-size: 12px;
        margin-bottom: 5px;
    }

    .level-expire {
        color: #529b2e;
        font-size: 12px;
    }
}
</style>
