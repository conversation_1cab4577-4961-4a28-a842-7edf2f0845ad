<script setup lang="ts">
import dayjs from 'dayjs';
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { useRouter } from 'vue-router';

import { getNoticeDetails } from '@/api/welcome';
import { NoticeType } from '@/api/welcome';

const isPreview = ref(false);

const route = useRoute();
/*const router = useRouter();
const content = ref('');*/

const id = route.query.id;

const imageEndIndex = ref(0);

// 公告内容
const notice = ref<NoticeType>({
    createTime: '',
    fileUrl: '',
    id: 0,
    policyDescribe: '',
    policyName: '',
});
const handGet = async (id: string) => {
    const res = await getNoticeDetails(id);
    notice.value = res;
    notice.value.fileUrl = res.fileUrl && JSON.parse(res.fileUrl as string);
};
if (id === 'preview') {
    isPreview.value = true;
    notice.value = JSON.parse(localStorage.getItem('notice') as string);
} else {
    isPreview.value = false;
    handGet(<string>id);
}

/**保持图片在前，方便公告展示 */
function sortFileListVal(list) {
    list.sort((a, b) => {
        if (a.type.startsWith('image') && b.type.startsWith('image')) {
            return 0;
        } else if (a.type.startsWith('image') && !b.type.startsWith('image')) {
            return -1;
        } else {
            return 1;
        }
    });
}
</script>

<template>
    <div class="bg-[#F0F3F7]">
        <div class="w-[1100px] mx-auto">
            <div class="py-5 ml-5">
                <el-breadcrumb separator=">">
                    <el-breadcrumb-item>
                        <template #default>
                            <span>当前位置:</span>
                        </template>
                    </el-breadcrumb-item>
                    <el-breadcrumb-item>
                        <router-link to="/welcome">首页</router-link>
                    </el-breadcrumb-item>
                    <el-breadcrumb-item>
                        <router-link to="/welcome">信用动态</router-link>
                    </el-breadcrumb-item>
                    <el-breadcrumb-item>正文</el-breadcrumb-item>
                </el-breadcrumb>
            </div>
        </div>
    </div>
    <div class="bg-[#F0F3F7] w-full min-h-screen">
        <div class="w-[1100px] mx-auto h-full">
            <div class="flex min-h-screen gap-6">
                <div class="w-[23%] bg-white flex-grow min-h-screen">
                    <div class="active py-5 w-full border-r-[4px] border-[#1790FF]">
                        <p class="text-2xl ml-4">信用动态</p>
                    </div>
                </div>
                <div class="w-[74%] bg-white">
                    <div class="text-center pb-2">
                        <p class="text-xl pt-6 pb-4">
                            {{ notice.policyName }}
                            <el-text v-if="isPreview"> [预览] </el-text>
                        </p>
                        <span class="text-sm text-[#999999]"
                            >发布时间:
                            {{ dayjs(notice?.createTime).format('YYYY-MM-DD  HH:mm:ss') }}</span
                        >
                    </div>
                    <div class="w-full px-5">
                        <div />
                        <div
                            class="w-full h-auto main-page"
                            v-html="decodeURIComponent(notice?.policyDescribe)"
                        />

                        <!--这里是文件列表-->
                        <template v-if="(notice?.fileUrl as Record<string, any>).length">
                            <div class="flex items-center">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        fill="#888888"
                                        d="M3 1h12.414L21 6.586V23H3zm2 2v18h14V9h-6V3zm10 .414V7h3.586z"
                                    ></path>
                                </svg>
                                附件:
                            </div>
                            <div
                                class="mt-[8px]"
                                v-for="(item, index) in (notice?.fileUrl as Record<string, any>) ||
                                []"
                                :key="item.url"
                            >
                                {{ index + 1 }}.
                                <el-link type="primary" :href="item.url" target="_blank">
                                    {{ item.url?.split('/').at(-1) }}
                                </el-link>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.active {
    color: #1790ff;
    background-color: #f1f6ff;
}

:deep(ol) {
    margin: 0 0 0 20px;
    list-style: decimal;
}

:deep(p) {
    display: block;
    color: #000000;
    line-height: 1.5;
    display: block;
    margin-top: 1em;
    margin-bottom: 1em;
    margin-left: 0;
    margin-right: 0;
    font-size: 16px;
}
:deep(.main-page) {
    img {
        width: 100% !important;
    }
}
</style>
