<script setup lang="ts">
/* import { Close } from '@element-plus/icons-vue'; */
import dayjs from 'dayjs';
import { nextTick, ref } from 'vue';
import { useRoute } from 'vue-router';

import { getFinancialPolicyDetails, getOliTeaPolicyDetails } from '@/api/welcome';
import { FinancialPolicyDO } from '@/api/welcome/type';
import { contentProcess } from '@/utils/RichTextUtil';

type NoticeDetail = FinancialPolicyDO & {
    /** 附件 */
    policyFileUrl?: Array<string>;
};

const isPreview = ref(false);

const route = useRoute();
/*const router = useRouter();
const content = ref('');*/

const id = route.query.id as string;
const policy = route.query.policy as string;

const imageEndIndex = ref(0);

// 公告内容
const notice = ref<NoticeDetail>({
    createTime: '',
    policyFileUrl: null,
    id: 0,
    policyContent: '',
    policyName: '',
});
const handGet = async (id: string) => {
    // const res = await getNoticeDetails(id);
    if (policy != '3') {
        const res = await getFinancialPolicyDetails({ id: id });
        let policyFileUrl = JSON.parse(res.policyFileUrl || '[]');
        notice.value = { ...res, policyFileUrl: policyFileUrl?.map((item) => item.url) };
    } else {
        const res = await getOliTeaPolicyDetails({ id: id });
        let policyFileUrl = JSON.parse(res.policyFileListJson || '[]');
        notice.value = { ...res, policyFileUrl: policyFileUrl?.map((item) => item.url) };
    }
};

(() => {
    if (id === 'preview') {
        isPreview.value = true;
        notice.value = JSON.parse(localStorage.getItem('notice') as string);
    } else {
        isPreview.value = false;
        handGet(id);
    }
})();

/** 保持图片在前，方便公告展示 */
function sortFileListVal(list) {
    list.sort((a, b) => {
        if (a.type.startsWith('image') && b.type.startsWith('image')) {
            return 0;
        } else if (a.type.startsWith('image') && !b.type.startsWith('image')) {
            return -1;
        } else {
            return 1;
        }
    });
}
const initData = async () => {
    nextTick(() => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth',
        });
    });
};
initData();
</script>

<template>
    <div class="bg-[#F0F3F7]">
        <div class="w-[1100px] mx-auto">
            <div class="py-5 ml-5">
                <el-breadcrumb separator=">">
                    <el-breadcrumb-item>
                        <template #default>
                            <span>当前位置:</span>
                        </template>
                    </el-breadcrumb-item>
                    <el-breadcrumb-item>
                        <router-link to="/welcome">首页</router-link>
                    </el-breadcrumb-item>
                    <el-breadcrumb-item>
                        <router-link to="/welcome">{{
                            policy ? '油茶政策' : '金融政策'
                        }}</router-link>
                    </el-breadcrumb-item>
                    <el-breadcrumb-item>正文</el-breadcrumb-item>
                </el-breadcrumb>
            </div>
        </div>
    </div>
    <!-- <div style="position: fixed; right: 200px; top: 20px">
        <el-icon size="20"><Close /></el-icon>
    </div> -->
    <div class="bg-[#F0F3F7] w-full min-h-screen">
        <div class="w-[1100px] mx-auto h-full">
            <div class="flex min-h-screen gap-6">
                <div class="w-[23%] bg-white flex-grow min-h-screen">
                    <div class="active py-5 w-full border-r-[4px] border-[#1790FF]">
                        <p style="text-align: center">{{ policy ? '油茶政策' : '金融政策' }}</p>
                    </div>
                </div>
                <div class="w-[74%] bg-white">
                    <div class="text-center pb-2">
                        <p class="text-xl pt-6 pb-4">
                            {{ notice.policyName }}
                            <el-text v-if="isPreview"> [预览] </el-text>
                        </p>
                        <span class="text-sm text-[#999999]"
                            >发布时间:
                            {{ dayjs(notice?.createTime).format('YYYY-MM-DD  HH:mm:ss') }}</span
                        >
                    </div>
                    <div class="w-full px-5">
                        <div />
                        <div
                            class="w-full h-auto main-page"
                            v-html="contentProcess.decode(notice?.policyContent)"
                        />

                        <!--这里是文件列表-->
                        <template v-if="notice?.policyFileUrl && notice?.policyFileUrl.length">
                            <div class="flex items-center mt-[20px]">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        fill="#888888"
                                        d="M3 1h12.414L21 6.586V23H3zm2 2v18h14V9h-6V3zm10 .414V7h3.586z"
                                    />
                                </svg>
                                附件:
                            </div>
                            <div
                                v-for="(item, index) in notice?.policyFileUrl"
                                :key="item"
                                class="mt-[8px] flex items-center"
                            >
                                {{ index + 1 }}.
                                <el-link
                                    type="primary"
                                    :href="item"
                                    target="_blank"
                                    class="ml-[5px]"
                                >
                                    {{ item?.split('/').at(-1) }}
                                </el-link>
                            </div>
                        </template>
                        <div class="h-[200px]" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.active {
    color: #1790ff;
    background-color: #f1f6ff;
}

:deep(ol) {
    margin: 0 0 0 20px;
    list-style: decimal;
}

:deep(p) {
    display: block;
    color: #000000;
    line-height: 1.5;
    display: block;
    margin-top: 1em;
    margin-bottom: 1em;
    margin-left: 0;
    margin-right: 0;
    font-size: 16px;
}
:deep(.main-page) {
    img {
        width: 100% !important;
    }
}
</style>
