<script setup lang="ts">
import { AddLocation, Phone, TakeawayBox } from '@element-plus/icons-vue';
import { nextTick, ref } from 'vue';

import {
    getHomeTeaSubjectList,
    getTeaDetail,
    getTeaStatistics,
    getTeaSubject,
} from '@/api/welcome/teaOrigin';
import { OliTeaStatisticsVO, TeaOriginListVO, TeaSubjectVO } from '@/api/welcome/type';
import BackButton from '@/components/welcomeZone/BackButton.vue';
import BorderBox from '@/components/welcomeZone/BorderBox.vue';
import CarouselPanel from '@/components/welcomeZone/CarouselPanel.vue';
import CustomTabs from '@/components/welcomeZone/customTabs.vue';
import StatisticsPanel from '@/components/welcomeZone/StatisticsPanel.vue';
import WelcomeLayout from '@/components/welcomeZone/welcomeLayout.vue';
import { getSubjectTypeLabel } from '@/dicts/SubjectTypeDicts';
import { getAssetsFileUrl } from '@/utils/file';
import SaleChannelModal from '@/views/welcome/zoneSeedling/_comp/saleChannelModal.vue';

/** 默认头像 */
const defaultAvatar = getAssetsFileUrl('welcome/seedling_default_avatar.png');
const initData = async () => {
    nextTick(() => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth',
        });
    });
};
initData();

/**茶园简介列表 */
const teaOriginList = ref<TeaOriginListVO[]>([]);
const getfetchTeaOriginList = async () => {
    const res = await getHomeTeaSubjectList();
    teaOriginList.value = res as any;
};
/**茶园主体分类统计 */
const total = ref(0);
const statisticsData = ref<OliTeaStatisticsVO>();
const getfetchTeaStatistics = async () => {
    const res = await getTeaStatistics();
    statisticsData.value = res as OliTeaStatisticsVO;
    total.value =
        Number(res.enterpriseCount) +
        Number(res.cooperativeCount) +
        Number(res.planterCount) +
        Number(res.farmerCount);
};
const teaSubjectData = ref<TeaSubjectVO>();
const getfetchTeaSubject = async () => {
    const res = await getTeaSubject();
    teaSubjectData.value = res as TeaSubjectVO;
};
const teaDeatilData = ref<TeaOriginListVO>();
const getPicUrl = (data) => {
    if (data) {
        let pic = JSON.parse(data);
        return pic.url;
    }
    return '';
};
const showChannelDialog = ref(false);
const teaData = ref<TeaOriginListVO>();
const showTeaDetailDialog = ref(false);

const handleCheckChannel = (data) => {
    teaData.value = data;
    showChannelDialog.value = true;
};
const handleCheckOrigin = async (orginName, id) => {
    const _orginName = orginName.split(',')[0];
    const res = await getTeaDetail({ teaOrigin: _orginName, id: id });
    teaDeatilData.value = res as TeaOriginListVO;
    showTeaDetailDialog.value = true;
};

getfetchTeaOriginList();
getfetchTeaStatistics();
getfetchTeaSubject();
</script>

<template>
    <div>
        <BackButton />
        <WelcomeLayout title="信用+绿茶经济" imgSrc="welcome/home_tea_bg.jpg" :isLeft="true">
            <custom-tabs active-name="白沙茶叶简介" class="mt-[0px]">
                <template #default>
                    <el-tab-pane label="白沙茶叶简介" name="白沙茶叶简介" />
                </template>
            </custom-tabs>
            <div class="border-box">
                <div class="flex flex-col justify-center items-center module__content">
                    <div class="flex justify-center items-center py-[30px]">
                        <h3 style="color: #000">白沙好茶，山水共酿，品味自然醇香</h3>
                    </div>
                    <div class="indent-8 text-[#666666] px-[80px] leading-[32px]">
                        山水黎乡，茶脉绵长。据白沙县志记载，当地于20世纪50年代中期开始垦荒种茶，20世纪60年代起闻名遐迩。白沙的茶叶种植面积占全省三分之一，有机茶园面积达三千多亩，同时，白沙茶树省级林木种质资源库是省内建设较规范、资源多样性居领先水平的茶树活体“基因库”，古茶树资源丰富。近年来，白沙
                        “中国早春茶之乡”“中国生态茶叶之乡”的名片持续擦亮。白沙茶，已经名副其实成为助力乡村振兴的“金叶子”。
                    </div>

                    <div class="mt-[30px]">
                        <h3 class="text-[#529B2E] text-[20px]">产地码 —— 扫码知产地，品质更放心</h3>
                    </div>
                    <div class="text-[#666666] px-[80px] leading-[32px] mt-[20px]">
                        茶叶产品赋码，通过对茶叶产地信息录入、传递、追湖，消费者只需扫一扫茶叶产品上的二维码，就能查看茶叶生产地，确认产品信息的真实性。
                    </div>
                </div>
            </div>

            <div>
                <!--   <template #default> -->
                <!--   <el-tab-pane label="茶园简介" name="茶园简介"> -->
                <div class="flex flex-row gap-[40px] justify-center items-center py-[50px]">
                    <div
                        v-for="data in [
                            {
                                title: '白沙绿茶',
                                image: getAssetsFileUrl('welcome/picture01.jpg'),
                                description:
                                    '2004年10月，国家质检总局批准对“白沙绿茶”实施地理标志产品保护。白沙绿茶产自白沙陨石坑境内，白沙陨石坑独特的土壤条件，以及陨石坑境内优良的生态环境，雨量充沛，常年雾气缭绕，加上近处无重工业，又远离城市，无污染，是高山云雾茶生长的最佳地域，为茶树的生长创造了优越的自然条件，造就了品质优良的白沙绿茶。经科学测定，陨石坑内的土壤含有矿物类达50多种，营养丰富，成就了白沙绿茶的不可复制性和稀有性。',
                            },
                            {
                                title: '白沙红茶',
                                image: getAssetsFileUrl('welcome/picture02.jpg'),
                                description:
                                    '白沙红茶的原材料产自地理环境优越的白沙陨石坑一带，自上市以来凭借其醇厚的口感和香气，迅速赢得市场，成为与白沙绿茶并肩的新星，受到广大茶爱好者的热烈追捧。白沙红茶的独特之处不仅在于风味，其健康功效也值得一提。它含有丰富的酚类物质，具有抗氧化、降低血脂、抑制动脉硬化、增强毛细血管功能以及抗突变的特性。此外，红茶中的脂多糖有助于降低血糖，其杀菌和消炎作用也是一大亮点。2023年，薄沙牌·白沙红茶（品香红）在中国茶叶流通协会举办的“宜红杯”红茶产品质量对标活动中斩获“特级产品”奖。',
                            },
                        ]"
                        :key="data.title"
                        class="w-[50%]"
                    >
                        <div class="border-box">
                            <div
                                class="flex flex-col justify-center items-center module__content_bottom"
                            >
                                <div class="custom-item overflow-hidden">
                                    <div
                                        class="text-center font-bold text-[20px] pb-[20px] text-[#529B2E]"
                                    >
                                        {{ data.title }}
                                    </div>
                                    <div class="flex flex-col w-full h-full">
                                        <img
                                            :src="data.image"
                                            class="w-full h-[280px] object-cover rounded-lg"
                                        />
                                        <div class="flex-1 overflow-hidden">
                                            <el-scrollbar
                                                height="250px"
                                                class="pr-[10px] pl-[10px] mt-[20px]"
                                            >
                                                <div
                                                    class="indent-8 leading-[32px] pb-[20px] text-[#666666]"
                                                >
                                                    {{ data.description }}
                                                </div>
                                            </el-scrollbar>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- </el-tab-pane> -->
                <!--  </template> -->
            </div>

            <div class="mt-[80px] w-full">
                <StatisticsPanel
                    :cover-img="getAssetsFileUrl('welcome/image02.png')"
                    :cover-img-height="'350px'"
                    :item-padding="'340px'"
                    :items="[
                        {
                            value: total || 0,
                            unit: '家',
                            title: '已入驻经营主体',
                        },
                        {
                            value: statisticsData?.enterpriseCount || 0,
                            unit: '家',
                            title: '入驻企业',
                        },
                        {
                            value: statisticsData?.cooperativeCount || 0,
                            unit: '家',
                            title: '入驻合作社',
                        },
                        {
                            value: statisticsData?.planterCount || 0,
                            unit: '家',
                            title: '入驻个体工商户',
                        },
                        {
                            value: statisticsData?.farmerCount || 0,
                            unit: '家',
                            title: '入驻农户',
                        },
                    ]"
                />
            </div>
            <div class="mt-[30px]">
                <div class="statistics-panel">
                    <div class="statistics-container">
                        <div class="statistics-item">
                            <div class="statistics-value">
                                {{ teaSubjectData?.originCodeApplyCount || 0 }}
                                <text class="statistics-unit">家</text>
                            </div>
                            <div class="statistics-title">产地码申领主体量</div>
                        </div>
                        <div class="statistics-item">
                            <div class="statistics-value">
                                {{ teaSubjectData?.originCodeApplyNum || 0 }}
                                <text class="statistics-unit">家</text>
                            </div>
                            <div class="statistics-title">产地码申领量</div>
                        </div>
                        <div class="statistics-item">
                            <div class="statistics-value">
                                {{ teaSubjectData?.originCodeViewCount || 0 }}
                                <text class="statistics-unit">次</text>
                            </div>

                            <div class="statistics-title">产地码查看量</div>
                        </div>
                    </div>
                </div>
            </div>

            <custom-tabs active-name="茶企简介" class="mt-[60px]">
                <template #default>
                    <el-tab-pane label="茶企简介" name="茶企简介">
                        <CarouselPanel :items="teaOriginList" :display-count="3">
                            <template #item="{ data }: any">
                                <BorderBox>
                                    <div
                                        v-if="data"
                                        class="custom-item w-full overflow-hidden h-[530px]"
                                    >
                                        <div class="flex flex-col w-full h-full overflow-hidden">
                                            <div class="flex">
                                                <div class="w-[130px] h-[130px] select-none">
                                                    <el-image
                                                        :src="
                                                            getPicUrl(data.avatar) || defaultAvatar
                                                        "
                                                        class="w-[130px] h-[130px] rounded-full"
                                                    />
                                                </div>
                                                <div class="flex flex-col pl-[20px]">
                                                    <h4
                                                        class="text-xl font-bold text-[black] h-[60px] border-[none]"
                                                    >
                                                        {{ data.subjectName }}
                                                    </h4>
                                                    <div class="flex">
                                                        <div class="mt-[4px] mr-[5px]">
                                                            <el-icon><TakeawayBox /></el-icon>
                                                        </div>
                                                        主体类型:
                                                        {{ getSubjectTypeLabel(data.subjectType) }}
                                                    </div>
                                                    <div class="flex">
                                                        <div class="mt-[4px] mr-[5px]">
                                                            <el-icon><Phone /></el-icon>
                                                        </div>
                                                        联系电话:
                                                        {{ data.telephone }}
                                                    </div>
                                                    <div class="flex">
                                                        <div class="mt-[4px] mr-[5px]">
                                                            <el-icon><AddLocation /></el-icon>
                                                        </div>
                                                        经营地址:
                                                        {{ data.subjectAddress }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div
                                                class="flex flex-col w-full h-full overflow-hidden"
                                            >
                                                <div
                                                    class="pt-4 text-[16px] color-[#666666] gap-2 flex flex-col overflow-hidden"
                                                >
                                                    <div class="flex-1 overflow-hidden">
                                                        <el-scrollbar
                                                            height="100%"
                                                            class="pr-[10px] pl-[10px]"
                                                        >
                                                            <div
                                                                class="mb-2 mt-4 font-bold text-lg text-[#222222] flex items-center"
                                                            >
                                                                <img
                                                                    class="mr-1"
                                                                    :src="
                                                                        getAssetsFileUrl(
                                                                            'welcome/tea_introduction.png',
                                                                        )
                                                                    "
                                                                />
                                                                茶企简介
                                                            </div>
                                                            <div class="text-gray-600 indent">
                                                                {{ data.introduction || '-' }}
                                                            </div>
                                                            <div
                                                                class="mb-2 mt-4 font-bold text-lg text-[#222222] flex items-center"
                                                            >
                                                                <img
                                                                    class="mr-1"
                                                                    :src="
                                                                        getAssetsFileUrl(
                                                                            'welcome/tea_introduction.png',
                                                                        )
                                                                    "
                                                                />
                                                                茶叶产地
                                                            </div>
                                                            <div
                                                                style="
                                                                    color: #409eff;
                                                                    cursor: pointer;
                                                                "
                                                                link
                                                                type="primary"
                                                            >
                                                                <template
                                                                    v-for="line in data.teaOrigin
                                                                        ? data.teaOrigin.split(',')
                                                                        : []"
                                                                    :key="line"
                                                                >
                                                                    <div
                                                                        class="indent"
                                                                        @click="
                                                                            handleCheckOrigin(
                                                                                line,
                                                                                data.id,
                                                                            )
                                                                        "
                                                                    >
                                                                        {{ line }}
                                                                    </div>
                                                                </template>
                                                                <div
                                                                    v-if="!data.teaOrigin"
                                                                    class="text-gray-600 indent"
                                                                >
                                                                    -
                                                                </div>
                                                            </div>
                                                            <div
                                                                class="mb-2 mt-4 font-bold text-lg text-[#222222] flex items-center"
                                                            >
                                                                <img
                                                                    class="mr-1"
                                                                    :src="
                                                                        getAssetsFileUrl(
                                                                            'welcome/tea_introduction.png',
                                                                        )
                                                                    "
                                                                />
                                                                经营茶叶品种
                                                            </div>
                                                            <div class="indent text-gray-600">
                                                                {{ data.teaType || '-' }}
                                                            </div>
                                                        </el-scrollbar>
                                                    </div>
                                                </div>
                                            </div>

                                            <div
                                                v-if="
                                                    data.purchaseLink ||
                                                    (data.purchaseQrcode != '[]' &&
                                                        data.purchaseQrcode)
                                                "
                                                class="m-auto flex items-center justify-center cursor-pointer"
                                                @click="handleCheckChannel(data)"
                                            >
                                                <img
                                                    class="w-[30px] h-[30px] mt-[10px]"
                                                    :src="getAssetsFileUrl('welcome/shopping.png')"
                                                />
                                                <span class="mt-[15px] ml-[5px] text-[#8FC31F]"
                                                    >购买渠道</span
                                                >
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else>
                                        <div
                                            class="text-center h-[580px] w-[320px] flex items-center text-center"
                                        >
                                            <div
                                                class="text-[#67C23A] w-full text-lg cursor-pointer"
                                            >
                                                查看更多
                                            </div>
                                        </div>
                                    </div>
                                </BorderBox>
                            </template>
                        </CarouselPanel>
                    </el-tab-pane>
                </template>
            </custom-tabs>
            <SaleChannelModal
                v-model="showChannelDialog"
                :saleChannel="teaData?.purchaseLink"
                :qrCodeUrl="teaData?.purchaseQrcode"
            />
            <el-dialog
                v-model="showTeaDetailDialog"
                center
                :title="'茶园信息'"
                width="430px"
                style="border-radius: 10px"
                class="dialog-bar-tea"
            >
                <BorderBox
                    ><div class="flex flex-col">
                        <div
                            v-if="teaDeatilData"
                            class="custom-item w-full overflow-hidden h-[600px]"
                        >
                            <div class="flex flex-col w-full h-full overflow-hidden">
                                <div class="h-[300px] overflow-hidden">
                                    <el-image
                                        :src="getPicUrl(teaDeatilData.originPic)"
                                        class="w-full h-full rounded-lg"
                                    />
                                </div>
                                <div class="flex flex-col w-full h-full overflow-hidden">
                                    <div
                                        class="pt-4 text-[16px] color-[#666666] gap-2 flex flex-col overflow-hidden"
                                    >
                                        <h3 class="text-xl font-bold text-[black] pl-[10px]">
                                            {{ teaDeatilData.teaEstate }}
                                        </h3>
                                        <div class="flex-1 overflow-hidden">
                                            <el-scrollbar height="100%" class="pr-[10px] pl-[10px]">
                                                <div class="flex">
                                                    <div class="mt-[4px] mr-[5px]">
                                                        <el-icon><TakeawayBox /></el-icon>
                                                    </div>
                                                    产地名称:
                                                    {{ teaDeatilData.originName }}
                                                </div>
                                                <div class="flex">
                                                    <div class="mt-[4px] mr-[5px]">
                                                        <el-icon><Phone /></el-icon>
                                                    </div>
                                                    茶园电话:
                                                    {{ teaDeatilData.contactPhone || '-' }}
                                                </div>
                                                <div class="flex">
                                                    <div class="mt-[4px] mr-[5px]">
                                                        <el-icon><AddLocation /></el-icon>
                                                    </div>
                                                    茶园地址:
                                                    {{ teaDeatilData.originAddress }}
                                                </div>
                                                <div class="flex">
                                                    <div class="mt-[4px] mr-[5px]">
                                                        <el-icon
                                                            ><el-image
                                                                :src="
                                                                    getAssetsFileUrl(
                                                                        'welcome/teaArea.png',
                                                                    )
                                                                "
                                                        /></el-icon>
                                                    </div>
                                                    茶园面积:
                                                    {{
                                                        teaDeatilData.estateArea == '0'
                                                            ? '--'
                                                            : teaDeatilData.estateArea || '--'
                                                    }}亩
                                                </div>
                                                <div class="mb-2 flex">
                                                    <div class="mt-[4px] mr-[5px]">
                                                        <el-icon
                                                            ><el-image
                                                                :src="
                                                                    getAssetsFileUrl(
                                                                        'welcome/teaType.png',
                                                                    )
                                                                "
                                                        /></el-icon>
                                                    </div>
                                                    种植品种:
                                                    {{ teaDeatilData.teaType }}
                                                </div>
                                                <div
                                                    class="mb-2 mt-4 font-bold text-lg text-[#222222] flex items-center"
                                                >
                                                    <img
                                                        class="mr-1"
                                                        :src="
                                                            getAssetsFileUrl(
                                                                'welcome/tea_introduction.png',
                                                            )
                                                        "
                                                    />
                                                    茶园简介
                                                </div>
                                                <div class="text-gray-600 indent">
                                                    {{ teaDeatilData.originIntroduction }}
                                                </div>
                                            </el-scrollbar>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dialog-footer mt-[20px]">
                            <el-button @click="showTeaDetailDialog = false">关闭</el-button>
                        </div>
                    </div>
                </BorderBox>
            </el-dialog>
        </WelcomeLayout>
    </div>
</template>
<style lang="scss">
:deep(.financial-zone) {
    .credit-module__content {
        padding: 0;
        overflow: hidden;
    }
}

:deep(.el-dialog__headerbtn) {
    .el-icon svg {
        height: 1.5em;
        width: 1.5em;
    }
}

.sports-section {
    padding: 60px 0;
    background-color: #f0f9eb;
    padding: 40px;
}

.sports-title {
    text-align: center;
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 10px;
}

.sports-title-line {
    width: 100px;
    height: 4px;
    background-color: #67c23a;
    margin: 0 auto;
    margin-top: 20px;
    margin-bottom: 40px;
}

.sports-subtitle {
    text-align: left;
    font-weight: bold;
    color: #529b2e;
    font-size: 24px;
    margin-bottom: 40px;
}

.sports-content {
    display: flex;
    margin: 40px auto;
}

.sports-text {
    flex: 1;
    color: #666666;
    p {
        margin-bottom: 20px;
        line-height: 1.8;
        text-indent: 2em;
    }
}

.sports-card {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    padding: 20px;
    gap: 30px;
    display: flex;
    flex-direction: row;
    width: 100%;
}

.credit-level {
    .level-item {
        margin-bottom: 30px;

        &:last-child {
            border-bottom: none;
        }
    }

    .level-title {
        font-weight: bold;
        color: #303133;
        margin-bottom: 10px;
    }

    .level-benefit {
        color: #67c23a;
        margin-bottom: 10px;
        margin-left: 20px;
    }

    .level-desc {
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 10px;
    }

    .level-time {
        color: #909399;
        font-size: 12px;
        margin-bottom: 5px;
    }

    .level-expire {
        color: #529b2e;
        font-size: 12px;
    }
}

.statistics-panel {
    background: #fff;
    border-radius: 16px;
}

.statistics-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.statistics-item {
    text-align: center;
    padding: 40px 20px;
    background: rgba(240, 249, 235, 0.9);
    border-radius: 5px;
    width: 31%;
}

.statistics-value {
    font-size: 32px;
    color: #67c23a;
    line-height: 1.2;
}

.statistics-unit {
    font-size: 16px;
    color: #67c23a;
    margin-top: 4px;
}

.statistics-title {
    font-size: 16px;
    color: #606266;
    margin-top: 12px;
}
.border-box {
    padding: 1px;
    border-radius: 8px;
    background-image: linear-gradient(180deg, rgba(225, 243, 216, 0.2) -9%, #b3e09c 65%);

    color: #666666;

    h2 {
        color: #529b2e;
    }

    .module__content {
        background: linear-gradient(to bottom, #f5f9f1 33%, #ffffff 80%);
        padding: 40px 30px;
        border-radius: 8px;
    }
    .module__content_bottom {
        background: linear-gradient(0deg, #fafcf8 33%, #ffffff 60%);
        padding: 40px 30px;
        border-radius: 8px;
    }
}
.rounded-full {
    border-radius: 50%;
}
.dialog-bar-tea {
    .el-dialog__body {
        padding: 0px !important;
    }
    .el-dialog__header {
        border-bottom: none !important;
        margin-left: 30px;
    }
}
.indent {
    text-indent: 2em; /* 首行缩进 2em */
}
</style>
