<script setup lang="ts">
import { AddLocation, Phone, TakeawayBox } from '@element-plus/icons-vue';
import { nextTick, ref } from 'vue';

/* import { PolicyList } from '@/api/welcome'; */
import {
    getOliTeaList,
    getOliTeaPolicy,
    getOliTeaStatistics,
    getOliTeaSubsidySubject,
} from '@/api/welcome/olitea';
import {
    FinancialPolicyDO,
    OliTeaStatisticsVO,
    OliTeaSubsidySubjectListVO,
    TeaGardenIntroductionListVO,
} from '@/api/welcome/type';
import BackButton from '@/components/welcomeZone/BackButton.vue';
import BorderBox from '@/components/welcomeZone/BorderBox.vue';
import CarouselPanel from '@/components/welcomeZone/CarouselPanel.vue';
import CreditBorderBox from '@/components/welcomeZone/CreditBorderBox.vue';
import CreditDynamicsScroll from '@/components/welcomeZone/creditDynamicsScroll.vue';
import CustomTabs from '@/components/welcomeZone/customTabs.vue';
import StatisticsPanel from '@/components/welcomeZone/StatisticsPanel.vue';
import WelcomeLayout from '@/components/welcomeZone/welcomeLayout.vue';
import router from '@/router';
import { getAssetsFileUrl } from '@/utils/file';
import AutoTable from '@/views/overviews/components/AutoTable/index.vue';
const initData = async () => {
    nextTick(() => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth',
        });
    });
    /*  fetchOliTeaList(); */
};
initData();

/**简介列表 */
const teaGardenIntroductionList = ref<TeaGardenIntroductionListVO[]>([]);
const getfetchOliTeaList = async () => {
    const res = await getOliTeaList();
    teaGardenIntroductionList.value = res as any;
};
/**油茶主体分类统计 */
const total = ref(0);
const statisticsData = ref<OliTeaStatisticsVO>();
const getfetchOliTeaStatistics = async () => {
    const res = await getOliTeaStatistics();
    statisticsData.value = res as OliTeaStatisticsVO;
    total.value = res.enterpriseCount + res.cooperativeCount + res.planterCount + res.farmerCount;
};

const currentSubpageIndex = ref(0);

const handleSubpageClick = (index: number) => {
    currentSubpageIndex.value = index;
};

const creditSupportArr = ref([]);

const getPicUrl = (data) => {
    let pic = JSON.parse(data);
    return pic.url;
};

const jumpNotice = (item: FinancialPolicyDO, type: string) => {
    console.log(item, type);
    console.log(router.getRoutes());
    type == '0'
        ? router.push('/welcome/notice' + '?id=' + item.id)
        : router.push('/welcome/supportNotice' + '?id=' + item.id + '&policy=3');
};
/**补贴政策列表 */
const policyList = ref<any[]>();
const getfetchOliTeaPolicy = async () => {
    try {
        const res = await getOliTeaPolicy();
        policyList.value = res.map((item) => ({
            id: item.id,
            name: item.policyName,
            createTime: item.updateTime,
            content: item.policyContent,
        }));
    } catch (e) {
        console.log(e);
    }
};

const subsidyList = ref<OliTeaSubsidySubjectListVO[]>([]);
const getfetchOliTeaSubsidySubject = async () => {
    try {
        const res = await getOliTeaSubsidySubject();
        res.forEach((item) => {
            if (item.planterName) {
                if (item.planterName.length > 2) {
                    item.planterName = item.planterName[0] + '*' + item.planterName.slice(2);
                }
            } else {
                item.planterName = '-';
            }
            item.survivalRate = item.survivalRate ? item.survivalRate + '%' : '-';
            item.subsidyAmount = item.subsidyAmount ? item.subsidyAmount.toLocaleString() : '-';
            item.township = item.township ? item.township : '-';
            item.plantArea = item.plantArea ? item.plantArea : '-';
            item.plantCount = item.plantCount ? item.plantCount : '-';
            item.checkStatus = item.checkStatus ? item.checkStatus : '-';
            item.remark = item.remark ? item.remark : '-';
        });
        subsidyList.value = res;
    } catch (e) {
        console.log(e);
    }
};
getfetchOliTeaPolicy();
getfetchOliTeaList();
getfetchOliTeaStatistics();
getfetchOliTeaSubsidySubject();
</script>

<template>
    <div>
        <BackButton />
        <WelcomeLayout title="信用+油茶产业" imgSrc="welcome/home_oilTea_bg.png">
            <custom-tabs active-name="油茶简介" class="mt-[0px]">
                <template #default>
                    <el-tab-pane label="油茶简介" name="油茶简介" />
                </template>
            </custom-tabs>
            <div class="border-box">
                <div class="flex flex-col justify-center items-center module__content">
                    <div class="flex justify-center items-center py-[30px">
                        <h3 style="color: #000; margin-bottom: 20px">
                            白沙油茶，健康之选；产业兴农，共赢未来
                        </h3>
                    </div>
                    <div class="text-[#666666] px-[80px] leading-[32px]">
                        山茶油富含不饱和脂肪酸、维生素E和多种天然活性物质，具有调节血脂、保护心脑血管、抗氧化和增强免疫力等保健功效，是绿色健康的高端食用油，深受市场青睐。白沙油茶作为地方特色优势产业，种植规模不断扩大，产品质量持续提升。政府大力支持油茶产业发展，通过政策扶持和产业规划，推动油茶品牌化、规模化，助力农民增收和区域经济转型。
                    </div>

                    <div class="mt-[40px]">
                        <img
                            class="w-[900px]"
                            :src="getAssetsFileUrl('welcome/image05.png')"
                            alt=""
                        />
                    </div>
                </div>
            </div>

            <custom-tabs active-name="油茶园简介" class="mt-[60px]">
                <template #default>
                    <el-tab-pane label="油茶园简介" name="油茶园简介">
                        <CarouselPanel :items="teaGardenIntroductionList" :display-count="3">
                            <template #item="{ data }">
                                <BorderBox>
                                    <div
                                        v-if="data"
                                        class="custom-item w-full overflow-hidden h-[500px]"
                                    >
                                        <div class="flex flex-col w-full h-full overflow-hidden">
                                            <div class="h-[300px] overflow-hidden">
                                                <el-image
                                                    :src="getPicUrl(data.estatePictureListJson)"
                                                    fit="cover"
                                                    class="w-full h-full rounded-lg"
                                                />
                                            </div>
                                            <div
                                                class="flex flex-col w-full h-full overflow-hidden"
                                            >
                                                <div
                                                    class="pt-4 text-sm color-[#666666] gap-2 flex flex-col overflow-hidden"
                                                >
                                                    <h3
                                                        class="text-xl font-bold mb-4 text-[black] pl-[10px]"
                                                    >
                                                        {{ data.estateName }}
                                                    </h3>
                                                    <div class="flex-1 overflow-hidden">
                                                        <el-scrollbar
                                                            height="100%"
                                                            class="pr-[10px] pl-[10px]"
                                                        >
                                                            <div class="flex">
                                                                <div class="mt-[2px] mr-[5px]">
                                                                    <el-icon
                                                                        ><TakeawayBox
                                                                    /></el-icon>
                                                                </div>
                                                                种植品种:
                                                                {{ data.plantType || '--' }}
                                                            </div>
                                                            <div class="flex">
                                                                <div class="mt-[2px] mr-[5px]">
                                                                    <el-icon
                                                                        ><TakeawayBox
                                                                    /></el-icon>
                                                                </div>
                                                                茶园面积:
                                                                {{
                                                                    data.estateArea == 0
                                                                        ? '--'
                                                                        : data.estateArea
                                                                }}亩
                                                            </div>

                                                            <div class="flex">
                                                                <div class="mt-[2px] mr-[5px]">
                                                                    <el-icon><Phone /></el-icon>
                                                                </div>
                                                                联系电话:
                                                                {{ data.contactPhone || '--' }}
                                                            </div>
                                                            <div class="mb-2 flex">
                                                                <div class="mt-[2px] mr-[5px]">
                                                                    <el-icon
                                                                        ><AddLocation
                                                                    /></el-icon>
                                                                </div>
                                                                经营地址:
                                                                {{ data.estateAddress }}
                                                            </div>
                                                            <div
                                                                class="mb-2 mt-4 font-bold text-lg text-[#222222] flex items-center"
                                                            >
                                                                <img
                                                                    class="mr-1"
                                                                    :src="
                                                                        getAssetsFileUrl(
                                                                            'welcome/tea_introduction.png',
                                                                        )
                                                                    "
                                                                />
                                                                商户简介
                                                            </div>
                                                            <div
                                                                class="text-gray-600"
                                                                style="line-height: 22px"
                                                            >
                                                                {{ data.estateIntroduction }}
                                                            </div>
                                                        </el-scrollbar>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else>
                                        <div
                                            class="text-center h-[580px] w-[320px] flex items-center text-center"
                                        >
                                            <div
                                                class="text-[#67C23A] w-full text-lg cursor-pointer"
                                            >
                                                查看更多
                                            </div>
                                        </div>
                                    </div>
                                </BorderBox>
                            </template>
                        </CarouselPanel>
                    </el-tab-pane>
                </template>
            </custom-tabs>

            <div class="mt-[180px]">
                <StatisticsPanel
                    :cover-img="getAssetsFileUrl('welcome/image02.png')"
                    :cover-img-height="'350px'"
                    :item-padding="'340px'"
                    :items="[
                        {
                            value: total || 0,
                            unit: '家',
                            title: '经营主体',
                        },
                        {
                            value: statisticsData?.enterpriseCount || 0,
                            unit: '家',
                            title: '企业',
                        },
                        {
                            value: statisticsData?.cooperativeCount || 0,
                            unit: '家',
                            title: '合作社',
                        },
                        {
                            value: statisticsData?.planterCount || 0,
                            unit: '个',
                            title: '基层组织',
                        },
                        {
                            value: statisticsData?.farmerCount || 0,
                            unit: '家',
                            title: '农户',
                        },
                    ]"
                />
            </div>

            <custom-tabs active-name="补贴政策" class="mt-[60px]">
                <template #default>
                    <el-tab-pane label="补贴政策" name="补贴政策">
                        <CreditBorderBox src="image/index/supportEnterprises_bg.jpg">
                            <CreditDynamicsScroll
                                :data="policyList"
                                @clickItem="jumpNotice($event, '1')"
                            />
                        </CreditBorderBox>
                    </el-tab-pane>
                </template>
            </custom-tabs>

            <custom-tabs active-name="补贴落实公示" class="mt-[60px]">
                <template #default>
                    <el-tab-pane label="补贴落实公示" name="补贴落实公示">
                        <CreditBorderBox cover-position="right">
                            <!-- <CreditBorderBox cover-position="right" src="welcome/picture04.jpg"> -->
                            <div class="finance-data-table">
                                <AutoTable
                                    :showHeader="true"
                                    :data="subsidyList"
                                    :columns="[
                                        { prop: 'township', label: '乡镇' },
                                        {
                                            prop: 'planterName',
                                            label: '种植户名称',
                                            width: 120,
                                        },
                                        {
                                            prop: 'plantArea',
                                            label: '种植面积',
                                        },
                                        {
                                            prop: 'plantCount',
                                            label: '种植株树',
                                        },
                                        {
                                            prop: 'survivalRate',
                                            label: '存活率',
                                        },
                                        {
                                            prop: 'checkStatus',
                                            label: '检查情况',
                                        },
                                        {
                                            prop: 'subsidyAmount',
                                            label: '补贴金额（元）',
                                            width: 130,
                                        },
                                        {
                                            prop: 'remark',
                                            label: '备注',
                                            width: 130,
                                        },
                                    ]"
                                />
                            </div>
                        </CreditBorderBox>
                    </el-tab-pane>
                </template>
            </custom-tabs>
        </WelcomeLayout>
    </div>
</template>
<style scoped lang="scss">
:deep(.financial-zone) {
    .credit-module__content {
        padding: 0;
        overflow: hidden;
    }
}
.sports-section {
    padding: 60px 0;
    background-color: #f0f9eb;
    padding: 40px;
}

.sports-title {
    text-align: center;
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 10px;
}

.sports-title-line {
    width: 100px;
    height: 4px;
    background-color: #67c23a;
    margin: 0 auto;
    margin-top: 20px;
    margin-bottom: 40px;
}

.sports-subtitle {
    text-align: left;
    font-weight: bold;
    color: #529b2e;
    font-size: 24px;
    margin-bottom: 40px;
}

.sports-content {
    display: flex;
    margin: 40px auto;
}

.sports-text {
    flex: 1;
    color: #666666;
    p {
        margin-bottom: 20px;
        line-height: 1.8;
        text-indent: 2em;
    }
}

.sports-card {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    padding: 20px;
    gap: 30px;
    display: flex;
    flex-direction: row;
    width: 100%;
}

.credit-level {
    .level-item {
        margin-bottom: 30px;

        &:last-child {
            border-bottom: none;
        }
    }

    .level-title {
        font-weight: bold;
        color: #303133;
        margin-bottom: 10px;
    }

    .level-benefit {
        color: #67c23a;
        margin-bottom: 10px;
        margin-left: 20px;
    }

    .level-desc {
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 10px;
    }

    .level-time {
        color: #909399;
        font-size: 12px;
        margin-bottom: 5px;
    }

    .level-expire {
        color: #529b2e;
        font-size: 12px;
    }
}
.finance-data-table {
    width: 98%;
    height: 460px;
    border: 1px solid #ebeef5;

    :deep(.el-table) {
        height: 100%;

        .el-table__body-wrapper {
            height: calc(100% - 40px) !important;
            overflow-y: hidden !important;
        }

        --el-table-border-color: #ebeef5;
        --el-table-header-bg-color: #ebeef5;

        th {
            background-color: #ebeef5;
            color: #909399;
            font-weight: 500;
            padding: 12px 0;
        }

        td {
            padding: 12px 0;
        }
    }
}
.border-box {
    padding: 1px;
    border-radius: 8px;
    background-image: linear-gradient(180deg, rgba(225, 243, 216, 0.2) -9%, #b3e09c 65%);

    color: #666666;

    h2 {
        color: #529b2e;
    }

    .module__content {
        background: linear-gradient(to bottom, #f7f9f5 33%, #ffffff 80%);
        padding: 40px 30px;
        border-radius: 8px;
    }
}
:deep(.el-table__empty-text) {
    margin-top: 170px;
}
</style>
