<script lang="ts">
export default {
    name: 'welcomeIndex',
};
</script>
<script setup lang="ts">
import { storageLocal } from '@pureadmin/utils';
import { nextTick, ref } from 'vue';
import { useRouter } from 'vue-router';

import { getWelcomePagePolicyList, PolicyList } from '@/api/welcome';
/* import { FinancialPolicyDO, FinancialProductDO } from '@/api/welcome/type'; */
// import BackButton from '@/components/welcomeZone/BackButton.vue';
import CreditBorderBox from '@/components/welcomeZone/CreditBorderBox.vue';
import CreditDynamicsScroll from '@/components/welcomeZone/creditDynamicsScroll.vue';
import CustomTabs from '@/components/welcomeZone/customTabs.vue';
import WelcomeLayout from '@/components/welcomeZone/welcomeLayout.vue';
import ZoneIntro from '@/components/welcomeZone/ZoneIntro.vue';
import { TokenKey } from '@/utils/auth';
import { getAssetsFileUrl } from '@/utils/file';

const router = useRouter();

/** 金融政策 */
const creditSupportArr = ref<any[]>([]);
const financingProductArr = ref<any[]>([]);

/**
 * 获取金融政策列表
 */
const getPolicyList = async (type?: string) => {
    /* if (type === '金融政策') { */
    const res = await getWelcomePagePolicyList();
    creditSupportArr.value = res.map((item) => ({
        id: item.id,
        name: item.policyName,
        createTime: item.updateTime,
        content: item.policyContent,
    }));
    /* } else if (type === '金融产品') {
        const res = await getFinancingProductList();
        financingProductArr.value = res.map((item) => ({
            id: item.id,
            name: item.fileName,
            createTime: item.createTime,
            content: item.fileContent,
        }));
    } */
};

const initData = async () => {
    nextTick(() => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth',
        });
    });
    await getPolicyList();
};
initData();
// 去除html标签
const stripHtmlTags = (str) => {
    if (str === null || str === '') {
        return false;
    } else {
        str = str.toString();
        return str.replace(/<[^>]*>/g, '');
    }
};

// 公告描述处理
const processedAnnouncementDescribe = (str: string) => {
    let decodedString = decodeURIComponent(str);
    let result = stripHtmlTags(decodedString);
    return result.length > 44 ? result.substring(0, 44) + '…' : result;
};

const openAdmin = () => {
    // const menus = usePermissionStoreHook().wholeMenus;
    const token = storageLocal().getItem(TokenKey);
    // 判断用户登录态，选择直接进入or登录
    /* if (menus.length) {
        router.push(menus[1].path);
    } else {
        router.push('/login');
    }*/
    if (token) {
        router.push('/overviews');
    } else {
        router.push('/login');
    }
};

/** 详情页面类型 */
enum DetailTypeEnum {
    Notice = '0',
    SupportNotice = '1',
}

/** 跳转详情页面 */
const jumpNotice = (item: PolicyList, type: DetailTypeEnum) => {
    console.log(item, type);
    console.log(router.getRoutes());
    type === DetailTypeEnum.Notice
        ? router.push('/welcome/notice' + '?id=' + item.id)
        : router.push('/welcome/supportNotice' + '?id=' + item.id);
};

/** 标签页名称 */
const tabPane = ref<string[]>(['金融产品', '金融政策']);

const goback = () => {
    router.back();
};

const activeTab = ref('金融产品');
const getPicture = (type?: string) => {
    if (type === '金融政策') {
        return 'image/index/supportEnterprises_bg.jpg';
    } else {
        return 'image/index/picture05.jpg';
    }
};
// 添加 tab 切换处理函数
const handleTabChange = (tab: string) => {
    activeTab.value = tab;
    /*   getPolicyList(tab); */
};
/* getPolicyList(activeTab.value); */
</script>

<template>
    <div>
        <WelcomeLayout
            title="诚信白沙服务专区"
            imgSrc="welcome/home_bg_head.jpg"
            background="welcome/home_bg_body.png"
        >
            <custom-tabs active-name="诚信苗木超市" class="mt-[0px]">
                <template #default>
                    <el-tab-pane label="信用+苗木超市" name="诚信苗木超市">
                        <ZoneIntro
                            title="白沙苗木，信用护航，优质保障"
                            button-text="进入“信用+苗木超市”"
                            cover-position="left"
                            src="welcome/home_seeding.png"
                            :path="'/welcome/seedling'"
                        >
                            <p>
                                白沙苗木超市位于海南白沙黎族自治县荣邦乡大岭居，依托海垦龙江大岭热带水果精品苗木培育产业园而建，是当地苗木产业的重要展示和销售平台，苗木超市整合了250余户经营主体生产经营苗木，占地面积达2500亩，形成了较大的产业规模，种植的苗木种类繁多，由单一的橡胶品种发展到芒果、菠萝蜜、牛油果、释迦果、莲雾、榴莲等40多个种类。目前苗木销量较为可观，外地扩展远销广东、广西等地，甚至辐射到越南、缅甸、老挝等东南亚国家。
                            </p>
                            <p>
                                建设白沙诚信苗木超市专区，为守信经营主体提供商品以及信用展示窗口，推动打造成为标准高、品质佳、品牌响的信用展示平台，并且通过"一户一码""一苗一码"向消费者展示经营主体信用情况，塑造“白沙金苗”信用名片，形成白沙苗木市场守信有益的价值导向。
                            </p>
                        </ZoneIntro>
                    </el-tab-pane>
                </template>
            </custom-tabs>

            <custom-tabs active-name="诚信油茶" class="mt-[60px]">
                <template #default>
                    <el-tab-pane label="信用+油茶产业" name="诚信油茶">
                        <ZoneIntro
                            title="白沙油茶，健康之选；产业兴农，共赢未来"
                            button-text="进入“信用+油茶产业”"
                            cover-position="right"
                            src="welcome/home_oilTea.png"
                            :path="'/welcome/oilTea'"
                        >
                            <p>
                                白沙山茶油距今已有千年历史，被称为"液体黄金"、"东方橄榄油"，以其优越地理环境、优质品质、先进工艺、丰富的营养价值和健康功效在市场上享有较高的知名度和美誉度。山茶油富含茶多酚、不饱和脂肪酸等珍贵成分，具有降脂护心、美容抗氧化、增强免疫力等多重健康功效。
                            </p>
                            <p>
                                白沙依托优越的自然条件，大力发展油茶种植，面积已超1万亩，主要种植海南本地山油茶及万海、海科大、琼东等优质嫁接苗。白沙加工厂通过低温物理冷榨工艺，每年生产300吨高品质茶油，每小时可生产1000至1200瓶，并对油茶果进行晾晒、分拣、深加工，大幅提升产品附加值。同时，白沙县还积极推动油茶副产品的销售和品牌建设，通过线上线下渠道将油茶产品推向市场。
                            </p>
                        </ZoneIntro>
                    </el-tab-pane>
                </template>
            </custom-tabs>

            <custom-tabs active-name="诚信茶叶" class="mt-[60px]">
                <template #default>
                    <el-tab-pane label="信用+绿茶经济" name="诚信茶叶">
                        <ZoneIntro
                            title="白沙好茶，山水共酿，品味自然醇香"
                            button-text="进入“信用+绿茶经济”"
                            cover-position="left"
                            src="welcome/home_tea.png"
                            :path="'/welcome/tea'"
                        >
                            <p>
                                白沙茶叶种类丰富，包括早春茶、冬茶、有机茶、野生茶和古树茶等，茶叶类型从单一绿茶向红茶、白茶等多元化升级，逐步形成品牌矩阵。近年来，白沙黎族自治县的茶叶种植面积和产量持续增长，2024年全县茶园面积超1万亩，茶叶的年产值也相当可观，茶产业为当地地方经济注入强劲活力。白沙茶产业从种植和初加工逐步拓展至深加工、销售和科研领域，并通过“茶+旅游”等模式，与旅游、文化产业深度融合。依托产地源和质量监督，推动茶叶标准化、品牌化建设，不断提升产品安全和市场竞争力，为地方经济发展注入新动力。
                            </p>
                            <p>
                                建设白沙诚信茶叶专区，可推动白沙茶叶产地信息溯源，加强产品质量监督，提高茶叶产地监管效能，减少“外地茶冒充本地茶”、“以次充好”等现象发生，确保茶叶来源真实性和安全性。
                            </p>
                        </ZoneIntro>
                    </el-tab-pane>
                </template>
            </custom-tabs>

            <div class="mt-16">
                <div class="sports-section">
                    <h2 class="sports-title">信用+体育赛事</h2>
                    <div class="sports-title-line" />
                    <div class="px-[60px]">
                        <div class="sports-subtitle">活力白沙，邀您共享体育魅力</div>
                        <div class="sports-text">
                            <p>
                                国家体育训练南方基地(海南白沙·综合)成功承办了多场国家级、省级赛事，比如全国男子拳击冠军赛、杭州亚运会武术选拔赛、全国射箭冠军赛等一系列高水平赛事。赛事的举办吸引了大量游客和专业运动员前来观赛和参与，带动了当地旅游业发展。同时为了满足赛事和游客的需求，建设了五星级标准的白沙奥运村（运动员公寓），提供了优质的住宿和训练条件。
                            </p>

                            <p>
                                为更好让利于市民和游客，白沙创新引入"金椰分"信用体系，守信个人可享受<span
                                    class="!text-[#529B2E]"
                                    >优惠折扣</span
                                >等福利。登录"金椰分"平台并授权查询，即可查看个人"金椰分"分值。根据分值区间，您可以领取相应的便利服务和折扣优惠。白沙，运动与旅游融合的热土，精彩不断，诚邀您来参与!在白沙，动起来，玩起来!
                            </p>
                        </div>
                    </div>
                    <div>
                        <el-image
                            :src="getAssetsFileUrl('welcome/sports_event.png')"
                            class="w-full"
                        />
                    </div>
                    <div v-if="false" class="sports-content">
                        <div class="sports-card">
                            <div class="card-header w-[45%]">
                                <img
                                    class="pb-8"
                                    :src="getAssetsFileUrl('welcome/image01.png')"
                                    alt="体育图标"
                                />
                                <div
                                    class="p-6 flex flex-row bg-[#F0F9EB] items-center gap-2 rounded-lg"
                                >
                                    <img
                                        class="w-[100px] h-[100px] border-2 border-dashed border-[#eeeeee]"
                                        :src="getAssetsFileUrl('welcome/qrcode.png')"
                                        alt="体育图标"
                                    />
                                    <div class="">
                                        <h5>领取方式1：</h5>
                                        <p class="text-sm text-[#666666]">
                                            扫码录"金椰分平台"小程序→进入"金椰分"→"信用+体育"领取
                                        </p>

                                        <h5 class="mt-4">领取方式2：</h5>
                                        <p class="text-sm text-[#666666]">
                                            登录"海易办app"→进入"金椰分"→"信用+体育"领取
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="credit-level">
                                <h1 class="mb-8">白沙文化体育中心羽毛球馆</h1>

                                <div
                                    v-for="(item, index) in [
                                        {
                                            point: 1000,
                                            desc: '在场馆闲时时间段，金椰分在1000分及以上的自然人享受每月一次免费使用单个场地半小时。场馆闲时时间段：周一至周五上午9:00-12:00；周一至周五下午14:30-16:00；周六、周日上午9:00-11:00',
                                            benefit: '免费赠送',
                                            expire: '2025年6月30日',
                                        },
                                        {
                                            point: 1020,
                                            desc: '金椰分在1020分及以上的自然人可每月免费享受一次以下福利（3选1）：1.场地加时，在个人实际消费时长的基础上免费增加半小时；2.免费更换羽毛球手胶服务；3.免费兑换能量饮品1瓶。',
                                            benefit: '免费加时、兑换饮品、兑换更换手胶服务',
                                            expire: '2025年6月30日',
                                        },
                                        {
                                            point: 1030,
                                            desc: '金椰分在1030分及以上的自然人享受租赁单个场地费9折优惠。',
                                            benefit: '折扣优惠',
                                            expire: '2025年6月30日',
                                        },
                                    ]"
                                    :key="index"
                                    class="level-item"
                                >
                                    <div class="level-title">
                                        金椰分{{ item.point }}分及以上<span class="level-benefit">{{
                                            item.benefit
                                        }}</span>
                                    </div>

                                    <div class="level-desc w-[600px]">
                                        {{ item.desc }}
                                    </div>
                                    <div class="level-expire">使用截止时间：{{ item.expire }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <custom-tabs :active-name="activeTab" class="mt-[60px]" @tab-change="handleTabChange">
                <template #default>
                    <el-tab-pane v-for="tab in tabPane" :key="tab" :label="tab" :name="tab">
                        <CreditBorderBox
                            :src="getPicture(activeTab)"
                            :is-full="activeTab === '金融产品'"
                        >
                            <CreditDynamicsScroll
                                v-if="activeTab === '金融政策'"
                                :data="creditSupportArr"
                                @clickItem="jumpNotice($event, DetailTypeEnum.SupportNotice)"
                            />
                            <!-- <CreditDynamicsScroll
                                v-else
                                :data="financingProductArr"
                                @clickItem="jumpNotice($event, DetailTypeEnum.SupportNotice)"
                            /> -->
                            <!-- <div><img :src="getAssetsFileUrl('image/index/picture05.jpg')" /></div> -->
                        </CreditBorderBox>
                    </el-tab-pane>
                </template>
            </custom-tabs>
        </WelcomeLayout>
    </div>
</template>

<style scoped lang="scss">
:deep(.financial-zone) {
    .credit-module__content {
        padding: 0;
        overflow: hidden;
    }
}

.sports-section {
    padding: 60px 0;
    background-color: #f0f9eb;
    padding: 40px;
}

.sports-title {
    text-align: center;
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 10px;
}

.sports-title-line {
    width: 80px;
    height: 4px;
    background-color: #67c23a;
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 40px;
}

.sports-subtitle {
    text-align: left;
    font-weight: bold;
    color: #529b2e;
    font-size: 24px;
    margin-bottom: 40px;
}

.sports-content {
    display: flex;
    margin: 40px auto;
}

.sports-text {
    flex: 1;
    color: #666666;
    p {
        margin-bottom: 20px;
        line-height: 1.8;
        text-indent: 2em;
    }
}

.sports-card {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    padding: 20px;
    gap: 30px;
    display: flex;
    flex-direction: row;
    width: 100%;
}

.credit-level {
    .level-item {
        margin-bottom: 30px;

        &:last-child {
            border-bottom: none;
        }
    }

    .level-title {
        font-weight: bold;
        color: #303133;
        margin-bottom: 10px;
        font-size: 18px;
    }

    .level-benefit {
        color: #529b2e;
        margin-bottom: 10px;
        margin-left: 20px;
        font-weight: 500;
        font-family: Source Han Sans;
        font-variation-settings: 'opsz' auto;
        font-feature-settings: 'kern' on;
    }

    .level-desc {
        // color: #606266;
        color: #3d3d3d;
        font-size: 16px;
        line-height: 1.6;
        margin-bottom: 5px;
    }

    .level-time {
        color: #909399;
        font-size: 14px;
        margin-bottom: 5px;
    }

    .level-expire {
        color: #529b2e;
        font-size: 14px;
    }
}
</style>
