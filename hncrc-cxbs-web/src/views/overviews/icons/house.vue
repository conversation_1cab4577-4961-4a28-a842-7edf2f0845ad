<template>
    <svg
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        fill="none"
        version="1.1"
        width="21.97534942626953"
        height="21.97050666809082"
        viewBox="0 0 21.97534942626953 21.97050666809082"
    >
        <g>
            <path
                d="M19.0765,12.4123C19.2072,12.5008,19.36,12.5475,19.5178,12.5475L19.5189,12.5472L19.5178,12.5451C20.171,12.5426,20.7823,12.287,21.2457,11.8297C21.7091,11.3724,21.968,10.7603,21.9753,10.1113C21.9753,10.0425,21.968,9.97609,21.9507,9.90971L20.0255,2.29858C19.8283,1.33981,19.3723,0.0688345,17.6961,0L4.23978,0C2.56359,0.0688345,2.1051,1.34227,1.91036,2.30104L0.0221849,9.91463C0.00739496,9.981,0,10.0474,0,10.1138C0.00985994,10.7628,0.266219,11.3749,0.729634,11.8322C1.19305,12.2894,1.80683,12.5426,2.45759,12.5475L2.46252,12.5475C2.62028,12.5475,2.77311,12.5008,2.90375,12.4123C4.41972,11.3897,5.58812,10.9545,7.06218,10.8611C7.85344,10.8685,8.31933,11.22,8.85916,11.6281C9.40392,12.0411,10.0226,12.5082,10.9371,12.518L10.9692,12.518C11.8714,12.518,12.532,12.0534,13.1162,11.6404C13.7029,11.2249,14.2082,10.8685,14.9797,10.8611C16.6313,10.9644,17.7726,11.5322,19.0765,12.4123ZM2.26039,10.9471C1.9005,10.8611,1.62443,10.5513,1.58252,10.1777L3.44605,2.6575C3.44852,2.64521,3.45098,2.63538,3.45345,2.62309C3.65804,1.61024,3.95137,1.58319,4.28908,1.56844L17.6493,1.56844C17.9895,1.58074,18.2803,1.61024,18.4849,2.62309L18.4923,2.65996L20.3928,10.1752C20.3509,10.5489,20.0748,10.8562,19.715,10.9447C18.3124,10.0277,16.9517,9.40083,15.0463,9.28528C15.0315,9.28283,15.0142,9.28283,14.9994,9.28283C13.7152,9.28283,12.8771,9.87284,12.2017,10.3498C11.6791,10.7185,11.3389,10.9447,10.9519,10.9398C10.5773,10.9373,10.2864,10.7308,9.80818,10.3694C9.16975,9.88513,8.37109,9.28283,7.03754,9.28283C7.02275,9.28283,7.00549,9.28528,6.9907,9.28528C5.29726,9.38854,3.90947,9.87284,2.26039,10.9471ZM5.46487,6.2369L16.4883,6.2369C16.9246,6.2369,17.2771,5.88536,17.2771,5.45022C17.2771,5.01509,16.9246,4.66354,16.4883,4.66354L5.46487,4.66354C5.02857,4.66354,4.67608,5.01509,4.67608,5.45022C4.67608,5.88536,5.02857,6.2369,5.46487,6.2369ZM1.55048,19.5367C1.56527,20.8814,2.67204,21.9705,4.02039,21.9705C4.02532,21.9705,4.02779,21.9705,4.03272,21.968L17.9007,21.968C18.5268,21.9435,19.1554,21.6755,19.6262,21.2379C20.1389,20.761,20.4175,20.139,20.4076,19.49L20.4076,14.8683C20.4076,14.4331,20.0551,14.0816,19.6188,14.0816C19.1825,14.0816,18.83,14.4331,18.83,14.8683L18.83,19.5097C18.8374,19.9301,18.3518,20.3652,17.8465,20.3947L4.02039,20.3947C3.53479,20.3947,3.13546,20.0038,3.12807,19.5195L3.12807,14.8683C3.12807,14.4331,2.77557,14.0816,2.33927,14.0816C1.90297,14.0816,1.55048,14.4331,1.55048,14.8683L1.55048,19.5367Z"
                fill-rule="evenodd"
                :fill="color"
                fill-opacity="1"
                style="mix-blend-mode: passthrough"
            />
        </g>
    </svg>
</template>
<script setup lang="ts">
defineProps<{
    color: string;
}>();
</script>
