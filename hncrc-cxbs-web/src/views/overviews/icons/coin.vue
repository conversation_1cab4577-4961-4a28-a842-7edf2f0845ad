<template>
    <svg
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        fill="none"
        version="1.1"
        width="21.979644775390625"
        height="21.959192276000977"
        viewBox="0 0 21.979644775390625 21.959192276000977"
    >
        <g>
            <path
                d="M0.836075,15.1795C2.5366,19.2832,6.54455,21.9592,10.9901,21.9592C17.0562,21.9592,21.9751,17.0484,21.9796,10.9881C21.9829,6.54643,19.3076,2.54058,15.2012,0.838418C11.095,-0.863478,6.36666,0.0736504,3.22212,3.21314C0.0773067,6.35262,-0.864447,11.0758,0.836075,15.1795ZM1.64207,10.986C1.64233,5.82831,5.82764,1.64715,10.9901,1.64715L10.9904,1.64849C16.1531,1.64849,20.3384,5.82992,20.3384,10.9879C20.3376,16.1458,16.152,20.3265,10.9893,20.3259C5.82656,20.3254,1.6418,16.144,1.64207,10.986ZM10.4154,15.8009C10.5684,15.9527,10.7747,16.0382,10.9902,16.039C11.2129,16.0317,11.4232,15.9342,11.5724,15.7688L15.8076,11.5294C16.1258,11.2094,16.1258,10.6931,15.8076,10.3733L11.5724,6.14176C11.2526,5.82384,10.7355,5.82384,10.4154,6.14176L6.17192,10.4055C5.85586,10.7263,5.85586,11.241,6.17192,11.5616L10.4154,15.8009ZM10.9899,14.0626L7.91242,10.988L10.9899,7.91335L14.0674,10.988L10.9899,14.0626Z"
                fill-rule="evenodd"
                :fill="color"
                fill-opacity="1"
                style="mix-blend-mode: passthrough"
            />
        </g>
    </svg>
</template>
<script setup lang="ts">
defineProps<{
    color: string;
}>();
</script>
