<template>
    <div class="overview-container h-full w-full flex flex-col">
        <div class="shrink-0 grow-0 pb-[10px]">
            <el-tabs
                v-model="activeTab"
                type="border-card"
                class="w-full tab demo-tabs"
                @tab-click="handleTabClick"
            >
                <el-tab-pane label="信用+苗木超市" name="seedling" />
                <el-tab-pane label="信用+绿茶经济" name="tea" />
                <el-tab-pane label="信用+油茶产业" name="olitea" />
                <el-tab-pane label="金融产品" name="financial" />
            </el-tabs>
        </div>

        <el-scrollbar class="grow-[1] w-full overflow-y-auto overflow-x-hidden">
            <section class="pr-[12px]">
                <!-- 顶部数据卡片 -->
                <div class="data-cards">
                    <div id="seedling" class="section-title scroll-target">信用+苗木超市</div>

                    <div class="data-cards-content">
                        <DataCard
                            v-for="(item, index) in data1Cards"
                            :key="index"
                            :title="item.title"
                            :value="item.value"
                            :unit="item.unit"
                            :icon="item.icon"
                            :highlightColor="item.highlightColor"
                            :backgroundColor="item.backgroundColor"
                        />
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="charts-container">
                    <el-row :gutter="20">
                        <!-- 左侧饼图 -->
                        <el-col :span="15">
                            <div class="custom-chart-card">
                                <div class="chart-title">
                                    <div class="chart-title-line" />
                                    <div>苗木信用评价等级分析</div>
                                    <div class="update-time">数据更新时间：{{ getDate() }}</div>
                                </div>
                                <el-row :gutter="20" class="flex">
                                    <el-col :span="10">
                                        <div ref="pieChart" class="chart-container-pie" />
                                    </el-col>
                                    <el-col :span="14" class="flex-1">
                                        <div class="grid grid-cols-2 gap-4 chart-grid pt-6">
                                            <div
                                                v-for="(item, index) in creditStatsDataList"
                                                :key="index"
                                                class="chart-item"
                                                :style="{
                                                    background: `linear-gradient(180deg, ${item.backgroundColorStart} -3%, ${item.backgroundColorEnd} 100%)`,
                                                }"
                                            >
                                                <div class="chart-item-header">
                                                    <div class="chart-item-title">
                                                        苗木参与评价主体量
                                                    </div>
                                                    <div
                                                        class="chart-item-name"
                                                        :style="{
                                                            color: item.fontColor,
                                                        }"
                                                    >
                                                        {{ item.name }}
                                                    </div>
                                                </div>
                                                <div class="chart-item-content">
                                                    <div class="chart-item-stats">
                                                        <div>
                                                            <span class="stats-value">{{
                                                                item.value
                                                            }}</span>
                                                            <span class="stats-unit">家</span>
                                                        </div>
                                                        <div>
                                                            <span class="stats-label">占比</span>
                                                            <span
                                                                class="stats-percentage"
                                                                :style="{
                                                                    color: item.highlightColor,
                                                                }"
                                                                >{{ item.percent }}%</span
                                                            >
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <svg
                                                            width="120"
                                                            height="60"
                                                            viewBox="0 0 120 60"
                                                        >
                                                            <rect
                                                                x="10"
                                                                y="35"
                                                                width="10"
                                                                height="20"
                                                                :fill="item.highlightColor"
                                                                rx="2"
                                                            />
                                                            <rect
                                                                x="25"
                                                                y="40"
                                                                width="10"
                                                                height="15"
                                                                :fill="item.secondaryColor"
                                                                rx="2"
                                                            />
                                                            <rect
                                                                x="40"
                                                                y="25"
                                                                width="10"
                                                                height="30"
                                                                :fill="item.highlightColor"
                                                                rx="2"
                                                            />
                                                            <rect
                                                                x="55"
                                                                y="20"
                                                                width="10"
                                                                height="35"
                                                                :fill="item.secondaryColor"
                                                                rx="2"
                                                            />
                                                            <rect
                                                                x="70"
                                                                y="25"
                                                                width="10"
                                                                height="30"
                                                                :fill="item.highlightColor"
                                                                rx="2"
                                                            />
                                                            <rect
                                                                x="85"
                                                                y="15"
                                                                width="10"
                                                                height="40"
                                                                :fill="item.secondaryColor"
                                                                rx="2"
                                                            />
                                                            <rect
                                                                x="100"
                                                                y="5"
                                                                width="10"
                                                                height="50"
                                                                :fill="item.highlightColor"
                                                                rx="2"
                                                            />
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </el-col>
                                </el-row>
                            </div>
                        </el-col>
                        <!-- 右侧环形图 -->
                        <el-col :span="9">
                            <div class="custom-chart-card">
                                <div class="chart-title">
                                    <div class="chart-title-line" />
                                    <div>苗木入驻经营主体分类统计</div>
                                </div>
                                <div ref="doughnutChart" class="chart-container" />
                            </div>
                        </el-col>
                    </el-row>
                </div>

                <div class="data-cards">
                    <div id="tea" class="section-title scroll-target">信用+绿茶经济</div>

                    <div class="data-cards-content">
                        <DataCard
                            v-for="(item, index) in data2Cards"
                            :key="index"
                            :title="item.title"
                            :value="item.value"
                            :unit="item.unit"
                            :icon="item.icon"
                            :highlightColor="item.highlightColor"
                            :backgroundColor="item.backgroundColor"
                        />
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="charts-container">
                    <el-row :gutter="20">
                        <!-- 左侧饼图 -->
                        <el-col :span="15">
                            <div class="custom-chart-card">
                                <div class="chart-title">
                                    <div
                                        class="chart-title-line"
                                        style="background-color: #34c759"
                                    />
                                    <div>诚信茶叶产地码申领数据分析</div>
                                    <div class="update-time">数据更新时间：{{ getDate() }}</div>
                                </div>
                                <el-row :gutter="20">
                                    <!-- 左侧指标卡片 -->
                                    <el-col :span="5">
                                        <div class="tea-stats">
                                            <div class="stat-card">
                                                <div class="stat-icon">
                                                    <el-image :src="icon01" />
                                                </div>
                                                <div class="stat-content">
                                                    <div class="stat-value">
                                                        {{ useCodeTotal
                                                        }}<span class="stat-unit">次</span>
                                                    </div>
                                                    <div class="stat-label">用码总数</div>
                                                </div>
                                            </div>
                                            <div class="stat-card">
                                                <div class="stat-icon">
                                                    <el-image :src="icon02" />
                                                </div>
                                                <div class="stat-content">
                                                    <div class="stat-value">
                                                        {{ teaLeafTotal
                                                        }}<span class="stat-unit">斤</span>
                                                    </div>
                                                    <div class="stat-label">当月茶青总量</div>
                                                </div>
                                            </div>
                                        </div>
                                    </el-col>
                                    <!-- 右侧表格 -->
                                    <el-col :span="19">
                                        <div class="tea-data-table">
                                            <div
                                                class="date-picker"
                                                style="margin-bottom: 10px; text-align: right"
                                            >
                                                <el-date-picker
                                                    v-model="DateRange"
                                                    type="daterange"
                                                    range-separator="-"
                                                    start-placeholder="开始日期"
                                                    end-placeholder="结束日期"
                                                    format="YYYY-MM-DD"
                                                    value-format="YYYY-MM-DD"
                                                    @change="handleDateRangeChange"
                                                />
                                            </div>
                                            <AutoTable
                                                :showHeader="true"
                                                :data="teaData"
                                                :columns="[
                                                    { prop: '$index', label: '序号', width: 80 },
                                                    { prop: 'subjectName', label: '申领主体名称' },
                                                    {
                                                        prop: 'totalUseCodeCount',
                                                        label: '用码数',
                                                        width: 100,
                                                    },
                                                    {
                                                        prop: 'totalViewCount',
                                                        label: '产地码查看数',
                                                        width: 120,
                                                    },
                                                    {
                                                        prop: 'totalTeaCount',
                                                        label: '当月茶青数',
                                                        width: 100,
                                                    },
                                                ]"
                                            />
                                        </div>
                                    </el-col>
                                </el-row>
                            </div>
                        </el-col>
                        <!-- 右侧环形图 -->
                        <el-col :span="9">
                            <div class="custom-chart-card">
                                <div class="chart-title">
                                    <div
                                        class="chart-title-line"
                                        style="background-color: #34c759"
                                    />
                                    <div>茶叶经营主体分类统计</div>
                                </div>
                                <div ref="teaBusinessChart" class="chart-container" />
                            </div>
                        </el-col>
                    </el-row>
                </div>

                <div class="data-cards">
                    <div id="olitea" class="section-title scroll-target">信用+油茶产业</div>

                    <div class="data-cards-content">
                        <DataCard
                            v-for="(item, index) in data3Cards"
                            :key="index"
                            :title="item.title"
                            :value="item.value"
                            :unit="item.unit"
                            :icon="item.icon"
                            :highlightColor="item.highlightColor"
                            :backgroundColor="item.backgroundColor"
                        />
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="charts-container">
                    <el-row :gutter="20">
                        <el-col :span="9">
                            <div class="custom-chart-card">
                                <div class="chart-title">
                                    <div
                                        class="chart-title-line"
                                        style="background-color: #ffbf08"
                                    />
                                    <div>油茶入驻经营主体分类统计</div>
                                </div>
                                <div ref="oilTeaChart" class="chart-container" />
                            </div>
                        </el-col>
                        <el-col :span="15">
                            <div class="custom-chart-card">
                                <div class="chart-title">
                                    <div
                                        class="chart-title-line"
                                        style="background-color: #ffbf08"
                                    />
                                    <div>油茶园</div>
                                </div>

                                <div class="oil-tea-data-table">
                                    <AutoTable
                                        :showHeader="true"
                                        :data="oilTeaData"
                                        :columns="[
                                            { prop: '$index', label: '序号', width: 80 },
                                            { prop: 'estateName', label: '茶园名称' },
                                            { prop: 'estateAddress', label: '茶园地址' },
                                            { prop: 'plantType', label: '茶园种植品种' },
                                            {
                                                prop: 'estateArea',
                                                label: '油茶园面积（亩）',
                                                width: 150,
                                            },
                                        ]"
                                    />
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </div>

                <div class="data-cards">
                    <div id="financial" class="section-title scroll-target">金融产品</div>

                    <div class="data-cards-content">
                        <DataCard
                            v-for="(item, index) in data4Cards"
                            :key="index"
                            :title="item.title"
                            :value="item.value"
                            :unit="item.unit"
                            :icon="item.icon"
                            :highlightColor="item.highlightColor"
                            :backgroundColor="item.backgroundColor"
                        />
                    </div>
                </div>

                <div class="charts-container">
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <div class="custom-chart-card">
                                <div class="chart-title">
                                    <div
                                        class="chart-title-line"
                                        style="background-color: #ea8c6d"
                                    />
                                    <div>金融产品申请主体</div>
                                </div>

                                <div class="finance-data-table">
                                    <AutoTable
                                        :showHeader="true"
                                        :data="financeData"
                                        :columns="[
                                            { prop: '$index', label: '序号', width: 80 },
                                            { prop: 'applySubject', label: '申请主体名称' },
                                            { prop: 'subjectType', label: '主体类型' },
                                            { prop: 'applyProduct', label: '申请产品' },
                                            { prop: 'applyTime', label: '申请时间' },
                                            {
                                                prop: 'applyAmount',
                                                label: '申请融资额（万元）',
                                                width: 150,
                                            },
                                            {
                                                prop: 'disburseAmount',
                                                label: '发放融资额（万元）',
                                                width: 150,
                                            },
                                        ]"
                                    />
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </section>
        </el-scrollbar>
    </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import * as echarts from 'echarts';
import { onBeforeMount, onMounted, ref } from 'vue';

import {
    getCreditStats,
    getFinanceProductDataBoard,
    getFinanceSubject,
    getOilTeaDataBoard,
    getOilTeaSubjectTypeStats,
    getStatistics,
    getSubjectTypeStats,
    getTeaApplyTeaSubjectData,
    getTeaDataBoard,
    getTeaSubjectDataAnalysis,
} from '@/api/overviews';
import { getoverViewsOliTeaList } from '@/api/welcome/olitea';
import { TeaGardenIntroductionListVO } from '@/api/welcome/type';
import { getSubjectTypeLabel } from '@/dicts/SubjectTypeDicts';
import { formatDate } from '@/utils/date';

import AutoTable from './components/AutoTable/index.vue';
import DataCard from './components/DataCard.vue';

const pieChart = ref();
const doughnutChart = ref();
const teaBusinessChart = ref();
const oilTeaChart = ref();

import icon01 from '@/assets/overviews/icon01.png';
import icon02 from '@/assets/overviews/icon02.png';

// 添加 tab 相关的响应式数据
const activeTab = ref('seedling');
const handleTabClick = (tab: any) => {
    const element = document.getElementById(tab.props.name);
    if (element) {
        // 使用 scrollIntoView 的兼容性写法
        try {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        } catch (e) {
            // 降级处理：如果不支持平滑滚动，使用传统方式
            const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
            window.scrollTo({
                top: elementPosition - 100, // 减去顶部固定导航栏的高度
                behavior: 'smooth',
            });
        }
    }
};

const getDate = () => {
    return dayjs().format('YYYY-MM-DD');
};
const originDate = ref<[Date, Date]>([
    dayjs().startOf('month').toDate(),
    dayjs().endOf('month').toDate(),
]);
const DateRange = ref<[string, string]>([
    dayjs(originDate.value[0]).format('YYYY-MM-DD'),
    dayjs(originDate.value[1]).format('YYYY-MM-DD'),
]);

const handleDateRangeChange = (value: any) => {
    DateRange.value = value;
    initGetTeaApplyTeaSubjectData();
};
// 卡片数据
const data1Cards = ref([
    {
        title: '苗木种类数',
        value: 5,
        unit: '种',
        icon: 'leaf',
        highlightColor: '#6B9AFF',
        backgroundColor: '#E6F4FE',
    },
    {
        title: '苗木品种数',
        value: 24,
        unit: '种',
        icon: 'leaf',
        highlightColor: '#6B9AFF',
        backgroundColor: '#E6F4FE',
    },
    {
        title: '入驻苗木经营主体',
        value: 200,
        unit: '家',
        icon: 'house',
        highlightColor: '#6B9AFF',
        backgroundColor: '#E6F4FE',
    },
    {
        title: '一户一码下载',
        value: 120,
        unit: '次',
        icon: 'qrcode',
        highlightColor: '#6B9AFF',
        backgroundColor: '#E6F4FE',
    },
    {
        title: '一户一码查看',
        value: 1466,
        unit: '次',
        icon: 'qrcode',
        highlightColor: '#6B9AFF',
        backgroundColor: '#E6F4FE',
    },
    {
        title: '一苗一码下载',
        value: 106,
        unit: '次',
        icon: 'qrcode',
        highlightColor: '#6B9AFF',
        backgroundColor: '#E6F4FE',
    },
    {
        title: '一苗一码查看',
        value: 486,
        unit: '次',
        icon: 'qrcode',
        highlightColor: '#6B9AFF',
        backgroundColor: '#E6F4FE',
    },
]);

const creditStatsDataList = ref([
    {
        name: 'A',
        value: 0,
        percent: 40,
        backgroundColorStart: 'rgba(254, 242, 242, 0.6)',
        backgroundColorEnd: 'rgba(254, 230, 230, 0.6)',
        fontColor: 'rgba(242, 95, 51, 0.1)',
        secondaryColor: '#FFB49E',
        highlightColor: '#F25F33',
    },
    {
        name: 'B',
        value: 0,
        percent: 30,
        backgroundColorStart: '#FEFCF2',
        backgroundColorEnd: '#FEF6E6',
        fontColor: 'rgba(255, 191, 8, 0.15)',
        secondaryColor: '#FFDB74',
        highlightColor: '#FFBF08',
    },
    {
        name: 'C',
        value: 0,
        percent: 20,
        backgroundColorStart: '#F2F9FE',
        backgroundColorEnd: '#E6F4FE',
        fontColor: 'rgba(22, 93, 255, 0.1)',
        secondaryColor: '#5A9CF8',
        highlightColor: '#165DFF',
    },
    {
        name: 'D',
        value: 0,
        percent: 10,
        backgroundColorStart: '#EBEEF9',
        backgroundColorEnd: '#E9EEFC',
        fontColor: 'rgba(138, 150, 188, 0.15)',
        secondaryColor: 'rgba(138, 150, 188, 0.5)',
        highlightColor: '#8A96BC',
    },
]);

const creditStatsDataTotal = ref<number>(0);

const data2Cards = ref([
    {
        title: '茶叶经营主体入驻',
        value: 456,
        unit: '家',
        icon: 'house',
        highlightColor: '#86DF6C',
        backgroundColor: '#F0FEE6',
    },
    {
        title: '茶叶产地个数',
        value: 24,
        unit: '个',
        icon: 'chart',
        highlightColor: '#86DF6C',
        backgroundColor: '#F0FEE6',
    },
    /* {
        title: '茶叶茶园个数',
        value: 4,
        unit: '个',
        icon: 'chart',
        highlightColor: '#86DF6C',
        backgroundColor: '#F0FEE6',
    }, */
    {
        title: '产地码申请主体',
        value: 200,
        unit: '家',
        icon: 'house',
        highlightColor: '#86DF6C',
        backgroundColor: '#F0FEE6',
    },
    {
        title: '申领茶青',
        value: 123,
        unit: '斤',
        icon: 'chart',
        highlightColor: '#86DF6C',
        backgroundColor: '#F0FEE6',
    },
    {
        title: '产地码申领',
        value: 66,
        unit: '个',
        icon: 'qrcode',
        highlightColor: '#86DF6C',
        backgroundColor: '#F0FEE6',
    },
    {
        title: '产地码查看',
        value: 1106,
        unit: '次',
        icon: 'qrcode',
        highlightColor: '#86DF6C',
        backgroundColor: '#F0FEE6',
    },
]);

const subjectTypeStatsData = ref([]);

const data3Cards = ref([
    {
        title: '油茶经营主体入驻',
        value: 0,
        unit: '家',
        icon: 'leaf',
        highlightColor: '#FFBF08',
        backgroundColor: '#FCECB9',
    },
    {
        title: '油茶园个数',
        value: 0,
        unit: '个',
        icon: 'leaf',
        highlightColor: '#FFBF08',
        backgroundColor: '#FCECB9',
    },
    {
        title: '油茶补贴主体数',
        value: 0,
        unit: '家',
        icon: 'leaf',
        highlightColor: '#FFBF08',
        backgroundColor: '#FCECB9',
    },
    {
        title: '油茶补贴金额',
        value: 0,
        unit: '万',
        icon: 'leaf',
        highlightColor: '#FFBF08',
        backgroundColor: '#FCECB9',
    },
]);
const data4Cards = ref([
    {
        title: '金融产品个数',
        value: 0,
        unit: '个',
        icon: 'chart',
        highlightColor: '#EA8C6D',
        backgroundColor: 'rgba(255, 180, 158, 0.3)',
    },
    /*  {
        title: '金融产品浏览',
        value: 0,
        unit: '次',
        icon: 'coin',
        highlightColor: '#EA8C6D',
        backgroundColor: 'rgba(255, 180, 158, 0.3)',
    },
    {
        title: '浏览经营主体数',
        value: 0,
        unit: '家',
        icon: 'house',
        highlightColor: '#EA8C6D',
        backgroundColor: 'rgba(255, 180, 158, 0.3)',
    }, */
    {
        title: '申请经营主体',
        value: 0,
        unit: '家',
        icon: 'house',
        highlightColor: '#EA8C6D',
        backgroundColor: 'rgba(255, 180, 158, 0.3)',
    },
    {
        title: '申请成功经营主体数',
        value: 0,
        unit: '个',
        icon: 'house',
        highlightColor: '#EA8C6D',
        backgroundColor: 'rgba(255, 180, 158, 0.3)',
    },
    {
        title: '融资总金额',
        value: 0,
        unit: '万元',
        icon: 'coins',
        highlightColor: '#EA8C6D',
        backgroundColor: 'rgba(255, 180, 158, 0.3)',
    },
]);

const teaData = ref([]);
const oilTeaData = ref<TeaGardenIntroductionListVO[]>([]);
interface FinanceData {
    applyProduct: string;
    applySubject: string;
    subjectType: string;
    applyTime: string;
    applyAmount: number;
    disburseAmount: number;
}
const financeData = ref<FinanceData[]>([]);

onMounted(() => {
    initGetCreditStats();
    initGetSubjectTypeStats();
    initOilTeaChart();
    initGetTeaDataBoard();
    initGetTeaApplyTeaSubjectData();
    initGetTeaSubjectDataAnalysis();
    initGetOilTeaDataBoard();
    initGetOilTeaSubjectTypeStats();
    initGetOliTeaList();
    initGetFinanceSubject();
    initGetFinanceProductDataBoard();
    initTeaBusinessChart();
});

// 初始化企业评级饼图
const initPieChart = () => {
    const chart = echarts.init(pieChart.value);
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
            orient: 'horizontal',
            bottom: 0,
        },
        title: [
            {
                text: '苗木参与评价主体量',
                subtext: creditStatsDataTotal.value.toString(),
                left: 'center',
                top: '40%',
                textStyle: {
                    fontSize: 12,
                    color: '#666666',
                    fontWeight: 'normal',
                    lineHeight: 32,
                },
                subtextStyle: {
                    fontSize: 38,
                    color: '#000',
                    lineHeight: 22,
                },
            },
            {
                text: ' ',
                subtext: '家',
                top: '40%',
                left: '60%',
                textStyle: {
                    fontSize: 14,
                    color: 'white',
                    fontWeight: 'normal',
                    lineHeight: 32,
                },
                subtextStyle: {
                    fontSize: 16,
                    color: '#000000',
                    lineHeight: 22,
                },
            },
        ],
        series: [
            {
                name: '苗木参与评价主体量',
                type: 'pie',
                radius: ['50%', '70%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 0,
                },
                label: {
                    show: false,
                    position: 'outside',
                    formatter: '{b}: {d}%',
                },
                emphasis: {
                    scaleSize: 10,
                    label: {
                        show: false,
                        fontSize: '16',
                        fontWeight: 'bold',
                    },
                },
                data: [
                    {
                        value: creditStatsDataList.value[0].value,
                        name: 'A级',
                        itemStyle: { color: '#FF6661' },
                    },
                    {
                        value: creditStatsDataList.value[1].value,
                        name: 'B级',
                        itemStyle: { color: '#FFDF61' },
                    },
                    {
                        value: creditStatsDataList.value[2].value,
                        name: 'C级',
                        itemStyle: { color: '#3277FF' },
                    },
                    {
                        value: creditStatsDataList.value[3].value,
                        name: 'D级',
                        itemStyle: { color: '#8A96BC' },
                    },
                ],
            },
        ],
    };
    chart.setOption(option);
    window.addEventListener('resize', () => {
        chart.resize();
    });
};

// 初始化主体分析环形图
const initDoughnutChart = () => {
    const chart = echarts.init(doughnutChart.value);
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
            orient: 'horizontal',
            bottom: 0,
            textStyle: {
                color: '#333',
            },
        },
        series: [
            {
                name: '经营主体类型',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'outside',
                    formatter: '{b}\n {d}%',
                },
                emphasis: {
                    scaleSize: 10,
                    label: {
                        show: false,
                        fontSize: '16',
                        fontWeight: 'bold',
                    },
                },
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 2,
                },
                data: [
                    {
                        value: subjectTypeStatsData.value[2] || 0,
                        name: '企业',
                        itemStyle: { color: '#6A9AF1' },
                    },
                    {
                        value: subjectTypeStatsData.value[0] || 0,
                        name: '合作社',
                        itemStyle: { color: '#8EB5F6' },
                    },
                    {
                        value: subjectTypeStatsData.value[3] || 0,
                        name: '个体工商户',
                        itemStyle: { color: '#AAC7F7' },
                    },
                    {
                        value: subjectTypeStatsData.value[1] || 0,
                        name: '农户',
                        itemStyle: { color: '#2D549C' },
                    },
                ],
            },
        ],
    };
    chart.setOption(option);
    window.addEventListener('resize', () => {
        chart.resize();
    });
};

// 添加新的初始化函数
const initTeaBusinessChart = () => {
    const chart = echarts.init(teaBusinessChart.value);
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
            orient: 'horizontal',
            bottom: 0,
        },
        series: [
            {
                name: '经营主体',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'outside',
                    formatter: '{b}\n{d}%',
                },
                emphasis: {
                    scaleSize: 10,
                    label: {
                        show: false,
                        fontSize: '16',
                        fontWeight: 'bold',
                    },
                },
                data: [
                    {
                        value: teaSubjectTypeData.value[0] || 0,
                        name: '企业',
                        itemStyle: { color: '#9CDD7A' },
                    },
                    {
                        value: teaSubjectTypeData.value[1] || 0,
                        name: '合作社',
                        itemStyle: { color: '#B8ED9D' },
                    },
                    {
                        value: teaSubjectTypeData.value[3] || 0,
                        name: '个体工商户',
                        itemStyle: { color: '#C6EEB1' },
                    },
                    {
                        value: teaSubjectTypeData.value[1] || 0,
                        name: '农户',
                        itemStyle: { color: '#558F36' },
                    },
                ],
            },
        ],
    };
    chart.setOption(option);
    window.onresize = chart.resize;
};

// 添加新的图表初始化函数
const initOilTeaChart = () => {
    const chart = echarts.init(oilTeaChart.value);
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
            orient: 'horizontal',
            bottom: 0,
        },
        series: [
            {
                name: '经营主体',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'outside',
                    formatter: '{b}\n{d}%',
                },
                emphasis: {
                    scaleSize: 10,
                    label: {
                        show: false,
                        fontSize: '16',
                        fontWeight: 'bold',
                    },
                },
                data: [
                    {
                        value: oilTeaSubjectTypeData.value[0] || 0,
                        name: '企业',
                        itemStyle: { color: '#F9DC83' },
                    },
                    {
                        value: oilTeaSubjectTypeData.value[1] || 0,
                        name: '合作社',
                        itemStyle: { color: '#FBE5A1' },
                    },
                    {
                        value: oilTeaSubjectTypeData.value[2] || 0,
                        name: '基层组织',
                        itemStyle: { color: '#FCECB9' },
                    },
                    {
                        value: oilTeaSubjectTypeData.value[3] || 0,
                        name: '农户',
                        itemStyle: { color: '#A18539' },
                    },
                ],
            },
        ],
    };
    chart.setOption(option);
    window.addEventListener('resize', () => {
        chart.resize();
    });
};

const initViews = () => {
    getStatistics().then((res: any) => {
        data1Cards.value[0].value = res.categoryCount || 0;
        data1Cards.value[1].value = res.varietyCount || 0;
        data1Cards.value[2].value = res.subjectCount || 0;
        data1Cards.value[3].value = res.oneCodeDownloadCount || 0;
        data1Cards.value[4].value = res.oneCodeViewCount || 0;
        data1Cards.value[5].value = res.oneSeedCodeDownloadCount || 0;
        data1Cards.value[6].value = res.oneSeedCodeViewCount || 0;
    });
};

const initGetCreditStats = () => {
    getCreditStats().then((res: any) => {
        creditStatsDataTotal.value = Number(res.totalCount);
        creditStatsDataList.value[0].value = res.levelACount ? Number(res.levelACount) : 0;
        creditStatsDataList.value[0].percent = res.levelAPercent
            ? percentNum(res.levelAPercent as string)
            : 0;
        creditStatsDataList.value[1].value = res.levelBCount ? Number(res.levelBCount) : 0;
        creditStatsDataList.value[1].percent = res.levelBPercent
            ? percentNum(res.levelBPercent as string)
            : 0;
        creditStatsDataList.value[2].value = res.levelCCount ? Number(res.levelCCount) : 0;
        creditStatsDataList.value[2].percent = res.levelCPercent
            ? percentNum(res.levelCPercent as string)
            : 0;
        creditStatsDataList.value[3].value = res.levelDCount ? Number(res.levelDCount) : 0;
        creditStatsDataList.value[3].percent = res.levelDPercent
            ? percentNum(res.levelDPercent as string)
            : 0;
        initPieChart();
    });
};
/**
苗木入驻经营主体分类统计 */
const initGetSubjectTypeStats = () => {
    getSubjectTypeStats().then((res: any) => {
        subjectTypeStatsData.value = [
            res.cooperativeCount ? res.cooperativeCount : 0, // 合作社占比
            res.farmerCount ? res.farmerCount : 0, // 农户占比
            res.enterpriseCount ? res.enterpriseCount : 0, // 企业占比
            res.planterCount ? res.planterCount : 0, // 个体工商户占比
        ];
        initDoughnutChart();
    });
};

/**诚信茶叶数据看板 */
const initGetTeaDataBoard = () => {
    getTeaDataBoard().then((res: any) => {
        data2Cards.value[0].value = res.subjectCount || 0;
        data2Cards.value[1].value = res.originCount || 0;
        data2Cards.value[2].value = res.originCodeApplyCount || 0;
        data2Cards.value[3].value = res.teaLeafAmount || 0;
        data2Cards.value[4].value = res.originCodeApplyNum || 0;
        data2Cards.value[5].value = res.originCodeViewCount || 0;
    });
};
const useCodeTotal = ref(0);
const teaLeafTotal = ref(0);
/**诚信茶叶产地码申领数据分析 */
const initGetTeaApplyTeaSubjectData = () => {
    getTeaApplyTeaSubjectData({
        startTime: DateRange.value ? DateRange.value[0] : '',
        endTime: DateRange.value ? DateRange.value[1] : '',
    }).then((res: any) => {
        let totalUseCodeCount = 0;
        let totalTeaCount = 0;
        res?.forEach((data: any) => {
            totalUseCodeCount += Number(data.totalUseCodeCount);
            totalTeaCount += Number(data.totalTeaCount);
        });
        useCodeTotal.value = totalUseCodeCount;
        teaLeafTotal.value = totalTeaCount;
        teaData.value = res;
    });
};

const teaSubjectTypeData = ref([]);
/**茶叶经营主体分类统计 */
const initGetTeaSubjectDataAnalysis = () => {
    getTeaSubjectDataAnalysis().then((res: any) => {
        teaSubjectTypeData.value = [
            res?.enterpriseCount ? res?.enterpriseCount : 0, // 企业占比
            res?.cooperativeCount ? res?.cooperativeCount : 0, // 合作社占比
            res?.planterCount ? res?.planterCount : 0, // 个体工商户占比
            res?.farmerCount ? res?.farmerCount : 0, // 农户户占比
        ];
        initTeaBusinessChart();
    });
};
const oilTeaSubjectTypeData = ref([]);
/**油茶入驻经营主体分类统计 */
const initGetOilTeaSubjectTypeStats = () => {
    getOilTeaSubjectTypeStats().then((res: any) => {
        oilTeaSubjectTypeData.value = [
            res.enterpriseCount ? res.enterpriseCount : 0, // 企业占比
            res.cooperativeCount ? res.cooperativeCount : 0, // 合作社占比
            res.planterCount ? res.planterCount : 0, // 个体工商户占比
            res.farmerCount ? res.farmerCount : 0, // 农户户占比
        ];
        initOilTeaChart();
    });
};

/**诚信油茶数据看板 */
const initGetOilTeaDataBoard = () => {
    getOilTeaDataBoard().then((res: any) => {
        data3Cards.value[0].value = res.subjectCount || 0;
        data3Cards.value[1].value = res.estateCount || 0;
        data3Cards.value[2].value = res.subsidySubjectCount || 0;
        data3Cards.value[3].value = res.subsidyAmount || 0;
    });
};
/**油茶茶园简介列表 */
const initGetOliTeaList = () => {
    getoverViewsOliTeaList().then((res: any) => {
        res.forEach((item) => {
            item.estateName = item.estateName ? item.estateName : '-';
            item.estateAddress = item.estateAddress ? item.estateAddress : '-';
            item.plantType = item.plantType ? item.plantType : '-';
            item.estateArea = item.estateArea ? item.estateArea : '-';
        });
        oilTeaData.value = res as TeaGardenIntroductionListVO[];
    });
};

/**金融产品申请主体 */
const initGetFinanceSubject = () => {
    getFinanceSubject().then((res) => {
        res.forEach((item: any) => {
            item.subjectType = getSubjectTypeLabel(item.subjectType);
            item.applyTime = formatDate(item.applyTime).split(' ')[0];
            item.applyAmount = item.applyAmount ? item.applyAmount : '-';
            item.applyProduct = item.applyProduct ? item.applyProduct : '-';
            item.subjectType = item.subjectType ? item.subjectType : '-';
            item.applyTime = item.applyTime ? item.applyTime : '-';
            item.applySubject = item.applySubject ? item.applySubject : '-';
            item.disburseAmount = item.disburseAmount ? item.disburseAmount : '-';
        });
        financeData.value = res as FinanceData[];
    });
};

/**金融产品数据看板 */
const initGetFinanceProductDataBoard = () => {
    getFinanceProductDataBoard().then((res: any) => {
        data4Cards.value[0].value = res.financialProductCount || 0;
        //data4Cards.value[1].value = res.financialProductViewCount || 0;
        //data4Cards.value[2].value = res.subjectViewCount || 0;
        data4Cards.value[1].value = res.applySubjectCount || 0;
        data4Cards.value[2].value = res.applySuccessSubjectCount || 0;
        data4Cards.value[3].value = res.totalAmount || 0;
    });
};

const percentNum = (percent: string) => {
    return Number(percent.replace('%', ''));
};
onBeforeMount(() => {
    initViews();
});
</script>

<style scoped lang="scss">
.overview-container {
    height: 100%;
    width: 100%;
    overflow: hidden;

    .tab {
        background-color: #fff;
        padding-right: 20px;
    }
    .data-cards {
        margin-bottom: 20px;

        .data-cards-content {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            justify-content: space-between;
        }
    }

    .charts-container {
        margin-bottom: 20px;
    }

    .custom-chart-card {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        padding: 0;
        width: 100%;
        height: 100%;
    }

    .chart-title {
        display: flex;
        align-items: center;
        gap: 8px;
        padding-left: 0;
        height: 48px;
        border-bottom: 1px solid #ebeef5;
        font-weight: normal;

        .chart-title-line {
            width: 4px;
            height: 22px;
            border-radius: 0 4px 4px 0;
            background-color: #165dff;
        }

        .update-time {
            margin-left: auto;
            padding-right: 20px;
            color: #999999;
        }
    }
    .chart-container-pie {
        height: 350px;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
    }

    .chart-container {
        height: 400px;
        width: 100%;
        padding: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
    }

    .chart-grid {
        margin-right: 16px;
    }

    .chart-item {
        border-radius: 4px;

        &-header {
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        &-title {
            color: #666666;
            flex: 1;
            padding-left: 8px;
            font-size: 16px;
        }

        &-name {
            font-size: 60px;
            padding: 0 10px;
        }

        &-content {
            display: flex;
            flex-direction: row;
            padding: 0 0 12px 12px;
        }

        &-stats {
            flex: 1;

            .stats-value {
                font-size: 24px;
            }

            .stats-unit {
                font-size: 16px;
                padding-left: 8px;
            }

            .stats-label {
                color: #4e5969;
                font-size: 14px;
            }

            .stats-percentage {
                padding-left: 8px;
            }
        }
    }

    .section-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin: 10px 0;
    }

    .tea-data-table {
        width: 100%;
        padding: 20px;
        height: 350px;

        :deep(.el-table) {
            height: 100%;
            .el-table__body-wrapper {
                height: calc(100% - 40px) !important;
                overflow-y: hidden !important;
            }

            --el-table-border-color: #ebeef5;
            --el-table-header-bg-color: #f5f7fa;

            th {
                background-color: var(--el-table-header-bg-color);
                color: #606266;
                font-weight: 500;
                padding: 12px 0;
            }

            td {
                padding: 12px 0;
            }
        }
    }

    .tea-stats {
        display: flex;
        flex-direction: column;
        gap: 16px;
        padding: 20px 0 0 20px;
        width: 100%;
        height: 100%;

        .stat-card {
            background: linear-gradient(180deg, #f5fef2 -3%, #e6feee 100%);
            border-radius: 4px;
            padding: 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            width: 100%;
            flex: 1;
            margin: 0;

            .stat-icon {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                background: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                color: #165dff;
            }

            .stat-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;

                .stat-value {
                    font-size: 24px;
                    font-weight: 500;
                    color: #1d2129;
                    line-height: 1.2;

                    .stat-unit {
                        font-size: 14px;
                        margin-left: 4px;
                        color: #4e5969;
                    }
                }

                .stat-label {
                    font-size: 14px;
                    color: #86909c;
                    margin-top: 4px;
                }
            }
        }
    }

    .oil-tea-data-table,
    .finance-data-table {
        width: 100%;
        padding: 20px;
        height: 520px;

        :deep(.el-table) {
            height: 100%;

            .el-table__body-wrapper {
                height: calc(100% - 40px) !important;
                overflow-y: hidden !important;
            }

            --el-table-border-color: #ebeef5;
            --el-table-header-bg-color: #f5f7fa;

            th {
                background-color: var(--el-table-header-bg-color);
                color: #606266;
                font-weight: 500;
                padding: 12px 0;
            }

            td {
                padding: 12px 0;
            }
        }
    }
    .scroll-target {
        scroll-margin-top: 100px;
    }
}
:deep(.el-tabs__nav-scroll) {
    background-color: #fff;
}
:deep(.el-tabs__content) {
    padding: 0;
}
:deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item) {
    color: #000;
}
:deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active) {
    color: var(--el-color-primary);
}
</style>
