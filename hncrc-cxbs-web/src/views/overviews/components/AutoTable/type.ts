import type { VNode } from 'vue';

export interface TableColumnsTypes {
    slot?: string;
    type?: 'default' | 'selection' | 'index' | 'expand';
    index?: number | ((index: number) => number);
    label: string;
    'column-key'?: string;
    prop: string;
    width?: string | number;
    'min-width'?: string | number;
    fixed?: 'left' | 'right' | boolean;
    'render-header'?: (data: { column: any; $index: number }) => void;
    sortable?: boolean | 'custom';
    'sort-method'?: <T = any>(a: T, b: T) => number;
    'sort-by'?: ((row: any, index: number) => string) | string | string[];
    'sort-orders'?: ('ascending' | 'descending' | null)[];
    resizable?: boolean;
    formatter?: (row: any, column: any, cellValue: any, index: number) => VNode | string;
    'show-overflow-tooltip'?: boolean;
    align?: 'left' | 'center' | 'right';
    'header-align'?: 'left' | 'center' | 'right';
    'class-name'?: string;
    'label-class-name'?: string;
    selectable?: (row: any, index: number) => boolean;
    'reserve-selection'?: boolean;
    filters?: Array<{ text: string; value: string }>;
    'filter-placement'?:
        | 'top'
        | 'top-start'
        | 'top-end'
        | 'bottom'
        | 'bottom-start'
        | 'bottom-end'
        | 'left'
        | 'left-start'
        | 'left-end'
        | 'right'
        | 'right-start'
        | 'right-end';
    'filter-multiple'?: boolean;
    'filter-method'?: (value: any, row: any, column: any) => true;
    'filtered-value'?: string[];
}
