<script setup lang="ts">
import { ElTable, TableInstance } from 'element-plus';
import { nextTick } from 'process';
import { type CSSProperties, onBeforeUnmount, onMounted, ref } from 'vue';

import type { TableColumnsTypes } from './type';

const props = withDefaults(
    defineProps<{
        data: any[];
        columns: TableColumnsTypes[];
        columnsMixin?: Partial<TableColumnsTypes>;
        /** 是否设置斑马线 */
        stripe?: boolean;
        /** 是否设置边框 */
        border?: boolean;
        headerCellStyle?: CSSProperties;
        showOverflowTooltip?: boolean;
        /** 是否显示表头 */
        showHeader?: boolean;
    }>(),
    {
        columnsMixin(): Partial<TableColumnsTypes> {
            return {
                align: 'left',
            };
        },
        stripe: false,
        border: false,
        headerCellStyle(): CSSProperties {
            return {
                color: '#929EB5',
                backgroundColor: '#F4F8FE',
            };
        },
        showOverflowTooltip: true,
        showHeader: true,
    },
);

const zero = (n: number) => {
    if (n < 10) {
        return '0' + n;
    } else {
        return n;
    }
};

const tableRef = ref<TableInstance>();
const scrollInterval = ref();
const scrollData = ref<number>(0);
defineExpose({
    autoScroll: () => {
        requestAnimationFrame(startAutoScroll);
    },
});

onMounted(() => {
    nextTick(() => {
        startAutoScroll();
    });
});

// 开始自动滚动
const startAutoScroll = () => {
    scrollInterval.value = setInterval(() => {
        const box = tableRef.value?.$el.querySelector('.el-table__body-wrapper');
        const content = tableRef.value?.$el.querySelector('.el-table__body');
        if (scrollData.value >= content.scrollHeight - box.offsetHeight) {
            tableRef.value.setScrollTop(0);
            scrollData.value = 0;
        } else {
            tableRef.value.setScrollTop(scrollData.value++);
        }
    }, 50);
};

// 停止自动滚动
const stopAutoScroll = () => {
    if (scrollInterval.value) {
        clearInterval(scrollInterval.value);
        scrollInterval.value = null;
    }
};

// 监听表格组件的滚动事件，停止自动滚动
const handleScroll = () => {
    if (scrollInterval.value) {
        stopAutoScroll();
    }
};

// 监听表格组件的鼠标移入事件，停止自动滚动
const handleMouseEnter = () => {
    if (scrollInterval.value) {
        stopAutoScroll();
    }
};

// 监听表格组件的鼠标移出事件，开始自动滚动
const handleMouseLeave = () => {
    if (!scrollInterval.value) {
        startAutoScroll();
    }
};

onBeforeUnmount(() => {
    stopAutoScroll();
});
</script>
<template>
    <ElTable
        ref="tableRef"
        :data="data"
        :border="border"
        :stripe="stripe"
        :header-cell-style="headerCellStyle"
        :show-overflow-tooltip="showOverflowTooltip"
        :show-header="showHeader"
        @scroll="handleScroll"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
    >
        <ElTable.TableColumn
            v-for="(item, i) in columns"
            v-bind="Object.assign({}, columnsMixin, item)"
            :key="i"
        >
            <template #default="scope">
                <span v-if="item.prop === '$index'">{{ zero(Number(scope.$index + 1)) }}</span>
                <slot v-else :name="item.prop" v-bind="scope">
                    {{ scope.row[item.prop] }}
                </slot>
            </template>
        </ElTable.TableColumn>
    </ElTable>
</template>
<style scope lang="scss"></style>
