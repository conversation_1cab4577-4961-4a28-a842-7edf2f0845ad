<template>
    <div class="data-card">
        <div class="card-content-left">
            <div class="card-title">{{ props.title }}</div>
            <div class="card-value">
                <span class="number">{{ props.value }}</span>
                <span class="unit">{{ props.unit }}</span>
            </div>
        </div>
        <div class="card-content-right">
            <span
                class="icon-wrapper"
                :style="{ backgroundColor: props.backgroundColor, color: props.highlightColor }"
            >
                <component :is="getIconComponent" :color="props.highlightColor" class="icon" />
            </span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue';

import Chart from '../icons/chart.vue';
import Coin from '../icons/coin.vue';
import Coins from '../icons/coins.vue';
import House from '../icons/house.vue';
import Leaf from '../icons/leaf.vue';
import Qrcode from '../icons/qrcode.vue';

const props = defineProps<{
    title: string;
    value: number;
    unit: string;
    icon: string;
    highlightColor: string;
    backgroundColor: string;
}>();

const getIconComponent = computed(() => {
    const iconMap = {
        leaf: Leaf,
        house: House,
        qrcode: Qrcode,
        chart: Chart,
        coin: Coin,
        coins: Coins,
    };
    return iconMap[props.icon] || Leaf;
});
</script>

<style scoped lang="scss">
.icon {
    width: 22px;
    height: 22px;
}
.data-card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: row;

    .card-content-left {
        display: flex;
        flex-direction: column;
        flex: 1;
    }

    .card-content-right {
        display: flex;
        align-items: center;
        justify-content: right;
    }

    .card-title {
        color: #666666;
        font-size: 14px;
    }

    .card-value {
        margin-top: 12px;

        .number {
            font-size: 32px;
        }

        .unit {
            margin-left: 8px;
            font-size: 14px;
        }
    }

    .icon-wrapper {
        border-radius: 50%;
        padding: 8px;
    }
}
</style>
