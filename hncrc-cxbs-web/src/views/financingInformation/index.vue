<script setup lang="ts">
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';
import { ref } from 'vue';

import {
    deleteById,
    downloadTemplate,
    getDetailById,
    getList,
    updateData,
    uploadFinancialProductFile,
} from '@/api/financingInformation';
import { defineEntityCrud } from '@/components/EntityCrud/hook';
import EntityCrud from '@/components/EntityCrud/index.vue';
import { EntityCrudProps } from '@/components/EntityCrud/type';
import { getEnumOptions } from '@/components/EntityCrud/util';
import { downloadBlobFile } from '@/utils/file';

const FileStatusEnum = {
    ONLINE: '公示中',
    OFFLINE: '已下线',
};

type FinancingInformation = {
    id?: string;
    fileName: string;
    fileContent: string;
    publicFileKey: string;
    fileStatus: keyof typeof FileStatusEnum;
    creator: string;
    updater: string;
    createTime: Date;
    updateTime: Date;
};

type FinancingListItem = {
    id: string;
    applySubject: string;
    applyProduct: string;
    applyTime: Date;
    subjectType: string;
    applyAmount: number;
    disburseAmount: number;
};

const dialogVisible = ref(false);
const financingList = ref<FinancingListItem[]>([]);
const entityCrudRef = ref();

const config: EntityCrudProps<FinancingInformation> = {
    entityName: 'FinancingInformation',
    displayName: '金融产品融资信息上传',
    filterFormItems: {
        fileName: {
            type: 'input',
            label: '文件名称',
        },
    },
    operations: [
        {
            type: 'button',
            label: '上传',
            colorize: 'primary',
            actionService: async () => {
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = '.xls, .xlsx';
                fileInput.onchange = async (e) => {
                    const file = (e.target as HTMLInputElement).files[0];
                    if (file) {
                        const formData = new FormData();
                        formData.append('file', file);
                        await uploadFinancialProductFile(formData);
                        ElMessage.success('上传成功');
                        entityCrudRef.value?.handleRefresh();
                    }
                };
                fileInput.click();
                Promise.resolve();
            },
        },
        {
            type: 'button',
            label: '下载模板',
            colorize: 'primary',
            actionService: async () => {
                const {
                    data,
                    fileName = '金融产品融资信息上传模版',
                    fileType = 'application/vnd.ms-excel;charset=utf-8',
                } = await downloadTemplate();
                if (data) {
                    const decodedFileName = decodeURIComponent(fileName);
                    downloadBlobFile(data, decodedFileName, fileType);
                }
            },
        },
    ],
    tableColumns: {
        fileName: {
            label: '文件名称',
        },
        createTime: {
            label: '上传时间',
            formatter: (row) => dayjs(row.createTime).format('YYYY-MM-DD'),
        },
        fileStatus: {
            label: '状态',
            formatter: (row) => FileStatusEnum[row.fileStatus],
        },
        operations: {
            label: '操作',
            width: '230px',
        },
    },
    createFormItems: {
        fileName: {
            type: 'input',
            label: '文件名称',
            required: true,
        },
        fileStatus: {
            type: 'select',
            label: '文件状态',
            options: getEnumOptions(FileStatusEnum),
            required: true,
        },
        fileContent: {
            type: 'multiple-document',
            label: '补贴公示文件',
            required: true,
        },
    },
    listFetchService: async (params) => {
        const result: any = await getList(params);
        return Promise.resolve(result);
    },
    publishButtonLabel: '发布',
    canPublish: (row) => row.fileStatus !== 'ONLINE',
    publishService: (record: FinancingInformation) => {
        console.log('record', record);
        record.fileStatus = 'ONLINE';
        return updateData(record);
    },

    unpublishButtonLabel: '下线',
    canUnpublish: (row) => row.fileStatus === 'ONLINE',
    unpublishService: (record: FinancingInformation) => {
        record.fileStatus = 'OFFLINE';
        return updateData(record);
    },

    deleteService: (record: FinancingInformation) => deleteById(record?.id),
    /** 自定义操作列按钮 */
    rowOperations: [
        {
            /** 操作类型 */
            type: 'link',
            /** 操作按钮文本 */
            label: '详情',
            /** 按钮样式计算函数 */
            colorize: () => 'primary',
            /** 操作执行函数 */
            actionService: async (params) => {
                const result = await getDetailById(params?.id);
                financingList.value = result;
                dialogVisible.value = true;
            },
            /** 是否显示 */
            canDisplay: () => true,
            displayIndex: -1,
        },
    ],
};

const entityCrudProps = defineEntityCrud(config);
</script>

<template>
    <div>
        <EntityCrud ref="entityCrudRef" v-bind="entityCrudProps">
            <template #tableFileStatus="{ row }: { row: FinancingInformation }">
                <el-tag :type="row.fileStatus === 'ONLINE' ? 'success' : 'warning'">
                    {{ FileStatusEnum[row.fileStatus] }}
                </el-tag>
            </template>
        </EntityCrud>

        <el-dialog
            v-model="dialogVisible"
            title="经营主体融资清单"
            width="50%"
            :close-on-click-modal="false"
        >
            <el-table :data="financingList" border style="width: 100%">
                <el-table-column type="index" label="序号" width="70" />
                <el-table-column prop="applySubject" label="申请主体名称" />
                <el-table-column prop="subjectType" label="主体类型" />
                <el-table-column prop="applyTime" label="申请时间" />
                <el-table-column prop="applyProduct" label="申请产品" />
                <el-table-column prop="applyAmount" label="申请融资额（万）" align="right" />
                <el-table-column prop="disburseAmount" label="发放融资额（万）" align="right" />
            </el-table>
        </el-dialog>
    </div>
</template>

<style scoped>
.el-dialog :deep(.el-dialog__header) {
    border-bottom: 1px solid #dcdfe6;
    padding: 15px 20px;
    margin-right: 0;
    background-color: #f5f7fa;
}

.el-dialog :deep(.el-dialog__body) {
    padding: 20px;
}
</style>
