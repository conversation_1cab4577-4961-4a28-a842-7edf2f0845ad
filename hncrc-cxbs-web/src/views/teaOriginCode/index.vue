<script setup lang="ts">
import QRCode from 'qrcode';
import { nextTick, ref, watch } from 'vue';

import {
    originGetOriginCodeHistory,
    originGetOriginCodeHistoryDetail,
    originGetOriginCodePage,
    originUpdateCodeStatus,
} from '@/api/originCode';
import { defineEntityCrud } from '@/components/EntityCrud/hook';
import EntityCrud from '@/components/EntityCrud/index.vue';
import { EntityCrudProps } from '@/components/EntityCrud/type';
import { defineSearchPage } from '@/components/SearchPage/hook';
import SearchPage from '@/components/SearchPage/index.vue';
import { getAssetsFileUrl } from '@/utils/file';

const OriginCodeStatusEnum = {
    ONLINE: '使用中',
    OFFLINE: '已下线',
};

// 定义实体类型
type TeaOriginCode = {
    id?: string;
    teaOriginId: string;
    teaSubjectName: string;
    teaOriginName: string;
    teaEstate: string;
    teaType: string;
    code: string;
    codeNum: string;
    viewCount: number;
    useCount: number;
    totalTeaWeight: number;
    promiseFileKey: string;
    promiseFileUrl: string;
    codeStatus: keyof typeof OriginCodeStatusEnum;
    creator: string;
    updater: string;
    createTime: string;
    updateTime: string;
    uniscid: string;
    expireTime: string;
};

type TeaOriginCodeHistory = {
    id?: string; // 主键
    teaOriginCodeId: string; // 产地码id
    useCount: number; // 用码次数
    totalTeaWeight: number; // 茶青总数（斤）
    createTime: string; // 操作时间
};

const historyId = ref<string>('');

const config: EntityCrudProps<TeaOriginCode> = {
    entityName: 'teaOriginCode',
    displayName: '产地码',
    filterFormItems: {
        teaOriginName: {
            type: 'input',
            label: '产地名称',
        },
    },
    tableColumns: {
        teaSubjectName: '产地主体名称',
        teaOriginName: '产地名称',
        teaEstate: '茶园名称',
        teaType: '茶叶品种',
        viewCount: '扫码查看次数',
        useCount: '用码累计数',
        totalTeaWeight: '累计茶青总数(斤)',
        promiseFileUrl: {
            label: '承诺书',
            minWidth: 150,
        },
        updateTime: '更新时间',
        codeStatus: '状态',
        operations: {
            label: '操作',
            width: '350',
        },
    },
    rowOperations: [
        {
            label: '查看产地码',
            type: 'link',
            actionService: (row) => {
                selectedOriginCode.value = row;
                codeVisible.value = true;
                return Promise.resolve();
            },
            displayIndex: -1,
        },
        {
            label: '历史记录',
            type: 'link',
            actionService: async (row) => {
                historyVisible.value = true;
                historyId.value = row.id;
            },
            displayIndex: -1,
        },
        {
            label: '下载产地码',
            type: 'link',
            actionService: (row) => {
                return generateAndDownloadCode(row);
            },
            displayIndex: -1,
        },
    ],
    // 这里需要替换为实际的API服务
    listFetchService: async (params) => {
        const result: any = await originGetOriginCodePage(params);
        return Promise.resolve(result);
    },
    publishButtonLabel: '激活',
    canPublish: (row) => row.codeStatus !== 'ONLINE',
    publishService: (record: TeaOriginCode) => {
        record.codeStatus = 'ONLINE';
        return originUpdateCodeStatus(record);
    },
    unpublishButtonLabel: '停用',
    canUnpublish: (row) => row.codeStatus === 'ONLINE',
    unpublishService: (record: TeaOriginCode) => {
        record.codeStatus = 'OFFLINE';
        return originUpdateCodeStatus(record);
    },
};

const entityCrudProps = defineEntityCrud(config);

const codeVisible = ref(false);
const selectedOriginCode = ref<TeaOriginCode | null>(null);

const historyVisible = ref(false);
const historyList = ref<TeaOriginCodeHistory[]>([]);

const historyDetailVisible = ref(false);
interface HistoryDetailData {
    teaEstate: string;
    teaOriginName: string;
    teaType: string;
    useCount: number;
    totalTeaWeight: number;
    teaOriginCodeHistoryList: any[];
}
const historyDetailData = ref<HistoryDetailData | null>(null);

const codeCanvas = ref<HTMLCanvasElement | null>(null);
const backgroundImage = getAssetsFileUrl('image/teaOriginCodeBg.png');

const handleHistory = async (row: TeaOriginCodeHistory) => {
    historyDetailVisible.value = true;
    const res = await originGetOriginCodeHistoryDetail({
        id: row.id,
    });
    historyDetailData.value = res as unknown as HistoryDetailData;
};

const historyListProps = defineSearchPage({
    service: async (params) => {
        const result: any = await originGetOriginCodeHistory({
            startTime: params.updateTime ? params.updateTime[0] : '',
            endTime: params.updateTime ? params.updateTime[1] : '',
            originCodeId: historyId.value,
        });
        return result;
    },
    formItems: [
        {
            is: 'date-picker',
            formItem: {
                prop: 'updateTime',
                label: '',
            },
            props: {
                type: 'daterange',
                startPlaceholder: '请选择开始时间',
                endPlaceholder: '请选择结束时间',
            },
        },
    ],
    tableColumns: {
        index: {},
        useCount: '用码次数',
        totalTeaWeight: '茶青总数(斤)',
        createTime: '操作时间',
        operations: {
            label: '操作',
            width: '90',
        },
    },
});

// 通用的画布绘制函数
const drawCanvas = async (
    canvas: HTMLCanvasElement,
    data: {
        companyName: string;
        uniscid: string;
        expireTime: string;
        code: string;
    },
) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 创建图片加载Promise
    const loadImage = () => {
        return new Promise((resolve) => {
            const img = new Image();
            img.src = backgroundImage;
            img.onload = () => resolve(img);
        });
    };

    // 等待背景图片加载
    const img = (await loadImage()) as HTMLImageElement;
    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

    // 绘制文字
    ctx.font = '56px Microsoft YaHei';
    ctx.fillStyle = '#000';
    ctx.textAlign = 'center';
    ctx.fillText(data.companyName, canvas.width / 2, 400);

    ctx.font = '44px Microsoft YaHei';
    ctx.fillStyle = '#666666';
    ctx.textAlign = 'center';
    ctx.fillText(data.uniscid, canvas.width / 2, 500);

    ctx.font = '32px Microsoft YaHei';
    ctx.fillStyle = '#A8ABB2';
    ctx.textAlign = 'center';
    ctx.fillText(data.expireTime, canvas.width / 2, 1360);

    // 生成并绘制二维码
    const qrCanvas = await QRCode.toCanvas(data.code, {
        width: 600,
        margin: 2,
        color: {
            dark: '#000000',
            light: '#ffffff',
        },
    });

    // 将二维码绘制到主画布上
    const x = (canvas.width - qrCanvas.width) / 2;
    const y = 640;
    ctx.drawImage(qrCanvas, x, y);
};

const generateAndDownloadCode = async (row: TeaOriginCode) => {
    const canvas = document.createElement('canvas');
    canvas.width = 1200;
    canvas.height = 1600;

    await drawCanvas(canvas, {
        companyName: row.teaSubjectName,
        uniscid: row.uniscid,
        expireTime: `产地码有效期：${row.expireTime ? row.expireTime : '-'}`,
        code: row.code,
    });

    // 下载图片
    canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `产地码_${row.teaOriginName}.png`;
        a.click();
        URL.revokeObjectURL(url);
    });
};

const initCanvas = async () => {
    const canvas = codeCanvas.value;
    if (!canvas) return;

    canvas.width = 1200;
    canvas.height = 1600;

    if (!selectedOriginCode.value) return;

    await drawCanvas(canvas, {
        companyName: selectedOriginCode.value.teaOriginName,
        uniscid: selectedOriginCode.value.uniscid,
        expireTime: `产地码有效期：${selectedOriginCode.value.expireTime ? selectedOriginCode.value.expireTime : '-'}`,
        code: selectedOriginCode.value.code,
    });
};

watch(codeVisible, (newVal) => {
    if (newVal) {
        // 使用 nextTick 确保 DOM 已更新
        nextTick(() => {
            initCanvas();
        });
    }
});

const downloadCode = () => {
    const canvas = codeCanvas.value;
    if (!canvas) return;
    canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'code.png';
        a.click();
    });
};
const handlePromiseFile = (row: TeaOriginCode) => {
    fetch(row.promiseFileUrl)
        .then((response) => response.blob())
        .then((blob) => {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '产地码申领承诺书.pdf';
            a.click();
            URL.revokeObjectURL(url);
        });
    window.open(row.promiseFileUrl, '_blank');
};
</script>
<template>
    <div>
        <div class="history-detail-wrapper">
            <el-dialog
                v-model="historyDetailVisible"
                :modal="true"
                :close-on-click-modal="true"
                :show-close="false"
                :append-to-body="true"
                title="查看详情"
                center
                width="800px"
            >
                <el-form :model="historyDetailData" label-width="100px" label-position="top">
                    <el-form-item label="茶园名称">
                        <el-input :value="historyDetailData?.teaEstate" disabled />
                    </el-form-item>
                    <el-form-item label="产地名称" prop="teaOriginName">
                        <el-input :value="historyDetailData?.teaOriginName" disabled />
                    </el-form-item>
                    <el-form-item label="茶叶品种" prop="teaType">
                        <el-input :value="historyDetailData?.teaType" disabled />
                    </el-form-item>
                    <el-form-item label="用码量（次）" prop="useCount">
                        <el-input :value="historyDetailData?.useCount" disabled />
                    </el-form-item>
                    <el-form-item label="茶青总数（斤）" prop="totalTeaWeight">
                        <el-input :value="historyDetailData?.totalTeaWeight" disabled />
                    </el-form-item>
                    <el-form-item label="产品规格说明" prop="teaOriginCodeHistoryList">
                        <el-table :data="historyDetailData?.teaOriginCodeHistoryList">
                            <el-table-column type="index" label="序号" width="100" />
                            <el-table-column prop="productName" label="贴标产品名称" />
                            <el-table-column
                                prop="productSpecs"
                                label="产品规格(净含量)"
                                width="200"
                            />
                            <el-table-column prop="codeCount" label="产地码数量" width="200" />
                        </el-table>
                    </el-form-item>
                </el-form>
                <div class="text-center pt-4">
                    <el-button size="large" @click="historyDetailVisible = false">关闭</el-button>
                </div>
            </el-dialog>
        </div>
        <div>
            <el-dialog
                v-model="codeVisible"
                :modal="true"
                :close-on-click-modal="true"
                :show-close="false"
                :append-to-body="true"
                class="code-dialog-wrapper"
                width="635px"
            >
                <canvas ref="codeCanvas" class="codeCanvas" />

                <div class="text-center pt-4">
                    <el-button
                        style="
                            font-size: 20px;
                            background: #5ca139;
                            color: #fff;
                            padding: 20px 40px;
                            border-radius: 4px;
                        "
                        @click="downloadCode"
                        >下载产地码</el-button
                    >
                </div>
            </el-dialog>
        </div>
        <div class="code-history-dialog-wrapper">
            <el-dialog
                v-model="historyVisible"
                :modal="true"
                :close-on-click-modal="true"
                :show-close="false"
                :append-to-body="true"
                width="800px"
                title="查看记录"
                center
            >
                <SearchPage v-bind="historyListProps">
                    <template #tableOperations="{ row }: { row: TeaOriginCodeHistory }">
                        <el-button link type="primary" @click="handleHistory(row)">详细</el-button>
                    </template>
                </SearchPage>
            </el-dialog>
        </div>
        <div>
            <EntityCrud v-bind="entityCrudProps">
                <template #tableCodeStatus="{ row }: { row: TeaOriginCode }">
                    <el-tag :type="row.codeStatus === 'OFFLINE' ? 'warning' : 'success'">
                        {{ OriginCodeStatusEnum[row.codeStatus] }}
                    </el-tag>
                </template>
                <template #tablePromiseFileUrl="{ row }: { row: TeaOriginCode }">
                    <el-button
                        v-if="row.promiseFileUrl"
                        link
                        type="primary"
                        @click="handlePromiseFile(row)"
                        >产地码申领承诺书</el-button
                    >
                    <div v-else>-</div>
                </template>
            </EntityCrud>
        </div>
    </div>
</template>

<style lang="less" scoped>
.codeCanvas {
    width: 600px;
    height: 800px;
    display: block;
    margin: 0 auto;
}
</style>
