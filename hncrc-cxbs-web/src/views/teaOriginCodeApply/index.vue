<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue';
import { ref, watch } from 'vue';

import {
    downLoadPromiseFile,
    originApplyOriginCode,
    originGetOriginApplyHistory,
    originGetOriginApplyHistoryDetail,
    originGetOriginCodePage,
    originGetOriginList,
    originUpdateOriginApply,
} from '@/api/originApply';
import { useEditableForm } from '@/components/EditableForm/hook';
import EditableForm from '@/components/EditableForm/index.vue';
import { defineEntityCrud } from '@/components/EntityCrud/hook';
import EntityCrud from '@/components/EntityCrud/index.vue';
import { EditableItem, EntityCrudProps } from '@/components/EntityCrud/type';
import { defineSearchPage } from '@/components/SearchPage/hook';
import SearchPage from '@/components/SearchPage/index.vue';
import { downloadBlobFile } from '@/utils/file';

const teaEstateList = ref<any[]>([]);
const originNameOptions = ref<{ label: string; value: string }[]>([]);
const teaEstateOptions = ref<{ label: string; value: string }[]>([]);
const selectedOriginName = ref<string>('');

const updateOriginNameOptions = () => {
    originNameOptions.value = teaEstateList.value.map((item, index) => ({
        label: item.originName,
        value: index.toString(),
    }));
    teaEstateOptions.value = [];
};

const updateTeaEstateOptions = async (selectedData: any) => {
    if (selectedData === undefined) {
        teaEstateOptions.value = [];
        return;
    }
    const result = await originGetOriginList();
    teaEstateOptions.value = result[selectedData].originList.map((item: any) => ({
        label: item.teaEstate ? item.teaEstate : '--',
        value: item.id,
    }));
};

watch(
    teaEstateList,
    () => {
        updateOriginNameOptions();
    },
    { immediate: true },
);

watch(selectedOriginName, (newValue) => {
    if (newValue !== undefined) {
        const index = parseInt(newValue, 10);
        const selectedData = teaEstateList.value[index];
        updateTeaEstateOptions(selectedData);
    }
});

const OriginCodeStatusEnum = {
    ONLINE: '使用中',
    OFFLINE: '已下线',
};

/** 产品规格 */
type ProductSpecification = {
    teaOriginCodeId: string;
    /** 贴标产品名称 */
    productName: string;
    /** 产品规格（净含量） */
    productSpecs: string;
    /** 产品码数量 */
    codeCount: number;
};

type TeaOriginCodeHistory = {
    id?: string; // 主键
    originCodeId: string; // 产地码id
    useCount: number; // 用码次数
    totalTeaWeight: number; // 茶青数（斤）
    createTime: string; // 操作时间
    teaOriginCodeHistoryList: ProductSpecification[];
};

// 定义实体类型
type TeaOriginCode = {
    codeId: string;
    id?: string;
    teaOriginId: string;
    teaOriginSubjectName: string;
    teaOriginName: string;
    teaEstate: string;
    teaType: string;
    code: string;
    codeNum: string;
    viewCount: number;
    useCount: number;
    totalTeaWeight: number;
    promiseFileKey: string;
    promiseFileUrl: string;
    teaOriginCodeHistoryList: ProductSpecification[];
    codeStatus: keyof typeof OriginCodeStatusEnum;
    creator: string;
    updater: string;
    createTime: string;
    updateTime: string;
};

const historyVisible = ref(false);
const historyId = ref('');
const historyListRef = ref(null);
watch(historyVisible, (newVal) => {
    if (newVal) {
        historyListRef.value?.refresh?.();
    }
});

/** 获取 产地码申领表单配置 */
const getTeaOriginCodeFormItems = (isUpdate: boolean = false): EditableItem<TeaOriginCode> => {
    return {
        codeId: {
            type: 'id',
        },
        teaOriginName: {
            type: 'select',
            label: '产地名称',
            options: originNameOptions.value,
            required: true,
            props: {
                disabled: isUpdate,
            },
        },
        teaEstate: {
            type: 'select',
            label: '茶园名称',
            options: teaEstateOptions.value,
            required: true,
            props: {
                disabled: isUpdate,
            },
        },
        teaType: {
            type: 'input',
            label: '茶叶品种',
            required: true,
            props: {
                disabled: isUpdate,
            },
        },
        useCount: {
            type: 'non-negative-number',
            label: '用码量',
            required: true,
        },
        totalTeaWeight: {
            type: 'non-negative-number',
            label: '茶青量(斤)',
            required: true,
        },
        teaOriginCodeHistoryList: '产品规格',
        promiseFileKey: {
            type: 'single-document',
            label: '附件',
            acceptType: '.pdf',
            tip: '上传附件,单个文件大小不超过20MB,最多可上传1个文件,且格式为 pdf',
        },
    };
};

const config: EntityCrudProps<TeaOriginCode> = {
    entityName: 'teaOriginCode',
    displayName: '产地码',
    filterFormItems: {
        originName: {
            type: 'input',
            label: '产地名称',
        },
    },
    operations: [
        {
            label: '产地申领承诺书',
            type: 'button',
            colorize: 'primary',
            actionService: async () => {
                const {
                    data,
                    fileName = '产地申领承诺书',
                    fileType = 'application/vnd.ms-excel',
                } = await downLoadPromiseFile();
                if (data) {
                    downloadBlobFile(data, fileName, fileType);
                }
            },
        },
    ],
    tableColumns: {
        teaOriginName: '产地名称',
        teaEstate: '茶园名称',
        teaType: '茶叶品种',
        viewCount: '扫码查看次数',
        useCount: '用码累计数',
        totalTeaWeight: '累计茶青总数(斤)',
        updateTime: '更新时间',
        codeStatus: '状态',
        operations: {
            label: '操作',
            width: '200',
        },
    },
    rowOperations: [
        {
            label: '历史记录',
            type: 'link',
            actionService: async (row) => {
                historyVisible.value = true;
                historyId.value = row.codeId;
            },
        },
    ],
    createFormItems: getTeaOriginCodeFormItems(false),
    listFetchService: async (params) => {
        const result: any = await originGetOriginCodePage(params);
        return Promise.resolve(result);
    },

    createButtonLabel: '申领产地码',
    createService: (newRecord: TeaOriginCode) => {
        newRecord.teaOriginCodeHistoryList = productSpecificationsData.value;
        newRecord.promiseFileKey = JSON.stringify(newRecord.promiseFileKey);
        return originApplyOriginCode({
            ...newRecord,
            originId: newRecord.teaEstate,
        });
    },

    canUpdate: (params: TeaOriginCode) => params.codeStatus === 'ONLINE',
    updateButtonLabel: '继续申报',
    useCreateFormItemsAsUpdate: false,
    updateFormItems: getTeaOriginCodeFormItems(true),
    updateService: async (params: TeaOriginCode) => {
        productSpecificationsData.value.map((item) => {
            item.teaOriginCodeId = params.codeId;
        });
        params.teaOriginCodeHistoryList = productSpecificationsData.value;
        params.promiseFileKey = JSON.stringify(params.promiseFileKey);
        return originUpdateOriginApply({
            ...params,
            originCodeId: params.codeId,
            originId: params.codeId,
        });
    },
};

const entityCrudProps = defineEntityCrud(config);

const productSpecificationsData = ref<ProductSpecification[]>([]);
const currentEditIndex = ref<number | null>(null);
const handleUpdateModelValue = (data: any, index: number | null) => {
    if (index !== null) {
        currentEditIndex.value = index;
        editEditable(productSpecificationsData.value[index]);
    } else {
        currentEditIndex.value = null;
        newEditable();
    }
};
const handleDelete = (index) => {
    productSpecificationsData.value = productSpecificationsData.value.filter((_, i) => i !== index);
};

const { formProps, newEditable, editEditable } = useEditableForm({
    addRequest: (data: ProductSpecification) => {
        if (!Array.isArray(productSpecificationsData.value)) {
            productSpecificationsData.value = [];
        }
        productSpecificationsData.value.push(data);
        return Promise.resolve();
    },
    editRequest: (data: ProductSpecification) => {
        if (currentEditIndex.value !== null) {
            productSpecificationsData.value[currentEditIndex.value] = data;
        }
        return Promise.resolve();
    },
    formItems: [
        {
            is: 'input',
            formItem: {
                label: '贴标产品名称',
                prop: 'productName',
                required: true,
            },
        },
        {
            is: 'input',
            formItem: {
                label: '产品规格（净含量）',
                prop: 'productSpecs',
                required: true,
            },
        },
        {
            is: 'input',
            formItem: {
                label: '产品码数量',
                prop: 'codeCount',
                rules: [
                    {
                        required: true,
                        message: '请输入产品码数量',
                    },
                    {
                        pattern: /^[0-9]*$/,
                        required: true,
                        message: '请输入合法的产品码数量',
                    },
                ],
            },
        },
    ],
});

/* const { formProps: continueDialogFormProps, showDialog: showContinueDialog } = useCreatableDialog({
    title: '继续申报',
    formItems: formItems,
    submitRequest: (data) => {
        return Promise.resolve();
    },
}); */

const handleHistory = async (row: TeaOriginCodeHistory) => {
    historyDetailVisible.value = true;
    const res = await originGetOriginApplyHistoryDetail({
        id: row.id,
    });
    historyDetailData.value = res;
};
const historyDetailVisible = ref(false);
const historyDetailData = ref<any>(null);

const historyListProps = defineSearchPage({
    service: async (params) => {
        const result: any = await originGetOriginApplyHistory({
            ...params,
            startTime: params.updateTime ? params.updateTime[0] : '',
            endTime: params.updateTime ? params.updateTime[1] : '',
            originCodeId: historyId.value,
        });
        return result;
    },
    formItems: [
        {
            is: 'date-picker',
            formItem: {
                prop: 'updateTime',
                label: '',
            },
            props: {
                type: 'daterange',
                startPlaceholder: '请选择开始时间',
                endPlaceholder: '请选择结束时间',
            },
        },
    ],
    tableColumns: {
        index: {},
        useCount: '用码次数',
        totalTeaWeight: '茶青总数(斤)',
        createTime: '操作时间',
        operations: {
            label: '操作',
            width: '90',
        },
    },
});

const getTeaEstateList = async () => {
    try {
        const result = await originGetOriginList();
        if (result && Array.isArray(result)) {
            teaEstateList.value = result.map((item: any) => ({
                originName: item.originName,
                taeEstate: item,
            }));
        } else {
            teaEstateList.value = [];
        }
    } catch (error) {
        console.error('获取茶园列表失败:', error);
        teaEstateList.value = [];
    }
};
const handleDownLoadPromiseFile = async () => {
    const {
        data,
        fileName = '产地申领承诺书',
        fileType = 'application/vnd.ms-excel',
    } = await downLoadPromiseFile();
    if (data) {
        downloadBlobFile(data, fileName, fileType);
    }
};

getTeaEstateList();
</script>

<template>
    <div>
        <div class="history-detail-wrapper">
            <el-dialog
                v-model="historyDetailVisible"
                :modal="true"
                :close-on-click-modal="true"
                :show-close="false"
                :append-to-body="true"
                title="查看详情"
                center
                width="800px"
            >
                <el-form :model="historyDetailData" label-width="100px" label-position="top">
                    <el-form-item label="茶园名称">
                        <el-input :value="historyDetailData?.teaEstate" disabled />
                    </el-form-item>
                    <el-form-item label="产地名称" prop="teaOriginName">
                        <el-input :value="historyDetailData?.teaOriginName" disabled />
                    </el-form-item>
                    <el-form-item label="茶叶品种" prop="teaType">
                        <el-input :value="historyDetailData?.teaType" disabled />
                    </el-form-item>
                    <el-form-item label="用码量（次）" prop="useCount">
                        <el-input :value="historyDetailData?.useCount" disabled />
                    </el-form-item>
                    <el-form-item label="茶青总数（斤）" prop="totalTeaWeight">
                        <el-input :value="historyDetailData?.totalTeaWeight" disabled />
                    </el-form-item>
                    <el-form-item label="产品规格说明" prop="teaOriginCodeHistoryList">
                        <el-table :data="historyDetailData?.teaOriginCodeHistoryList">
                            <el-table-column type="index" label="序号" width="100" />
                            <el-table-column prop="productName" label="贴标产品名称" />
                            <el-table-column
                                prop="productSpecs"
                                label="产品规格(净含量)"
                                width="200"
                            />
                            <el-table-column prop="codeCount" label="产地码数量" width="200" />
                        </el-table>
                    </el-form-item>
                </el-form>
                <div class="text-center pt-4">
                    <el-button size="large" @click="historyDetailVisible = false">关闭</el-button>
                </div>
            </el-dialog>
        </div>
        <div class="code-history-dialog-wrapper">
            <el-dialog
                v-model="historyVisible"
                :modal="true"
                :close-on-click-modal="true"
                :show-close="false"
                :append-to-body="true"
                width="800px"
                title="查看记录"
                center
            >
                <SearchPage v-bind="historyListProps" ref="historyListRef">
                    <template #tableOperations="{ row }: { row: TeaOriginCodeHistory }">
                        <el-button link type="primary" @click="handleHistory(row)">详细</el-button>
                    </template>
                </SearchPage>
            </el-dialog>
        </div>

        <EditableForm v-bind="formProps" />

        <EntityCrud
            v-bind="entityCrudProps"
            @create-form-change="
                (values) => {
                    if (values.teaOriginName !== undefined) {
                        const index = parseInt(values.teaOriginName, 10);
                        const selectedData = teaEstateList[index];
                        updateTeaEstateOptions(selectedData);
                    }
                }
            "
        >
            <template #teaOriginName="{ formData, props }">
                {{ console.log(999, props, formData) }}
                <el-select
                    v-model="formData.teaOriginName"
                    placeholder="请选择产地名称"
                    :disabled="props?.disabled"
                    @change="
                        (value) => {
                            formData.teaEstate = ''; // 清空茶园名称
                            const index = Number(value);
                            const selectedData = index;
                            updateTeaEstateOptions(selectedData);
                        }
                    "
                >
                    <el-option
                        v-for="option in originNameOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                    />
                </el-select>
            </template>
            <template #teaEstate="{ formData, props }">
                <el-select
                    v-model="formData.teaEstate"
                    placeholder="请选择茶园名称"
                    :disabled="props?.disabled"
                >
                    <el-option
                        v-for="option in teaEstateOptions"
                        :key="option.label"
                        :label="option.label"
                        :value="option.value"
                    />
                </el-select>
            </template>
            <template #teaType="{ formData, props }">
                <el-input
                    v-model="formData.teaType"
                    :disabled="props?.disabled"
                    placeholder="请输入茶叶品种"
                />
            </template>
            <template #tableCodeStatus="{ row }: { row: TeaOriginCode }">
                <el-tag :type="row.codeStatus === 'OFFLINE' ? 'warning' : 'success'">
                    {{ OriginCodeStatusEnum[row.codeStatus] }}
                </el-tag>
            </template>
            <template #teaOriginCodeHistoryList="data">
                <div class="w-full">
                    <el-table :fit="true" class="w-full" :data="productSpecificationsData">
                        <el-table-column type="index" label="序号" width="100" />
                        <el-table-column prop="productName" label="贴标产品名称" width="200" />
                        <el-table-column
                            prop="productSpecs"
                            label="产品规格（净含量）"
                            width="200"
                        />
                        <el-table-column prop="codeCount" label="产品码数量" />
                        <el-table-column label="操作" width="120">
                            <template #default="{ $index }">
                                <el-button
                                    link
                                    type="primary"
                                    @click="() => handleUpdateModelValue(data, $index)"
                                    >编辑</el-button
                                >
                                <el-button link type="danger" @click="() => handleDelete($index)"
                                    >删除</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="w-full">
                    <el-button
                        type="default"
                        class="w-full"
                        :icon="Plus"
                        @click="() => handleUpdateModelValue(null, null)"
                    >
                        新增
                    </el-button>
                </div>

                <div class="mt-4">
                    产地码申领承诺书：<el-button
                        type="primary"
                        link
                        @click="handleDownLoadPromiseFile"
                        >下载模板</el-button
                    >
                </div>
            </template>
        </EntityCrud>
    </div>
</template>
