<script setup lang="ts">
import { ElMessage } from 'element-plus';
import { computed, ref } from 'vue';

import {
    originCreateOrigin,
    originDeleteOriginById,
    originGetApplyCountPage,
    originGetOriginById,
    originGetOriginPage,
    originUpdateOrigin,
} from '@/api/origin';
import { defineEntityCrud } from '@/components/EntityCrud/hook';
import EntityCrud from '@/components/EntityCrud/index.vue';
import { EntityCrudProps } from '@/components/EntityCrud/type';
import ImageUpload from '@/components/ImageUpload/index.vue';
import { defineSearchPage } from '@/components/SearchPage/hook';
import SearchPage from '@/components/SearchPage/index.vue';
import { getSubjectTypeLabel } from '@/dicts/SubjectTypeDicts';

const entityCrudRef = ref<any>(null);
const OriginStatusEnum = {
    ONLINE: '使用中',
    OFFLINE: '已下线',
};

// 定义茶叶产地实体类型
type TeaOrigin = {
    id?: string;
    originName: string;
    teaType: string;
    teaEstate: string;
    estateArea: number;
    teaYouthYield: number;
    contactPhone: string;
    originAddress: string;
    originIntroduction: string;
    originPic: string;
    originStatus: keyof typeof OriginStatusEnum;
    creator: string;
    updater: string;
    createTime: string;
    updateTime: string;
    //被申领次数
    applyCount: number;
};

interface ApplyCount {
    subjectName: string;
    subjectType: string;
    creditCode: string;
    createTime: string;
}

const applyCountListProps = defineSearchPage<ApplyCount>({
    tableColumns: {
        index: {},
        subjectName: '产地主体名称',
        subjectType: '产地主体类型',
        uniscid: '产地主体标识',
        createTime: '申请时间',
    },
    service: async (params) =>
        await originGetApplyCountPage({ ...params, originId: applyDetailData.value?.id }).then(
            (res) => {
                res.list.forEach((item) => {
                    item.subjectType = getSubjectTypeLabel(item.subjectType);
                });
                console.log(res);
                return res;
            },
        ),
});

const editDialogVisible = ref(false);
const editData = ref<TeaOrigin | null>(null);
const editFormRef = ref<any>(null);

const rules = {
    originName: [{ required: true, message: '请输入产地名称', trigger: 'blur' }],
    teaEstate: [{ required: true, message: '请输入茶园名称', trigger: 'blur' }],
    originAddress: [{ required: true, message: '请输入产地地址', trigger: 'blur' }],
    originPic: [{ required: true, message: '请上传产地图片', trigger: 'change' }],
};

const config: EntityCrudProps<TeaOrigin> = {
    entityName: 'teaOrigin',
    displayName: '茶叶产地',
    filterFormItems: {
        originName: {
            type: 'input',
            label: '产地名称',
        },
    },
    tableColumns: {
        id: '产地编码',
        originName: '产地名称',
        teaEstate: '茶园名称',
        teaType: '种植品种',
        applyCount: '被申领次数',
        updateTime: '更新时间',
        originStatus: '状态',
    },
    createFormItems: {
        id: {
            type: 'id',
            label: 'id',
        },
        originName: {
            type: 'input',
            label: '产地名称',
            required: true,
        },
        teaType: {
            type: 'input',
            label: '种植品种',
        },
        teaEstate: {
            type: 'input',
            label: '茶园名称',
        },
        estateArea: {
            type: 'number',
            label: '茶园面积(亩)',
        },
        teaYouthYield: {
            type: 'number',
            label: '茶青产量(吨)',
        },
        contactPhone: {
            type: 'input',
            label: '联系电话',
        },
        originAddress: {
            type: 'input',
            label: '经营地址',
            required: true,
        },
        originIntroduction: {
            type: 'textarea',
            label: '茶园简介',
        },
        originPic: {
            type: 'single-image',
            label: '产地图片',
            required: true,
            tip: '上传图片，大小不超过2MB',
        },
    },

    // 这里需要替换为实际的API服务
    listFetchService: async (params) => {
        const result: any = await originGetOriginPage(params);
        return Promise.resolve(result);
    },
    createButtonLabel: '新增产地',
    createService: (newRecord: TeaOrigin) => {
        newRecord.originPic = JSON.stringify(newRecord.originPic);
        return originCreateOrigin(newRecord);
    },

    publishButtonLabel: '上线',
    canPublish: (row) => row.originStatus !== 'ONLINE',
    publishService: async (record: TeaOrigin) => {
        const res = await originGetOriginById(record.id);
        res.originStatus = 'ONLINE';
        return originUpdateOrigin(res);
    },
    unpublishButtonLabel: '下线',
    canUnpublish: (row) => row.originStatus === 'ONLINE',
    unpublishService: async (record: TeaOrigin) => {
        const res = await originGetOriginById(record.id);
        res.originStatus = 'OFFLINE';
        return originUpdateOrigin(res);
    },
    deleteService: (row: TeaOrigin) => {
        return originDeleteOriginById(row.id);
    },
    rowOperations: [
        {
            type: 'link',
            label: '编辑',
            colorize: () => 'primary',
            displayIndex: -1,
            actionService: async (row) => {
                const res = await originGetOriginById(row.id);
                editData.value = res;
                editDialogVisible.value = true;
                return Promise.resolve();
            },
            canDisplay: () => true,
        },
    ],
};
const applyDetailVisible = ref(false);
const applyDetailData = ref<any>({});
const handleApply = (row: TeaOrigin) => {
    applyDetailVisible.value = true;
    applyDetailData.value = row;
};
const entityCrudProps = defineEntityCrud(config);

const handleEdit = async () => {
    if (!editData.value) return;
    try {
        await editFormRef.value?.validate();
        const dataToUpdate = { ...editData.value };
        if (typeof dataToUpdate.originPic === 'object') {
            dataToUpdate.originPic = JSON.stringify(dataToUpdate.originPic);
        }
        await originUpdateOrigin(dataToUpdate);
        editDialogVisible.value = false;
        ElMessage.success('操作成功');
        entityCrudRef.value.handleRefresh();
    } catch (error) {
        if (error === false) {
            ElMessage.error('请填写必填项');
            return;
        }
        console.error('更新失败:', error);
    }
};

const beforeAvatarUpload = (file: File) => {
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
        ElMessage.error('上传图片大小不能超过 2MB!');
    }
    return isLt2M;
};
const getOriginPic = (originPic: string) => {
    try {
        return JSON.parse(originPic);
    } catch (e) {
        return null;
    }
};

const uploadModelValue = computed({
    get: () => {
        if (!editData.value?.originPic) return null;
        return getOriginPic(editData.value.originPic);
    },
    set: (val: any) => {
        if (editData.value) {
            editData.value.originPic = val;
        }
    },
});
</script>

<template>
    <div class="tea-origin-container">
        <div class="entity-crud-wrapper">
            <EntityCrud v-bind="entityCrudProps" ref="entityCrudRef">
                <template #tableOriginStatus="{ row }: { row: TeaOrigin }">
                    <el-tag :type="row.originStatus === 'OFFLINE' ? 'warning' : 'success'">
                        {{ OriginStatusEnum[row.originStatus] }}
                    </el-tag>
                </template>
                <template #tableApplyCount="{ row }: { row: TeaOrigin }">
                    <el-button type="primary" link @click="handleApply(row)">{{
                        row.applyCount || 0
                    }}</el-button>
                </template>
            </EntityCrud>
        </div>

        <el-dialog
            v-model="applyDetailVisible"
            :modal="true"
            :close-on-click-modal="true"
            :show-close="false"
            :append-to-body="true"
            title="产地申领记录"
            center
        >
            <div class="text-sm font-bold mb-5">
                产地名称：{{ applyDetailData.originName }} &nbsp;&nbsp;&nbsp;&nbsp;被申领次数：{{
                    applyDetailData.applyCount || 0
                }}
            </div>
            <SearchPage v-bind="applyCountListProps" />
        </el-dialog>

        <el-dialog
            v-model="editDialogVisible"
            :title="'编辑' + config.displayName"
            :width="800"
            destroy-on-close
        >
            <div class="edit-form-wrapper">
                <el-form
                    v-if="editData"
                    ref="editFormRef"
                    :model="editData"
                    :rules="rules"
                    label-position="top"
                >
                    <el-form-item label="产地名称" required prop="originName">
                        <el-input v-model="editData.originName" placeholder="请输入产地名称" />
                    </el-form-item>
                    <el-form-item label="茶叶类型">
                        <el-input v-model="editData.teaType" placeholder="请输入茶叶类型" />
                    </el-form-item>
                    <el-form-item label="茶园名称" required prop="teaEstate">
                        <el-input v-model="editData.teaEstate" placeholder="请输入茶园名称" />
                    </el-form-item>
                    <el-form-item label="茶园面积(亩)">
                        <el-input
                            v-model="editData.estateArea"
                            placeholder="请输入茶园面积"
                            type="number"
                            :min="0"
                            style="width: 100%"
                        />
                    </el-form-item>
                    <el-form-item label="茶青产量(吨)">
                        <el-input
                            v-model="editData.teaYouthYield"
                            placeholder="请输入茶青产量"
                            type="number"
                            :min="0"
                            style="width: 100%"
                        />
                    </el-form-item>
                    <el-form-item label="联系电话">
                        <el-input v-model="editData.contactPhone" placeholder="请输入联系电话" />
                    </el-form-item>
                    <el-form-item label="产地地址" required prop="originAddress">
                        <el-input v-model="editData.originAddress" placeholder="请输入产地地址" />
                    </el-form-item>
                    <el-form-item label="茶园简介">
                        <el-input
                            v-model="editData.originIntroduction"
                            type="textarea"
                            placeholder="请输入茶园简介"
                            :rows="4"
                        />
                    </el-form-item>
                    <el-form-item label="产地图片" required prop="originPic">
                        <div class="image-upload-wrapper">
                            <ImageUpload
                                v-model="uploadModelValue"
                                :before-upload="beforeAvatarUpload"
                                :max-file-count="1"
                            />
                            <!-- <div class="el-upload__tip">上传图片，大小不超过2MB</div> -->
                        </div>
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleEdit">确定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style scoped>
.tea-origin-container {
    width: 100%;
}

.entity-crud-wrapper,
.edit-form-wrapper {
    width: 100%;
}

.image-upload-wrapper {
    width: 100%;
}

.el-upload__tip {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
}
</style>
