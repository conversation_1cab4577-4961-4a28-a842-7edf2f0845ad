<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue';
import { ref } from 'vue';

import {
    seedlingAddApi,
    seedlingDeleteApi,
    seedlingDetailApi,
    seedlingPageApi,
    seedlingUpdateApi,
} from '@/api/seedling';
import { useEditableForm } from '@/components/EditableForm/hook';
import EditableForm from '@/components/EditableForm/index.vue';
import { defineEntityCrud } from '@/components/EntityCrud/hook';
import EntityCrud from '@/components/EntityCrud/index.vue';
import { EntityCrudProps } from '@/components/EntityCrud/type';
/* import { getEnumOptions } from '@/components/EntityCrud/util'; */
import { contentProcess } from '@/utils/RichTextUtil';

const SeedlingStatusEnum = {
    ONLINE: '使用中',
    OFFLINE: '已下架',
};

/** 相关政策 */
type RelatedPolicy = {
    /** 政策名称 */
    policyName: string;
    /** 政策内容 */
    policyDetail: string;
    /** 政策附件 */
    policyAttachments: string | string[];
};

type Seedling = {
    id?: string;
    seedlingCategory: string;
    seedlingVariety: string;
    seedlingSpecs: string;
    seedlingAdvantages: string;
    extraInfo: string;
    seedlingStatus: keyof typeof SeedlingStatusEnum;
    relatedPolicy: number;
    relatedPolicyListJson: string;
    policyList: RelatedPolicy[];
    seedlingPictureJson: string;
};

const config: EntityCrudProps<Seedling> = {
    entityName: 'seedling',
    displayName: '苗木信息',
    filterFormItems: {
        seedlingCategory: {
            type: 'input',
            label: '苗木种类',
        },
        seedlingVariety: {
            type: 'input',
            label: '苗木品种',
        },
    },
    operations: [
        {
            overwriteInternalType: 'create',
            type: 'button',
            label: '新增苗木',
            colorize: 'primary',
            actionService: (params, internalService) => {
                relatedPolicyListData.value = [];
                internalService?.(params);
                return Promise.resolve();
            },
        },
    ],
    tableColumns: {
        seedlingCategory: {
            label: '苗木种类',
        },
        seedlingVariety: {
            label: '苗木品种',
            formatter: (row) => (row.seedlingVariety ? row.seedlingVariety : '-'),
        },
        seedlingSpecs: {
            label: '规格情况',
            formatter: (row) => (row.seedlingSpecs ? row.seedlingSpecs : '-'),
        },
        seedlingAdvantages: {
            label: '特点优势',
            formatter: (row) => (row.seedlingAdvantages ? row.seedlingAdvantages : '-'),
        },
        relatedPolicy: {
            label: '相关政策(条)',
        },
        seedlingStatus: {
            label: '状态',
            formatter: (row) => SeedlingStatusEnum[row.seedlingStatus],
        },
        operations: {
            label: '操作',
            width: 250,
        },
    },
    createFormItems: {
        id: {
            type: 'id',
        },
        seedlingCategory: {
            type: 'input',
            label: '苗木种类',
            required: true,
        },
        seedlingVariety: {
            type: 'input',
            label: '苗木品种',
        },
        seedlingSpecs: {
            type: 'textarea',
            label: '规格情况',
            props: {
                autosize: { minRows: 5, maxRows: 10 },
                maxlength: 500,
                showWordLimit: true,
            },
        },
        seedlingAdvantages: {
            type: 'textarea',
            label: '特点优势',
            props: {
                autosize: { minRows: 5, maxRows: 10 },
                maxlength: 500,
                showWordLimit: true,
            },
        },
        seedlingPictureJson: {
            type: 'single-image',
            label: '苗木图片',
            required: true,
            tip: '单个图片大小不超过2MB，建议上传16:9比例的照片最多可上传1张图片,且格式为 jpg、png',
            acceptType: '.jpg,.png',
        },
        /*  extraInfo: {
            type: 'textarea',
            label: '其他信息',
        }, */
        /* seedlingStatus: {
            type: 'select',
            label: '苗木状态',
            options: getEnumOptions(SeedlingStatusEnum),
            required: true,
        }, */
        /*  relatedPolicy: {
            type: 'number',
            label: '相关政策(条)',
        }, */
        /*    relatedPolicyListJson: {
            type: 'select',
            label: '相关政策列表',
            options: [], // 这里要要从后端获取政策列表
        }, */
        policyList: '相关政策',
    },
    rowOperations: [
        {
            overwriteInternalType: 'update',
            type: 'link',
            label: '编辑',
            actionService: (row, internalService) => {
                relatedPolicyListData.value = JSON.parse(row.relatedPolicyListJson);
                internalService?.(row);
                return Promise.resolve();
            },
        },
    ],
    listFetchService: async (params) => {
        const result: any = await seedlingPageApi(params);
        result.list.forEach((item) => {
            item.seedlingPictureJson = JSON.parse(item.seedlingPictureJson);
        });
        return Promise.resolve(result);
    },
    createService: (newRecord: Seedling) => {
        let record = { ...newRecord };
        record.policyList = relatedPolicyListData.value;
        record.seedlingPictureJson = JSON.stringify(record.seedlingPictureJson);
        return seedlingAddApi(record);
    },

    useCreateFormItemsAsUpdate: true,
    updateService: (updateRecord: Seedling) => {
        let record = { ...updateRecord };
        record.policyList = relatedPolicyListData.value;
        record.seedlingPictureJson = JSON.stringify(record.seedlingPictureJson);
        return seedlingUpdateApi(record);
    },
    publishButtonLabel: '上线',
    canPublish: (row) => row.seedlingStatus !== 'ONLINE',
    publishService: (record: Seedling) => {
        record.seedlingStatus = 'ONLINE';
        relatedPolicyListData.value.forEach((item) => {
            item.policyDetail = contentProcess.encode(item.policyDetail);
        });
        record.policyList = relatedPolicyListData.value;
        record.seedlingPictureJson = record.seedlingPictureJson
            ? JSON.stringify(record.seedlingPictureJson)
            : null;
        return seedlingUpdateApi(record);
    },

    unpublishButtonLabel: '下线',
    canUnpublish: (row) => row.seedlingStatus === 'ONLINE',
    unpublishService: (record: Seedling) => {
        relatedPolicyListData.value.forEach((item) => {
            item.policyDetail = contentProcess.encode(item.policyDetail);
        });
        record.policyList = relatedPolicyListData.value;
        record.seedlingPictureJson = record.seedlingPictureJson
            ? JSON.stringify(record.seedlingPictureJson)
            : null;
        record.seedlingStatus = 'OFFLINE';
        return seedlingUpdateApi(record);
    },

    useCreateFormItemsAsDetail: true,
    detailFetchService: (row) => seedlingDetailApi(row.id),
    deleteService: (row: Seedling) => {
        return seedlingDeleteApi(row.id);
    },
};
const relatedPolicyListData = ref<RelatedPolicy[]>([]);
const currentEditIndex = ref<number | null>(null);
const { formProps, newEditable, editEditable } = useEditableForm({
    addRequest: (data: RelatedPolicy) => {
        if (!Array.isArray(relatedPolicyListData.value)) {
            relatedPolicyListData.value = [];
        }
        data.policyDetail = contentProcess.encode(data.policyDetail);
        data.policyAttachments = data.policyAttachments || [];
        relatedPolicyListData.value.push(data);
        return Promise.resolve();
    },
    editRequest: (data: RelatedPolicy) => {
        if (currentEditIndex.value !== null) {
            data.policyDetail = contentProcess.encode(data.policyDetail);
            relatedPolicyListData.value[currentEditIndex.value] = data;
        }
        return Promise.resolve();
    },
    formItems: [
        {
            is: 'input',
            formItem: {
                label: '政策名称',
                prop: 'policyName',
                required: true,
            },
            props: {
                placeholder: '请输入政策名称',
            },
        },
        {
            is: 'editor',
            formItem: {
                label: '政策内容',
                prop: 'policyDetail',
                required: true,
            },
            props: {
                placeholder: '请输入政策内容',
            },
        },
        {
            is: 'document-upload',
            formItem: {
                label: '政策附件',
                prop: 'policyAttachments',
            },
            props: {
                maxFileCount: 10,
                maxSize: 20,
                tip: '最多上传10个文件，单个文件大小不超过20MB，支持 image/*、xls、xlsx、pdf、doc、docx 格式',
            },
        },
    ],
    width: '820px',
});
const handleUpdateModelValue = (data: any, index: number | null) => {
    if (index !== null) {
        currentEditIndex.value = index;
        const res = relatedPolicyListData.value[index];
        res.policyDetail = contentProcess.decode(res.policyDetail);
        editEditable(res);
    } else {
        currentEditIndex.value = null;
        newEditable();
    }
};
const handleDelete = (index) => {
    relatedPolicyListData.value = relatedPolicyListData.value.filter((_, i) => i !== index);
};
const entityCrudProps = defineEntityCrud(config);
</script>

<template>
    <div>
        <EditableForm v-bind="formProps" />
        <EntityCrud v-bind="entityCrudProps">
            <template #tableSeedlingStatus="{ row }: { row: Seedling }">
                <el-tag v-if="row.seedlingStatus === 'ONLINE'" type="success">使用中</el-tag>
                <el-tag v-else type="warning">已下线</el-tag>
            </template>
            <template #policyList="data">
                <div class="w-full">
                    <el-table :fit="true" class="w-full" :data="relatedPolicyListData">
                        <el-table-column prop="policyName" label="政策名称" />
                        <!--    <el-table-column prop="policyContent" label="政策内容" width="200" />
                        <el-table-column prop="policyFileList" label="政策附件" /> -->
                        <el-table-column label="操作" width="150">
                            <template #default="{ $index }">
                                <el-button
                                    link
                                    type="primary"
                                    @click="() => handleUpdateModelValue(data, $index)"
                                    >编辑</el-button
                                >
                                <el-button link type="danger" @click="() => handleDelete($index)"
                                    >删除</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="w-full">
                    <el-button
                        type="default"
                        class="w-full"
                        :icon="Plus"
                        @click="() => handleUpdateModelValue(null, null)"
                    >
                        新增
                    </el-button>
                </div>
            </template>
        </EntityCrud>
    </div>
</template>
