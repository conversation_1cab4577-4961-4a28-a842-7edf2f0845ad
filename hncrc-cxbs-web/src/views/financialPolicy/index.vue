<script setup lang="ts">
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';

import { addData, deleteById, getList, updateData } from '@/api/financialPolicy';
import { defineEntityCrud } from '@/components/EntityCrud/hook';
import EntityCrud from '@/components/EntityCrud/index.vue';
import { EntityCrudProps } from '@/components/EntityCrud/type';
import { contentProcess } from '@/utils/RichTextUtil';

const router = useRouter();

const PolicyStatusEnum = {
    ONLINE: '使用中',
    OFFLINE: '已下线',
};

interface FileItem {
    key: string;
    url: string;
}

type FinancialPolicy = {
    id?: string;
    policyName: string;
    policyContent: string;
    policyStatus: keyof typeof PolicyStatusEnum;
    policyFileUrl: string;
    createTime?: Date;
    updateTime?: Date;
};

const config: EntityCrudProps<FinancialPolicy> = {
    entityName: 'financialPolicy',
    displayName: '金融政策',
    filterFormItems: {
        policyName: {
            type: 'input',
            label: '政策名称',
        },
    },
    createButtonLabel: '新增政策',
    tableColumns: {
        policyName: '政策名称',
        createTime: {
            label: '创建时间',
            formatter: (row) => dayjs(row.createTime).format('YYYY-MM-DD'),
        },
        updateTime: {
            label: '更新时间',
            formatter: (row) => dayjs(row.updateTime).format('YYYY-MM-DD'),
        },
        policyStatus: {
            label: '状态',
            formatter: (row) => PolicyStatusEnum[row.policyStatus],
        },
        operations: {
            label: '操作',
            width: '230px',
        },
    },
    createFormItems: {
        id: {
            type: 'id',
        },
        policyName: {
            type: 'input',
            label: '政策名称',
            required: true,
        },
        policyContent: {
            type: 'editor',
            label: '政策内容描述',
            required: true,
        },
        /*      policyStatus: {
            type: 'select',
            label: '政策状态',
            options: getEnumOptions(PolicyStatusEnum),
            required: true,
        }, */
        policyFileUrl: {
            type: 'multiple-document',
            label: '政策附件',
            maxFileCount: 10,
            tip: '上传附件,单个文件大小不超过20MB最多可上传 10 个文件,且格式为image/*、xls、xlsx、pdf、doc、 docx',
        },
    },
    listFetchService: async (params) => {
        const result: any = await getList(params);
        result.list.forEach((item) => {
            item.policyFileUrl = JSON.parse(item.policyFileUrl);
            item.policyContent = contentProcess.decode(item.policyContent);
        });
        return Promise.resolve(result);
    },
    createService: (newRecord: FinancialPolicy) => {
        newRecord.policyFileUrl = JSON.stringify(newRecord.policyFileUrl);
        newRecord.policyContent = contentProcess.encode(newRecord.policyContent);
        return addData(newRecord);
    },

    useCreateFormItemsAsUpdate: true,
    updateService: (updateRecord: FinancialPolicy) => {
        updateRecord.policyFileUrl = JSON.stringify(updateRecord.policyFileUrl);
        updateRecord.policyContent = contentProcess.encode(updateRecord.policyContent);
        return updateData(updateRecord);
    },

    publishButtonLabel: '上线',
    canPublish: (row) => row.policyStatus !== 'ONLINE',
    publishService: (record: FinancialPolicy) => {
        record.policyStatus = 'ONLINE';
        record.policyFileUrl = JSON.stringify(record.policyFileUrl);
        record.policyContent = contentProcess.encode(record.policyContent);
        return updateData(record);
    },

    unpublishButtonLabel: '下线',
    canUnpublish: (row) => row.policyStatus === 'ONLINE',
    unpublishService: (record: FinancialPolicy) => {
        record.policyStatus = 'OFFLINE';
        record.policyFileUrl = JSON.stringify(record.policyFileUrl);
        record.policyContent = contentProcess.encode(record.policyContent);
        return updateData(record);
    },

    deleteService: (record: FinancialPolicy) => deleteById(record?.id),

    /** 自定义操作列按钮 */
    rowOperations: [
        {
            /** 操作类型 */
            type: 'link',
            /** 操作按钮文本 */
            label: '详情',
            /** 按钮样式计算函数 */
            colorize: () => 'primary',
            /** 操作执行函数 */
            actionService: async (params) => {
                router.push('/welcome/supportNotice' + '?id=' + params.id);
            },
            /** 是否显示 */
            canDisplay: () => true,
            displayIndex: -1,
        },
    ],
};

const entityCrudProps = defineEntityCrud(config);
</script>

<template>
    <div>
        <EntityCrud v-bind="entityCrudProps">
            <template #tablePolicyStatus="{ row }: { row: FinancialPolicy }">
                <el-tag v-if="row.policyStatus === 'ONLINE'" type="success">使用中</el-tag>
                <el-tag v-else type="warning">已下线</el-tag>
            </template>
        </EntityCrud>
    </div>
</template>
