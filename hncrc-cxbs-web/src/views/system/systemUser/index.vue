<template>
    <div>
        <ListContainer title="用户管理">
            <template #searchForm>
                <el-form
                    ref="queryFormRef"
                    :inline="true"
                    :model="queryForm"
                    class="demo-form-inline"
                    @submit.prevent
                >
                    <el-form-item label="用户状态" prop="status">
                        <el-select v-model="queryForm.status" clearable class="w-240">
                            <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="用户账号" prop="userAccount">
                        <el-input
                            v-model.trim="queryForm.userAccount"
                            placeholder="用户账号"
                            clearable
                            class="w-240"
                            maxlength="100"
                        />
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" native-type="submit" @click="changeParam"
                            >筛选</el-button
                        >
                        <el-button @click="resetForm(queryFormRef)">重置</el-button>
                    </el-form-item>
                </el-form>
            </template>
            <el-button type="primary" style="height: 32px; line-height: 10px" @click="add"
                >新增</el-button
            >
            <el-button
                style="height: 32px; line-height: 10px"
                :disabled="checkIds.length === 0"
                @click="closeUserState"
                >停用</el-button
            >
            <el-button
                style="height: 32px; line-height: 10px"
                :disabled="checkIds.length === 0"
                @click="openUserState"
                >启用</el-button
            >
            <el-button
                type="danger"
                style="height: 32px; line-height: 10px"
                :disabled="checkIds.length === 0"
                @click="delUsers"
                >删除</el-button
            >
        </ListContainer>
        <div class="table-wrapper">
            <CommonTable
                ref="table"
                :table-data="list"
                :table-columns="tableColumns"
                :total="total"
                :loading="loading"
                :is-show-toolbar="false"
                :is-show-selection="true"
                :is-show-selection-info="true"
                :current-page="queryForm.pageNo"
                :page-size="queryForm.pageSize"
                :selectable="selectable"
                @selection-change="handleSelectionChange"
                @pageChange="
                    async (page) => {
                        queryForm.pageNo = page.pageNum;
                        queryForm.pageSize = page.pageSize;
                        await getList();
                    }
                "
            >
                <template #status="{ scope }">
                    <span v-html="userStateFilter(scope.row.status)" />
                </template>

                <template #operation="{ scope }">
                    <el-button
                        v-if="scope.row.roles.includes('茶叶产地主体用户')"
                        text
                        type="primary"
                        @click.stop="updateMainInfo(scope.row)"
                        >更新主体信息</el-button
                    >

                    <el-button text type="primary" @click.stop="edit(scope.row)">修改</el-button>
                    <el-button text type="primary" @click.stop="UserPermissions(scope.row)"
                        >分配角色</el-button
                    >
                    <el-button text type="primary" @click.stop="viewPermissions(scope.row)"
                        >查看权限</el-button
                    >
                </template>
            </CommonTable>
        </div>
        <SysAddUser ref="sysAddUserRef" @success="getList" />
        <UserPermissionsDialog ref="userPermissionsRef" @success="getList" />
        <UserViewPermissionDialog ref="userViewPermissionsRef" />
        <UserUpdateInfoDialog ref="userUpdateInfoRef" @success="getList" />
        <UserUpdateTeaSubjectDialog ref="userUpdateTeaSubjectRef" @success="getList" />
    </div>
</template>
<script setup lang="ts">
import type { FormInstance } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

import { originDeleteSubject } from '@/api/origin';
import * as UserApi from '@/api/system/user';
import SysAddUser from '@/components/biz/dialogs/sysAddUser.vue';
import UserPermissionsDialog from '@/components/biz/dialogs/UserPermissionsDialog.vue';
import UserUpdateInfoDialog from '@/components/biz/dialogs/UserUpdateInfoDialog.vue';
import UserUpdateTeaSubjectDialog from '@/components/biz/dialogs/UserUpdateTeaSubjectDialog.vue';
import UserViewPermissionDialog from '@/components/biz/dialogs/UserViewPermissionDialog.vue';
import { useMessage } from '@/hooks/web/useMessage';
import { userStateFilter } from '@/utils/filters/user.state.filter';
const sysAddUserRef = ref();
const userUpdateInfoRef = ref();
const userUpdateTeaSubjectRef = ref();
const userPermissionsRef = ref();
const userViewPermissionsRef = ref();
const queryFormRef = ref<FormInstance>();

const message = useMessage(); // 消息弹窗

const queryForm = reactive({
    pageNo: 1,
    pageSize: 10,
    status: '',
    roleName: '',
    userAccount: '',
});

const checkIds = ref([]);
const loading = ref(false);
const total = ref(0);
const list = ref([]);
const options = ref([
    {
        value: '',
        label: '全部状态',
    },
    {
        value: '1',
        label: '在用',
    },
    {
        value: '0',
        label: '停用',
    },
]);
const tableColumns = [
    {
        prop: 'userAccount',
        label: '用户账号',
        minWidth: 150,
        ellipsis: false,
    },
    {
        prop: 'userName',
        label: '用户姓名',
        minWidth: 150,
        ellipsis: true,
    },
    {
        prop: 'mobile',
        label: '手机号码',
        minWidth: 150,
        ellipsis: true,
    },

    {
        prop: 'status',
        label: '状态',
        minWidth: 100,
        ellipsis: true,
        slot: 'status',
    },
    {
        prop: 'operation',
        label: '操作',
        width: 500,
        fixed: 'right',
        slot: 'operation',
    },
];

/** 多选 */
const selectable = () => {
    return true;
};

/**
 * 多选变化
 * @param rows 选中项
 */
const handleSelectionChange = (rows) => {
    checkIds.value = rows.map((row) => row.id);
};

/** 查询 */
const changeParam = () => {
    queryForm.pageNo = 1;
    getList();
};

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    changeParam();
};

/** 添加 */
const add = () => {
    sysAddUserRef.value?.open();
};

/** 编辑 */
const edit = (row) => {
    sysAddUserRef.value?.open(row);
};

/** 更新主体信息 */
const updateMainInfo = (row) => {
    userUpdateTeaSubjectRef.value?.open(row);
};

/** 定义权限 */
const UserPermissions = (row) => {
    userPermissionsRef.value?.open(row);
};

/** 查看权限 */
const viewPermissions = (row) => {
    userViewPermissionsRef.value?.open(row);
};

/** 删除 */
const delUsers = async () => {
    try {
        await message.confirm('确定删除?');
        await UserApi.delUser({ ids: checkIds.value });
        for (let index = 0; index < checkIds.value.length; index++) {
            const element = checkIds.value[index];
            await originDeleteSubject(element);
        }
        message.success('删除成功');
        getList();
    } catch (error) {}
};

/** 停用 */
const closeUserState = async () => {
    try {
        await message.confirm('确定停用?');
        await UserApi.updateUserState({ ids: checkIds.value, status: 0 });
        message.success('停用成功');
        getList();
    } catch (error) {}
};

/** 启用 */
const openUserState = async () => {
    try {
        await message.confirm('确定启用?');
        await UserApi.updateUserState({ ids: checkIds.value, status: 1 });
        message.success('启用成功');
        getList();
    } catch (error) {}
};

/** 查询列表 */
const getList = async () => {
    loading.value = true;
    try {
        const data = await UserApi.userSearchBy(queryForm);
        total.value = Number(data.total);

        for (let i in data.list) {
            const v = data.list[i];
            const roles = await UserApi.getRoleDtoListByUserId(v.id);
            data.list[i].roles = roles.map((item) => item.roleCode);
        }
        list.value = data.list;
    } finally {
        loading.value = false;
    }
};

/** 初始化 **/
onMounted(() => {
    getList();
});
</script>

<style lang="less" scope></style>
