<template>
    <div>
        <ListContainer title="权限管理">
            <el-button type="primary" style="height: 32px; line-height: 10px" @click="add"
                >新增</el-button
            >
            <el-button
                style="height: 32px; line-height: 10px"
                :disabled="checkIds.length === 0"
                @click="hideMenu"
                >隐藏</el-button
            >
            <el-button
                style="height: 32px; line-height: 10px"
                :disabled="checkIds.length === 0"
                @click="displayMenu"
                >显示</el-button
            >
            <el-button
                type="danger"
                style="height: 32px; line-height: 10px"
                :disabled="checkIds.length === 0"
                @click="delMenus"
                >删除</el-button
            >
        </ListContainer>
        <div class="table-wrapper">
            <CommonTable
                ref="table"
                :isShowPagination="false"
                :table-data="list"
                :table-columns="tableColumns"
                border
                :loading="loading"
                :is-show-toolbar="false"
                :is-show-selection="true"
                :is-show-selection-info="true"
                :row-key="'id'"
                :tree-props="{ children: 'children' }"
                @selection-change="handleSelectionChange"
            >
                <template #menuType="{ scope }">
                    <el-tag :type="scope.row.menuType === 1 ? 'primary' : 'success'">{{
                        menuTypeFilter(scope.row.menuType)
                    }}</el-tag>
                </template>
                <template #isDisabled="{ scope }">
                    <span>{{ disabledFilter(scope.row.isDisabled) }}</span>
                </template>

                <template #operation="{ scope }">
                    <el-button type="primary" @click.stop="edit(scope.row)">修改</el-button>
                </template>
            </CommonTable>
        </div>
        <EditPageDialog ref="menuFormRef" @success="getList" />
    </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue';

import * as MenuApi from '@/api/system/menu';
import EditPageDialog from '@/components/biz/dialogs/EditPageDialog.vue';
import { useMessage } from '@/hooks/web/useMessage';
import { disabledFilter, menuTypeFilter } from '@/utils/filters/menu.state.filter';

import tableColumns from './tableColumns';

const message = useMessage(); // 消息弹窗
const menuFormRef = ref();

const checkIds = ref([]);
const loading = ref(false);
const list = ref([]);

/**
 * 多选变化
 * @param rows 选中项
 */
const handleSelectionChange = (rows) => {
    checkIds.value = rows.map((row) => row.id);
};

/** 查询 */
const changeParam = () => {
    getList();
};

/** 添加 */
const add = () => {
    menuFormRef.value?.open();
};

/** 编辑 */
const edit = (row) => {
    menuFormRef.value?.open(row);
};

/** 删除菜单 */
const delMenus = async () => {
    try {
        await message.confirm('确定删除?');
        await MenuApi.delMenus({ menuIds: checkIds.value });
        message.success('删除成功');
        getList();
    } catch (error) {}
};

/** 隐藏菜单 */
const hideMenu = async () => {
    try {
        await message.confirm('确定隐藏菜单?');
        await MenuApi.hiddenMenus({ menuIds: checkIds.value, status: 0 });
        message.success('停用成功');
        getList();
    } catch (error) {}
};

/** 显示菜单 */
const displayMenu = async () => {
    try {
        await message.confirm('确定显示菜单?');
        await MenuApi.hiddenMenus({ menuIds: checkIds.value, status: 1 });
        message.success('停用成功');
        getList();
    } catch (error) {}
};
function buildMenuTree(flatMenu) {
    const map = {};
    const tree = [];

    flatMenu.forEach((menuItem) => {
        map[menuItem.id] = { ...menuItem, children: [] };
    });

    flatMenu.forEach((menuItem) => {
        if (menuItem.parentId && map[menuItem.parentId]) {
            map[menuItem.parentId].children.push(map[menuItem.id]);
        } else {
            tree.push(map[menuItem.id]);
        }
    });

    return tree;
}
/** 查询列表 */
const getList = async () => {
    loading.value = true;
    try {
        const data = await MenuApi.getAllMenu();
        list.value = buildMenuTree(data);
    } finally {
        loading.value = false;
    }
};

/** 初始化 **/
onMounted(() => {
    getList();
});
</script>

<style lang="less" scope></style>
