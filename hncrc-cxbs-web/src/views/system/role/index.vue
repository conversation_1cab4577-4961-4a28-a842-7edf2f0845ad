<template>
    <div>
        <ListContainer title="角色管理">
            <template #searchForm>
                <el-form
                    ref="queryFormRef"
                    :inline="true"
                    :model="queryForm"
                    class="demo-form-inline"
                    @submit.prevent
                >
                    <el-form-item label="角色名称" prop="roleName">
                        <el-input
                            v-model="queryForm.roleName"
                            placeholder="角色名称"
                            class="w-240"
                            maxlength="100"
                            style="margin-right: 8px"
                        />
                    </el-form-item>
                    <el-form-item label="角色状态" prop="roleState">
                        <el-select
                            v-model="queryForm.roleState"
                            clearable
                            placeholder="用户状态"
                            class="w-240"
                            style="margin-right: 8px"
                            @change="changeParam()"
                        >
                            <el-option
                                v-for="item in stateOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" native-type="submit" @click="changeParam"
                            >筛选</el-button
                        >
                        <el-button @click="resetQuery(queryFormRef)">重置</el-button>
                    </el-form-item>
                </el-form>
            </template>
            <el-button type="primary" style="height: 32px; line-height: 10px" @click="add"
                >新增</el-button
            >
            <el-button
                style="height: 32px; line-height: 10px"
                :disabled="checkIds.length === 0"
                @click="closeRoleState()"
                >停用</el-button
            >
            <el-button
                style="height: 32px; line-height: 10px"
                :disabled="checkIds.length === 0"
                @click="openRoleState()"
                >启用</el-button
            >
            <el-button
                type="danger"
                style="height: 32px; line-height: 10px"
                :disabled="checkIds.length === 0"
                @click="delRoles"
                >删除</el-button
            >
        </ListContainer>
        <div class="table-wrapper">
            <CommonTable
                ref="tableRef"
                :table-data="list"
                :table-columns="tableColumns"
                :total="total"
                :loading="loading"
                :is-show-toolbar="false"
                :is-show-selection="true"
                :is-show-selection-info="true"
                :current-page="queryForm.pageNo"
                :page-size="queryForm.pageSize"
                :selectable="selectable"
                @selection-change="handleSelectionChange"
                @pageChange="
                    async (page) => {
                        queryForm.pageNo = page.pageNum;
                        queryForm.pageSize = page.pageSize;
                        await getList();
                    }
                "
            >
                <template #status="{ scope }">
                    <span v-html="roleStateFilter(scope.row.status)" />
                </template>

                <template #operation="{ scope }">
                    <el-button text type="primary" @click.stop="rolePermissions(scope.row)"
                        >定义权限</el-button
                    >
                </template>
            </CommonTable>
        </div>
        <InsertRoleDialog ref="roleFormRef" @success="getList" />
        <RolePermissionsDialog ref="rolePermissionsRef" />
    </div>
</template>
<script setup lang="ts">
import type { FormInstance } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

import * as RoleApi from '@/api/system/role';
import InsertRoleDialog from '@/components/biz/dialogs/InsertRoleDialog.vue';
import RolePermissionsDialog from '@/components/biz/dialogs/RolePermissionsDialog.vue';
import { useMessage } from '@/hooks/web/useMessage';
import { roleStateFilter } from '@/utils/filters/role.state.filter';

import tableColumns from './tableColumns';
const message = useMessage(); // 消息弹窗

const queryFormRef = ref();

const roleFormRef = ref();

const tableRef = ref();

const queryForm = reactive({
    pageNo: 1,
    pageSize: 10,
    roleName: '',
    roleState: '',
});

const stateOptions = ref([
    {
        value: '',
        label: '全部状态',
    },
    {
        value: '1',
        label: '在用',
    },
    {
        value: '0',
        label: '停用',
    },
]);
const checkIds = ref([]);
const loading = ref(false);
const total = ref(0);
const list = ref([]);

const rolePermissionsRef = ref();
/** 定义权限 */
const rolePermissions = (row) => {
    rolePermissionsRef.value?.open(row);
};

/** 删除角色 */
const delRoles = async () => {
    try {
        await message.confirm('确定删除?');
        await RoleApi.delRole(checkIds.value);
        message.success('删除成功');
        getList();
    } catch (error) {}
};

/** 停用角色 */
const closeRoleState = async () => {
    try {
        await message.confirm('确定停用?');
        await RoleApi.updateRoleState({ ids: checkIds.value, status: '0' });
        message.success('停用成功');
        getList();
    } catch (error) {}
};

/** 启用角色 */
const openRoleState = async () => {
    try {
        await message.confirm('确定启用?');
        await RoleApi.updateRoleState({ ids: checkIds.value, status: '1' });
        message.success('启用成功');
        getList();
    } catch (error) {}
};

/** 多选 */
const selectable = () => {
    return true;
};

/**
 * 多选变化
 * @param rows 选中项
 */
const handleSelectionChange = (rows) => {
    checkIds.value = rows.map((row) => row.id);
};

/** 查询 */
const changeParam = () => {
    queryForm.pageNo = 1;
    getList();
};

/** 重置 */
const resetQuery = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    changeParam();
};

/** 添加 */
const add = () => {
    roleFormRef.value?.open();
};

/** 查询列表 */
const getList = async () => {
    loading.value = true;
    try {
        const data = await RoleApi.roleSearchBy(queryForm);
        list.value = data.list;
        total.value = Number(data.total);
    } finally {
        loading.value = false;
    }
};

/** 初始化 **/
onMounted(() => {
    getList();
});
</script>

<style lang="less" scope></style>
