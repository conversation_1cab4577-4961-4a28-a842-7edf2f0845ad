/**遍历对象 过滤掉没有值的属性 */
export const filterObject = (obj: any) => {
    const newObj = {};
    for (const key in obj) {
        if (obj[key] !== undefined && obj[key] !== null && obj[key] !== '') {
            newObj[key] = obj[key];
        }
    }
    return newObj;
};

/**清空 vue3 reactive 对象 */
export const clearReactive = (obj: any) => {
    for (const key in obj) {
        obj[key] = undefined;
    }
};

/**获取当前地址的query对象 */
export const getCurrentPathQuery = () => {
    const searchParams = new URLSearchParams(window.location.href.split('?')[1]);
    const o = {};
    for (const [key, value] of searchParams) {
        o[key] = value;
    }
    return o;
};

/**将二维数组转为 Table 行 所需数据 */
export const getRowTableAttrs = (
    rows: Array<[string, string | number | boolean, Record<string, string>?]>,
) => {
    const data = {};
    const columns = [];
    for (const i in rows) {
        const [label, value, attrs = {}] = rows[i];
        const prop = attrs.prop || i;
        Object.assign(data, {
            [prop]: value,
        });
        columns.push(
            Object.assign(
                {
                    prop: prop,
                    label: label,
                },
                attrs,
            ),
        );
    }
    return {
        data: [data],
        columns: columns,
    };
};

/**将二维数组转为 Table 列 所需数据 */
export const getColumnTableAttrs = (
    column: Array<string | Record<string, string>>,
    nest: Array<string | number | boolean>[],
) => {
    const columns = column.map((item, index) => {
        return Object.assign(
            {
                prop: String(index),
                label: item,
            },
            typeof item === 'string' ? {} : item,
        );
    });
    const data = nest.map((item, _) => {
        const row = {};
        for (const i in item) {
            const v = item[i];
            row[columns[i].prop] = v;
        }
        return row;
    });
    return {
        data: data,
        columns: columns,
    };
};

/**
 * @description: value 格式化后的数据
 * @param {*} value 数字
 * @param {*} fixed 小数点位数
 * @return {*} string
 */
export const getValueToFixed = (value: string, fixed: 0 | 1 | 2 = 0) => {
    const val = String(value);
    if (fixed === 0) {
        return val.replace(/\D/g, '');
    } else if (fixed === 1) {
        return val.replace(/[^\d.]/g, '').replace(/^(\-)*(\d+)\.(\d).*$/, '$1$2.$3');
    } else if (fixed === 2) {
        return val
            .replace(/[^\d.]/g, '')
            .replace(/\.{1,}/g, '.')
            .replace('.', '$#$')
            .replace(/\./g, '')
            .replace('$#$', '.')
            .replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
            .replace(/^\./g, '');
    }
};

/**
 * @description: 判断是否为boolean类型
 */
export const isBoolean = (o) => {
    return Object.prototype.toString.call(o).slice(8, -1) === 'Boolean';
};
/**
 * 重置对象
 * @param form
 */
export const resetObject = <T>(form: T): T => {
    const _form: T = {} as T;
    for (const key in form) {
        _form[key] = null;
    }
    return _form;
};

/**
 * 判断一个值是否非空（字符串时空值也算有值）
 * @param value
 * @returns
 */
export const notNull = (value: any) => {
    return value !== null && value !== undefined;
};

/** 判断一个对象是否为空 */
export const isEmptyObject = (obj: any) => {
    for (const key in obj) {
        return false;
    }
    return true;
};
