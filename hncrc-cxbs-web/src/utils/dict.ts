/**
 * 字典翻译
 * @param dicts 字典列表
 * @param dictVal 字典值
 * @returns
 */
export function translateDict<T extends boolean | number | string = string>(
    dicts: Dict<T>[],
    dictVal: T,
): string {
    if (dictVal === null || dictVal === undefined) return '';

    const dict = dicts.find((item) => {
        return item.value === dictVal;
    });

    return dict?.label || (dictVal ? dictVal.toString() : '');
}

/**
 * 将字典转换为Map<value, label>并返回新生成的Map
 * @param dicts
 */
export function dicts2Map<T extends boolean | number | string = string>(dicts: Dict<T>[]) {
    const retMap = new Map<T, string>();
    dicts.forEach((item) => {
        retMap.set(item.value, item.label);
    });
    return retMap;
}

/**
 * 将Map的key和value互换
 * @param map
 * @returns
 */
export function mapReverse<
    T extends boolean | number | string = string,
    K extends boolean | number | string = string,
>(map: Map<T, K>) {
    const retMap = new Map<K, T>();
    map.forEach((value, key) => {
        retMap.set(value, key);
    });
    return retMap;
}
