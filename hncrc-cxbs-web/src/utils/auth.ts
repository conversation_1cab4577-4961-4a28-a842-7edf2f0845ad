import { storageLocal } from '@pureadmin/utils';
import Cookies from 'js-cookie';

import type { UserResult } from '@/api/login';
import { useUserStoreHook } from '@/store/modules/user';

export interface DataInfo {
    /** 用户名 */
    userName?: string;
    /** token */
    token?: string;
    userId?: number;
}

export interface ZoneInfo {
    id: string;
    subZoneList?: {
        backgroundImage: string;
        cardImage: string;
        id: string;
        status: string;
        zone: string;
    }[];
    zoneId: string;
    zoneName: string;
    userId: number;
}

export const userKey = 'HNCRC-ADMIN-USER-INFO';
export const TokenKey = 'HNCRC-ADMIN-TOKEN';
export const ZoneKey = 'HNCRC-ADMIN-ZONE';
/**
 * 通过`multiple-tabs`是否在`cookie`中，判断用户是否已经登录系统，
 * 从而支持多标签页打开已经登录的系统后无需再登录。
 * 浏览器完全关闭后`multiple-tabs`将自动从`cookie`中销毁，
 * 再次打开浏览器需要重新登录系统
 * */
export const multipleTabsKey = 'hncrc-admin-multiple-tabs';

/** 获取`token` */
export function getAuthInfo(): DataInfo {
    return storageLocal().getItem(userKey);
}

export function setAuthInfo(res: UserResult) {
    storageLocal().setItem(TokenKey, res.token);
    useUserStoreHook().SET_USERNAME(res.userInfo.userName);
    storageLocal().setItem(userKey, {
        userName: res.userInfo.userName,
        token: res.token,
        userId: res.userInfo.id,
    });
    Cookies.set(multipleTabsKey, 'true');
}

/** 删除`token`以及key值为`user-info`的localStorage信息 */
export function removeAuthInfo() {
    storageLocal().removeItem(TokenKey);
    storageLocal().removeItem(userKey);
    storageLocal().removeItem(ZoneKey);
    Cookies.remove(multipleTabsKey);
}

/** 获取`专区信息` */
export function getZoneInfo(): ZoneInfo {
    return storageLocal().getItem(ZoneKey);
}

export function setZoneInfo(res: ZoneInfo) {
    storageLocal().setItem(ZoneKey, {
        userId: res.userId,
        zoneId: res.zoneId,
        subZoneList: res?.subZoneList,
        zoneName: res.zoneName,
        id: res.id,
    });
}
