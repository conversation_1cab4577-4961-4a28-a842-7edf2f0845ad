import xss from 'xss';

interface ContentProcessOptions {
    /** 是否进行 XSS 过滤 */
    xssFilter?: boolean;
}

/**
 * HTML实体编码
 */
const escapeHtml = (html: string): string => {
    return html
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
};

/**
 * HTML实体解码
 */
export const unescapeHtml = (encodedHtml: string): string => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = encodedHtml;
    return tempDiv.textContent || tempDiv.innerText || '';
};

interface ImageOptions {
    alt?: string;
    className?: string;
    width?: number | string;
    height?: number | string;
}

/**
 * 插入图片
 */
export const insertImage = (imgUrl: string, options: ImageOptions = {}): string => {
    const { alt = '', className = '', width, height } = options;
    const attrs = [
        `src="${escapeHtml(imgUrl)}"`,
        `alt="${escapeHtml(alt)}"`,
        className && `class="${escapeHtml(className)}"`,
        width && `width="${width}"`,
        height && `height="${height}"`,
    ]
        .filter(Boolean)
        .join(' ');

    return `<img ${attrs} />`;
};

interface LinkOptions {
    className?: string;
    target?: '_blank' | '_self' | '_parent' | '_top';
    rel?: string;
}

/**
 * 插入链接
 */
export const insertLink = (url: string, text: string, options: LinkOptions = {}): string => {
    const {
        className = '',
        target,
        rel = target === '_blank' ? 'noopener noreferrer' : '',
    } = options;
    const attrs = [
        `href="${escapeHtml(url)}"`,
        className && `class="${escapeHtml(className)}"`,
        target && `target="${target}"`,
        rel && `rel="${rel}"`,
    ]
        .filter(Boolean)
        .join(' ');

    return `<a ${attrs}>${escapeHtml(text)}</a>`;
};

/**
 * 文本样式处理
 */
export const textStyle = {
    bold: (text: string): string => `<strong>${escapeHtml(text)}</strong>`,
    italic: (text: string): string => `<em>${escapeHtml(text)}</em>`,
    underline: (text: string): string => `<u>${escapeHtml(text)}</u>`,
};

interface ListOptions {
    className?: string;
}

/**
 * 列表处理
 */
export const list = {
    unordered: (items: string[], options: ListOptions = {}): string => {
        const { className = '' } = options;
        const classAttr = className ? ` class="${escapeHtml(className)}"` : '';
        const listItems = items.map((item) => `<li>${escapeHtml(item)}</li>`).join('');
        return `<ul${classAttr}>${listItems}</ul>`;
    },

    ordered: (items: string[], options: ListOptions = {}): string => {
        const { className = '' } = options;
        const classAttr = className ? ` class="${escapeHtml(className)}"` : '';
        const listItems = items.map((item) => `<li>${escapeHtml(item)}</li>`).join('');
        return `<ol${classAttr}>${listItems}</ol>`;
    },
};

interface ParagraphOptions {
    className?: string;
}

/**
 * 创建段落
 */
export const createParagraph = (text: string, options: ParagraphOptions = {}): string => {
    const { className = '' } = options;
    const classAttr = className ? ` class="${escapeHtml(className)}"` : '';
    return `<p${classAttr}>${escapeHtml(text)}</p>`;
};

/**
 * 内容处理工具
 */
export const contentProcess = {
    /**
     * 处理内容（用于保存到服务器）
     * @param content 原始内容
     * @param options 处理选项
     */
    encode: (content: string, options: ContentProcessOptions = {}): string => {
        const { xssFilter = true } = options;

        if (!content) return '';

        let processedContent = content;

        // XSS 过滤
        if (xssFilter) {
            processedContent = xss(processedContent, {
                stripIgnoreTag: false,
                escapeHtml: (html) => html, // 转义 HTML
                whiteList: {}, // 空白名单，不允许任何标签
            });
        }

        // 确保所有 HTML 标签都被转义
        return escapeHtml(processedContent);
    },

    /**
     * 处理内容（用于从服务器获取后显示）
     * @param content 服务器返回的内容
     */
    decode: (content: string): string => {
        if (!content) return '';

        // 将转义的 HTML 实体转换回 HTML
        return content
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .replace(/&#x2F;/g, '/');
    },

    /**
     * 批量处理内容
     * @param list 包含内容的数据列表
     * @param contentKey 内容字段的键名
     */
    batchDecode: <T extends Record<string, any>>(
        list: T[],
        contentKey: keyof T = 'content' as keyof T,
    ): T[] => {
        return list.map((item) => ({
            ...item,
            [contentKey]: contentProcess.decode(item[contentKey] as string),
        }));
    },
};
