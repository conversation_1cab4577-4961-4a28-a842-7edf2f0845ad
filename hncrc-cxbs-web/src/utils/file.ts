/** 获取 src/assets下path目录下的资源文件路径 */
export const getAssetsFileUrl = (path: string) => {
    return new URL(`../assets/${path}`, import.meta.url).href;
};

/**
 * 读取文件内容
 * @param file - 文件对象
 * @returns 文件内容
 *
 * @param file
 * @returns
 */
export const readFileAsText = async function (file: File): Promise<string> {
    const fileReader = new FileReader();
    let text: string | null = null;

    // 读取文件内容
    await new Promise((resolve) => {
        fileReader.onload = () => {
            text = fileReader.result as string;
            resolve(true);
        };
        fileReader.readAsText(file, 'utf8');
    });

    return text || '';
};

/**
 * 下载二进制文件（将后二进制数据转换为下载地址并下载）
 * @param blobContent 二进制数据
 * @param fileName 下载的文件名
 * @param contentType 文件类型
 */
export function downloadBlobFile(blobContent: Blob, fileName: string, contentType?: string) {
    const blob = new Blob([blobContent], { type: contentType || 'application/octet-stream' });
    const blobUrl = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = blobUrl;
    a.download = fileName;
    a.click();
    window.URL.revokeObjectURL(blobUrl);
}
