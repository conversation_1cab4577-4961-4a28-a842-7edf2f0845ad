<!doctype html>
<html>

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />

    <meta name="keywords" content="海南征信平台,海易信,海南征信,信用海南,海易信官网,信用海南,征信产品,金融产品,金融机构" />
    <meta name="description"
        content="海南省征信有限公司是经海南省委省政府批复、在人民银行海口中心支行支持下成立的国有控股省级地方征信公司,以服务中小微企业为核心，依托大数据、云计算等先进技术，整合政府扶持政策、公共信用信息、征信产品服务、企业金融需求、金融产品等各类资源，实现对中小微企业线上全流程金融服务，解决企业融资难题，着力打造永不落幕的银企对接会。" />

    <!--share begin -->
    <meta property="og:title" content="海南征信平台" />
    <meta property="og:description"
        content="海南省征信有限公司是经海南省委省政府批复、在人民银行海口中心支行支持下成立的国有控股省级地方征信公司,以服务中小微企业为核心，依托大数据、云计算等先进技术，整合政府扶持政策、公共信用信息、征信产品服务、企业金融需求、金融产品等各类资源，实现对中小微企业线上全流程金融服务，解决企业融资难题，着力打造永不落幕的银企对接会。" />
    <meta property="og:url" content="https://www.hainancrc.com" />
    <!-- share end -->

    <meta name="ColumnName" content="海易信" />
    <meta name="ColumnDescription" content="海易信" />
    <meta name="ColumnKeywords" content="海易信" />
    <meta name="ColumnType" content="海易信" />
    <link rel="icon" href="/favicon.ico" />
    <title>海南征信平台</title>
    <script>
        window.process = {};
    </script>
    <script>
        !function (r) { var n = {}; function o(e) { var t; return (n[e] || (t = n[e] = { i: e, l: !1, exports: {} }, r[e].call(t.exports, t, t.exports, o), t.l = !0, t)).exports } o.m = r, o.c = n, o.d = function (e, t, r) { o.o(e, t) || Object.defineProperty(e, t, { enumerable: !0, get: r }) }, o.r = function (e) { "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e, "__esModule", { value: !0 }) }, o.t = function (t, e) { if (1 & e && (t = o(t)), 8 & e) return t; if (4 & e && "object" == typeof t && t && t.__esModule) return t; var r = Object.create(null); if (o.r(r), Object.defineProperty(r, "default", { enumerable: !0, value: t }), 2 & e && "string" != typeof t) for (var n in t) o.d(r, n, function (e) { return t[e] }.bind(null, n)); return r }, o.n = function (e) { var t = e && e.__esModule ? function () { return e.default } : function () { return e }; return o.d(t, "a", t), t }, o.o = function (e, t) { return Object.prototype.hasOwnProperty.call(e, t) }, o.p = "", o(o.s = 7) }([function (e, t) { e.exports = function (e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }, e.exports.__esModule = !0, e.exports.default = e.exports }, function (e, t, r) { var o = r(5); function n(e, t) { for (var r = 0; r < t.length; r++) { var n = t[r]; n.enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, o(n.key), n) } } e.exports = function (e, t, r) { return t && n(e.prototype, t), r && n(e, r), Object.defineProperty(e, "prototype", { writable: !1 }), e }, e.exports.__esModule = !0, e.exports.default = e.exports }, function (e, t, r) { var n = r(5); e.exports = function (e, t, r) { return (t = n(t)) in e ? Object.defineProperty(e, t, { value: r, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = r, e }, e.exports.__esModule = !0, e.exports.default = e.exports }, function (t, e) { function r(e) { return t.exports = r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (e) { return typeof e } : function (e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, t.exports.__esModule = !0, t.exports.default = t.exports, r(e) } t.exports = r, t.exports.__esModule = !0, t.exports.default = t.exports }, function (e, t, r) { r = r(9)(); e.exports = r; try { regeneratorRuntime = r } catch (e) { "object" == typeof globalThis ? globalThis.regeneratorRuntime = r : Function("r", "regeneratorRuntime = r")(r) } }, function (e, t, r) { var n = r(3).default, o = r(8); e.exports = function (e) { return e = o(e, "string"), "symbol" === n(e) ? e : String(e) }, e.exports.__esModule = !0, e.exports.default = e.exports }, function (e, t) { function c(e, t, r, n, o, i, a) { try { var s = e[i](a), c = s.value } catch (e) { return void r(e) } s.done ? t(c) : Promise.resolve(c).then(n, o) } e.exports = function (s) { return function () { var e = this, a = arguments; return new Promise(function (t, r) { var n = s.apply(e, a); function o(e) { c(n, t, r, o, i, "next", e) } function i(e) { c(n, t, r, o, i, "throw", e) } o(void 0) }) } }, e.exports.__esModule = !0, e.exports.default = e.exports }, function (e, t, r) { e.exports = r(10) }, function (e, t, r) { var n = r(3).default; e.exports = function (e, t) { if ("object" !== n(e) || null === e) return e; var r = e[Symbol.toPrimitive]; if (void 0 === r) return ("string" === t ? String : Number)(e); if (r = r.call(e, t || "default"), "object" !== n(r)) return r; throw new TypeError("@@toPrimitive must return a primitive value.") }, e.exports.__esModule = !0, e.exports.default = e.exports }, function (k, e, t) { var P = t(3).default; function r() { "use strict"; k.exports = function () { return a }, k.exports.__esModule = !0, k.exports.default = k.exports; var c, a = {}, e = Object.prototype, u = e.hasOwnProperty, l = Object.defineProperty || function (e, t, r) { e[t] = r.value }, t = "function" == typeof Symbol ? Symbol : {}, n = t.iterator || "@@iterator", r = t.asyncIterator || "@@asyncIterator", o = t.toStringTag || "@@toStringTag"; function i(e, t, r) { return Object.defineProperty(e, t, { value: r, enumerable: !0, configurable: !0, writable: !0 }), e[t] } try { i({}, "") } catch (c) { i = function (e, t, r) { return e[t] = r } } function s(e, t, r, n) { var o, i, a, s, t = t && t.prototype instanceof y ? t : y, t = Object.create(t.prototype), n = new E(n || []); return l(t, "_invoke", { value: (o = e, i = r, a = n, s = f, function (e, t) { if (s === h) throw new Error("Generator is already running"); if (s === g) { if ("throw" === e) throw t; return { value: c, done: !0 } } for (a.method = e, a.arg = t; ;) { var r = a.delegate; if (r) { r = function e(t, r) { var n = r.method, o = t.iterator[n]; if (o === c) return r.delegate = null, "throw" === n && t.iterator.return && (r.method = "return", r.arg = c, e(t, r), "throw" === r.method) || "return" !== n && (r.method = "throw", r.arg = new TypeError("The iterator does not provide a '" + n + "' method")), w; n = p(o, t.iterator, r.arg); if ("throw" === n.type) return r.method = "throw", r.arg = n.arg, r.delegate = null, w; o = n.arg; return o ? o.done ? (r[t.resultName] = o.value, r.next = t.nextLoc, "return" !== r.method && (r.method = "next", r.arg = c), r.delegate = null, w) : o : (r.method = "throw", r.arg = new TypeError("iterator result is not an object"), r.delegate = null, w) }(r, a); if (r) { if (r === w) continue; return r } } if ("next" === a.method) a.sent = a._sent = a.arg; else if ("throw" === a.method) { if (s === f) throw s = g, a.arg; a.dispatchException(a.arg) } else "return" === a.method && a.abrupt("return", a.arg); s = h; r = p(o, i, a); if ("normal" === r.type) { if (s = a.done ? g : d, r.arg === w) continue; return { value: r.arg, done: a.done } } "throw" === r.type && (s = g, a.method = "throw", a.arg = r.arg) } }) }), t } function p(e, t, r) { try { return { type: "normal", arg: e.call(t, r) } } catch (e) { return { type: "throw", arg: e } } } a.wrap = s; var f = "suspendedStart", d = "suspendedYield", h = "executing", g = "completed", w = {}; function y() { } function v() { } function m() { } var t = {}, b = (i(t, n, function () { return this }), Object.getPrototypeOf), b = b && b(b(j([]))), O = (b && b !== e && u.call(b, n) && (t = b), m.prototype = y.prototype = Object.create(t)); function T(e) { ["next", "throw", "return"].forEach(function (t) { i(e, t, function (e) { return this._invoke(t, e) }) }) } function x(a, s) { var t; l(this, "_invoke", { value: function (r, n) { function e() { return new s(function (e, t) { !function t(e, r, n, o) { var i, e = p(a[e], a, r); if ("throw" !== e.type) return (r = (i = e.arg).value) && "object" == P(r) && u.call(r, "__await") ? s.resolve(r.__await).then(function (e) { t("next", e, n, o) }, function (e) { t("throw", e, n, o) }) : s.resolve(r).then(function (e) { i.value = e, n(i) }, function (e) { return t("throw", e, n, o) }); o(e.arg) }(r, n, e, t) }) } return t = t ? t.then(e, e) : e() } }) } function S(e) { var t = { tryLoc: e[0] }; 1 in e && (t.catchLoc = e[1]), 2 in e && (t.finallyLoc = e[2], t.afterLoc = e[3]), this.tryEntries.push(t) } function I(e) { var t = e.completion || {}; t.type = "normal", delete t.arg, e.completion = t } function E(e) { this.tryEntries = [{ tryLoc: "root" }], e.forEach(S, this), this.reset(!0) } function j(t) { if (t || "" === t) { var r, e = t[n]; if (e) return e.call(t); if ("function" == typeof t.next) return t; if (!isNaN(t.length)) return r = -1, (e = function e() { for (; ++r < t.length;)if (u.call(t, r)) return e.value = t[r], e.done = !1, e; return e.value = c, e.done = !0, e }).next = e } throw new TypeError(P(t) + " is not iterable") } return l(O, "constructor", { value: v.prototype = m, configurable: !0 }), l(m, "constructor", { value: v, configurable: !0 }), v.displayName = i(m, o, "GeneratorFunction"), a.isGeneratorFunction = function (e) { e = "function" == typeof e && e.constructor; return !!e && (e === v || "GeneratorFunction" === (e.displayName || e.name)) }, a.mark = function (e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, m) : (e.__proto__ = m, i(e, o, "GeneratorFunction")), e.prototype = Object.create(O), e }, a.awrap = function (e) { return { __await: e } }, T(x.prototype), i(x.prototype, r, function () { return this }), a.AsyncIterator = x, a.async = function (e, t, r, n, o) { void 0 === o && (o = Promise); var i = new x(s(e, t, r, n), o); return a.isGeneratorFunction(t) ? i : i.next().then(function (e) { return e.done ? e.value : i.next() }) }, T(O), i(O, o, "Generator"), i(O, n, function () { return this }), i(O, "toString", function () { return "[object Generator]" }), a.keys = function (e) { var t, r = Object(e), n = []; for (t in r) n.push(t); return n.reverse(), function e() { for (; n.length;) { var t = n.pop(); if (t in r) return e.value = t, e.done = !1, e } return e.done = !0, e } }, a.values = j, E.prototype = { constructor: E, reset: function (e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = c, this.done = !1, this.delegate = null, this.method = "next", this.arg = c, this.tryEntries.forEach(I), !e) for (var t in this) "t" === t.charAt(0) && u.call(this, t) && !isNaN(+t.slice(1)) && (this[t] = c) }, stop: function () { this.done = !0; var e = this.tryEntries[0].completion; if ("throw" === e.type) throw e.arg; return this.rval }, dispatchException: function (r) { if (this.done) throw r; var n = this; function e(e, t) { return i.type = "throw", i.arg = r, n.next = e, t && (n.method = "next", n.arg = c), !!t } for (var t = this.tryEntries.length - 1; 0 <= t; --t) { var o = this.tryEntries[t], i = o.completion; if ("root" === o.tryLoc) return e("end"); if (o.tryLoc <= this.prev) { var a = u.call(o, "catchLoc"), s = u.call(o, "finallyLoc"); if (a && s) { if (this.prev < o.catchLoc) return e(o.catchLoc, !0); if (this.prev < o.finallyLoc) return e(o.finallyLoc) } else if (a) { if (this.prev < o.catchLoc) return e(o.catchLoc, !0) } else { if (!s) throw new Error("try statement without catch or finally"); if (this.prev < o.finallyLoc) return e(o.finallyLoc) } } } }, abrupt: function (e, t) { for (var r = this.tryEntries.length - 1; 0 <= r; --r) { var n = this.tryEntries[r]; if (n.tryLoc <= this.prev && u.call(n, "finallyLoc") && this.prev < n.finallyLoc) { var o = n; break } } var i = (o = o && ("break" === e || "continue" === e) && o.tryLoc <= t && t <= o.finallyLoc ? null : o) ? o.completion : {}; return i.type = e, i.arg = t, o ? (this.method = "next", this.next = o.finallyLoc, w) : this.complete(i) }, complete: function (e, t) { if ("throw" === e.type) throw e.arg; return "break" === e.type || "continue" === e.type ? this.next = e.arg : "return" === e.type ? (this.rval = this.arg = e.arg, this.method = "return", this.next = "end") : "normal" === e.type && t && (this.next = t), w }, finish: function (e) { for (var t = this.tryEntries.length - 1; 0 <= t; --t) { var r = this.tryEntries[t]; if (r.finallyLoc === e) return this.complete(r.completion, r.afterLoc), I(r), w } }, catch: function (e) { for (var t = this.tryEntries.length - 1; 0 <= t; --t) { var r, n, o = this.tryEntries[t]; if (o.tryLoc === e) return "throw" === (r = o.completion).type && (n = r.arg, I(o)), n } throw new Error("illegal catch attempt") }, delegateYield: function (e, t, r) { return this.delegate = { iterator: j(e), resultName: t, nextLoc: r }, "next" === this.method && (this.arg = c), w } }, a } k.exports = r, k.exports.__esModule = !0, k.exports.default = k.exports }, function (C, e, t) { "use strict"; t.r(e); function b() { var t = { s: !0, ia: [""], wc: 40, pv: { s: true, ia: [""] }, je: { s: true, ia: [""] }, hl: { s: true, ia: [""], uh: !1, rl: 1e3, sl: 1e3 }, rl: { s: true, ia: [""] }, bl: { s: true }, lc: { s: false }, sc: { r: 100, c: 3 }, whiteS: { "s": false, "ignoreD": 100, "sureW": 20, "possW": 50, "scale": 0.3 } }, r = ""; try { r = window.localStorage.WF_CONFIG ? JSON.parse(window.localStorage.WF_CONFIG) : t } catch (e) { r = t } return r } function y() { var e = {}, t = navigator.userAgent, r = t.match(/(Android);?[\s\/]+([\d.]+)?/), n = t.match(/(iPad).*OS\s([\d_]+)/), o = !n && t.match(/(iPhone\sOS)\s([\d_]+)/), i = t.match(/Android\s[\S\s]+Build\//), a = window.screen.width, s = window.screen.height; if (e.ios = e.android = e.iphone = e.ipad = e.androidChrome = !1, e.isWeixin = /MicroMessenger/i.test(t), e.os = "web", e.deviceName = "PC", e.deviceSize = a + "×" + s, r && (e.os = "android", e.osVersion = r[2], e.android = !0, e.androidChrome = 0 <= t.toLowerCase().indexOf("chrome")), (n || o) && (e.os = "ios", e.ios = !0), o && (e.osVersion = o[2].replace(/_/g, "."), e.iphone = !0), n && (e.osVersion = n[2].replace(/_/g, "."), e.ipad = !0), e.ios && e.osVersion && 0 <= t.indexOf("Version/") && "10" === e.osVersion.split(".")[0] && (e.osVersion = t.toLowerCase().split("version/")[1].split(" ")[0]), e.iphone) { r = "iphone"; 320 === a && 480 === s ? r = "4" : 320 === a && 568 === s ? r = "5/SE" : 375 === a && 667 === s ? r = "6/7/8" : 414 === a && 736 === s ? r = "6/7/8 Plus" : 375 === a && 812 === s ? r = "X/S/Max" : 414 === a && 896 === s ? r = "11/Pro-Max" : 375 === a && 812 === s ? r = "11-Pro/mini" : 390 === a && 844 === s ? r = "12/Pro" : 428 === a && 926 === s && (r = "12-Pro-Max"), e.deviceName = "iphone " + r } else if (e.ipad) e.deviceName = "ipad"; else if (i) { for (var c = i[0].split(";"), u = "", l = 0; l < c.length; l++)-1 != c[l].indexOf("Build") && (u = c[l].replace(/Build\//g, "")); "" == u && (u = c[1]), e.deviceName = u.replace(/(^\s*)|(\s*$)/g, "") } return -1 == t.indexOf("Mobile") && (a = navigator.userAgent.toLowerCase(), e.browserName = "其他", s = "", 0 < a.indexOf("msie") ? (s = a.match(/msie [\d.]+;/gi)[0], e.browserName = "ie", e.browserVersion = s.split("/")[1]) : 0 < a.indexOf("edg") ? (s = a.match(/edg\/[\d.]+/gi)[0], e.browserName = "edge", e.browserVersion = s.split("/")[1]) : 0 < a.indexOf("firefox") ? (s = a.match(/firefox\/[\d.]+/gi)[0], e.browserName = "firefox", e.browserVersion = s.split("/")[1]) : 0 < a.indexOf("safari") && a.indexOf("chrome") < 0 ? (s = a.match(/safari\/[\d.]+/gi)[0], e.browserName = "safari", e.browserVersion = s.split("/")[1]) : 0 < a.indexOf("chrome") && (s = a.match(/chrome\/[\d.]+/gi)[0], e.browserName = "chrome", e.browserVersion = s.split("/")[1], 0 < a.indexOf("360se")) && (e.browserName = "360")), e.webView = (o || n) && t.match(/.*AppleWebKit(?!.*Safari)/i), e } function v(e) { return e = K(e), -1 != V.indexOf(e) } function m(e, t) { var r = void 0 === (r = b().whiteS.scale) ? .3 : r, n = e.offsetWidth, o = e.offsetHeight, i = document.createElement("canvas"); i.style.display = "none", i.width = n * r, i.height = o * r, window.html2canvas && window.html2canvas(e, { scale: r, canvas: i, logging: !1, width: n, height: o, useCORS: !0 }).then(function (e) { e = e.toDataURL("image/webp").replace("data:image/webp;base64,", ""), e = B.b64Code(e); t(e) }) } var r = t(2), n = t.n(r), r = t(3), o = t.n(r), _ = "CUSTOMER_PV", U = "CUS_LEAVE", N = "PAGE_LOAD", q = "HTTP_LOG", D = "JS_ERROR", R = "ELE_BEHAVIOR", M = "RESOURCE_LOAD", i = "WF_DEBUG_STATUS", l = "LAST_BROWSE_DATE", W = "WM_PAGE_ENTRY_TIME", u = "WM_VISIT_PAGE_COUNT", O = "object" === ("undefined" == typeof navigator ? "undefined" : o()(navigator)) && "Microsoft Internet Explorer" == navigator.appName && parseInt(navigator.appVersion.split(";")[1].replace(/[ ]/g, "").replace("MSIE", "")) <= 9, J = 1, L = 4, r = t(0), a = t.n(r), r = t(1), p = t.n(r), B = new (function () { function e() { a()(this, e) } return p()(e, [{ key: "formatDate", value: function (e, t) { var e = new Date(e), r = e.getFullYear(), n = 9 < (n = e.getMonth() + 1) ? n : "0" + n, o = 9 < (o = e.getDate()) ? o : "0" + o, i = 9 < (i = e.getHours()) ? i : "0" + i, a = 9 < (a = e.getMinutes()) ? a : "0" + a, e = 9 < (e = e.getSeconds()) ? e : "0" + e; return t.replace("y", r).replace("M", n).replace("d", o).replace("h", i).replace("m", a).replace("s", e) } }, { key: "getUuid", value: function () { var e = this.formatDate((new Date).getTime(), "yMdhms"); return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (e) { var t = 16 * Math.random() | 0; return ("x" == e ? t : 3 & t | 8).toString(16) }) + "-" + e } }, { key: "b64Code", value: function (t) { t = encodeURIComponent(t); try { return btoa(encodeURIComponent(t).replace(/%([0-9A-F]{2})/g, function (e, t) { return String.fromCharCode("0x" + t) })) } catch (e) { return t } } }, { key: "perfSubtract", value: function (e, t) { return 0 === e || 0 === t ? 0 : e - t } }, { key: "getKeyByWebMonitorId", value: function (e) { var t = "", r = "old", n = this.getUuid(), o = this.getWfCookie("monitorCustomerKeys"), i = (new Date).getTime() + 31104e7; return o ? (o = JSON.parse(o))[e] ? t = o[e] : (o[e] = n, this.setWfCookie("monitorCustomerKeys", JSON.stringify(o), i), t = n, r = "new") : ((o = {})[e] = n, this.setWfCookie("monitorCustomerKeys", JSON.stringify(o), i), t = n, r = "new"), { customerKey: t, status: r } } }, { key: "getWebMonitorId", value: function () { var e = "webfunny_20241231_103037_pro", t = sessionStorage.CUSTOMER_WEB_MONITOR_ID || e; return t = -1 !== (t = /^webfunny\d*(_\d{8}_\d{6}(_[a-z]+)?)?$/.test(t) ? t : e).indexOf("_pro") && (e = this.getAppInfo("env")) ? t.replace("_pro", "_" + e) : t } }, { key: "getCusInfo", value: function (e) { var t = window.localStorage; return e && (t.wmUserInfo ? JSON.parse(t.wmUserInfo) : {})[e] || "" } }, { key: "getAppInfo", value: function (e) { var t = window.localStorage; return e && (t.wmAppInfo ? JSON.parse(t.wmAppInfo) : {})[e] || "" } }, { key: "setWfCookie", value: function (e, t, r) { var n = window.localStorage, t = { data: t, expires: r }; n.WEBFUNNY_COOKIE ? ((r = JSON.parse(n.WEBFUNNY_COOKIE))[e] = t, n.WEBFUNNY_COOKIE = JSON.stringify(r)) : ((r = {})[e] = t, n.WEBFUNNY_COOKIE = JSON.stringify(r)) } }, { key: "getWfCookie", value: function (e) { var t, r = window.localStorage, n = null; return r.WEBFUNNY_COOKIE && (t = (n = JSON.parse(r.WEBFUNNY_COOKIE))[e]) ? parseInt(t.expires, 10) < (new Date).getTime() ? (delete n[e], r.WEBFUNNY_COOKIE = JSON.stringify(n), "") : t.data : "" } }, { key: "isTodayBrowse", value: function (e) { var t = window.localStorage, r = t[e], n = (new Date).getFullYear() + "-" + ((new Date).getMonth() + 1) + "-" + (new Date).getDate(); return !(!r || n != r) || (t[e] = n, !1) } }, { key: "checkIgnore", value: function (e, t) { var r = b(); if (!t) return !0; try { for (var n = t.replace(/ /g, ""), o = r[e].ia || [], i = !0, a = 0; a < o.length; a++) { var s = o[a].replace(/ /g, ""); if (s && -1 != n.indexOf(s)) { i = !1; break } } return i } catch (e) { } } }, { key: "checkHttpReqResLen", value: function (e, t) { if (!e) return "无内容"; var r = b().hl, n = parseInt(r.rl, 10) || 2e3, r = parseInt(r.sl, 10) || 2e3, o = ""; if (e && e.length < ("req" === t ? n : r)) try { o = e } catch (e) { o = "" } else o = "内容太长"; return o } }, { key: "loadJs", value: function (e, t, r) { var n = document.createElement("script"), e = (n.async = 1, n.src = e, "function" == typeof t && (n.onload = t), "function" == typeof r && (n.onerror = r), document.getElementsByTagName("script")[0]); return e.parentNode.insertBefore(n, e), e } }]), e }()), f = function () { function o(e) { var t = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : ""; a()(this, o), this.url = e, this.uploadDebugUrl = t } return p()(o, [{ key: "sendByXhr", value: function (e, r) { if (this.url) try { var n = window.XMLHttpRequest ? new XMLHttpRequest : new ActiveXObject("Microsoft.XMLHTTP"), t = (n.open("post", this.url, !0), n.setRequestHeader("Content-Type", "application/json"), n.onreadystatechange = function () { if (4 == n.readyState) { var t = {}; try { t = n.responseText ? JSON.parse(n.responseText) : {} } catch (e) { t = {} } o.handleAjaxRes("success", t, r) } }, n.onerror = function (e) { o.handleAjaxRes("error") }, JSON.stringify(e || [])); n.send(t) } catch (e) { } } }, { key: "sendByBeacon", value: function (e) { this.url && (window.navigator && "function" == typeof navigator.sendBeacon ? navigator.sendBeacon(this.url, JSON.stringify(e)) : this.sendByXhr(e)) } }], [{ key: "handleAjaxRes", value: function (e, t, r) { var n = window.localStorage; "success" === e && (t && t.data && t.data.d && (n[i] = "c" == t.data.d ? "connected" : "disconnect", e = t.data.c) && (n.setItem("WF_CONFIG", t.data.c), 0 == JSON.parse(e).s) && (n = (new Date).getTime() + 6e5, B.setWfCookie("webfunnyStart", "p", n)), "function" == typeof r) && r(t) } }]), o }(), V = ["html", "body", "#app", "#root"], K = function (e) { return e.id ? "#" + e.id : e.className ? "." + e.className.split(" ").filter(function (e) { return !!e }).join(".") : e.nodeName.toLowerCase() }; function s(t, e) { var r, n = Object.keys(t); return Object.getOwnPropertySymbols && (r = Object.getOwnPropertySymbols(t), e && (r = r.filter(function (e) { return Object.getOwnPropertyDescriptor(t, e).enumerable })), n.push.apply(n, r)), n } function T(t) { for (var e = 1; e < arguments.length; e++) { var r = null != arguments[e] ? arguments[e] : {}; e % 2 ? s(Object(r), !0).forEach(function (e) { n()(t, e, r[e]) }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : s(Object(r)).forEach(function (e) { Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(r, e)) }) } return t } var d = function () { function e() { a()(this, e) } return p()(e, [{ key: "getPageViewInfo", value: function (e, t) { try { var r = document.referrer ? document.referrer.split("?")[0] : "", n = B.b64Code(r), o = y(), i = (s = B.getKeyByWebMonitorId(B.getWebMonitorId()), c = s.customerKey, u = "o", "new" === s.status ? u = "n_uv" : (s = B.isTodayBrowse(l), (c = c ? c.match(/\d{14}/g) : []) && 0 < c.length && (c = (c = c[0].match(/\d{2}/g))[0] + c[1] + "-" + c[2] + "-" + c[3] + " " + c[4] + ":" + c[5] + ":" + c[6], c = new Date(c).getTime(), u = 2e3 < (new Date).getTime() - c ? 0 == s ? "o_uv" : "o" : "n_uv")), u), a = { uploadType: _, happenTime: (new Date).getTime(), pageKey: B.getUuid(), deviceName: o.deviceName, deviceSize: o.deviceSize, os: o.os + (o.osVersion ? " " + o.osVersion : ""), browserName: o.browserName, browserVersion: o.browserVersion, monitorIp: "", country: "", province: "", city: "", loadType: t, newStatus: i, referrer: n }; return T(T({}, e), a) } catch (e) { } var s, c, u } }, { key: "getLoadPageInfo", value: function (e, t) { try { var r = y(), n = window.performance ? window.performance.timing : {}, o = n.fetchStart, i = B.perfSubtract(n.responseStart, o), a = B.perfSubtract(n.domContentLoadedEventEnd, o), s = B.perfSubtract(n.loadEventStart, o), c = B.perfSubtract(n.domainLookupEnd, n.domainLookupStart), u = B.perfSubtract(n.connectEnd, n.connectStart), l = B.perfSubtract(n.connectEnd, n.secureConnectionStart), p = B.perfSubtract(n.responseStart, n.requestStart), f = B.perfSubtract(n.responseEnd, n.responseStart), d = B.perfSubtract(n.domInteractive, n.responseEnd), h = B.perfSubtract(n.loadEventStart, n.domContentLoadedEventEnd), g = navigator && navigator.connection && navigator.connection.effectiveType || "unknown", w = { uploadType: N, happenTime: (new Date).getTime(), loadType: t, firstByte: i, domReady: a, pageCompleteLoaded: s, dns: c, tcp: u, ssl: l, response: p, conTrans: f, domAnalysis: d, resourceLoaded: h, effectiveType: g, os: r.os }; return T(T({}, e), w) } catch (e) { } } }, { key: "getLeavePageViewInfo", value: function (e, t) { t = t.leaveType, t = void 0 === t ? 1 : t; try { var r = { uploadType: U, happenTime: (new Date).getTime(), leaveType: t }; return T(T({}, e), r) } catch (e) { } } }, { key: "getWhiteScreenShotInfo", value: function (t, r) { var n = ""; try { var e = b().whiteS; if (e && e.s) { var o = e.sureW, i = void 0 === o ? 20 : o, a = e.possW, s = void 0 === a ? 50 : a, c = e.ignoreD, u = void 0 === c ? 100 : c, l = document.querySelector("body").getElementsByTagName("*").length; if (!(u < l)) { var p, f = y(), d = f.os, h = f.osVersion, g = void 0 === h ? "" : h; if ("web" !== g) { if (!g) return; var w = +g.split(".")[0]; if ("android" === d && w < 10) return; if ("ios" === d && w < 9) return } !0 === function () { for (var e = !0, t = 1; t <= 9; t++) { var r = document.elementsFromPoint(window.innerWidth * t / 10, window.innerHeight / 2), n = document.elementsFromPoint(window.innerWidth / 2, window.innerHeight * t / 10); if (!v(r[0])) { e = !1; break } if (!v(n[0])) { e = !1; break } } return e }() ? m(document.body, function (e) { e = { uploadType: "SCREEN_SHOT", happenTime: (new Date).getTime(), description: "极可能白屏", screenInfo: e, imgType: "jpeg" }; n = T(T({}, t), e), r(n) }) : (p = "", l <= i ? p = "极可能白屏" : l <= s && (p = "疑似白屏"), l <= s && m(document.body, function (e) { e = { uploadType: "SCREEN_SHOT", happenTime: (new Date).getTime(), description: p, screenInfo: e, imgType: "jpeg" }; n = T(T({}, t), e), r(n) })) } } } catch (e) { } } }]), e }(), c = null, x = new (function () { function e() { a()(this, e), this.uploadUrl = "", this.queues = [] } return p()(e, [{ key: "start", value: function () { var e, t = this, r = b(), n = parseInt(r.wc || "40", 10); c || (e = 0, c = setInterval(function () { (n <= e || 20 < t.queues.length) && (t.fireTasks(), e = 0), e++ }, 200), this.finallyFireTasks()) } }, { key: "addTask", value: function (e, t) { this.uploadUrl = e, this.queues.push(t) } }, { key: "fireTasks", value: function () { this.queues && 0 < this.queues.length && new f(this.uploadUrl).sendByXhr(this.queues), this.queues = [] } }, { key: "finallyFireTasks", value: function () { var e = this; window.addEventListener("beforeunload", function () { e.queues.length && (new f(e.uploadUrl).sendByBeacon(e.queues), e.queues = []) }) } }]), e }()), A = p()(function e(t) { a()(this, e); t = t.webMonitorId; this.webMonitorId = t, this.projectVersion = B.getAppInfo("projectVersion"), this.env = B.getAppInfo("env"), this.userId = B.getCusInfo("userId"), this.firstUserParam = B.getCusInfo("userTag"), this.completeUrl = B.b64Code(window.location.href), this.simpleUrl = B.b64Code(window.location.href.split("?")[0]), this.customerKey = B.getKeyByWebMonitorId(B.getWebMonitorId()).customerKey }); function h(e) { var t = ""; try { for (var r = [], n = e.length, o = 0; o < n; o++)r.push(e[o]); var i = {}; i.log = r, i.userId = B.getCusInfo("userId"), i.happenTime = (new Date).getTime(), t = B.b64Code(JSON.stringify(i)) } catch (e) { t = "reWriteConsole fail" } return t } var g = new (function () { function e() { a()(this, e), this.uploadUrl = "" } return p()(e, [{ key: "recordConsole", value: function (t) { var r = window.localStorage, n = console.log, o = console.warn; console.log = function () { var e = h(arguments); return "connected" === r[i] && new f(t.uploadDebugUrl).sendByXhr({ consoleInfo: e }), n.apply(console, arguments) }, console.warn = function () { var e = h(arguments); return "connected" === r[i] && new f(t.uploadDebugUrl).sendByXhr({ warnInfo: e }), o.apply(console, arguments) } } }, { key: "uploadLocalInfo", value: function (e) { var t = window.localStorage, r = B.getCusInfo("userId"), n = {}; for (o in t) "function" == typeof t[o] || 1e3 < t[o].length || (n[o] = t[o]); try { n = B.b64Code(JSON.stringify(n)) } catch (e) { n = "" } var o, i = {}; for (o in sessionStorage) "function" == typeof sessionStorage[o] || 1e3 < sessionStorage[o].length || (i[o] = sessionStorage[o]); try { i = B.b64Code(JSON.stringify(i)) } catch (e) { i = "" } var a = B.b64Code(document.cookie); new f(e.uploadDebugUrl).sendByXhr({ localInfo: n, sessionInfo: i, cookieInfo: a, userId: r || "userId" }, function (e) { e && e.data && 1 == +e.data.clear && (e = t.wmUserInfo, localStorage.clear(), localStorage.wmUserInfo = e, sessionStorage.clear(), t.WEBFUNNY_COOKIE = "") }) } }]), e }()), w = window.performance && "function" == typeof window.performance.getEntries ? window.performance.getEntries() : null, S = new (function () { function r() { a()(this, r), this.uploadUrl = "", this.defaultLocation = window.location.href.split("?")[0], this.lastPvRecordTime = 0, this.lastPvRecordUrl = "" } return p()(r, [{ key: "recordFirstPv", value: function (e) { var t = new A(e), r = window.localStorage, n = (new Date).getTime(), n = (r[W] = n, w && w[0] && "navigate" === w[0].type ? "load" : "reload"), t = (new d).getPageViewInfo(t, n); new f(e.uploadUrl).sendByXhr([t], function () { e.uploadDebugUrl && "connected" === r[i] && (g.recordConsole(e), g.uploadLocalInfo(e), setInterval(function () { g.uploadLocalInfo(e) }, 2e4)) }) } }, { key: "start", value: function (t) { var e = window, n = window.history; e.Event && "function" == typeof e.Event && (n.pushState = (e = function (e) { var t = n[e], r = new Event(e); return function () { var e = t.apply(this, arguments); return r.arguments = arguments, window.dispatchEvent(r), e } })("pushState"), n.replaceState = e("replaceState")), window.addEventListener("hashchange", function () { r.recordPageViewInfo(t, !1) }), window.addEventListener("popstate", function () { var e = window.location.href.split("?")[0].split("#")[0]; this.defaultLocation != e && (r.recordPageViewInfo(t, !1), this.defaultLocation = e) }), window.addEventListener("pushState", function (e) { r.recordPageViewInfo(t, !1) }), window.addEventListener("replaceState", function (e) { r.recordPageViewInfo(t, !1) }) } }, { key: "recordLeavePageViewInfo", value: function (c) { window.addEventListener("beforeunload", function () { var e, t, r = window.localStorage, n = new A(c), o = encodeURIComponent(window.location.href.split("?")[0]), i = (new Date).getTime(), a = (B.isTodayBrowse(l), null), i = B.formatDate(i, "y-M-d"), s = r[u]; s && (e = (s = s.split("$$$"))[0], t = s[1], s = parseInt(s[2], 10), i == t) ? o != e && 1 == s && (r[u] = o + "$$$" + i + "$$$2", a = (new d).getLeavePageViewInfo(n, 2)) : (r[u] = o + "$$$" + i + "$$$1", a = (new d).getLeavePageViewInfo(n, 1)), a && new f(c.uploadUrl).sendByBeacon([a]) }) } }, { key: "recordLoadPageInfo", value: function (r) { window.addEventListener("load", function () { setTimeout(function () { var e = new d, t = w && w[0] && "navigate" === w[0].type ? "load" : "reload", t = e.getLoadPageInfo(new A(r), t); x.addTask(r.uploadUrl, t), e.getWhiteScreenShotInfo(new A(r), function (e) { e && x.addTask(r.uploadUrl, e) }) }, 1e3) }) } }], [{ key: "recordPageViewInfo", value: function (e) { var t = 1 < arguments.length && void 0 !== arguments[1] && arguments[1], r = 2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : "reload", n = encodeURIComponent(window.location.href.split("?")[0]), o = (new Date).getTime(); n === this.lastPvRecordUrl && o - this.lastPvRecordTime < 300 ? this.lastPvRecordTime = o : (this.lastPvRecordTime = o, this.lastPvRecordUrl = n, o = (new d).getPageViewInfo(new A(e), r), !0 === t ? new f(e.uploadUrl).sendByXhr([o]) : x.addTask(e.uploadUrl, o)) } }]), r }()); function I(t, e) { var r, n = Object.keys(t); return Object.getOwnPropertySymbols && (r = Object.getOwnPropertySymbols(t), e && (r = r.filter(function (e) { return Object.getOwnPropertyDescriptor(t, e).enumerable })), n.push.apply(n, r)), n } function E(t) { for (var e = 1; e < arguments.length; e++) { var r = null != arguments[e] ? arguments[e] : {}; e % 2 ? I(Object(r), !0).forEach(function (e) { n()(t, e, r[e]) }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : I(Object(r)).forEach(function (e) { Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(r, e)) }) } return t } var X = function () { function e() { a()(this, e) } return p()(e, [{ key: "getJavaScriptErrorInfo", value: function (e, t) { var r = t.infoType, n = t.origin_errorMsg, t = t.origin_errorObj; try { var o, i, a = n || "", s = t || "", c = "", u = ""; if (0 !== a.length || 0 !== s.length) return 1e3 <= a.length && (a = a.substring(0, 999)), 3e3 <= s.length && (s = s.substring(0, 2999)), 80 <= a.length ? u = a.substring(0, 80) : 0 < a.length && a.length < 80 && (u = a), a && (c = ("string" == typeof s ? s : JSON.stringify(s)).split(": ")[0].replace('"', "")), o = y(), i = { uploadType: D, happenTime: (new Date).getTime(), infoType: r, pageKey: B.getUuid(), deviceName: o.deviceName, os: o.os + (o.osVersion ? " " + o.osVersion : ""), browserName: o.browserName, browserVersion: o.browserVersion, monitorIp: "", country: "", province: "", city: "", simpleErrorMessage: B.b64Code(c + ": " + u), errorMessage: B.b64Code(c + ": " + a), errorStack: B.b64Code(s), browserInfo: B.b64Code(navigator.userAgent) }, E(E({}, e), i) } catch (e) { } } }]), e }(), $ = new (function () { function s() { a()(this, s), this.uploadUrl = "" } return p()(s, [{ key: "recordJsError", value: function (i) { var r, a = new A(i); window.onerror = function (e, t, r, n, o) { o = o ? o.stack : null; s.storeError(i, a, { infoType: "on_error", origin_errorMsg: e, origin_errorObj: o }) }, !0 != O && (r = console.error, console.error = function (e) { var t = e && e.message || e, e = e && e.stack; if (e) s.storeError(i, a, { infoType: "on_error", origin_errorMsg: t, origin_errorObj: e }); else { if ("object" == o()(t)) try { t = JSON.stringify(t) } catch (e) { t = "错误无法解析" } s.storeError(i, a, { infoType: "console_error", origin_errorMsg: t, origin_errorObj: "CustomizeError: " + t }) } return r.apply(console, arguments) }, window.onunhandledrejection = function (e) { var t = "", r = "", r = "object" === o()(e.reason) ? (t = e.reason.message, e.reason.stack) : (t = e.reason, ""); ": " === t && (t = r), s.storeError(i, a, { infoType: "on_error", origin_errorMsg: t, origin_errorObj: "UncaughtInPromiseError: " + r }) }) } }], [{ key: "storeError", value: function (e, t, r) { var n = r.origin_errorMsg, t = (new X).getJavaScriptErrorInfo(t, r); B.checkIgnore("je", n) && x.addTask(e.uploadUrl, t) } }]), s }()), r = t(6), G = t.n(r), r = t(4), Y = t.n(r); function z(t, e) { var r, n = Object.keys(t); return Object.getOwnPropertySymbols && (r = Object.getOwnPropertySymbols(t), e && (r = r.filter(function (e) { return Object.getOwnPropertyDescriptor(t, e).enumerable })), n.push.apply(n, r)), n } function j(t) { for (var e = 1; e < arguments.length; e++) { var r = null != arguments[e] ? arguments[e] : {}; e % 2 ? z(Object(r), !0).forEach(function (e) { n()(t, e, r[e]) }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : z(Object(r)).forEach(function (e) { Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(r, e)) }) } return t } var Q = function () { function e() { a()(this, e) } return p()(e, [{ key: "getHttpLogInfo", value: function (e, t) { try { var r = t.simpleUrl, n = t.url, o = t.headerText, i = t.requestText, a = t.responseText, s = t.segment, c = void 0 === s ? "" : s, u = t.traceId, l = void 0 === u ? "" : u, p = j(j({ uploadType: q, happenTime: (new Date).getTime() }, t), {}, { simpleUrl: B.b64Code(r), httpUrl: B.b64Code(n), headerText: B.b64Code(o), requestText: B.b64Code(i), responseText: B.b64Code(a), segment: c, traceId: l }); return j(j({}, e), p) } catch (e) { } } }]), e }(); function Z(t, e) { var r, n = Object.keys(t); return Object.getOwnPropertySymbols && (r = Object.getOwnPropertySymbols(t), e && (r = r.filter(function (e) { return Object.getOwnPropertyDescriptor(t, e).enumerable })), n.push.apply(n, r)), n } function H(t) { for (var e = 1; e < arguments.length; e++) { var r = null != arguments[e] ? arguments[e] : {}; e % 2 ? Z(Object(r), !0).forEach(function (e) { n()(t, e, r[e]) }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : Z(Object(r)).forEach(function (e) { Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(r, e)) }) } return t } var F = ["/wfMonitor/upLog", "/wfMonitor/upDLog", "/wfMonitor/upLogs", "/wfEvent/upEvent", "/wfEvent/upEvents", "/wfEvent/initCf"], ee = window.fetch, te = new (function () { function V() { a()(this, V), this.uploadUrl = "" } return p()(V, [{ key: "recordHttpLog", value: function (x) { var S, I, E, e, j, k, P, t = window.XMLHttpRequest, r = XMLHttpRequest.prototype.send, i = XMLHttpRequest.prototype.open; r && i && (t.getRequestConfig = [], S = B.getUuid(), I = B.getUuid(), E = x.webMonitorId, e = x.hasTrace, j = void 0 === e ? "0" : e, k = new A(x), window.XMLHttpRequest = function () { var e = new t; return e.addEventListener("readystatechange", function () { !function (e) { e = new CustomEvent(e, { detail: this }), window.dispatchEvent(e) }.call(this, "xhrReadyStateChange") }, !1), e.open = function (e, t, r, n, o) { return this.getRequestConfig = arguments, i.apply(this, arguments) }, e.send = function (e) { return this.getRequestBody = e, r.apply(this, arguments) }, e }, P = [], window.addEventListener("xhrReadyStateChange", function (l) { try { for (var e = B.getUuid(), t = B.getUuid(), p = { traceId: e, service: E, spans: [], serviceInstance: B.getAppInfo("projectVersion"), serviceEnvironment: B.getAppInfo("env"), traceSegmentId: t }, f = window.location.pathname, r = l.detail.readyState, d = l.detail.getRequestConfig, h = {}, n = (d[1].startsWith("http://") || d[1].startsWith("https://") ? h = new URL(d[1]) : d[1].startsWith("//") ? h = new URL("".concat(window.location.protocol).concat(d[1])) : (h = new URL(window.location.href)).pathname = d[1], !1), o = 0; o < F.length; o++)if (-1 !== h.href.indexOf(F[o])) { n = !0; break } if (!0 !== n) { if (r === J) { if (P.push({ event: l.detail, startTime: (new Date).getTime(), traceId: p.traceId, traceSegmentId: p.traceSegmentId }), "0" == j) return; var i = String(B.b64Code(p.traceId)), a = String(B.b64Code(p.traceSegmentId)), s = String(B.b64Code(p.service)), c = String(B.b64Code(p.serviceInstance)), u = String(B.b64Code(f)), g = String(B.b64Code(window.location.host)), w = p.spans.length, y = "".concat(1, "-", i, "-").concat(a, "-").concat(w, "-").concat(s, "-").concat(c, "-").concat(u, "-").concat(g); l.detail.setRequestHeader("sw8", y) } if (r === L) for (var v, m, b, O = (new Date).getTime() + 1, T = 0; T < P.length; T++)if (b = function () { if (P[T].event.readyState === L) { var e = {}, t = (P[T].event.status && (e = new URL(P[T].event.responseURL)), l.detail.getRequestBody), r = ""; if ("blob" === (l.detail.responseType + "").toLowerCase()) { var n = new FileReader; n.onload = function () { r = n.result }; try { n.readAsText(l.detail.response, "utf-8") } catch (e) { r = e.detail.response + "" } } else { if (!(v = l.detail)) return { v: void 0 }; "" !== (m = v.responseType) && "text" !== m || (r = v.responseText), "json" === m && (r = JSON.stringify(v.response)) } var t = B.checkHttpReqResLen(t || "", "req"), r = B.checkHttpReqResLen(r || "", "res"), o = d[0], i = l.detail, a = i.status, i = i.statusText, s = P[T].startTime, c = P[T], u = c.traceId, c = c.traceSegmentId, a = { operationName: f, startTime: s, endTime: O, spanId: p.spans.length, spanLayer: "Http", spanType: "Exit", isError: 0 === a || 400 <= a, parentSpanId: p.spans.length - 1, componentId: 10001, peer: e.host, tags: [{ key: "http.method", value: o }, { key: "url", value: P[T].event.responseURL || "".concat(h.protocol, "//").concat(h.host).concat(h.pathname) }, { key: "sessionId", value: I }, { key: "viewId", value: S }] }; (p = H(H({}, p), {}, { traceId: u, traceSegmentId: c })).spans.push(a), "0" == j && (p = ""), V.storeHttpLog(x, k, { uploadType: q, method: o, headerText: "", simpleUrl: e.href, url: h, status: l.detail.status, statusText: i, statusResult: "request", requestText: "", responseText: "", currentTime: s, loadTime: 0 }), V.storeHttpLog(x, k, { uploadType: q, method: o, headerText: "", simpleUrl: e.href, url: h, status: l.detail.status, statusText: i, statusResult: "response", requestText: t, responseText: r, currentTime: O, loadTime: O - s, segment: p, traceId: u }), P.splice(T, 1) } }()) return b.v } } catch (l) { } })) } }, { key: "recordFetch", value: function (N) { var D = N.webMonitorId, e = N.hasTrace, R = void 0 === e ? "0" : e, M = B.getUuid(), W = B.getUuid(); window.fetch = G()(Y.a.mark(function e() { var t, r, n, o, i, a, s, c, u, l, p, f, d, h, g, w, y, v, m, b, O, T, x, S, I, E, j, k, P, L, C, _, U = arguments; return Y.a.wrap(function (e) { for (; ;)switch (e.prev = e.next) { case 0: for (t = (new Date).getTime(), r = B.getUuid(), n = B.getUuid(), o = { traceId: r, service: D, spans: [], serviceInstance: B.getAppInfo("projectVersion"), serviceEnvironment: B.getAppInfo("env"), traceSegmentId: "" }, i = {}, a = U.length, s = new Array(a), c = 0; c < a; c++)s[c] = U[c]; s[0].startsWith("http://") || s[0].startsWith("https://") ? i = new URL(s[0]) : s[0].startsWith("//") ? i = new URL("".concat(window.location.protocol).concat(s[0])) : (i = new URL(window.location.href)).pathname = s[0], u = !1, l = 0; case 9: if (l < F.length) { if (-1 !== i.href.indexOf(F[l])) return u = !0, e.abrupt("break", 16); e.next = 13 } else e.next = 16; break; case 13: l++, e.next = 9; break; case 16: if (!0 === u) return e.abrupt("return"); e.next = 18; break; case 18: return p = new A(N), f = window.location.pathname, "1" == R && (w = String(B.b64Code(o.traceId)), y = String(B.b64Code(n)), I = String(B.b64Code(o.service)), d = String(B.b64Code(o.serviceInstance)), h = String(B.b64Code(f)), g = String(B.b64Code(window.location.host)), v = o.spans.length, w = "".concat(1, "-", w, "-").concat(y, "-").concat(v, "-").concat(I, "-").concat(d, "-").concat(h, "-").concat(g), s[1] || (s[1] = {}), s[1].headers || (s[1].headers = {}), s[1].headers.sw8 = w), e.next = 23, ee.apply(void 0, s); case 23: y = e.sent, v = y.clone(), m = "get", b = s[0], O = window.location.href.split("?")[0], S = x = T = "", (I = s[1]) && (m = I.method, T = JSON.stringify(I.headers), x = B.checkHttpReqResLen(I.body || "", "req")); try { y && 200 === y.status ? (E = (new Date).getTime() + 1, j = y.statusText, k = E - t, y.text().then(function (e) { S = B.checkHttpReqResLen(e || "", "res"); e = "", e = b.startsWith("http://") || b.startsWith("https://") ? new URL(b) : b.startsWith("//") ? new URL("".concat(window.location.protocol).concat(b)) : b, e = { operationName: f, startTime: t, endTime: E, spanId: o.spans.length, spanLayer: "Http", spanType: "Exit", isError: !1, parentSpanId: o.spans.length - 1, componentId: 10001, peer: e.host, tags: [{ key: "http.method", value: m }, { key: "url", value: b }, { key: "sessionId", value: W }, { key: "viewId", value: M }] }; (o = H(H({}, o), {}, { traceId: r, traceSegmentId: n })).spans.push(e), "0" == R && (o = ""), V.storeHttpLog(N, p, { uploadType: q, method: m, headerText: "", simpleUrl: O, url: b, status: 200, statusText: j, statusResult: "request", requestText: "", responseText: "", currentTime: t, loadTime: 0 }), V.storeHttpLog(N, p, { uploadType: q, method: m, headerText: T, simpleUrl: O, url: b, status: 200, statusText: j, statusResult: "response", requestText: x, responseText: S, currentTime: E, loadTime: k, segment: o, traceId: r }) }).catch(function (e) { })) : (P = (new Date).getTime(), L = y.status, C = y.statusText, _ = P - t, y.text().then(function (e) { S = B.checkHttpReqResLen(e || "", "res"), V.storeHttpLog(N, p, { uploadType: q, method: m, headerText: "", simpleUrl: O, url: b, status: L, statusText: C, statusResult: "request", requestText: "", responseText: "", currentTime: t, loadTime: 0 }), V.storeHttpLog(N, p, { uploadType: q, method: m, headerText: T, simpleUrl: O, url: b, status: L, statusText: C, statusResult: "response", requestText: x, responseText: S, currentTime: P, loadTime: _ }) }).catch(function (e) { })) } catch (e) { } return e.abrupt("return", v); case 35: case "end": return e.stop() } }, e) })) } }], [{ key: "storeHttpLog", value: function (e, t, r) { var n = r.simpleUrl; B.checkIgnore("hl", n) && (n = (new Q).getHttpLogInfo(t, r), x.addTask(e.uploadUrl, n)) } }]), V }()); function re(t, e) { var r, n = Object.keys(t); return Object.getOwnPropertySymbols && (r = Object.getOwnPropertySymbols(t), e && (r = r.filter(function (e) { return Object.getOwnPropertyDescriptor(t, e).enumerable })), n.push.apply(n, r)), n } function k(t) { for (var e = 1; e < arguments.length; e++) { var r = null != arguments[e] ? arguments[e] : {}; e % 2 ? re(Object(r), !0).forEach(function (e) { n()(t, e, r[e]) }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : re(Object(r)).forEach(function (e) { Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(r, e)) }) } return t } var ne = function () { function e() { a()(this, e) } return p()(e, [{ key: "getResourceErrorInfo", value: function (e, t) { try { var r = k(k({ uploadType: M, happenTime: (new Date).getTime() }, t), {}, { sourceUrl: B.b64Code(t.sourceUrl) }); return k(k({}, e), r) } catch (e) { } } }]), e }(), oe = new (function () { function i() { a()(this, i), this.uploadUrl = "" } return p()(i, [{ key: "recordResourceError", value: function (n) { var o = new A(n); window.addEventListener("error", function (e) { var t = e.target.localName, r = ""; "link" === t ? r = e.target.href : "script" === t && (r = e.target.src), r = r ? r.split("?")[0] : "", i.store(n, o, { sourceUrl: r, elementType: t, status: "0" }) }, !0) } }], [{ key: "store", value: function (e, t, r) { var n = r.sourceUrl; B.checkIgnore("rl", n) && (n = (new ne).getResourceErrorInfo(t, r), x.addTask(e.uploadUrl, n)) } }]), i }()); function ie(t, e) { var r, n = Object.keys(t); return Object.getOwnPropertySymbols && (r = Object.getOwnPropertySymbols(t), e && (r = r.filter(function (e) { return Object.getOwnPropertyDescriptor(t, e).enumerable })), n.push.apply(n, r)), n } function P(t) { for (var e = 1; e < arguments.length; e++) { var r = null != arguments[e] ? arguments[e] : {}; e % 2 ? ie(Object(r), !0).forEach(function (e) { n()(t, e, r[e]) }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : ie(Object(r)).forEach(function (e) { Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(r, e)) }) } return t } var ae = function () { function e() { a()(this, e) } return p()(e, [{ key: "getClickInfo", value: function (e, t) { try { var r = P(P({ uploadType: R, happenTime: (new Date).getTime() }, t), {}, { className: B.b64Code(t.className), placeholder: B.b64Code(t.placeholder), inputValue: B.b64Code(t.inputValue) }); return P(P({}, e), r) } catch (e) { } } }]), e }(), se = new (function () { function c() { a()(this, c), this.uploadUrl = "" } return p()(c, [{ key: "recordClick", value: function (a) { var s = new A(a); window.addEventListener("click", function (e) { var t, r, n, o, i; e && (o = e.target.tagName, i = n = r = t = "", "svg" != e.target.tagName && "use" != e.target.tagName && (t = e.target.className, r = e.target.placeholder || "", n = e.target.value || "", i = (i = 100 < (i = e.target.innerText ? e.target.innerText.replace(/\s*/g, "") : "").length ? i.substring(0, 50) + " ... " + i.substring(i.length - 49, i.length - 1) : i).replace(/\s/g, "")), c.store(a, s, { behaviorType: "click", className: t, placeholder: r, inputValue: n, tagName: o, innerText: i })) }, !0) } }], [{ key: "store", value: function (e, t, r) { t = (new ae).getClickInfo(t, r); x.addTask(e.uploadUrl, t) } }]), c }()); function ce(t, e) { var r, n = Object.keys(t); return Object.getOwnPropertySymbols && (r = Object.getOwnPropertySymbols(t), e && (r = r.filter(function (e) { return Object.getOwnPropertyDescriptor(t, e).enumerable })), n.push.apply(n, r)), n } function ue(t) { for (var e = 1; e < arguments.length; e++) { var r = null != arguments[e] ? arguments[e] : {}; e % 2 ? ce(Object(r), !0).forEach(function (e) { n()(t, e, r[e]) }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : ce(Object(r)).forEach(function (e) { Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(r, e)) }) } return t } window.sessionStorage || (window.sessionStorage = {}), window.localStorage || (window.localStorage = {}); var t = { customOptions: { wfConfig: b() }, start: function (e) { this.customOptions = ue(ue({}, this.customOptions), e); try { var t = b(), r = t.whiteS; if (r && r.s) { if (window.html2canvas) return; B.loadJs("".concat(this.customOptions.assetsDomain, "/wf_assets/screenShot/html2canvas.min.js")) } for (var n = !1, o = t.ia || [], i = !1, a = 0; a < o.length; a++) { var s = o[a].replace(/ /g, ""); if (s && -1 != (window.location.href + window.location.hash).indexOf(s)) { i = !0; break } } var c, u, l, p, f, d = B.getWfCookie("webfunnyStart") || t.s; if (d && "p" != d && !i) { var h, g, w, y, v, m = B.getWfCookie("samplingChoose"); if (2 === m) return; 1 !== m && (g = (h = t.sc || { r: 100, c: 3 }).r, w = +h.c, g < 100) ? (y = Math.ceil(100 * Math.random()), v = (new Date).getTime() + 24 * w * 3600 * 1e3, y <= g ? (B.setWfCookie("samplingChoose", 1, v), n = !0) : B.setWfCookie("samplingChoose", 2, v)) : n = !0 } !1 !== n && (x.start(), c = t.pv, u = t.je, l = t.hl, p = t.rl, f = t.bl, c.s && this.pageView(this.customOptions), u.s && this.javaScriptError(this.customOptions), l.s && !1 == O && this.http(e), p.s && !1 == O && this.resource(e), f.s && !1 == O) && this.action(e) } catch (e) { } }, init: function (e) { var t; window.localStorage && (t = e.env, e = e.projectVersion, window.localStorage.wmAppInfo = JSON.stringify({ projectVersion: void 0 === e ? "1.0.1" : e, env: void 0 === t ? "pro" : t })) }, initUser: function (e) { var t = e.userId, e = e.userTag; window.localStorage && (window.localStorage.wmUserInfo = JSON.stringify({ userId: void 0 === t ? "userId" : t, userTag: void 0 === e ? "tag" : e })) }, pageView: function (e) { try { S.recordFirstPv(e), !1 == O && S.start(e), S.recordLoadPageInfo(e), S.recordLeavePageViewInfo(e) } catch (e) { } }, javaScriptError: function (e) { try { $.recordJsError(e) } catch (e) { } }, http: function (e) { try { te.recordHttpLog(e), te.recordFetch(e) } catch (e) { } }, resource: function (e) { try { oe.recordResourceError(e) } catch (e) { } }, action: function (e) { try { se.recordClick(e) } catch (e) { } } }, r = (window.WebfunnyMonitor = t, ""), le = "", le = (window.webfunnyDomain && window.webfunnyDomain.startsWith("http") ? (r = "".concat(window.webfunnyDomain || "zxa-b.hainancrc.com", "/wfMonitor/upLogs"), "") : window.location.href.startsWith("file") ? (r = "https://".concat(window.webfunnyDomain || "zxa-b.hainancrc.com", "/wfMonitor/upLogs"), "https://") : (r = "//".concat(window.webfunnyDomain || "zxa-b.hainancrc.com", "/wfMonitor/upLogs"), "//")).concat(window.webfunnyDomain || "zxa-b.hainancrc.com", "/wfMonitor/upDLog"), pe = B.getWebMonitorId(); t.start({ webMonitorId: pe, appName: "Pro-诚信白沙", uploadUrl: r, uploadDebugUrl: le, assetsDomain: "//zxa.hainancrc.com", hasTrace: window.webfunnyHasTrace || "0" }), e.default = t }]);
    </script>
    <script>
        var webfunnyGlobal = window; !function (n) { var r = {}; function o(t) { var e; return (r[t] || (e = r[t] = { i: t, l: !1, exports: {} }, n[t].call(e.exports, e, e.exports, o), e.l = !0, e)).exports } o.m = n, o.c = r, o.d = function (t, e, n) { o.o(t, e) || Object.defineProperty(t, e, { enumerable: !0, get: n }) }, o.r = function (t) { "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(t, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(t, "__esModule", { value: !0 }) }, o.t = function (e, t) { if (1 & t && (e = o(e)), 8 & t) return e; if (4 & t && "object" == typeof e && e && e.__esModule) return e; var n = Object.create(null); if (o.r(n), Object.defineProperty(n, "default", { enumerable: !0, value: e }), 2 & t && "string" != typeof e) for (var r in e) o.d(n, r, function (t) { return e[t] }.bind(null, r)); return n }, o.n = function (t) { var e = t && t.__esModule ? function () { return t.default } : function () { return t }; return o.d(e, "a", e), e }, o.o = function (t, e) { return Object.prototype.hasOwnProperty.call(t, e) }, o.p = "", o(o.s = 20) }([function (t, e, n) { "use strict"; n.d(e, "k", function () { return f }), n.d(e, "c", function () { return d }), n.d(e, "f", function () { return h }), n.d(e, "g", function () { return p }), n.d(e, "d", function () { return w }), n.d(e, "j", function () { return m }), n.d(e, "i", function () { return v }), n.d(e, "h", function () { return x }), n.d(e, "e", function () { return E }), n.d(e, "a", function () { return O }); var r = n(6), c = n.n(r), r = n(15), o = n.n(r), a = (n(23), n(1)), i = n(5), s = n(4); function u(t) { return "[object Array]" === Object.prototype.toString.apply(t) } function l(t) { return "[object Object]" === Object.prototype.toString.apply(t) } function f() { var t = window.location.search, e = t.indexOf("?"), a = {}; return -1 !== e && t.substring(e + 1).split("&").forEach(function (t) { var t = t.split("="), e = decodeURIComponent(t[0]), t = decodeURIComponent(t[1] || ""), n = 0, r = e.split("."), o = r.length, e = r[o - 1], i = a; if (1 < o) for (; n < o - 1; n++)i[r[n]] && l(i[r[n]]) || (i[r[n]] = {}), i = i[r[n]]; "[]" !== e.substring(-2) ? i[e] = t : i[e = e.substring(0, e.length - 2)] ? i[e].push(t) : i[e] = [t] }), a } function d(t, e) { var n, r = { success: !1, field: "" }; for (n in e) { if (!Object.prototype.hasOwnProperty.call(t, n)) return r.success = !1, r.field = n, r; r.success = !0 } return r } function h(t) { try { var e = document; return e.evaluate(t, e).iterateNext() } catch (t) { return console.log(t), null } } function p(t, e) { var n, e = e || document; if (n = e.querySelectorAll, "undefined" !== c()(n)) return e.querySelectorAll("[" + t + "]"); for (var r = [], o = e.getElementsByTagName("*"), i = 0, a = o.length; i < a; i++) { var s = o[i]; null !== s.getAttribute(t) && r.push(s) } return r } function g(t, e) { var n, r, o, i = 2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : (new Date).getTime() + 2592e6; window ? (n = { data: e, expires: i }, (r = localStorage).WEBFUNNY_COOKIE ? ((o = JSON.parse(r.WEBFUNNY_COOKIE))[t] = n, r.WEBFUNNY_COOKIE = JSON.stringify(o)) : ((o = {})[t] = n, r.WEBFUNNY_COOKIE = JSON.stringify(o))) : webfunnyGlobal && (n = { data: e, expires: i }, (r = webfunnyGlobal.getStorageSync(a.e)) ? (r[t] = n, webfunnyGlobal.setStorageSync(a.e, r)) : ((o = {})[t] = n, webfunnyGlobal.setStorageSync(a.e, o))) } function y(t) { var e; if (window) return n = null, (r = localStorage).WEBFUNNY_COOKIE && (e = (n = JSON.parse(r.WEBFUNNY_COOKIE))[t]) ? parseInt(e.expires, 10) < (new Date).getTime() ? (delete n[t], r.WEBFUNNY_COOKIE = JSON.stringify(n), "") : e.data : ""; if (webfunnyGlobal) { var n, r = webfunnyGlobal.getStorageSync(a.e); if (r) return (n = r[t]) ? parseInt(n.expires, 10) < (new Date).getTime() ? (delete r[t], webfunnyGlobal.setStorageSync(a.e, r), "") : n.data : "" } return "" } function b() { var t = (new Date).Format("yyyyMMddhhmmssS"); return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (t) { var e = 16 * Math.random() | 0; return ("x" == t ? e : 3 & e | 8).toString(16) }) + "-" + t } function w() { return t = a.b, e = "", n = b(), r = y("monitorCustomerKeys"), o = (new Date).getTime() + 31104e7, r ? (r = JSON.parse(r))[t] ? e = r[t] : (r[t] = n, g("monitorCustomerKeys", JSON.stringify(r), o), e = n, 0) : ((r = {})[t] = n, g("monitorCustomerKeys", JSON.stringify(r), o), e = n, 0), e; var t, e, n, r, o } function m(t) { var e = ""; if (window) { var n = localStorage; if (!t) return ""; e = (n.wmUserInfo ? JSON.parse(n.wmUserInfo) : {})[t] } else { if (!t) return ""; webfunnyGlobal && (e = ((n = webfunnyGlobal.getStorageSync("wmUserInfo")) ? JSON.parse(n) : {})[t]) } return e || "" } function v() { var t, e = ""; return window ? e = "full" === (0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : "") ? window.location.href : window.location.href.split("?")[0] : getCurrentPages && 0 < (t = getCurrentPages()).length && (e = getCurrentPages()[t.length - 1].route), e } function x() { var t, e = y("monitorCustomerKey"); return e ? (t = "", (e = e ? e.match(/\d{14}/g) : []) && 0 < e.length && (t = (e = e[0].match(/\d{2}/g))[0] + e[1] + "-" + e[2] + "-" + e[3] === (new Date).Format("yyyy-MM-dd") ? 1 : 2)) : t = 1, t } function E() { if (window) { var t = {}, e = navigator.userAgent, n = e.match(/(Android);?[\s\/]+([\d.]+)?/), r = e.match(/(iPad).*OS\s([\d_]+)/), o = !r && e.match(/(iPhone\sOS)\s([\d_]+)/), i = e.match(/Android\s[\S\s]+Build\//), a = window.screen.width, s = window.screen.height; if (t.ios = t.android = t.iphone = t.ipad = t.androidChrome = !1, t.isWeixin = /MicroMessenger/i.test(e), t.os = "web", t.deviceName = "PC", t.deviceSize = a + "×" + s, t.platform = navigator.platform, n && (t.os = "android", t.osVersion = n[2], t.android = !0, t.androidChrome = 0 <= e.toLowerCase().indexOf("chrome")), (r || o) && (t.os = "ios", t.ios = !0), o && (t.osVersion = o[2].replace(/_/g, "."), t.iphone = !0), r && (t.osVersion = r[2].replace(/_/g, "."), t.ipad = !0), t.ios && t.osVersion && 0 <= e.indexOf("Version/") && "10" === t.osVersion.split(".")[0] && (t.osVersion = e.toLowerCase().split("version/")[1].split(" ")[0]), t.iphone) { var n = "".concat(a, " x ").concat(s); 320 === a && 480 === s ? n = "4" : 320 === a && 568 === s ? n = "5/SE" : 375 === a && 667 === s ? n = "6/7/8" : 414 === a && 736 === s ? n = "6/7/8 Plus" : 375 === a && 812 === s ? n = "X/S/Max" : 414 === a && 896 === s ? n = "11/Pro-Max" : 375 === a && 812 === s ? n = "11-Pro/mini" : 390 === a && 844 === s ? n = "12/13/Pro" : 428 === a && 926 === s && (n = "12/13/Pro-Max"), t.deviceName = "iphone " + n } else if (t.ipad) t.deviceName = "ipad"; else if (i) { for (var c = i[0].split(";"), u = "", l = 0; l < c.length; l++)-1 != c[l].indexOf("Build") && (u = c[l].replace(/Build\//g, "")); "" == u && (u = c[1]), t.deviceName = u.replace(/(^\s*)|(\s*$)/g, "") } return -1 == e.indexOf("Mobile") && (o = navigator.userAgent.toLowerCase(), t.browserName = "其他", r = "", 0 < o.indexOf("msie") ? (r = o.match(/msie [\d.]+;/gi)[0], t.browserName = "ie", t.browserVersion = r.split("/")[1]) : 0 < o.indexOf("edg") ? (r = o.match(/edg\/[\d.]+/gi)[0], t.browserName = "edge", t.browserVersion = r.split("/")[1]) : 0 < o.indexOf("firefox") ? (r = o.match(/firefox\/[\d.]+/gi)[0], t.browserName = "firefox", t.browserVersion = r.split("/")[1]) : 0 < o.indexOf("safari") && o.indexOf("chrome") < 0 ? (r = o.match(/safari\/[\d.]+/gi)[0], t.browserName = "safari", t.browserVersion = r.split("/")[1]) : 0 < o.indexOf("chrome") && (r = o.match(/chrome\/[\d.]+/gi)[0], t.browserName = "chrome", t.browserVersion = r.split("/")[1], 0 < o.indexOf("360se")) && (t.browserName = "360")), { deviceName: t.deviceName, system: "".concat(t.os, " ").concat(t.osVersion || ""), os: t.os, platform: t.platform, browserName: t.browserName } } return n = s = a = "", webfunnyGlobal && (a = (i = webfunnyGlobal.getSystemInfoSync() || {}).model, s = i.system, n = i.platform), e = "", r = s.split(" "), { deviceName: a, system: s, os: e = 1 < r.length ? r[0] : e, platform: n } } function O(t, e, n) { t.addEventListener ? t.addEventListener(e, n, !1) : t.attachEvent ? t.attachEvent("on" + e, n) : t["on" + e] = n } e.b = { isString: function (t) { return "[object String]" === Object.prototype.toString.apply(t) }, isNumber: function (t) { return "[object Number]" === Object.prototype.toString.apply(t) }, isArray: u, isObject: l, isDate: function (t) { return "[object Date]" === Object.prototype.toString.apply(t) }, isBoolean: function (t) { return "[object Boolean]" === Object.prototype.toString.apply(t) }, getUuid: b, getWfCookie: y, setWfCookie: g, b64Code: function (t) { var e = encodeURIComponent(t); try { if (window) return btoa(encodeURIComponent(e).replace(/%([0-9A-F]{2})/g, function (t, e) { return String.fromCharCode("0x" + e) })); for (var n = new Uint8Array(e.length), r = 0; r < e.length; r++)n[r] = e.charCodeAt(r); return webfunnyGlobal ? webfunnyGlobal.arrayBufferToBase64(n) : "" } catch (t) { return e } }, getPath: v, getXPath: function (t) { for (var e = t.target, n = []; e.nodeType === Node.ELEMENT_NODE;) { var r = e.nodeName.toLowerCase(), r = function (t, e) { for (var n = 0; t;)t.nodeName.toLowerCase() === e && (n += 1), t = t.previousElementSibling; return n }(e, r); n.push("".concat(e.tagName.toLowerCase()).concat(1 === r ? "" : "[".concat(r, "]"))), e = e.parentNode } return "/".concat(n.reverse().join("/")) }, getEleByXpath: h, combineObject: function (t, e) { return o()(t, e) }, getCustomerKey: w, getLastHeartBeatTime: function (t) { var e = (new Date).getTime(), n = y("lastHearBeatTime"); return n && 1 !== t || (g("lastHearBeatTime", e, e + a.a.HEART_RATE + 5e3), n = e), n }, getWeUserInfo: m, setFirstActionTime: function (t) { for (var e = y("weFunnelConfig").funnelList, n = void 0 === e ? [] : e, r = 0; r < n.length; r++) { var o = n[r], i = o.s, i = void 0 === i ? "" : i, a = o.c, a = void 0 === a ? "" : a, o = o.t, o = void 0 === o ? 1 : o, o = (new Date).getTime() + 24 * o * 3600 * 1e3; -1 !== i.indexOf(+t) && (i = (new Date).Format("yyyyMMdd"), g("".concat(a, "-FirstStepDay"), "".concat(a, "-").concat(i), o)) } }, getFirstActionTime: function (t) { for (var e = y("weFunnelConfig").funnelList, n = void 0 === e ? [] : e, r = "", o = 0; o < n.length; o++) { var i = n[o], a = i.s, a = void 0 === a ? "" : a, i = i.c, i = void 0 === i ? "" : i; -1 !== a.indexOf(+t) && (r += y("".concat(i, "-FirstStepDay")) + ",") } return r }, addTask: function (t) { s.a.addTask(i.a.trackUrl, t) }, fireTasks: function () { s.a.fireTasks() } } }, function (t, e, n) { "use strict"; n.d(e, "b", function () { return r }), n.d(e, "a", function () { return o }), n.d(e, "d", function () { return i }), n.d(e, "c", function () { return a }), n.d(e, "e", function () { return s }); var r = "event_20240424_113603769", o = { HEART_RATE: 15e3, MOUSE_STAY_LIMIT: 5e3 }, i = { REQUIRED: "required", LENGTH: "length", TYPE: "type", RANGE: "range" }, a = { REQUIRED: "$field$,输入值不可为空", LENGTH: "$field$,输入超长，最大长度为$rule$", TYPE: "$field$,输入的类型不正确，应为$rule$类型", RANGE: "$field$,输入不在范围内，应在$range1$~$range2$之内", LACK: "埋点字段缺失" }, s = "WEBFUNNY_COOKIE" }, function (t, e, n) { "use strict"; e.a = { mouseStayInfo: "", stayTime: 0 } }, function (t, e, n) { "use strict"; n.d(e, "a", function () { return a }), n.d(e, "d", function () { return s }), n.d(e, "b", function () { return c }), n.d(e, "c", function () { return u }); var r = n(5), o = n(7), i = n(4); function a() { var t, e = r.a.eventDomain; return !window || (t = (t = window.location.protocol).replace(/file/g, "http"), -1 < e.indexOf("http")) ? e : t + e } function s(t, e, n, r, o) { if (window) try { var i = window.XMLHttpRequest ? new XMLHttpRequest : new ActiveXObject("Microsoft.XMLHTTP"), a = (i.open(t, e, !0), i.setRequestHeader("Content-Type", "application/json;charset=UTF-8"), i.onreadystatechange = function () { if (4 == i.readyState) { var e = {}; try { e = i.responseText ? JSON.parse(i.responseText) : {} } catch (t) { console.log(t), e = {} } try { "function" == typeof r && r(e) } catch (t) { console.log(t) } } }, i.onerror = function (t) { console.warn("【【 " + e + " 请求不通，请处理。】】", t); try { "function" == typeof o && o() } catch (t) { console.log(t) } }, JSON.stringify(n || {})); i.send(a) } catch (t) { console.log(t) } } function c(t) { new o.a(r.a.trackUrl).sendByXhr([t]) } function u(t) { i.a.addTask(r.a.trackUrl, t) } }, function (t, e, n) { "use strict"; var r = n(8), o = n.n(r), r = n(9), i = n.n(r), a = n(7), s = n(2), c = n(0), u = null, r = function () { function t() { o()(this, t), this.uploadUrl = "", this.queues = [] } return i()(t, [{ key: "start", value: function () { var t, e = this; u || (t = 0, u = setInterval(function () { (40 <= t || 20 < e.queues.length) && (e.fireTasks(), t = 0), t++ }, 200), this.finallyFireTasks()) } }, { key: "addTask", value: function (t, e) { this.uploadUrl = t, this.queues.push(e) } }, { key: "fireTasks", value: function () { var t = Object(c.k)().webfunnyHeatMap; "1" === (void 0 === t ? 0 : t) ? s.a.stayTime = 0 : this.queues && 0 < this.queues.length && new a.a(this.uploadUrl).sendByXhr(this.queues), this.queues = [] } }, { key: "finallyFireTasks", value: function () { var e = this; window.addEventListener("beforeunload", function () { try { var t = Object(c.k)().webfunnyHeatMap; "1" === (void 0 === t ? 0 : t) ? (s.a.stayTime = 0, e.queues = []) : (s.a.stayTime && (webfunnyGlobal.webfunnyEvent("13").trackEvent({ stayTime: s.a.stayTime }), s.a.stayTime = 0), console.log("页面离开时，队列内容", e.queues), e.queues.length && (new a.a(e.uploadUrl).sendByBeacon(e.queues), e.queues = [])) } catch (t) { console.log("finallyFireTasks error", t) } }) } }]), t }(); e.a = new r }, function (t, e, n) { "use strict"; var r = "//zxa-b.hainancrc.com", o = "", o = window && 0 === r.length ? window.location.origin : r; e.a = { trackUrl: "".concat(o, "/wfEvent/upEvents"), eventDomain: o, requestMethod: "POST" } }, function (e, t) { function n(t) { return e.exports = n = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (t) { return typeof t } : function (t) { return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t }, e.exports.__esModule = !0, e.exports.default = e.exports, n(t) } e.exports = n, e.exports.__esModule = !0, e.exports.default = e.exports }, function (t, e, n) { "use strict"; var r = n(8), o = n.n(r), r = n(9), i = n.n(r), a = n(3); e.a = function () { function e(t) { o()(this, e), this.url = t } return i()(e, [{ key: "sendByXhr", value: function (t) { if (this.url) try { window ? Object(a.d)("post", this.url, t, function (t) { e.handleAjaxRes("success", t) }, function () { e.handleAjaxRes("error") }) : webfunnyGlobal && webfunnyGlobal.request({ url: this.url, data: t, method: "POST", header: { "content-type": "application/json; charset=UTF-8" }, success: function () { } }) } catch (t) { console.log(t) } } }, { key: "sendByBeacon", value: function (t) { this.url && (window.navigator && "function" == typeof navigator.sendBeacon ? navigator.sendBeacon(this.url, JSON.stringify(t)) : this.sendByXhr(t)) } }], [{ key: "handleAjaxRes", value: function (t, e) { t } }]), e }() }, function (t, e) { t.exports = function (t, e) { if (!(t instanceof e)) throw new TypeError("Cannot call a class as a function") }, t.exports.__esModule = !0, t.exports.default = t.exports }, function (t, e, n) { var o = n(13); function r(t, e) { for (var n = 0; n < e.length; n++) { var r = e[n]; r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(t, o(r.key), r) } } t.exports = function (t, e, n) { return e && r(t.prototype, e), n && r(t, n), Object.defineProperty(t, "prototype", { writable: !1 }), t }, t.exports.__esModule = !0, t.exports.default = t.exports }, function (t, e, n) { "use strict"; n.d(e, "a", function () { return o }), n.d(e, "c", function () { return i }), n.d(e, "b", function () { return a }); var M = n(1), s = n(4), e = n(17), c = n.n(e), e = n(12), u = n.n(e), r = n(3), L = n(0), l = n(2), o = function (e) { window ? Object(r.d)("post", Object(r.a)() + "/wfEvent/initCf", { projectId: M.b }, function (t) { e(t.data) }, function () { }) : webfunnyGlobal && webfunnyGlobal.request({ url: Object(r.a)() + "/wfEvent/initCf", data: { projectId: M.b }, dataType: "json", method: "POST", success: function (t) { t = t.data.data || t.data; e(t) } }) }; var i = function () { var r, o, i, a; s.a.finallyFireTasks(), r = function (t) { t = t.Format("mm:ss"); l.a.stayTime ? l.a.stayTime += 1 : l.a.stayTime = 1, "0" == t.substring(4) && s.a.fireTasks() }, o = (new Date).getTime(), i = 0, a = function () { var t = c()(u.a.mark(function t() { var e, n; return u.a.wrap(function (t) { for (; ;)switch (t.prev = t.next) { case 0: i++, e = new Date, n = (new Date).getTime(), (n = 1e3 - (n - (o + 1e3 * i))) < 0 && (n = 0); try { r(e) } catch (t) { console("timer error：", t) } setTimeout(a, n); case 9: case "end": return t.stop() } }, t) })); return function () { return t.apply(this, arguments) } }(), setTimeout(a, 1e3) }, a = function () { var t, P, C, e, n; !0 !== window.WF_EVENT_SHOW_HEATMAP && (window.WF_EVENT_SHOW_HEATMAP = !0, n = (e = Object(L.k)()).webfunnyHeatMap, t = e.webfunnyCardId, P = e.webfunnyStartTime, C = e.webfunnyEndTime, "1" === (void 0 === n ? 0 : n)) && ((e = document.createElement("style")).innerHTML = "\n      .webfunny-tip {\n        position: absolute; z-index: 9999;\n        width: 50px; height: 20px; text-align: center; color: #fff; font-size: 12px;line-height: 20px; border-radius: 3px;\n      }\n      .webfunny-triangle {\n        width: 0;height: 0;border-left: 5px solid transparent; border-right: 5px solid transparent;\n        margin-left: 21px;\n      }\n      .webfunny-info {\n        position: absolute;\n        width: 150px;\n        height: auto;\n        background: #333;\n        border-radius: 4px;\n        padding: 5px;\n        left: 50px;\n        margin-top: -25px;\n        text-align: left;\n        color: #fff;\n        background: #fa840c;\n        box-shadow: 1px 0px 7px #fa840c;\n        display: none;\n      }\n      .webfunny-tip:hover .webfunny-info {\n        display: block;\n      }\n    ", (n = document.head || document.getElementsByTagName("head")[0]).insertBefore(e, n.firstChild), Object(r.d)("post", Object(r.a)() + "/wfEvent/buryPointCard/getHeatMapPerData", { webfunnyCardId: t, webfunnyStartTime: P, webfunnyEndTime: C }, function (t) { try { var e = "array" == typeof t.data ? t.data : t.data.pathList; if (L.b.setWfCookie("wf_fe_domain", t.data.fe), L.b.setWfCookie("wf_click_pointId", t.data.pointId), e && e.length) { for (var n = 0, r = 0, o = 0; o < e.length; o++) { var i = e[o], a = i.count, s = void 0 === a ? 0 : a, c = i.userCount; n += +s, r += +(void 0 === c ? 0 : c) } for (var u = 0; u < e.length; u++) { var l = e[u], f = l.xPath, d = l.percentage, h = l.count, p = void 0 === h ? 0 : h, g = l.userCount; j = N = S = I = S = T = _ = O = E = x = v = m = w = b = void 0, m = (y = { xPath: f, percentage: d, count: p, userCount: void 0 === g ? 0 : g, totalCount: n, totalUserCount: r, webfunnyStartTime: P, webfunnyEndTime: C }).xPath, v = y.percentage, x = y.count, E = y.userCount, O = y.totalCount, _ = y.totalUserCount, T = y.webfunnyStartTime, y = y.webfunnyEndTime, I = S = "", 50 <= v ? (S = "#FFA166", I = "#FF8639") : 10 <= v ? (S = "#7A79FF", I = "#6D6CE5") : 0 <= v && (S = "#28C989", I = "#24B27A"), S = " background: linear-gradient(180deg, ".concat(S, " 0%, ").concat(I, " 116.67%); filter: drop-shadow(0px 2px 6px rgba(0, 0, 0, 0.20));"), N = document.createElement("div"), (j = Object(L.f)(m)) && null !== j && (w = (j = j.getBoundingClientRect()).x, b = j.y, w = w + j.width / 2 - 25, j = b - 10, N.className = "webfunny-tip", N.style = "top: ".concat(j, "px; left: ").concat(w, "px; ").concat(S), N.textContent = v + "%", (b = document.createElement("div")).className = "webfunny-triangle", b.style = " border-top: 5px solid ".concat(I, "; "), N.appendChild(b), j = L.b.b64Code(m), w = L.b.getWfCookie("wf_click_pointId"), S = "//".concat(L.b.getWfCookie("wf_fe_domain"), "/wf_event/eventSearch.html?projectId=").concat(M.b, "&pointId=").concat(w, "&xPath=").concat(j, "&startDate=").concat(T, "&endDate=").concat(y), (I = document.createElement("div")).className = "webfunny-info", I.innerHTML = "\n  <label>点击次数：".concat(x, "次</label><br>\n  <label>点击人数：").concat(E, "人</label><br>\n  <label>点击比例：").concat(v, "%</label><br>\n  <label>总数：").concat(O, "次、").concat(_, '人</label><br>\n  <label><a target="_blank" href="').concat(S, '" style="color: #fff;">查看详情</a></label>\n  '), N.appendChild(I), document.body.appendChild(N)) } } window.WF_EVENT_SHOW_HEATMAP = !0 } catch (t) { console.log("heatmap data error", t) } var y, b, w, m, v, x, E, O, _, T, S, I, N, j }, function () { })) } }, function (t, e, n) { "use strict"; n.d(e, "a", function () { return r }); var c = n(1), u = n(0); function r(t, e) { var n = !1, r = Object(u.c)(t, e), o = r.success, r = r.field; if (n = o) { for (var i in e) if (!(n = function (t, e, n) { var r, o = !1; for (r in n) { var i, a = n[r]; if (r === c.d.REQUIRED && a) { if (!(o = void 0 !== t)) return i = c.c.REQUIRED.replace(/\$field\$/g, e).replace(/\$rule\$/g, ""), console.warn(i), o } else if (r === c.d.LENGTH && a) { if (!(o = t.toString().length <= a)) return i = c.c.LENGTH.replace(/\$field\$/g, e).replace(/\$rule\$/g, a), console.warn(i), o } else if (r === c.d.TYPE && a) { var s = "is" + a, s = u.b[s]; if (!(o = s(t))) return s = c.c.TYPE.replace(/\$field\$/g, e).replace(/\$rule\$/g, a), console.warn(s), o } else if (r === c.d.RANGE && a) { s = t >= a[0] && t < a[1]; if (!s) return a = c.c.RANGE.replace(/\$field\$/g, e).replace(/\$range1\$/g, a[0]).replace(/\$range2\$/g, a[1]), console.warn(a), s } } return o }(t[i], i, e[i]))) return n } else o = "".concat(c.c.LACK, " - ").concat(r), console.warn(o); return n } }, function (t, e, n) { n = n(26)(); t.exports = n; try { regeneratorRuntime = n } catch (t) { "object" == typeof globalThis ? globalThis.regeneratorRuntime = n : Function("r", "regeneratorRuntime = r")(n) } }, function (t, e, n) { var r = n(6).default, o = n(24); t.exports = function (t) { return t = o(t, "string"), "symbol" == r(t) ? t : String(t) }, t.exports.__esModule = !0, t.exports.default = t.exports }, function (t, e) { !function () { "use strict"; window && null == Element.prototype.getAttributeNames && (Element.prototype.getAttributeNames = function () { for (var t = this.attributes, e = t.length, n = new Array(e), r = 0; r < e; r++)n[r] = t[r].name; return n }) }() }, function (t, e) { function n() { return t.exports = n = Object.assign ? Object.assign.bind() : function (t) { for (var e = 1; e < arguments.length; e++) { var n, r = arguments[e]; for (n in r) Object.prototype.hasOwnProperty.call(r, n) && (t[n] = r[n]) } return t }, t.exports.__esModule = !0, t.exports.default = t.exports, n.apply(this, arguments) } t.exports = n, t.exports.__esModule = !0, t.exports.default = t.exports }, function (t, e, n) { var r = n(13); t.exports = function (t, e, n) { return (e = r(e)) in t ? Object.defineProperty(t, e, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : t[e] = n, t }, t.exports.__esModule = !0, t.exports.default = t.exports }, function (t, e) { function c(t, e, n, r, o, i, a) { try { var s = t[i](a), c = s.value } catch (t) { return void n(t) } s.done ? e(c) : Promise.resolve(c).then(r, o) } t.exports = function (s) { return function () { var t = this, a = arguments; return new Promise(function (e, n) { var r = s.apply(t, a); function o(t) { c(r, e, n, o, i, "next", t) } function i(t) { c(r, e, n, o, i, "throw", t) } o(void 0) }) } }, t.exports.__esModule = !0, t.exports.default = t.exports }, function (t, e, n) { "use strict"; n.d(e, "a", function () { return r }); var u = n(0), r = function () { var t = Object(u.d)(), e = Object(u.j)("userId"), n = Object(u.e)(), r = n.deviceName, r = void 0 === r ? "" : r, o = n.system, o = void 0 === o ? "" : o, i = n.os, i = void 0 === i ? "" : i, a = n.platform, a = void 0 === a ? "" : a, n = n.browserName, n = void 0 === n ? "" : n, s = Object(u.i)(), c = Object(u.j)("platform"); return { weCustomerKey: t, weUserId: e, wePath: s, weDeviceName: r, wePlatform: c || a, weSystem: o, weOs: i, weBrowserName: n, weNewStatus: Object(u.h)() } } }, function (t, e, n) { "use strict"; n.d(e, "a", function () { return x }); n(25), n(14); var l = n(0), o = function (t, e) { if ("function" == typeof t.getAttributeNames && -1 < t.getAttributeNames().indexOf(e)) return !0 }, r = function (t) { Object.prototype.hasOwnProperty.call(t.dataset, "webfunnyExposureEle") || (t.dataset.webfunnyExposureEle = "") }, i = function (t) { return !!Object.prototype.hasOwnProperty.call(t.dataset, "webfunnyExposureEle") }, e = n(16), a = n.n(e), f = n(2), d = n(1); function s(e, t) { var n, r = Object.keys(e); return Object.getOwnPropertySymbols && (n = Object.getOwnPropertySymbols(e), t && (n = n.filter(function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable })), r.push.apply(r, n)), r } function h(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? s(Object(n), !0).forEach(function (t) { a()(e, t, n[t]) }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : s(Object(n)).forEach(function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) }) } return e } var c = {}, u = [], p = "_webfunny-eo", g = "_webfunny-ce", y = function () { var t = document.getElementsByTagName("*"); Array.prototype.forEach.call(t, function (t, e) { var n, r; o(t, p) && (u.push(t), n = t.getAttribute(p)) && (t = t, r = e, Object.prototype.hasOwnProperty.call(t.dataset, "exposureKey".concat(r)) || (t.dataset["exposureKey".concat(r)] = ""), Object.defineProperty(c, "data-exposure-key".concat(e), { value: JSON.parse(n), configureable: !0, writable: !0, enumerable: !0 })) }), u = u.reduce(function (t, e) { return -1 === t.indexOf(e) && t.push(e), t }, []) }, b = function () { y(), u.forEach(function (t) { i(t) || (r(t), v(t)) }) }; function w(t) { var e; Object.prototype.hasOwnProperty.call(t, "pointId") ? (e = t.pointId, Object.prototype.hasOwnProperty.call(window._webfunnyEvent, e) ? (delete t.pointId, window._webfunnyEvent[e].trackEvent(t)) : console.warn("请检查pointId上送的值是否正确")) : console.warn("pointId为必须上送字段，请检查是否正确上送") } function m() { MutationObserver && new MutationObserver(function (t) { for (var e = 0, n = t.length; e < n; e++)t[e].addedNodes[0] && o(t[e].addedNodes[0], p) && b(), t[e].target.querySelector("[".concat(p, "]")) && !i(t[e].target.querySelector("[".concat(p, "]"))) && b(), t[e].attributeName === p && t[e].target.attributes[p] && b() }).observe(document.documentElement, { childList: !0, attributes: !0, subtree: !0 }) } var v = function (n) { var t, e = 0; o(n, "_webfunny-eo-ratio") && (t = n.getAttribute("_webfunny-eo-ratio"), e = Object.is(t, "0") ? 0 : parseFloat(t)); new IntersectionObserver(function (t, e) { t.forEach(function (t) { 0 < t.intersectionRatio && ((t = Object.keys(n.dataset).find(function (t) { if (-1 < t.indexOf("exposureKey")) return t })) ? (t = "data-exposure-key" + t.slice(11), w(c[t])) : console.warn("曝光需要上送对应数据"), e.disconnect()) }) }, { root: null, rootMargin: "0px", threshold: e }).observe(n) }; var x = function () { var t; window && (b(), m(), setTimeout(function () { for (var t = Object(l.g)(g), e = 0, n = t.length; e < n; e++) { var r = t[e]; Object(l.a)(r, "click", function (t) { t = t.target.getAttribute(g); t ? w(JSON.parse(t)) : console.warn("曝光需要上送对应数据") }) } }, 500), window.addEventListener("click", function (t) { if (t) try { var e = document.body.scrollWidth || window.innerWidth, n = document.title, r = l.b.b64Code(l.b.getPath()), o = l.b.b64Code(l.b.getPath("full")), i = { weTitle: n, wePath: r, weScrollWidth: e - e % 20, weScrollHeigh: document.body.scrollHeight || window.innerHeight, weXPath: l.b.getXPath(t), weFullPath: o, wePageX: t.pageX, wePageY: t.pageY, weRatio: window.devicePixelRatio }; webfunnyGlobal.webfunnyEvent("15").trackEvent(i) } catch (t) { console.log("点击事件", t) } }, !0), window.addEventListener("mousemove", function (u) { u && (t && clearTimeout(t), t = setTimeout(function () { try { var t, e, n, r, o = document.title, i = document.body.scrollWidth || window.innerWidth, a = l.b.b64Code(l.b.getPath()), s = l.b.b64Code(l.b.getPath("full")), c = { weTitle: o, wePath: a, weScrollWidth: i - i % 20, weScrollHeigh: document.body.scrollHeight || window.innerHeight, weXPath: l.b.getXPath(u), weFullPath: s, wePageX: u.pageX, wePageY: u.pageY, weRatio: window.devicePixelRatio }; f.a.mouseStayInfo ? 300 < (n = (t = (new Date).getTime()) - (void 0 === (e = (f.a.mouseStayInfo || {}).startTime) ? 0 : e)) && (n = n > d.a.MOUSE_STAY_LIMIT ? d.a.MOUSE_STAY_LIMIT : n, r = h(h({}, c), {}, { stayTime: n }), webfunnyGlobal.webfunnyEvent("14").trackEvent(r), f.a.mouseStayInfo = h(h({}, c), {}, { startTime: t })) : f.a.mouseStayInfo = h(h({}, c), {}, { startTime: (new Date).getTime() }) } catch (t) { console.log("mousemove error: ", t) } }, 200)) }, !0)) } }, function (t, e, n) { t.exports = n(21) }, function (t, e, c) { "use strict"; c.r(e), function (t) { var e = c(11), n = c(3), r = c(19), o = c(0), i = c(10), a = c(18), s = window || t; try { s.WE_INIT_FLG = !1, Object(i.a)(function (t) { s.WE_INIT_FLG = !0; var t = t || {}, e = t.interval, e = void 0 === e ? 5 : e, t = t.funnel, t = void 0 === t ? [] : t; t.length && o.b.setWfCookie("weFunnelConfig", { funnelList: t }, (new Date).getTime() + 864e5), Object(i.b)(), Object(i.c)(e) }), o.b && (s.webfunnyEventUtils = o.b, s.webfunnyEventGetCustomerInfo = a.a, s.webfunnyEventUtils.getCustomerKey(), s.webfunnyEventUtils.getLastHeartBeatTime(1)), e.a && n.c && (s.webfunnyEventValidateParams = e.a, s.webfunnyEventSendRequest = n.b, s.webfunnyEventStoreLogs = n.c, s._webfunnyEvent = { 13: { fields: { stayTime: { required: true, type: 'Number', length: 10 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('13'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('13'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '13' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, 14: { fields: { stayTime: { required: true, type: 'Number', length: 10 }, weFullPath: { required: true, type: 'String', length: 2000 }, weTitle: { required: true, type: 'String', length: 500 }, weXPath: { required: true, type: 'String', length: 500 }, wePageX: { required: true, type: 'Number', length: 10 }, wePageY: { required: true, type: 'Number', length: 10 }, weScrollWidth: { required: true, type: 'Number', length: 10 }, weScrollHeigh: { required: true, type: 'Number', length: 10 }, weRatio: { required: true, type: 'Number', length: 50 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('14'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('14'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '14' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, 15: { fields: { weFullPath: { required: true, type: 'String', length: 2000 }, weTitle: { required: true, type: 'String', length: 500 }, weXPath: { required: true, type: 'String', length: 500 }, wePageX: { required: true, type: 'Number', length: 10 }, wePageY: { required: true, type: 'Number', length: 10 }, weScrollWidth: { required: true, type: 'Number', length: 10 }, weScrollHeigh: { required: true, type: 'Number', length: 10 }, weRatio: { required: true, type: 'Number', length: 50 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('15'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('15'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '15' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, 16: { fields: { pageUrl: { required: true, type: 'String', length: 500 }, quDao: { required: true, type: 'String', length: 20 }, waiBuYingYongId: { required: true, type: 'String', length: 20 }, waiBuYingYongChangJingId: { required: true, type: 'String', length: 20 }, yongHuShenFen: { required: true, type: 'Number', length: 2 }, userCreditCode: { required: true, type: 'String', length: 30 }, userCorpName: { required: true, type: 'String', length: 100 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('16'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('16'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '16' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, 28: { fields: { quDao: { required: true, type: 'String', length: 20 }, waiBuYingYongId: { required: true, type: 'String', length: 20 }, waiBuYingYongChangJingId: { required: true, type: 'String', length: 20 }, yongHuShenFen: { required: true, type: 'Number', length: 2 }, userCreditCode: { required: true, type: 'String', length: 30 }, userCorpName: { required: true, type: 'String', length: 100 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('28'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('28'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '28' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, 29: { fields: { quDao: { required: true, type: 'String', length: 20 }, waiBuYingYongId: { required: true, type: 'String', length: 20 }, waiBuYingYongChangJingId: { required: true, type: 'String', length: 20 }, yongHuShenFen: { required: true, type: 'Number', length: 2 }, userCreditCode: { required: true, type: 'String', length: 30 }, userCorpName: { required: true, type: 'String', length: 100 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('29'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('29'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '29' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, 30: { fields: { quDao: { required: true, type: 'String', length: 20 }, waiBuYingYongId: { required: true, type: 'String', length: 20 }, waiBuYingYongChangJingId: { required: true, type: 'String', length: 20 }, yongHuShenFen: { required: true, type: 'Number', length: 2 }, userCreditCode: { required: true, type: 'String', length: 30 }, userCorpName: { required: true, type: 'String', length: 100 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('30'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('30'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '30' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, 31: { fields: { quDao: { required: true, type: 'String', length: 20 }, creditCode: { required: true, type: 'String', length: 20 }, waiBuYingYongId: { required: true, type: 'String', length: 20 }, waiBuYingYongChangJingId: { required: true, type: 'String', length: 20 }, yongHuShenFen: { required: true, type: 'Number', length: 2 }, userCreditCode: { required: true, type: 'String', length: 30 }, userCorpName: { required: true, type: 'String', length: 100 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('31'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('31'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '31' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, 32: { fields: { quDao: { required: true, type: 'String', length: 20 }, waiBuYingYongId: { required: true, type: 'String', length: 20 }, waiBuYingYongChangJingId: { required: true, type: 'String', length: 20 }, yongHuShenFen: { required: true, type: 'Number', length: 2 }, userCreditCode: { required: true, type: 'String', length: 30 }, userCorpName: { required: true, type: 'String', length: 100 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('32'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('32'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '32' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, 33: { fields: { pageUrl: { required: true, type: 'String', length: 500 }, quDao: { required: true, type: 'String', length: 20 }, fuWuZhuanQu: { required: true, type: 'String', length: 20 }, ziFuWuZhuanQu: { required: true, type: 'String', length: 20 }, waiBuYingYongId: { required: true, type: 'String', length: 20 }, waiBuYingYongChangJingId: { required: true, type: 'String', length: 20 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('33'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('33'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '33' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, 34: { fields: { quDao: { required: true, type: 'String', length: 20 }, fuWuZhuanQu: { required: true, type: 'String', length: 20 }, ziFuWuZhuanQu: { required: true, type: 'String', length: 20 }, waiBuYingYongId: { required: true, type: 'String', length: 20 }, waiBuYingYongChangJingId: { required: true, type: 'String', length: 20 }, yongHuShenFen: { required: true, type: 'Number', length: 2 }, userCreditCode: { required: true, type: 'String', length: 30 }, userCorpName: { required: true, type: 'String', length: 100 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('34'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('34'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '34' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, 35: { fields: { quDao: { required: true, type: 'String', length: 20 }, creditCode: { required: true, type: 'String', length: 20 }, fuWuZhuanQu: { required: true, type: 'String', length: 20 }, ziFuWuZhuanQu: { required: true, type: 'String', length: 20 }, waiBuYingYongId: { required: true, type: 'String', length: 20 }, waiBuYingYongChangJingId: { required: true, type: 'String', length: 20 }, yongHuShenFen: { required: true, type: 'Number', length: 2 }, userCreditCode: { required: true, type: 'String', length: 30 }, userCorpName: { required: true, type: 'String', length: 100 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('35'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('35'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '35' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, 36: { fields: { quDao: { required: true, type: 'String', length: 20 }, creditCode: { required: true, type: 'String', length: 20 }, fuWuZhuanQu: { required: true, type: 'String', length: 20 }, yongHuShenFen: { required: true, type: 'Number', length: 2 }, userCreditCode: { required: true, type: 'String', length: 30 }, userCorpName: { required: true, type: 'String', length: 100 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('36'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('36'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '36' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, 37: { fields: { quDao: { required: true, type: 'String', length: 20 }, waiBuYingYongId: { required: true, type: 'String', length: 20 }, waiBuYingYongChangJingId: { required: true, type: 'String', length: 20 }, yongHuShenFen: { required: true, type: 'Number', length: 2 }, userCreditCode: { required: true, type: 'String', length: 30 }, userCorpName: { required: true, type: 'String', length: 100 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('37'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('37'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '37' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, 38: { fields: { quDao: { required: true, type: 'String', length: 20 }, waiBuYingYongId: { required: true, type: 'String', length: 20 }, waiBuYingYongChangJingId: { required: true, type: 'String', length: 20 }, bannerType: { required: true, type: 'String', length: 20 }, bannerTitle: { required: true, type: 'String', length: 30 }, yongHuShenFen: { required: true, type: 'Number', length: 2 }, userCreditCode: { required: true, type: 'String', length: 30 }, userCorpName: { required: true, type: 'String', length: 100 }, }, trackEvent: function (params, configs) { if (window.webfunnyEventValidateParams(params, this.fields)) { window.webfunnyEventUtils.setFirstActionTime('38'); var weFirstStepDay = window.webfunnyEventUtils.getFirstActionTime('38'); var customerInfo = window.webfunnyEventGetCustomerInfo(); for (var key in params) { if (typeof params[key] !== "number") { params[key] = window.webfunnyEventUtils.b64Code(params[key]) } }; window.webfunnyEventUtils.combineObject(params, { projectId: 'event_20240424_113603769', pointId: '38' }); window.webfunnyEventUtils.combineObject(params, customerInfo); window.webfunnyEventUtils.combineObject(params, { weFirstStepDay: weFirstStepDay }); window.webfunnyEventUtils.addTask(params); if (configs && configs.upNow === true) { window.webfunnyEventUtils.fireTasks() } } } }, }), Object(r.a)() } catch (t) { console.warn(t) } }.call(this, c(22)) }, function (t, e) { var n = function () { return this }(); try { n = n || new Function("return this")() } catch (t) { "object" == typeof window && (n = window) } t.exports = n }, function (t, e) { Date.prototype.Format || (Date.prototype.Format = function (t) { var e, n = t, r = { "M+": this.getMonth() + 1, "d+": this.getDate(), "h+": this.getHours(), "m+": this.getMinutes(), "s+": this.getSeconds(), "q+": Math.floor((this.getMonth() + 3) / 3), S: this.getMilliseconds() }; for (e in /(y+)/.test(n) && (n = n.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length))), r) new RegExp("(" + e + ")").test(n) && (n = n.replace(RegExp.$1, 1 === RegExp.$1.length ? r[e] : ("00" + r[e]).substr(("" + r[e]).length))); return n }) }, function (t, e, n) { var r = n(6).default; t.exports = function (t, e) { if ("object" != r(t) || !t) return t; var n = t[Symbol.toPrimitive]; if (void 0 === n) return ("string" === e ? String : Number)(t); if (n = n.call(t, e || "default"), "object" != r(n)) return n; throw new TypeError("@@toPrimitive must return a primitive value.") }, t.exports.__esModule = !0, t.exports.default = t.exports }, function (t, e) { !function () { "use strict"; var h, n, p, g; function i(t) { try { return t.defaultView && t.defaultView.frameElement || null } catch (t) { return null } } function u(t) { this.time = t.time, this.target = t.target, this.rootBounds = r(t.rootBounds), this.boundingClientRect = r(t.boundingClientRect), this.intersectionRect = r(t.intersectionRect || l()), this.isIntersecting = !!t.intersectionRect; var t = this.boundingClientRect, t = t.width * t.height, e = this.intersectionRect, e = e.width * e.height; this.intersectionRatio = t ? Number((e / t).toFixed(4)) : this.isIntersecting ? 1 : 0 } function t(t, e) { var n, r, o, e = e || {}; if ("function" != typeof t) throw new Error("callback must be a function"); if (e.root && 1 != e.root.nodeType && 9 != e.root.nodeType) throw new Error("root must be a Document or Element"); this._checkForIntersections = (n = this._checkForIntersections.bind(this), r = this.THROTTLE_TIMEOUT, o = null, function () { o = o || setTimeout(function () { n(), o = null }, r) }), this._callback = t, this._observationTargets = [], this._queuedEntries = [], this._rootMarginValues = this._parseRootMargin(e.rootMargin), this.thresholds = this._initThresholds(e.threshold), this.root = e.root || null, this.rootMargin = this._rootMarginValues.map(function (t) { return t.value + t.unit }).join(" "), this._monitoringDocuments = [], this._monitoringUnsubscribes = [] } function a(t, e, n, r) { "function" == typeof t.addEventListener ? t.addEventListener(e, n, r || !1) : "function" == typeof t.attachEvent && t.attachEvent("on" + e, n) } function s(t, e, n, r) { "function" == typeof t.removeEventListener ? t.removeEventListener(e, n, r || !1) : "function" == typeof t.detachEvent && t.detachEvent("on" + e, n) } function y(t) { var e; try { e = t.getBoundingClientRect() } catch (t) { } return e ? e.width && e.height ? e : { top: e.top, right: e.right, bottom: e.bottom, left: e.left, width: e.right - e.left, height: e.bottom - e.top } : l() } function l() { return { top: 0, bottom: 0, left: 0, right: 0, width: 0, height: 0 } } function r(t) { return !t || "x" in t ? t : { top: t.top, y: t.top, bottom: t.bottom, left: t.left, x: t.left, right: t.right, width: t.width, height: t.height } } function b(t, e) { var n = e.top - t.top, t = e.left - t.left; return { top: n, left: t, height: e.height, width: e.width, bottom: n + e.height, right: t + e.width } } function o(t, e) { for (var n = e; n;) { if (n == t) return !0; n = w(n) } return !1 } function w(t) { var e = t.parentNode; return 9 == t.nodeType && t != h ? i(t) : (e = e && e.assignedSlot ? e.assignedSlot.parentNode : e) && 11 == e.nodeType && e.host ? e.host : e } function c(t) { return t && 9 === t.nodeType } "object" == typeof window && ("IntersectionObserver" in window && "IntersectionObserverEntry" in window && "intersectionRatio" in window.IntersectionObserverEntry.prototype ? "isIntersecting" in window.IntersectionObserverEntry.prototype || Object.defineProperty(window.IntersectionObserverEntry.prototype, "isIntersecting", { get: function () { return 0 < this.intersectionRatio } }) : (h = function () { for (var t = window.document, e = i(t); e;)e = i(t = e.ownerDocument); return t }(), n = [], g = p = null, t.prototype.THROTTLE_TIMEOUT = 100, t.prototype.POLL_INTERVAL = null, t.prototype.USE_MUTATION_OBSERVER = !0, t._setupCrossOriginUpdater = function () { return p = p || function (t, e) { g = t && e ? b(t, e) : l(), n.forEach(function (t) { t._checkForIntersections() }) } }, t._resetCrossOriginUpdater = function () { g = p = null }, t.prototype.observe = function (e) { var t = this._observationTargets.some(function (t) { return t.element == e }); if (!t) { if (!e || 1 != e.nodeType) throw new Error("target must be an Element"); this._registerInstance(), this._observationTargets.push({ element: e, entry: null }), this._monitorIntersections(e.ownerDocument), this._checkForIntersections() } }, t.prototype.unobserve = function (e) { this._observationTargets = this._observationTargets.filter(function (t) { return t.element != e }), this._unmonitorIntersections(e.ownerDocument), 0 == this._observationTargets.length && this._unregisterInstance() }, t.prototype.disconnect = function () { this._observationTargets = [], this._unmonitorAllIntersections(), this._unregisterInstance() }, t.prototype.takeRecords = function () { var t = this._queuedEntries.slice(); return this._queuedEntries = [], t }, t.prototype._initThresholds = function (t) { t = t || [0]; return (t = Array.isArray(t) ? t : [t]).sort().filter(function (t, e, n) { if ("number" != typeof t || isNaN(t) || t < 0 || 1 < t) throw new Error("threshold must be a number between 0 and 1 inclusively"); return t !== n[e - 1] }) }, t.prototype._parseRootMargin = function (t) { t = (t || "0px").split(/\s+/).map(function (t) { t = /^(-?\d*\.?\d+)(px|%)$/.exec(t); if (t) return { value: parseFloat(t[1]), unit: t[2] }; throw new Error("rootMargin must be specified in pixels or percent") }); return t[1] = t[1] || t[0], t[2] = t[2] || t[0], t[3] = t[3] || t[1], t }, t.prototype._monitorIntersections = function (e) { var n, r, o, t = e.defaultView; t && -1 == this._monitoringDocuments.indexOf(e) && (n = this._checkForIntersections, o = r = null, this.POLL_INTERVAL ? r = t.setInterval(n, this.POLL_INTERVAL) : (a(t, "resize", n, !0), a(e, "scroll", n, !0), this.USE_MUTATION_OBSERVER && "MutationObserver" in t && (o = new t.MutationObserver(n)).observe(e, { attributes: !0, childList: !0, characterData: !0, subtree: !0 })), this._monitoringDocuments.push(e), this._monitoringUnsubscribes.push(function () { var t = e.defaultView; t && (r && t.clearInterval(r), s(t, "resize", n, !0)), s(e, "scroll", n, !0), o && o.disconnect() }), t = this.root && (this.root.ownerDocument || this.root) || h, e != t) && (t = i(e)) && this._monitorIntersections(t.ownerDocument) }, t.prototype._unmonitorIntersections = function (r) { var o, t, e = this._monitoringDocuments.indexOf(r); -1 == e || (o = this.root && (this.root.ownerDocument || this.root) || h, this._observationTargets.some(function (t) { if ((e = t.element.ownerDocument) == r) return !0; for (; e && e != o;) { var e, n = i(e); if ((e = n && n.ownerDocument) == r) return !0 } return !1 })) || (t = this._monitoringUnsubscribes[e], this._monitoringDocuments.splice(e, 1), this._monitoringUnsubscribes.splice(e, 1), t(), r != o && (e = i(r)) && this._unmonitorIntersections(e.ownerDocument)) }, t.prototype._unmonitorAllIntersections = function () { var t = this._monitoringUnsubscribes.slice(0); this._monitoringDocuments.length = 0; for (var e = this._monitoringUnsubscribes.length = 0; e < t.length; e++)t[e]() }, t.prototype._checkForIntersections = function () { var s, c; (this.root || !p || g) && (s = this._rootIsInDom(), c = s ? this._getRootRect() : l(), this._observationTargets.forEach(function (t) { var e = t.element, n = y(e), r = this._rootContainsTarget(e), o = t.entry, i = s && r && this._computeTargetAndRootIntersection(e, n, c), a = null, t = (this._rootContainsTarget(e) ? p && !this.root || (a = c) : a = l(), t.entry = new u({ time: window.performance && performance.now && performance.now(), target: e, boundingClientRect: n, rootBounds: a, intersectionRect: i })); o ? s && r ? this._hasCrossedThreshold(o, t) && this._queuedEntries.push(t) : o && o.isIntersecting && this._queuedEntries.push(t) : this._queuedEntries.push(t) }, this), this._queuedEntries.length) && this._callback(this.takeRecords(), this) }, t.prototype._computeTargetAndRootIntersection = function (t, e, n) { if ("none" != window.getComputedStyle(t).display) { for (var r, o, i = e, a = w(t), s = !1; !s && a;) { var c, u, l, f = null, d = 1 == a.nodeType ? window.getComputedStyle(a) : {}; if ("none" == d.display) return null; if (a == this.root || 9 == a.nodeType ? (s = !0, a == this.root || a == h ? p && !this.root ? !g || 0 == g.width && 0 == g.height ? i = f = a = null : f = g : f = n : (c = (l = w(a)) && y(l), u = l && this._computeTargetAndRootIntersection(l, c, n), c && u ? (a = l, f = b(c, u)) : i = a = null)) : a != (l = a.ownerDocument).body && a != l.documentElement && "visible" != d.overflow && (f = y(a)), f && (c = f, u = i, o = r = f = d = void 0, d = Math.max(c.top, u.top), f = Math.min(c.bottom, u.bottom), r = Math.max(c.left, u.left), c = Math.min(c.right, u.right), o = f - d, i = 0 <= (u = c - r) && 0 <= o ? { top: d, bottom: f, left: r, right: c, width: u, height: o } : null), !i) break; a = a && w(a) } return i } }, t.prototype._getRootRect = function () { var t, e; return e = this.root && !c(this.root) ? y(this.root) : (e = (t = c(this.root) ? this.root : h).documentElement, t = t.body, { top: 0, left: 0, right: e.clientWidth || t.clientWidth, width: e.clientWidth || t.clientWidth, bottom: e.clientHeight || t.clientHeight, height: e.clientHeight || t.clientHeight }), this._expandRectByRootMargin(e) }, t.prototype._expandRectByRootMargin = function (n) { var t = this._rootMarginValues.map(function (t, e) { return "px" == t.unit ? t.value : t.value * (e % 2 ? n.width : n.height) / 100 }), t = { top: n.top - t[0], right: n.right + t[1], bottom: n.bottom + t[2], left: n.left - t[3] }; return t.width = t.right - t.left, t.height = t.bottom - t.top, t }, t.prototype._hasCrossedThreshold = function (t, e) { var n = t && t.isIntersecting ? t.intersectionRatio || 0 : -1, r = e.isIntersecting ? e.intersectionRatio || 0 : -1; if (n !== r) for (var o = 0; o < this.thresholds.length; o++) { var i = this.thresholds[o]; if (i == n || i == r || i < n != i < r) return !0 } }, t.prototype._rootIsInDom = function () { return !this.root || o(h, this.root) }, t.prototype._rootContainsTarget = function (t) { var e = this.root && (this.root.ownerDocument || this.root) || h; return o(e, t) && (!this.root || e == t.ownerDocument) }, t.prototype._registerInstance = function () { n.indexOf(this) < 0 && n.push(this) }, t.prototype._unregisterInstance = function () { var t = n.indexOf(this); -1 != t && n.splice(t, 1) }, window.IntersectionObserver = t, window.IntersectionObserverEntry = u)) }() }, function (N, t, e) { var j = e(6).default; function n() { "use strict"; N.exports = function () { return a }, N.exports.__esModule = !0, N.exports.default = N.exports; var c, a = {}, t = Object.prototype, u = t.hasOwnProperty, l = Object.defineProperty || function (t, e, n) { t[e] = n.value }, e = "function" == typeof Symbol ? Symbol : {}, r = e.iterator || "@@iterator", n = e.asyncIterator || "@@asyncIterator", o = e.toStringTag || "@@toStringTag"; function i(t, e, n) { return Object.defineProperty(t, e, { value: n, enumerable: !0, configurable: !0, writable: !0 }), t[e] } try { i({}, "") } catch (c) { i = function (t, e, n) { return t[e] = n } } function s(t, e, n, r) { var o, i, a, s, e = e && e.prototype instanceof b ? e : b, e = Object.create(e.prototype), r = new S(r || []); return l(e, "_invoke", { value: (o = t, i = n, a = r, s = d, function (t, e) { if (s === p) throw new Error("Generator is already running"); if (s === g) { if ("throw" === t) throw e; return { value: c, done: !0 } } for (a.method = t, a.arg = e; ;) { var n = a.delegate; if (n) { n = function t(e, n) { var r = n.method, o = e.iterator[r]; if (o === c) return n.delegate = null, "throw" === r && e.iterator.return && (n.method = "return", n.arg = c, t(e, n), "throw" === n.method) || "return" !== r && (n.method = "throw", n.arg = new TypeError("The iterator does not provide a '" + r + "' method")), y; r = f(o, e.iterator, n.arg); if ("throw" === r.type) return n.method = "throw", n.arg = r.arg, n.delegate = null, y; o = r.arg; return o ? o.done ? (n[e.resultName] = o.value, n.next = e.nextLoc, "return" !== n.method && (n.method = "next", n.arg = c), n.delegate = null, y) : o : (n.method = "throw", n.arg = new TypeError("iterator result is not an object"), n.delegate = null, y) }(n, a); if (n) { if (n === y) continue; return n } } if ("next" === a.method) a.sent = a._sent = a.arg; else if ("throw" === a.method) { if (s === d) throw s = g, a.arg; a.dispatchException(a.arg) } else "return" === a.method && a.abrupt("return", a.arg); s = p; n = f(o, i, a); if ("normal" === n.type) { if (s = a.done ? g : h, n.arg === y) continue; return { value: n.arg, done: a.done } } "throw" === n.type && (s = g, a.method = "throw", a.arg = n.arg) } }) }), e } function f(t, e, n) { try { return { type: "normal", arg: t.call(e, n) } } catch (t) { return { type: "throw", arg: t } } } a.wrap = s; var d = "suspendedStart", h = "suspendedYield", p = "executing", g = "completed", y = {}; function b() { } function w() { } function m() { } var e = {}, v = (i(e, r, function () { return this }), Object.getPrototypeOf), v = v && v(v(I([]))), x = (v && v !== t && u.call(v, r) && (e = v), m.prototype = b.prototype = Object.create(e)); function E(t) { ["next", "throw", "return"].forEach(function (e) { i(t, e, function (t) { return this._invoke(e, t) }) }) } function O(a, s) { var e; l(this, "_invoke", { value: function (n, r) { function t() { return new s(function (t, e) { !function e(t, n, r, o) { var i, t = f(a[t], a, n); if ("throw" !== t.type) return (n = (i = t.arg).value) && "object" == j(n) && u.call(n, "__await") ? s.resolve(n.__await).then(function (t) { e("next", t, r, o) }, function (t) { e("throw", t, r, o) }) : s.resolve(n).then(function (t) { i.value = t, r(i) }, function (t) { return e("throw", t, r, o) }); o(t.arg) }(n, r, t, e) }) } return e = e ? e.then(t, t) : t() } }) } function _(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e) } function T(t) { var e = t.completion || {}; e.type = "normal", delete e.arg, t.completion = e } function S(t) { this.tryEntries = [{ tryLoc: "root" }], t.forEach(_, this), this.reset(!0) } function I(e) { if (e || "" === e) { var n, t = e[r]; if (t) return t.call(e); if ("function" == typeof e.next) return e; if (!isNaN(e.length)) return n = -1, (t = function t() { for (; ++n < e.length;)if (u.call(e, n)) return t.value = e[n], t.done = !1, t; return t.value = c, t.done = !0, t }).next = t } throw new TypeError(j(e) + " is not iterable") } return l(x, "constructor", { value: w.prototype = m, configurable: !0 }), l(m, "constructor", { value: w, configurable: !0 }), w.displayName = i(m, o, "GeneratorFunction"), a.isGeneratorFunction = function (t) { t = "function" == typeof t && t.constructor; return !!t && (t === w || "GeneratorFunction" === (t.displayName || t.name)) }, a.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, m) : (t.__proto__ = m, i(t, o, "GeneratorFunction")), t.prototype = Object.create(x), t }, a.awrap = function (t) { return { __await: t } }, E(O.prototype), i(O.prototype, n, function () { return this }), a.AsyncIterator = O, a.async = function (t, e, n, r, o) { void 0 === o && (o = Promise); var i = new O(s(t, e, n, r), o); return a.isGeneratorFunction(e) ? i : i.next().then(function (t) { return t.done ? t.value : i.next() }) }, E(x), i(x, o, "Generator"), i(x, r, function () { return this }), i(x, "toString", function () { return "[object Generator]" }), a.keys = function (t) { var e, n = Object(t), r = []; for (e in n) r.push(e); return r.reverse(), function t() { for (; r.length;) { var e = r.pop(); if (e in n) return t.value = e, t.done = !1, t } return t.done = !0, t } }, a.values = I, S.prototype = { constructor: S, reset: function (t) { if (this.prev = 0, this.next = 0, this.sent = this._sent = c, this.done = !1, this.delegate = null, this.method = "next", this.arg = c, this.tryEntries.forEach(T), !t) for (var e in this) "t" === e.charAt(0) && u.call(this, e) && !isNaN(+e.slice(1)) && (this[e] = c) }, stop: function () { this.done = !0; var t = this.tryEntries[0].completion; if ("throw" === t.type) throw t.arg; return this.rval }, dispatchException: function (n) { if (this.done) throw n; var r = this; function t(t, e) { return i.type = "throw", i.arg = n, r.next = t, e && (r.method = "next", r.arg = c), !!e } for (var e = this.tryEntries.length - 1; 0 <= e; --e) { var o = this.tryEntries[e], i = o.completion; if ("root" === o.tryLoc) return t("end"); if (o.tryLoc <= this.prev) { var a = u.call(o, "catchLoc"), s = u.call(o, "finallyLoc"); if (a && s) { if (this.prev < o.catchLoc) return t(o.catchLoc, !0); if (this.prev < o.finallyLoc) return t(o.finallyLoc) } else if (a) { if (this.prev < o.catchLoc) return t(o.catchLoc, !0) } else { if (!s) throw new Error("try statement without catch or finally"); if (this.prev < o.finallyLoc) return t(o.finallyLoc) } } } }, abrupt: function (t, e) { for (var n = this.tryEntries.length - 1; 0 <= n; --n) { var r = this.tryEntries[n]; if (r.tryLoc <= this.prev && u.call(r, "finallyLoc") && this.prev < r.finallyLoc) { var o = r; break } } var i = (o = o && ("break" === t || "continue" === t) && o.tryLoc <= e && e <= o.finallyLoc ? null : o) ? o.completion : {}; return i.type = t, i.arg = e, o ? (this.method = "next", this.next = o.finallyLoc, y) : this.complete(i) }, complete: function (t, e) { if ("throw" === t.type) throw t.arg; return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), y }, finish: function (t) { for (var e = this.tryEntries.length - 1; 0 <= e; --e) { var n = this.tryEntries[e]; if (n.finallyLoc === t) return this.complete(n.completion, n.afterLoc), T(n), y } }, catch: function (t) { for (var e = this.tryEntries.length - 1; 0 <= e; --e) { var n, r, o = this.tryEntries[e]; if (o.tryLoc === t) return "throw" === (n = o.completion).type && (r = n.arg, T(o)), r } throw new Error("illegal catch attempt") }, delegateYield: function (t, e, n) { return this.delegate = { iterator: I(t), resultName: e, nextLoc: n }, "next" === this.method && (this.arg = c), y } }, a } N.exports = n, N.exports.__esModule = !0, N.exports.default = N.exports }]);
    </script>
</head>

<body>
    <noscript>
        <strong>We're sorry but 海南征信平台 doesn't work properly without JavaScript enabled.
            Please enable it to continue.</strong>
    </noscript>
    <div id="app">
        <style>
            html,
            body,
            #app {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                overflow: hidden;
            }

            .loader,
            .loader::before,
            .loader::after {
                width: 2.5em;
                height: 2.5em;
                border-radius: 50%;
                animation: load-animation 1.8s infinite ease-in-out;
                animation-fill-mode: both;
            }

            .loader {
                position: relative;
                top: 0;
                margin: 80px auto;
                font-size: 10px;
                color: #406eeb;
                text-indent: -9999em;
                transform: translateZ(0);
                transform: translate(-50%, 0);
                animation-delay: -0.16s;
            }

            .loader::before,
            .loader::after {
                position: absolute;
                top: 0;
                content: '';
            }

            .loader::before {
                left: -3.5em;
                animation-delay: -0.32s;
            }

            .loader::after {
                left: 3.5em;
            }

            @keyframes load-animation {

                0%,
                80%,
                100% {
                    box-shadow: 0 2.5em 0 -1.3em;
                }

                40% {
                    box-shadow: 0 2.5em 0 0;
                }
            }
        </style>
        <div class="loader"></div>
    </div>
    <script type="module" src="/src/main.ts"></script>
</body>

</html>