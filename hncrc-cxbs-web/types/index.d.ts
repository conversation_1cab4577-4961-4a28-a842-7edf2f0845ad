// 此文件跟同级目录的 global.d.ts 文件一样也是全局类型声明，只不过这里存放一些零散的全局类型，无需引入直接在 .vue 、.ts 、.tsx 文件使用即可获得类型提示

type RefType<T> = T | null;

type EmitType = (event: string, ...args: any[]) => void;

type TargetContext = '_self' | '_blank';

type ComponentRef<T extends HTMLElement = HTMLDivElement> = ComponentElRef<T> | null;

type ElRef<T extends HTMLElement = HTMLDivElement> = Nullable<T>;

type ForDataType<T> = {
    [P in T]?: ForDataType<T[P]>;
};

type AnyFunction<T> = (...args: any[]) => T;

type PropType<T> = VuePropType<T>;

type Writable<T> = {
    -readonly [P in keyof T]: T[P];
};

type Nullable<T> = T | null;

type NonNullable<T> = T extends null | undefined ? never : T;

type Recordable<T = any> = Record<string, T>;

type ReadonlyRecordable<T = any> = {
    readonly [key: string]: T;
};

type Indexable<T = any> = {
    [key: string]: T;
};

type DeepPartial<T> = {
    [P in keyof T]?: DeepPartial<T[P]>;
};

type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

type Exclusive<T, U> = (Without<T, U> & U) | (Without<U, T> & T);

type TimeoutHandle = ReturnType<typeof setTimeout>;

type IntervalHandle = ReturnType<typeof setInterval>;

type Effect = 'light' | 'dark';

interface ChangeEvent extends Event {
    target: HTMLInputElement;
}

interface WheelEvent {
    path?: EventTarget[];
}

interface ImportMetaEnv extends ViteEnv {
    __: unknown;
}

interface Fn<T = any, R = T> {
    (...arg: T[]): R;
}

interface PromiseFn<T = any, R = T> {
    (...arg: T[]): Promise<R>;
}

interface ComponentElRef<T extends HTMLElement = HTMLDivElement> {
    $el: T;
}

function parseInt(s: string | number, radix?: number): number;

function parseFloat(string: string | number): number;

/** 分页接口数据返回格式 */
interface BasePaginationFetchRes<T> {
    list: T[];
    total: number;
}

/** 文流接口返回数据类型 */
interface BlobDataRes {
    /** 二进制数据 */
    data: Blob;
    /** 文件名 */
    fileName: string;
    /** 文件类型 */
    fileType: string;
}

/** 基础字典类型 */
interface DictBase<T extends boolean | number | string = string> {
    /** id */
    id: number | string;
    /** 字典值 */
    value: T;
    /** 字典显示文本 */
    label: string;
}

/** 字典类型 */
interface Dict<T extends boolean | number | string = string> extends DictBase<T> {
    /** 字典对应的code */
    code: string;
    /** 字典所属的父级字典 */
    parentCode?: string;
    /** 排序 */
    sort?: number;
    /** 删除标识 */
    delFlag?: T;
    /** 备注 */
    remark?: string;
    /** 创建时间 */
    createTime?: string;
}

/** 带配置的字典类型 一般来说都是配置颜色什么的，config 为一个对象 */
interface DictWithConfig<T, C = AnyObject> extends Dict<T> {
    /** 字典配置 */
    config: C;
}
