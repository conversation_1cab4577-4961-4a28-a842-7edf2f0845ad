iName=""
k8sConfigPath=""
pullSercret=""
k8sNs=""
DOCKER_LOGIN_NAME=${DOCKER_LOGIN_NAME}
DOCKER_LOGIN_PASSWORD=${DOCKER_LOGIN_PASSWORD}
yarnInstall() {
	/usr/local/nodejs/bin/yarn
}

initEnvVal() {
	# sit
	if [ "$deployEnv"x = "sit"x ]; then
		iName=$sitIName
		k8sConfigPath=$sitK8sConfigPath
		pullSercret=$sitPullSercret
		k8sNs=$sitK8sNs
	# pre
	elif [ "$deployEnv"x = "pre"x ]; then
		iName=$preIName
		k8sConfigPath=$preK8sConfigPath
		pullSercret=$prePullSercret
		k8sNs=$preK8sNs
	# prod
	else
		iName=$prodIName
		k8sConfigPath=$prodK8sConfigPath
		pullSercret=$prodPullSercret
		k8sNs=$prodK8sNs
	fi
}

buildImage() {
	docker -v
	docker build --network=host -t $iName $WORKSPACE/dockerBuild/ --build-arg DEPLOY_ENV=$deployEnv
	echo $DOCKER_LOGIN_PASSWORD | docker login -u $DOCKER_LOGIN_NAME --password-stdin harbor.ffcs.cn
	docker push $iName
}

genK8sDeployYaml() {
	cp $WORKSPACE/deploy/deployYaml/$deployEnv.yaml $WORKSPACE/dockerBuild/
	mv $WORKSPACE/dockerBuild/$deployEnv.yaml $WORKSPACE/dockerBuild/deploy.yaml
	sed -i "s! $targetProjectNameReplace! $projectName! g" $WORKSPACE/dockerBuild/deploy.yaml
	sed -i "s! $targetImageReplace! $iName! g" $WORKSPACE/dockerBuild/deploy.yaml
	sed -i "s! $targetNsReplace! $k8sNs! g" $WORKSPACE/dockerBuild/deploy.yaml
	sed -i "s! $targetPullSecretReplace! $pullSercret! g" $WORKSPACE/dockerBuild/deploy.yaml
}

doK8sDeploy() {
	kubectl --kubeconfig $k8sConfigPath apply -f $WORKSPACE/dockerBuild/deploy.yaml
}

initPublishWorkspace() {
	rm -rf $WORKSPACE/dockerBuild
	mkdir dockerBuild
}
