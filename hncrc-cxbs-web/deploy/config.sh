buildTime=$(date "+%Y%m%d%H%M%S")
# 工程名
projectName="hncrc-credit-cxbs-web"

# 镜像名
sitIName="harbor.ffcs.cn/hn-credit/${projectName}:${buildTime}-${deployEnv}"
preIName="harbor.ffcs.cn/hn-credit/${projectName}:${buildTime}-${deployEnv}"
prodIName="harbor.ffcs.cn/hnzx-credit-prd/${projectName}:${buildTime}-${deployEnv}"

# k8s configPath
sitK8sConfigPath="/opt/.kube/config"
preK8sConfigPath="/opt/.kube/config"
prodK8sConfigPath="/opt/prd/.kube/config"

# k8s镜像拉取secret
sitPullSercret="docker-pull-secret"
prePullSercret="docker-pull-secret"
prodPullSercret="docker-pull-secret"

# k8s命名空间名
sitK8sNs="hncrc-sit"
preK8sNs="ctyun-register"
prodK8sNs="default"

# k8s部署yaml变量替换key
targetProjectNameReplace="projectName"
targetImageReplace="imageBuildName"
targetNsReplace="k8sNamespace"
targetPullSecretReplace="k8sPullSecret"
