{"name": "hncrc-manage-platform-web", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "serve": "yarn dev", "build:prod": "rimraf dist && vite build", "build:pre": "rimraf dist && vite build --mode sit", "build:sit": "rimraf dist && vite build --mode sit", "report": "rimraf dist && vite build", "preview": "vite preview", "preview:build": "yarn build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f . -r", "cloc": "cloc . --exclude-dir=node_modules --exclude-lang=YAML", "clean:cache": "rimraf .eslintcache && rimraf yarn-lock.yaml && rimraf node_modules && yarn store prune && yarn install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{html,vue,css,scss}\" --cache-location node_modules/.cache/stylelint/", "lint": "yarn lint:eslint && yarn lint:prettier && yarn lint:stylelint"}, "dependencies": {"@pureadmin/utils": "^2.4.4", "@vueuse/core": "^10.8.0", "@vueuse/motion": "^2.1.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "animate.css": "^4.1.1", "axios": "^1.6.7", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.5.0", "element-plus": "^2.5.6", "html2canvas": "^1.4.1", "intro.js": "^7.2.0", "js-cookie": "^3.0.5", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.1.7", "pinyin-pro": "^3.19.6", "qrcode": "^1.5.3", "qs": "^6.11.2", "responsive-storage": "^2.2.0", "typeit": "8.7.1", "ua-parser-js": "^1.0.38", "vue": "3.5.6", "vue-hooks-plus": "^2.2.1", "vue-i18n": "^9.9.1", "vue-router": "^4.3.0", "vue-tippy": "^6.4.1", "vue-types": "^5.1.1", "wujie-vue3": "^1.0.22", "xss": "^1.0.15"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.2", "@commitlint/types": "^18.6.1", "@eslint/js": "^8.57.0", "@faker-js/faker": "^8.4.1", "@iconify-icons/ep": "^1.2.12", "@iconify-icons/ri": "^1.2.10", "@iconify/vue": "^4.1.1", "@intlify/unplugin-vue-i18n": "^2.0.0", "@pureadmin/theme": "^3.2.0", "@types/gradient-string": "^1.1.5", "@types/intro.js": "^5.1.5", "@types/js-cookie": "^3.0.6", "@types/node": "^20.11.20", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.11", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.17", "boxen": "^7.1.1", "cloc": "^2.11.0", "cssnano": "^6.0.5", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-simple-import-sort": "^12.0.0", "eslint-plugin-unused-imports": "^3.1.0", "eslint-plugin-vue": "^9.22.0", "gradient-string": "^2.0.2", "husky": "^9.0.11", "less": "^4.2.0", "lint-staged": "^15.2.2", "postcss": "^8.4.35", "postcss-html": "^1.6.0", "postcss-import": "^16.0.1", "postcss-scss": "^4.0.9", "prettier": "^3.2.5", "rimraf": "^5.0.5", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.71.1", "stylelint": "^16.2.1", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard-scss": "^13.0.0", "stylelint-prettier": "^5.0.0", "svgo": "^3.2.0", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.1.4", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-fake-server": "^2.1.1", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-router-warn": "^1.0.0", "vite-svg-loader": "^5.1.0", "vue-component-type-helpers": "^2.1.10", "vue-eslint-parser": "^9.4.2", "vue-tsc": "^1.8.27"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}